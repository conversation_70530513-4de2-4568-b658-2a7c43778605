{"version": 3, "sources": ["../../@tiptap/extension-placeholder/src/placeholder.ts"], "sourcesContent": ["import { Editor, Extension, isNodeEmpty } from '@tiptap/core'\nimport { Node as ProsemirrorNode } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\nimport { Decoration, DecorationSet } from '@tiptap/pm/view'\n\nexport interface PlaceholderOptions {\n  /**\n   * **The class name for the empty editor**\n   * @default 'is-editor-empty'\n   */\n  emptyEditorClass: string\n\n  /**\n   * **The class name for empty nodes**\n   * @default 'is-empty'\n   */\n  emptyNodeClass: string\n\n  /**\n   * **The placeholder content**\n   *\n   * You can use a function to return a dynamic placeholder or a string.\n   * @default 'Write something …'\n   */\n  placeholder:\n    | ((PlaceholderProps: {\n        editor: Editor\n        node: ProsemirrorNode\n        pos: number\n        hasAnchor: boolean\n      }) => string)\n    | string\n\n  /**\n   * See https://github.com/ueberdosis/tiptap/pull/5278 for more information.\n   * @deprecated This option is no longer respected and this type will be removed in the next major version.\n   */\n  considerAnyAsEmpty?: boolean\n\n  /**\n   * **Checks if the placeholder should be only shown when the editor is editable.**\n   *\n   * If true, the placeholder will only be shown when the editor is editable.\n   * If false, the placeholder will always be shown.\n   * @default true\n   */\n  showOnlyWhenEditable: boolean\n\n  /**\n   * **Checks if the placeholder should be only shown when the current node is empty.**\n   *\n   * If true, the placeholder will only be shown when the current node is empty.\n   * If false, the placeholder will be shown when any node is empty.\n   * @default true\n   */\n  showOnlyCurrent: boolean\n\n  /**\n   * **Controls if the placeholder should be shown for all descendents.**\n   *\n   * If true, the placeholder will be shown for all descendents.\n   * If false, the placeholder will only be shown for the current node.\n   * @default false\n   */\n  includeChildren: boolean\n}\n\n/**\n * This extension allows you to add a placeholder to your editor.\n * A placeholder is a text that appears when the editor or a node is empty.\n * @see https://www.tiptap.dev/api/extensions/placeholder\n */\nexport const Placeholder = Extension.create<PlaceholderOptions>({\n  name: 'placeholder',\n\n  addOptions() {\n    return {\n      emptyEditorClass: 'is-editor-empty',\n      emptyNodeClass: 'is-empty',\n      placeholder: 'Write something …',\n      showOnlyWhenEditable: true,\n      showOnlyCurrent: true,\n      includeChildren: false,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('placeholder'),\n        props: {\n          decorations: ({ doc, selection }) => {\n            const active = this.editor.isEditable || !this.options.showOnlyWhenEditable\n            const { anchor } = selection\n            const decorations: Decoration[] = []\n\n            if (!active) {\n              return null\n            }\n\n            const isEmptyDoc = this.editor.isEmpty\n\n            doc.descendants((node, pos) => {\n              const hasAnchor = anchor >= pos && anchor <= pos + node.nodeSize\n              const isEmpty = !node.isLeaf && isNodeEmpty(node)\n\n              if ((hasAnchor || !this.options.showOnlyCurrent) && isEmpty) {\n                const classes = [this.options.emptyNodeClass]\n\n                if (isEmptyDoc) {\n                  classes.push(this.options.emptyEditorClass)\n                }\n\n                const decoration = Decoration.node(pos, pos + node.nodeSize, {\n                  class: classes.join(' '),\n                  'data-placeholder':\n                    typeof this.options.placeholder === 'function'\n                      ? this.options.placeholder({\n                        editor: this.editor,\n                        node,\n                        pos,\n                        hasAnchor,\n                      })\n                      : this.options.placeholder,\n                })\n\n                decorations.push(decoration)\n              }\n\n              return this.options.includeChildren\n            })\n\n            return DecorationSet.create(doc, decorations)\n          },\n        },\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwEa,IAAA,cAAc,UAAU,OAA2B;EAC9D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,kBAAkB;MAClB,gBAAgB;MAChB,aAAa;MACb,sBAAsB;MACtB,iBAAiB;MACjB,iBAAiB;;;EAIrB,wBAAqB;AACnB,WAAO;MACL,IAAI,OAAO;QACT,KAAK,IAAI,UAAU,aAAa;QAChC,OAAO;UACL,aAAa,CAAC,EAAE,KAAK,UAAS,MAAM;AAClC,kBAAM,SAAS,KAAK,OAAO,cAAc,CAAC,KAAK,QAAQ;AACvD,kBAAM,EAAE,OAAM,IAAK;AACnB,kBAAM,cAA4B,CAAA;AAElC,gBAAI,CAAC,QAAQ;AACX,qBAAO;;AAGT,kBAAM,aAAa,KAAK,OAAO;AAE/B,gBAAI,YAAY,CAAC,MAAM,QAAO;AAC5B,oBAAM,YAAY,UAAU,OAAO,UAAU,MAAM,KAAK;AACxD,oBAAM,UAAU,CAAC,KAAK,UAAU,YAAY,IAAI;AAEhD,mBAAK,aAAa,CAAC,KAAK,QAAQ,oBAAoB,SAAS;AAC3D,sBAAM,UAAU,CAAC,KAAK,QAAQ,cAAc;AAE5C,oBAAI,YAAY;AACd,0BAAQ,KAAK,KAAK,QAAQ,gBAAgB;;AAG5C,sBAAM,aAAa,WAAW,KAAK,KAAK,MAAM,KAAK,UAAU;kBAC3D,OAAO,QAAQ,KAAK,GAAG;kBACvB,oBACE,OAAO,KAAK,QAAQ,gBAAgB,aAChC,KAAK,QAAQ,YAAY;oBACzB,QAAQ,KAAK;oBACb;oBACA;oBACA;mBACD,IACC,KAAK,QAAQ;gBACpB,CAAA;AAED,4BAAY,KAAK,UAAU;;AAG7B,qBAAO,KAAK,QAAQ;YACtB,CAAC;AAED,mBAAO,cAAc,OAAO,KAAK,WAAW;;QAE/C;OACF;;;AAGN,CAAA;", "names": []}