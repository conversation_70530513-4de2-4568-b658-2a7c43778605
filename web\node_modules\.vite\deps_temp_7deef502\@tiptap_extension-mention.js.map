{"version": 3, "sources": ["../../@tiptap/extension-mention/src/mention.ts"], "sourcesContent": ["import { mergeAttributes, Node } from '@tiptap/core'\nimport { DOMOutputSpec, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { PluginKey } from '@tiptap/pm/state'\nimport Suggestion, { SuggestionOptions } from '@tiptap/suggestion'\n\n// See `addAttributes` below\nexport interface MentionNodeAttrs {\n  /**\n   * The identifier for the selected item that was mentioned, stored as a `data-id`\n   * attribute.\n   */\n  id: string | null;\n  /**\n   * The label to be rendered by the editor as the displayed text for this mentioned\n   * item, if provided. Stored as a `data-label` attribute. See `renderLabel`.\n   */\n  label?: string | null;\n}\n\nexport type MentionOptions<SuggestionItem = any, Attrs extends Record<string, any> = MentionNodeAttrs> = {\n  /**\n   * The HTML attributes for a mention node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * A function to render the label of a mention.\n   * @deprecated use renderText and renderHTML instead\n   * @param props The render props\n   * @returns The label\n   * @example ({ options, node }) => `${options.suggestion.char}${node.attrs.label ?? node.attrs.id}`\n   */\n  renderLabel?: (props: { options: MentionOptions<SuggestionItem, Attrs>; node: ProseMirrorNode }) => string\n\n  /**\n   * A function to render the text of a mention.\n   * @param props The render props\n   * @returns The text\n   * @example ({ options, node }) => `${options.suggestion.char}${node.attrs.label ?? node.attrs.id}`\n   */\n  renderText: (props: { options: MentionOptions<SuggestionItem, Attrs>; node: ProseMirrorNode }) => string\n\n  /**\n   * A function to render the HTML of a mention.\n   * @param props The render props\n   * @returns The HTML as a ProseMirror DOM Output Spec\n   * @example ({ options, node }) => ['span', { 'data-type': 'mention' }, `${options.suggestion.char}${node.attrs.label ?? node.attrs.id}`]\n   */\n  renderHTML: (props: { options: MentionOptions<SuggestionItem, Attrs>; node: ProseMirrorNode }) => DOMOutputSpec\n\n  /**\n   * Whether to delete the trigger character with backspace.\n   * @default false\n   */\n  deleteTriggerWithBackspace: boolean\n\n  /**\n   * The suggestion options.\n   * @default {}\n   * @example { char: '@', pluginKey: MentionPluginKey, command: ({ editor, range, props }) => { ... } }\n   */\n  suggestion: Omit<SuggestionOptions<SuggestionItem, Attrs>, 'editor'>\n}\n\n/**\n * The plugin key for the mention plugin.\n * @default 'mention'\n */\nexport const MentionPluginKey = new PluginKey('mention')\n\n/**\n * This extension allows you to insert mentions into the editor.\n * @see https://www.tiptap.dev/api/extensions/mention\n */\nexport const Mention = Node.create<MentionOptions>({\n  name: 'mention',\n\n  priority: 101,\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n      renderText({ options, node }) {\n        return `${options.suggestion.char}${node.attrs.label ?? node.attrs.id}`\n      },\n      deleteTriggerWithBackspace: false,\n      renderHTML({ options, node }) {\n        return [\n          'span',\n          mergeAttributes(this.HTMLAttributes, options.HTMLAttributes),\n          `${options.suggestion.char}${node.attrs.label ?? node.attrs.id}`,\n        ]\n      },\n      suggestion: {\n        char: '@',\n        pluginKey: MentionPluginKey,\n        command: ({ editor, range, props }) => {\n          // increase range.to by one when the next node is of type \"text\"\n          // and starts with a space character\n          const nodeAfter = editor.view.state.selection.$to.nodeAfter\n          const overrideSpace = nodeAfter?.text?.startsWith(' ')\n\n          if (overrideSpace) {\n            range.to += 1\n          }\n\n          editor\n            .chain()\n            .focus()\n            .insertContentAt(range, [\n              {\n                type: this.name,\n                attrs: props,\n              },\n              {\n                type: 'text',\n                text: ' ',\n              },\n            ])\n            .run()\n\n          // get reference to `window` object from editor element, to support cross-frame JS usage\n          editor.view.dom.ownerDocument.defaultView?.getSelection()?.collapseToEnd()\n        },\n        allow: ({ state, range }) => {\n          const $from = state.doc.resolve(range.from)\n          const type = state.schema.nodes[this.name]\n          const allow = !!$from.parent.type.contentMatch.matchType(type)\n\n          return allow\n        },\n      },\n    }\n  },\n\n  group: 'inline',\n\n  inline: true,\n\n  selectable: false,\n\n  atom: true,\n\n  addAttributes() {\n    return {\n      id: {\n        default: null,\n        parseHTML: element => element.getAttribute('data-id'),\n        renderHTML: attributes => {\n          if (!attributes.id) {\n            return {}\n          }\n\n          return {\n            'data-id': attributes.id,\n          }\n        },\n      },\n\n      label: {\n        default: null,\n        parseHTML: element => element.getAttribute('data-label'),\n        renderHTML: attributes => {\n          if (!attributes.label) {\n            return {}\n          }\n\n          return {\n            'data-label': attributes.label,\n          }\n        },\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: `span[data-type=\"${this.name}\"]`,\n      },\n    ]\n  },\n\n  renderHTML({ node, HTMLAttributes }) {\n    if (this.options.renderLabel !== undefined) {\n      console.warn('renderLabel is deprecated use renderText and renderHTML instead')\n      return [\n        'span',\n        mergeAttributes({ 'data-type': this.name }, this.options.HTMLAttributes, HTMLAttributes),\n        this.options.renderLabel({\n          options: this.options,\n          node,\n        }),\n      ]\n    }\n    const mergedOptions = { ...this.options }\n\n    mergedOptions.HTMLAttributes = mergeAttributes({ 'data-type': this.name }, this.options.HTMLAttributes, HTMLAttributes)\n    const html = this.options.renderHTML({\n      options: mergedOptions,\n      node,\n    })\n\n    if (typeof html === 'string') {\n      return [\n        'span',\n        mergeAttributes({ 'data-type': this.name }, this.options.HTMLAttributes, HTMLAttributes),\n        html,\n      ]\n    }\n    return html\n  },\n\n  renderText({ node }) {\n    if (this.options.renderLabel !== undefined) {\n      console.warn('renderLabel is deprecated use renderText and renderHTML instead')\n      return this.options.renderLabel({\n        options: this.options,\n        node,\n      })\n    }\n    return this.options.renderText({\n      options: this.options,\n      node,\n    })\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Backspace: () => this.editor.commands.command(({ tr, state }) => {\n        let isMention = false\n        const { selection } = state\n        const { empty, anchor } = selection\n\n        if (!empty) {\n          return false\n        }\n\n        state.doc.nodesBetween(anchor - 1, anchor, (node, pos) => {\n          if (node.type.name === this.name) {\n            isMention = true\n            tr.insertText(\n              this.options.deleteTriggerWithBackspace ? '' : this.options.suggestion.char || '',\n              pos,\n              pos + node.nodeSize,\n            )\n\n            return false\n          }\n        })\n\n        return isMention\n      }),\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      Suggestion({\n        editor: this.editor,\n        ...this.options.suggestion,\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAsEa,mBAAmB,IAAI,UAAU,SAAS;AAM1C,IAAA,UAAU,KAAK,OAAuB;EACjD,MAAM;EAEN,UAAU;EAEV,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;MAChB,WAAW,EAAE,SAAS,KAAI,GAAE;;AAC1B,eAAO,GAAG,QAAQ,WAAW,IAAI,IAAG,KAAA,KAAK,MAAM,WAAK,QAAA,OAAA,SAAA,KAAI,KAAK,MAAM,EAAE;;MAEvE,4BAA4B;MAC5B,WAAW,EAAE,SAAS,KAAI,GAAE;;AAC1B,eAAO;UACL;UACA,gBAAgB,KAAK,gBAAgB,QAAQ,cAAc;UAC3D,GAAG,QAAQ,WAAW,IAAI,IAAG,KAAA,KAAK,MAAM,WAAS,QAAA,OAAA,SAAA,KAAA,KAAK,MAAM,EAAE;;;MAGlE,YAAY;QACV,MAAM;QACN,WAAW;QACX,SAAS,CAAC,EAAE,QAAQ,OAAO,MAAK,MAAM;;AAGpC,gBAAM,YAAY,OAAO,KAAK,MAAM,UAAU,IAAI;AAClD,gBAAM,iBAAgB,KAAA,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,UAAM,QAAA,OAAA,SAAA,SAAA,GAAA,WAAW,GAAG;AAErD,cAAI,eAAe;AACjB,kBAAM,MAAM;;AAGd,iBACG,MAAK,EACL,MAAK,EACL,gBAAgB,OAAO;YACtB;cACE,MAAM,KAAK;cACX,OAAO;YACR;YACD;cACE,MAAM;cACN,MAAM;YACP;WACF,EACA,IAAG;AAGN,WAAA,MAAA,KAAA,OAAO,KAAK,IAAI,cAAc,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAE,aAAY,OAAI,QAAA,OAAA,SAAA,SAAA,GAAA,cAAa;;QAE1E,OAAO,CAAC,EAAE,OAAO,MAAK,MAAM;AAC1B,gBAAM,QAAQ,MAAM,IAAI,QAAQ,MAAM,IAAI;AAC1C,gBAAM,OAAO,MAAM,OAAO,MAAM,KAAK,IAAI;AACzC,gBAAM,QAAQ,CAAC,CAAC,MAAM,OAAO,KAAK,aAAa,UAAU,IAAI;AAE7D,iBAAO;;MAEV;;;EAIL,OAAO;EAEP,QAAQ;EAER,YAAY;EAEZ,MAAM;EAEN,gBAAa;AACX,WAAO;MACL,IAAI;QACF,SAAS;QACT,WAAW,aAAW,QAAQ,aAAa,SAAS;QACpD,YAAY,gBAAa;AACvB,cAAI,CAAC,WAAW,IAAI;AAClB,mBAAO,CAAA;;AAGT,iBAAO;YACL,WAAW,WAAW;;;MAG3B;MAED,OAAO;QACL,SAAS;QACT,WAAW,aAAW,QAAQ,aAAa,YAAY;QACvD,YAAY,gBAAa;AACvB,cAAI,CAAC,WAAW,OAAO;AACrB,mBAAO,CAAA;;AAGT,iBAAO;YACL,cAAc,WAAW;;;MAG9B;;;EAIL,YAAS;AACP,WAAO;MACL;QACE,KAAK,mBAAmB,KAAK,IAAI;MAClC;;;EAIL,WAAW,EAAE,MAAM,eAAc,GAAE;AACjC,QAAI,KAAK,QAAQ,gBAAgB,QAAW;AAC1C,cAAQ,KAAK,iEAAiE;AAC9E,aAAO;QACL;QACA,gBAAgB,EAAE,aAAa,KAAK,KAAI,GAAI,KAAK,QAAQ,gBAAgB,cAAc;QACvF,KAAK,QAAQ,YAAY;UACvB,SAAS,KAAK;UACd;SACD;;;AAGL,UAAM,gBAAgB,EAAE,GAAG,KAAK,QAAO;AAEvC,kBAAc,iBAAiB,gBAAgB,EAAE,aAAa,KAAK,KAAI,GAAI,KAAK,QAAQ,gBAAgB,cAAc;AACtH,UAAM,OAAO,KAAK,QAAQ,WAAW;MACnC,SAAS;MACT;IACD,CAAA;AAED,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO;QACL;QACA,gBAAgB,EAAE,aAAa,KAAK,KAAI,GAAI,KAAK,QAAQ,gBAAgB,cAAc;QACvF;;;AAGJ,WAAO;;EAGT,WAAW,EAAE,KAAI,GAAE;AACjB,QAAI,KAAK,QAAQ,gBAAgB,QAAW;AAC1C,cAAQ,KAAK,iEAAiE;AAC9E,aAAO,KAAK,QAAQ,YAAY;QAC9B,SAAS,KAAK;QACd;MACD,CAAA;;AAEH,WAAO,KAAK,QAAQ,WAAW;MAC7B,SAAS,KAAK;MACd;IACD,CAAA;;EAGH,uBAAoB;AAClB,WAAO;MACL,WAAW,MAAM,KAAK,OAAO,SAAS,QAAQ,CAAC,EAAE,IAAI,MAAK,MAAM;AAC9D,YAAI,YAAY;AAChB,cAAM,EAAE,UAAS,IAAK;AACtB,cAAM,EAAE,OAAO,OAAM,IAAK;AAE1B,YAAI,CAAC,OAAO;AACV,iBAAO;;AAGT,cAAM,IAAI,aAAa,SAAS,GAAG,QAAQ,CAAC,MAAM,QAAO;AACvD,cAAI,KAAK,KAAK,SAAS,KAAK,MAAM;AAChC,wBAAY;AACZ,eAAG,WACD,KAAK,QAAQ,6BAA6B,KAAK,KAAK,QAAQ,WAAW,QAAQ,IAC/E,KACA,MAAM,KAAK,QAAQ;AAGrB,mBAAO;;QAEX,CAAC;AAED,eAAO;MACT,CAAC;;;EAIL,wBAAqB;AACnB,WAAO;MACL,WAAW;QACT,QAAQ,KAAK;QACb,GAAG,KAAK,QAAQ;OACjB;;;AAGN,CAAA;", "names": []}