{"version": 3, "sources": ["../../prosemirror-model/dist/index.js", "../../orderedmap/dist/index.js"], "sourcesContent": ["import OrderedMap from 'orderedmap';\n\nfunction findDiffStart(a, b, pos) {\n    for (let i = 0;; i++) {\n        if (i == a.childCount || i == b.childCount)\n            return a.childCount == b.childCount ? null : pos;\n        let childA = a.child(i), childB = b.child(i);\n        if (childA == childB) {\n            pos += childA.nodeSize;\n            continue;\n        }\n        if (!childA.sameMarkup(childB))\n            return pos;\n        if (childA.isText && childA.text != childB.text) {\n            for (let j = 0; childA.text[j] == childB.text[j]; j++)\n                pos++;\n            return pos;\n        }\n        if (childA.content.size || childB.content.size) {\n            let inner = findDiffStart(childA.content, childB.content, pos + 1);\n            if (inner != null)\n                return inner;\n        }\n        pos += childA.nodeSize;\n    }\n}\nfunction findDiffEnd(a, b, posA, posB) {\n    for (let iA = a.childCount, iB = b.childCount;;) {\n        if (iA == 0 || iB == 0)\n            return iA == iB ? null : { a: posA, b: posB };\n        let childA = a.child(--iA), childB = b.child(--iB), size = childA.nodeSize;\n        if (childA == childB) {\n            posA -= size;\n            posB -= size;\n            continue;\n        }\n        if (!childA.sameMarkup(childB))\n            return { a: posA, b: posB };\n        if (childA.isText && childA.text != childB.text) {\n            let same = 0, minSize = Math.min(childA.text.length, childB.text.length);\n            while (same < minSize && childA.text[childA.text.length - same - 1] == childB.text[childB.text.length - same - 1]) {\n                same++;\n                posA--;\n                posB--;\n            }\n            return { a: posA, b: posB };\n        }\n        if (childA.content.size || childB.content.size) {\n            let inner = findDiffEnd(childA.content, childB.content, posA - 1, posB - 1);\n            if (inner)\n                return inner;\n        }\n        posA -= size;\n        posB -= size;\n    }\n}\n\n/**\nA fragment represents a node's collection of child nodes.\n\nLike nodes, fragments are persistent data structures, and you\nshould not mutate them or their content. Rather, you create new\ninstances whenever needed. The API tries to make this easy.\n*/\nclass Fragment {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The child nodes in this fragment.\n    */\n    content, size) {\n        this.content = content;\n        this.size = size || 0;\n        if (size == null)\n            for (let i = 0; i < content.length; i++)\n                this.size += content[i].nodeSize;\n    }\n    /**\n    Invoke a callback for all descendant nodes between the given two\n    positions (relative to start of this fragment). Doesn't descend\n    into a node when the callback returns `false`.\n    */\n    nodesBetween(from, to, f, nodeStart = 0, parent) {\n        for (let i = 0, pos = 0; pos < to; i++) {\n            let child = this.content[i], end = pos + child.nodeSize;\n            if (end > from && f(child, nodeStart + pos, parent || null, i) !== false && child.content.size) {\n                let start = pos + 1;\n                child.nodesBetween(Math.max(0, from - start), Math.min(child.content.size, to - start), f, nodeStart + start);\n            }\n            pos = end;\n        }\n    }\n    /**\n    Call the given callback for every descendant node. `pos` will be\n    relative to the start of the fragment. The callback may return\n    `false` to prevent traversal of a given node's children.\n    */\n    descendants(f) {\n        this.nodesBetween(0, this.size, f);\n    }\n    /**\n    Extract the text between `from` and `to`. See the same method on\n    [`Node`](https://prosemirror.net/docs/ref/#model.Node.textBetween).\n    */\n    textBetween(from, to, blockSeparator, leafText) {\n        let text = \"\", first = true;\n        this.nodesBetween(from, to, (node, pos) => {\n            let nodeText = node.isText ? node.text.slice(Math.max(from, pos) - pos, to - pos)\n                : !node.isLeaf ? \"\"\n                    : leafText ? (typeof leafText === \"function\" ? leafText(node) : leafText)\n                        : node.type.spec.leafText ? node.type.spec.leafText(node)\n                            : \"\";\n            if (node.isBlock && (node.isLeaf && nodeText || node.isTextblock) && blockSeparator) {\n                if (first)\n                    first = false;\n                else\n                    text += blockSeparator;\n            }\n            text += nodeText;\n        }, 0);\n        return text;\n    }\n    /**\n    Create a new fragment containing the combined content of this\n    fragment and the other.\n    */\n    append(other) {\n        if (!other.size)\n            return this;\n        if (!this.size)\n            return other;\n        let last = this.lastChild, first = other.firstChild, content = this.content.slice(), i = 0;\n        if (last.isText && last.sameMarkup(first)) {\n            content[content.length - 1] = last.withText(last.text + first.text);\n            i = 1;\n        }\n        for (; i < other.content.length; i++)\n            content.push(other.content[i]);\n        return new Fragment(content, this.size + other.size);\n    }\n    /**\n    Cut out the sub-fragment between the two given positions.\n    */\n    cut(from, to = this.size) {\n        if (from == 0 && to == this.size)\n            return this;\n        let result = [], size = 0;\n        if (to > from)\n            for (let i = 0, pos = 0; pos < to; i++) {\n                let child = this.content[i], end = pos + child.nodeSize;\n                if (end > from) {\n                    if (pos < from || end > to) {\n                        if (child.isText)\n                            child = child.cut(Math.max(0, from - pos), Math.min(child.text.length, to - pos));\n                        else\n                            child = child.cut(Math.max(0, from - pos - 1), Math.min(child.content.size, to - pos - 1));\n                    }\n                    result.push(child);\n                    size += child.nodeSize;\n                }\n                pos = end;\n            }\n        return new Fragment(result, size);\n    }\n    /**\n    @internal\n    */\n    cutByIndex(from, to) {\n        if (from == to)\n            return Fragment.empty;\n        if (from == 0 && to == this.content.length)\n            return this;\n        return new Fragment(this.content.slice(from, to));\n    }\n    /**\n    Create a new fragment in which the node at the given index is\n    replaced by the given node.\n    */\n    replaceChild(index, node) {\n        let current = this.content[index];\n        if (current == node)\n            return this;\n        let copy = this.content.slice();\n        let size = this.size + node.nodeSize - current.nodeSize;\n        copy[index] = node;\n        return new Fragment(copy, size);\n    }\n    /**\n    Create a new fragment by prepending the given node to this\n    fragment.\n    */\n    addToStart(node) {\n        return new Fragment([node].concat(this.content), this.size + node.nodeSize);\n    }\n    /**\n    Create a new fragment by appending the given node to this\n    fragment.\n    */\n    addToEnd(node) {\n        return new Fragment(this.content.concat(node), this.size + node.nodeSize);\n    }\n    /**\n    Compare this fragment to another one.\n    */\n    eq(other) {\n        if (this.content.length != other.content.length)\n            return false;\n        for (let i = 0; i < this.content.length; i++)\n            if (!this.content[i].eq(other.content[i]))\n                return false;\n        return true;\n    }\n    /**\n    The first child of the fragment, or `null` if it is empty.\n    */\n    get firstChild() { return this.content.length ? this.content[0] : null; }\n    /**\n    The last child of the fragment, or `null` if it is empty.\n    */\n    get lastChild() { return this.content.length ? this.content[this.content.length - 1] : null; }\n    /**\n    The number of child nodes in this fragment.\n    */\n    get childCount() { return this.content.length; }\n    /**\n    Get the child node at the given index. Raise an error when the\n    index is out of range.\n    */\n    child(index) {\n        let found = this.content[index];\n        if (!found)\n            throw new RangeError(\"Index \" + index + \" out of range for \" + this);\n        return found;\n    }\n    /**\n    Get the child node at the given index, if it exists.\n    */\n    maybeChild(index) {\n        return this.content[index] || null;\n    }\n    /**\n    Call `f` for every child node, passing the node, its offset\n    into this parent node, and its index.\n    */\n    forEach(f) {\n        for (let i = 0, p = 0; i < this.content.length; i++) {\n            let child = this.content[i];\n            f(child, p, i);\n            p += child.nodeSize;\n        }\n    }\n    /**\n    Find the first position at which this fragment and another\n    fragment differ, or `null` if they are the same.\n    */\n    findDiffStart(other, pos = 0) {\n        return findDiffStart(this, other, pos);\n    }\n    /**\n    Find the first position, searching from the end, at which this\n    fragment and the given fragment differ, or `null` if they are\n    the same. Since this position will not be the same in both\n    nodes, an object with two separate positions is returned.\n    */\n    findDiffEnd(other, pos = this.size, otherPos = other.size) {\n        return findDiffEnd(this, other, pos, otherPos);\n    }\n    /**\n    Find the index and inner offset corresponding to a given relative\n    position in this fragment. The result object will be reused\n    (overwritten) the next time the function is called. @internal\n    */\n    findIndex(pos, round = -1) {\n        if (pos == 0)\n            return retIndex(0, pos);\n        if (pos == this.size)\n            return retIndex(this.content.length, pos);\n        if (pos > this.size || pos < 0)\n            throw new RangeError(`Position ${pos} outside of fragment (${this})`);\n        for (let i = 0, curPos = 0;; i++) {\n            let cur = this.child(i), end = curPos + cur.nodeSize;\n            if (end >= pos) {\n                if (end == pos || round > 0)\n                    return retIndex(i + 1, end);\n                return retIndex(i, curPos);\n            }\n            curPos = end;\n        }\n    }\n    /**\n    Return a debugging string that describes this fragment.\n    */\n    toString() { return \"<\" + this.toStringInner() + \">\"; }\n    /**\n    @internal\n    */\n    toStringInner() { return this.content.join(\", \"); }\n    /**\n    Create a JSON-serializeable representation of this fragment.\n    */\n    toJSON() {\n        return this.content.length ? this.content.map(n => n.toJSON()) : null;\n    }\n    /**\n    Deserialize a fragment from its JSON representation.\n    */\n    static fromJSON(schema, value) {\n        if (!value)\n            return Fragment.empty;\n        if (!Array.isArray(value))\n            throw new RangeError(\"Invalid input for Fragment.fromJSON\");\n        return new Fragment(value.map(schema.nodeFromJSON));\n    }\n    /**\n    Build a fragment from an array of nodes. Ensures that adjacent\n    text nodes with the same marks are joined together.\n    */\n    static fromArray(array) {\n        if (!array.length)\n            return Fragment.empty;\n        let joined, size = 0;\n        for (let i = 0; i < array.length; i++) {\n            let node = array[i];\n            size += node.nodeSize;\n            if (i && node.isText && array[i - 1].sameMarkup(node)) {\n                if (!joined)\n                    joined = array.slice(0, i);\n                joined[joined.length - 1] = node\n                    .withText(joined[joined.length - 1].text + node.text);\n            }\n            else if (joined) {\n                joined.push(node);\n            }\n        }\n        return new Fragment(joined || array, size);\n    }\n    /**\n    Create a fragment from something that can be interpreted as a\n    set of nodes. For `null`, it returns the empty fragment. For a\n    fragment, the fragment itself. For a node or array of nodes, a\n    fragment containing those nodes.\n    */\n    static from(nodes) {\n        if (!nodes)\n            return Fragment.empty;\n        if (nodes instanceof Fragment)\n            return nodes;\n        if (Array.isArray(nodes))\n            return this.fromArray(nodes);\n        if (nodes.attrs)\n            return new Fragment([nodes], nodes.nodeSize);\n        throw new RangeError(\"Can not convert \" + nodes + \" to a Fragment\" +\n            (nodes.nodesBetween ? \" (looks like multiple versions of prosemirror-model were loaded)\" : \"\"));\n    }\n}\n/**\nAn empty fragment. Intended to be reused whenever a node doesn't\ncontain anything (rather than allocating a new empty fragment for\neach leaf node).\n*/\nFragment.empty = new Fragment([], 0);\nconst found = { index: 0, offset: 0 };\nfunction retIndex(index, offset) {\n    found.index = index;\n    found.offset = offset;\n    return found;\n}\n\nfunction compareDeep(a, b) {\n    if (a === b)\n        return true;\n    if (!(a && typeof a == \"object\") ||\n        !(b && typeof b == \"object\"))\n        return false;\n    let array = Array.isArray(a);\n    if (Array.isArray(b) != array)\n        return false;\n    if (array) {\n        if (a.length != b.length)\n            return false;\n        for (let i = 0; i < a.length; i++)\n            if (!compareDeep(a[i], b[i]))\n                return false;\n    }\n    else {\n        for (let p in a)\n            if (!(p in b) || !compareDeep(a[p], b[p]))\n                return false;\n        for (let p in b)\n            if (!(p in a))\n                return false;\n    }\n    return true;\n}\n\n/**\nA mark is a piece of information that can be attached to a node,\nsuch as it being emphasized, in code font, or a link. It has a\ntype and optionally a set of attributes that provide further\ninformation (such as the target of the link). Marks are created\nthrough a `Schema`, which controls which types exist and which\nattributes they have.\n*/\nclass Mark {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The type of this mark.\n    */\n    type, \n    /**\n    The attributes associated with this mark.\n    */\n    attrs) {\n        this.type = type;\n        this.attrs = attrs;\n    }\n    /**\n    Given a set of marks, create a new set which contains this one as\n    well, in the right position. If this mark is already in the set,\n    the set itself is returned. If any marks that are set to be\n    [exclusive](https://prosemirror.net/docs/ref/#model.MarkSpec.excludes) with this mark are present,\n    those are replaced by this one.\n    */\n    addToSet(set) {\n        let copy, placed = false;\n        for (let i = 0; i < set.length; i++) {\n            let other = set[i];\n            if (this.eq(other))\n                return set;\n            if (this.type.excludes(other.type)) {\n                if (!copy)\n                    copy = set.slice(0, i);\n            }\n            else if (other.type.excludes(this.type)) {\n                return set;\n            }\n            else {\n                if (!placed && other.type.rank > this.type.rank) {\n                    if (!copy)\n                        copy = set.slice(0, i);\n                    copy.push(this);\n                    placed = true;\n                }\n                if (copy)\n                    copy.push(other);\n            }\n        }\n        if (!copy)\n            copy = set.slice();\n        if (!placed)\n            copy.push(this);\n        return copy;\n    }\n    /**\n    Remove this mark from the given set, returning a new set. If this\n    mark is not in the set, the set itself is returned.\n    */\n    removeFromSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (this.eq(set[i]))\n                return set.slice(0, i).concat(set.slice(i + 1));\n        return set;\n    }\n    /**\n    Test whether this mark is in the given set of marks.\n    */\n    isInSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (this.eq(set[i]))\n                return true;\n        return false;\n    }\n    /**\n    Test whether this mark has the same type and attributes as\n    another mark.\n    */\n    eq(other) {\n        return this == other ||\n            (this.type == other.type && compareDeep(this.attrs, other.attrs));\n    }\n    /**\n    Convert this mark to a JSON-serializeable representation.\n    */\n    toJSON() {\n        let obj = { type: this.type.name };\n        for (let _ in this.attrs) {\n            obj.attrs = this.attrs;\n            break;\n        }\n        return obj;\n    }\n    /**\n    Deserialize a mark from JSON.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            throw new RangeError(\"Invalid input for Mark.fromJSON\");\n        let type = schema.marks[json.type];\n        if (!type)\n            throw new RangeError(`There is no mark type ${json.type} in this schema`);\n        let mark = type.create(json.attrs);\n        type.checkAttrs(mark.attrs);\n        return mark;\n    }\n    /**\n    Test whether two sets of marks are identical.\n    */\n    static sameSet(a, b) {\n        if (a == b)\n            return true;\n        if (a.length != b.length)\n            return false;\n        for (let i = 0; i < a.length; i++)\n            if (!a[i].eq(b[i]))\n                return false;\n        return true;\n    }\n    /**\n    Create a properly sorted mark set from null, a single mark, or an\n    unsorted array of marks.\n    */\n    static setFrom(marks) {\n        if (!marks || Array.isArray(marks) && marks.length == 0)\n            return Mark.none;\n        if (marks instanceof Mark)\n            return [marks];\n        let copy = marks.slice();\n        copy.sort((a, b) => a.type.rank - b.type.rank);\n        return copy;\n    }\n}\n/**\nThe empty set of marks.\n*/\nMark.none = [];\n\n/**\nError type raised by [`Node.replace`](https://prosemirror.net/docs/ref/#model.Node.replace) when\ngiven an invalid replacement.\n*/\nclass ReplaceError extends Error {\n}\n/*\nReplaceError = function(this: any, message: string) {\n  let err = Error.call(this, message)\n  ;(err as any).__proto__ = ReplaceError.prototype\n  return err\n} as any\n\nReplaceError.prototype = Object.create(Error.prototype)\nReplaceError.prototype.constructor = ReplaceError\nReplaceError.prototype.name = \"ReplaceError\"\n*/\n/**\nA slice represents a piece cut out of a larger document. It\nstores not only a fragment, but also the depth up to which nodes on\nboth side are ‘open’ (cut through).\n*/\nclass Slice {\n    /**\n    Create a slice. When specifying a non-zero open depth, you must\n    make sure that there are nodes of at least that depth at the\n    appropriate side of the fragment—i.e. if the fragment is an\n    empty paragraph node, `openStart` and `openEnd` can't be greater\n    than 1.\n    \n    It is not necessary for the content of open nodes to conform to\n    the schema's content constraints, though it should be a valid\n    start/end/middle for such a node, depending on which sides are\n    open.\n    */\n    constructor(\n    /**\n    The slice's content.\n    */\n    content, \n    /**\n    The open depth at the start of the fragment.\n    */\n    openStart, \n    /**\n    The open depth at the end.\n    */\n    openEnd) {\n        this.content = content;\n        this.openStart = openStart;\n        this.openEnd = openEnd;\n    }\n    /**\n    The size this slice would add when inserted into a document.\n    */\n    get size() {\n        return this.content.size - this.openStart - this.openEnd;\n    }\n    /**\n    @internal\n    */\n    insertAt(pos, fragment) {\n        let content = insertInto(this.content, pos + this.openStart, fragment);\n        return content && new Slice(content, this.openStart, this.openEnd);\n    }\n    /**\n    @internal\n    */\n    removeBetween(from, to) {\n        return new Slice(removeRange(this.content, from + this.openStart, to + this.openStart), this.openStart, this.openEnd);\n    }\n    /**\n    Tests whether this slice is equal to another slice.\n    */\n    eq(other) {\n        return this.content.eq(other.content) && this.openStart == other.openStart && this.openEnd == other.openEnd;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.content + \"(\" + this.openStart + \",\" + this.openEnd + \")\";\n    }\n    /**\n    Convert a slice to a JSON-serializable representation.\n    */\n    toJSON() {\n        if (!this.content.size)\n            return null;\n        let json = { content: this.content.toJSON() };\n        if (this.openStart > 0)\n            json.openStart = this.openStart;\n        if (this.openEnd > 0)\n            json.openEnd = this.openEnd;\n        return json;\n    }\n    /**\n    Deserialize a slice from its JSON representation.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            return Slice.empty;\n        let openStart = json.openStart || 0, openEnd = json.openEnd || 0;\n        if (typeof openStart != \"number\" || typeof openEnd != \"number\")\n            throw new RangeError(\"Invalid input for Slice.fromJSON\");\n        return new Slice(Fragment.fromJSON(schema, json.content), openStart, openEnd);\n    }\n    /**\n    Create a slice from a fragment by taking the maximum possible\n    open value on both side of the fragment.\n    */\n    static maxOpen(fragment, openIsolating = true) {\n        let openStart = 0, openEnd = 0;\n        for (let n = fragment.firstChild; n && !n.isLeaf && (openIsolating || !n.type.spec.isolating); n = n.firstChild)\n            openStart++;\n        for (let n = fragment.lastChild; n && !n.isLeaf && (openIsolating || !n.type.spec.isolating); n = n.lastChild)\n            openEnd++;\n        return new Slice(fragment, openStart, openEnd);\n    }\n}\n/**\nThe empty slice.\n*/\nSlice.empty = new Slice(Fragment.empty, 0, 0);\nfunction removeRange(content, from, to) {\n    let { index, offset } = content.findIndex(from), child = content.maybeChild(index);\n    let { index: indexTo, offset: offsetTo } = content.findIndex(to);\n    if (offset == from || child.isText) {\n        if (offsetTo != to && !content.child(indexTo).isText)\n            throw new RangeError(\"Removing non-flat range\");\n        return content.cut(0, from).append(content.cut(to));\n    }\n    if (index != indexTo)\n        throw new RangeError(\"Removing non-flat range\");\n    return content.replaceChild(index, child.copy(removeRange(child.content, from - offset - 1, to - offset - 1)));\n}\nfunction insertInto(content, dist, insert, parent) {\n    let { index, offset } = content.findIndex(dist), child = content.maybeChild(index);\n    if (offset == dist || child.isText) {\n        if (parent && !parent.canReplace(index, index, insert))\n            return null;\n        return content.cut(0, dist).append(insert).append(content.cut(dist));\n    }\n    let inner = insertInto(child.content, dist - offset - 1, insert);\n    return inner && content.replaceChild(index, child.copy(inner));\n}\nfunction replace($from, $to, slice) {\n    if (slice.openStart > $from.depth)\n        throw new ReplaceError(\"Inserted content deeper than insertion position\");\n    if ($from.depth - slice.openStart != $to.depth - slice.openEnd)\n        throw new ReplaceError(\"Inconsistent open depths\");\n    return replaceOuter($from, $to, slice, 0);\n}\nfunction replaceOuter($from, $to, slice, depth) {\n    let index = $from.index(depth), node = $from.node(depth);\n    if (index == $to.index(depth) && depth < $from.depth - slice.openStart) {\n        let inner = replaceOuter($from, $to, slice, depth + 1);\n        return node.copy(node.content.replaceChild(index, inner));\n    }\n    else if (!slice.content.size) {\n        return close(node, replaceTwoWay($from, $to, depth));\n    }\n    else if (!slice.openStart && !slice.openEnd && $from.depth == depth && $to.depth == depth) { // Simple, flat case\n        let parent = $from.parent, content = parent.content;\n        return close(parent, content.cut(0, $from.parentOffset).append(slice.content).append(content.cut($to.parentOffset)));\n    }\n    else {\n        let { start, end } = prepareSliceForReplace(slice, $from);\n        return close(node, replaceThreeWay($from, start, end, $to, depth));\n    }\n}\nfunction checkJoin(main, sub) {\n    if (!sub.type.compatibleContent(main.type))\n        throw new ReplaceError(\"Cannot join \" + sub.type.name + \" onto \" + main.type.name);\n}\nfunction joinable($before, $after, depth) {\n    let node = $before.node(depth);\n    checkJoin(node, $after.node(depth));\n    return node;\n}\nfunction addNode(child, target) {\n    let last = target.length - 1;\n    if (last >= 0 && child.isText && child.sameMarkup(target[last]))\n        target[last] = child.withText(target[last].text + child.text);\n    else\n        target.push(child);\n}\nfunction addRange($start, $end, depth, target) {\n    let node = ($end || $start).node(depth);\n    let startIndex = 0, endIndex = $end ? $end.index(depth) : node.childCount;\n    if ($start) {\n        startIndex = $start.index(depth);\n        if ($start.depth > depth) {\n            startIndex++;\n        }\n        else if ($start.textOffset) {\n            addNode($start.nodeAfter, target);\n            startIndex++;\n        }\n    }\n    for (let i = startIndex; i < endIndex; i++)\n        addNode(node.child(i), target);\n    if ($end && $end.depth == depth && $end.textOffset)\n        addNode($end.nodeBefore, target);\n}\nfunction close(node, content) {\n    node.type.checkContent(content);\n    return node.copy(content);\n}\nfunction replaceThreeWay($from, $start, $end, $to, depth) {\n    let openStart = $from.depth > depth && joinable($from, $start, depth + 1);\n    let openEnd = $to.depth > depth && joinable($end, $to, depth + 1);\n    let content = [];\n    addRange(null, $from, depth, content);\n    if (openStart && openEnd && $start.index(depth) == $end.index(depth)) {\n        checkJoin(openStart, openEnd);\n        addNode(close(openStart, replaceThreeWay($from, $start, $end, $to, depth + 1)), content);\n    }\n    else {\n        if (openStart)\n            addNode(close(openStart, replaceTwoWay($from, $start, depth + 1)), content);\n        addRange($start, $end, depth, content);\n        if (openEnd)\n            addNode(close(openEnd, replaceTwoWay($end, $to, depth + 1)), content);\n    }\n    addRange($to, null, depth, content);\n    return new Fragment(content);\n}\nfunction replaceTwoWay($from, $to, depth) {\n    let content = [];\n    addRange(null, $from, depth, content);\n    if ($from.depth > depth) {\n        let type = joinable($from, $to, depth + 1);\n        addNode(close(type, replaceTwoWay($from, $to, depth + 1)), content);\n    }\n    addRange($to, null, depth, content);\n    return new Fragment(content);\n}\nfunction prepareSliceForReplace(slice, $along) {\n    let extra = $along.depth - slice.openStart, parent = $along.node(extra);\n    let node = parent.copy(slice.content);\n    for (let i = extra - 1; i >= 0; i--)\n        node = $along.node(i).copy(Fragment.from(node));\n    return { start: node.resolveNoCache(slice.openStart + extra),\n        end: node.resolveNoCache(node.content.size - slice.openEnd - extra) };\n}\n\n/**\nYou can [_resolve_](https://prosemirror.net/docs/ref/#model.Node.resolve) a position to get more\ninformation about it. Objects of this class represent such a\nresolved position, providing various pieces of context\ninformation, and some helper methods.\n\nThroughout this interface, methods that take an optional `depth`\nparameter will interpret undefined as `this.depth` and negative\nnumbers as `this.depth + value`.\n*/\nclass ResolvedPos {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The position that was resolved.\n    */\n    pos, \n    /**\n    @internal\n    */\n    path, \n    /**\n    The offset this position has into its parent node.\n    */\n    parentOffset) {\n        this.pos = pos;\n        this.path = path;\n        this.parentOffset = parentOffset;\n        this.depth = path.length / 3 - 1;\n    }\n    /**\n    @internal\n    */\n    resolveDepth(val) {\n        if (val == null)\n            return this.depth;\n        if (val < 0)\n            return this.depth + val;\n        return val;\n    }\n    /**\n    The parent node that the position points into. Note that even if\n    a position points into a text node, that node is not considered\n    the parent—text nodes are ‘flat’ in this model, and have no content.\n    */\n    get parent() { return this.node(this.depth); }\n    /**\n    The root node in which the position was resolved.\n    */\n    get doc() { return this.node(0); }\n    /**\n    The ancestor node at the given level. `p.node(p.depth)` is the\n    same as `p.parent`.\n    */\n    node(depth) { return this.path[this.resolveDepth(depth) * 3]; }\n    /**\n    The index into the ancestor at the given level. If this points\n    at the 3rd node in the 2nd paragraph on the top level, for\n    example, `p.index(0)` is 1 and `p.index(1)` is 2.\n    */\n    index(depth) { return this.path[this.resolveDepth(depth) * 3 + 1]; }\n    /**\n    The index pointing after this position into the ancestor at the\n    given level.\n    */\n    indexAfter(depth) {\n        depth = this.resolveDepth(depth);\n        return this.index(depth) + (depth == this.depth && !this.textOffset ? 0 : 1);\n    }\n    /**\n    The (absolute) position at the start of the node at the given\n    level.\n    */\n    start(depth) {\n        depth = this.resolveDepth(depth);\n        return depth == 0 ? 0 : this.path[depth * 3 - 1] + 1;\n    }\n    /**\n    The (absolute) position at the end of the node at the given\n    level.\n    */\n    end(depth) {\n        depth = this.resolveDepth(depth);\n        return this.start(depth) + this.node(depth).content.size;\n    }\n    /**\n    The (absolute) position directly before the wrapping node at the\n    given level, or, when `depth` is `this.depth + 1`, the original\n    position.\n    */\n    before(depth) {\n        depth = this.resolveDepth(depth);\n        if (!depth)\n            throw new RangeError(\"There is no position before the top-level node\");\n        return depth == this.depth + 1 ? this.pos : this.path[depth * 3 - 1];\n    }\n    /**\n    The (absolute) position directly after the wrapping node at the\n    given level, or the original position when `depth` is `this.depth + 1`.\n    */\n    after(depth) {\n        depth = this.resolveDepth(depth);\n        if (!depth)\n            throw new RangeError(\"There is no position after the top-level node\");\n        return depth == this.depth + 1 ? this.pos : this.path[depth * 3 - 1] + this.path[depth * 3].nodeSize;\n    }\n    /**\n    When this position points into a text node, this returns the\n    distance between the position and the start of the text node.\n    Will be zero for positions that point between nodes.\n    */\n    get textOffset() { return this.pos - this.path[this.path.length - 1]; }\n    /**\n    Get the node directly after the position, if any. If the position\n    points into a text node, only the part of that node after the\n    position is returned.\n    */\n    get nodeAfter() {\n        let parent = this.parent, index = this.index(this.depth);\n        if (index == parent.childCount)\n            return null;\n        let dOff = this.pos - this.path[this.path.length - 1], child = parent.child(index);\n        return dOff ? parent.child(index).cut(dOff) : child;\n    }\n    /**\n    Get the node directly before the position, if any. If the\n    position points into a text node, only the part of that node\n    before the position is returned.\n    */\n    get nodeBefore() {\n        let index = this.index(this.depth);\n        let dOff = this.pos - this.path[this.path.length - 1];\n        if (dOff)\n            return this.parent.child(index).cut(0, dOff);\n        return index == 0 ? null : this.parent.child(index - 1);\n    }\n    /**\n    Get the position at the given index in the parent node at the\n    given depth (which defaults to `this.depth`).\n    */\n    posAtIndex(index, depth) {\n        depth = this.resolveDepth(depth);\n        let node = this.path[depth * 3], pos = depth == 0 ? 0 : this.path[depth * 3 - 1] + 1;\n        for (let i = 0; i < index; i++)\n            pos += node.child(i).nodeSize;\n        return pos;\n    }\n    /**\n    Get the marks at this position, factoring in the surrounding\n    marks' [`inclusive`](https://prosemirror.net/docs/ref/#model.MarkSpec.inclusive) property. If the\n    position is at the start of a non-empty node, the marks of the\n    node after it (if any) are returned.\n    */\n    marks() {\n        let parent = this.parent, index = this.index();\n        // In an empty parent, return the empty array\n        if (parent.content.size == 0)\n            return Mark.none;\n        // When inside a text node, just return the text node's marks\n        if (this.textOffset)\n            return parent.child(index).marks;\n        let main = parent.maybeChild(index - 1), other = parent.maybeChild(index);\n        // If the `after` flag is true of there is no node before, make\n        // the node after this position the main reference.\n        if (!main) {\n            let tmp = main;\n            main = other;\n            other = tmp;\n        }\n        // Use all marks in the main node, except those that have\n        // `inclusive` set to false and are not present in the other node.\n        let marks = main.marks;\n        for (var i = 0; i < marks.length; i++)\n            if (marks[i].type.spec.inclusive === false && (!other || !marks[i].isInSet(other.marks)))\n                marks = marks[i--].removeFromSet(marks);\n        return marks;\n    }\n    /**\n    Get the marks after the current position, if any, except those\n    that are non-inclusive and not present at position `$end`. This\n    is mostly useful for getting the set of marks to preserve after a\n    deletion. Will return `null` if this position is at the end of\n    its parent node or its parent node isn't a textblock (in which\n    case no marks should be preserved).\n    */\n    marksAcross($end) {\n        let after = this.parent.maybeChild(this.index());\n        if (!after || !after.isInline)\n            return null;\n        let marks = after.marks, next = $end.parent.maybeChild($end.index());\n        for (var i = 0; i < marks.length; i++)\n            if (marks[i].type.spec.inclusive === false && (!next || !marks[i].isInSet(next.marks)))\n                marks = marks[i--].removeFromSet(marks);\n        return marks;\n    }\n    /**\n    The depth up to which this position and the given (non-resolved)\n    position share the same parent nodes.\n    */\n    sharedDepth(pos) {\n        for (let depth = this.depth; depth > 0; depth--)\n            if (this.start(depth) <= pos && this.end(depth) >= pos)\n                return depth;\n        return 0;\n    }\n    /**\n    Returns a range based on the place where this position and the\n    given position diverge around block content. If both point into\n    the same textblock, for example, a range around that textblock\n    will be returned. If they point into different blocks, the range\n    around those blocks in their shared ancestor is returned. You can\n    pass in an optional predicate that will be called with a parent\n    node to see if a range into that parent is acceptable.\n    */\n    blockRange(other = this, pred) {\n        if (other.pos < this.pos)\n            return other.blockRange(this);\n        for (let d = this.depth - (this.parent.inlineContent || this.pos == other.pos ? 1 : 0); d >= 0; d--)\n            if (other.pos <= this.end(d) && (!pred || pred(this.node(d))))\n                return new NodeRange(this, other, d);\n        return null;\n    }\n    /**\n    Query whether the given position shares the same parent node.\n    */\n    sameParent(other) {\n        return this.pos - this.parentOffset == other.pos - other.parentOffset;\n    }\n    /**\n    Return the greater of this and the given position.\n    */\n    max(other) {\n        return other.pos > this.pos ? other : this;\n    }\n    /**\n    Return the smaller of this and the given position.\n    */\n    min(other) {\n        return other.pos < this.pos ? other : this;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let str = \"\";\n        for (let i = 1; i <= this.depth; i++)\n            str += (str ? \"/\" : \"\") + this.node(i).type.name + \"_\" + this.index(i - 1);\n        return str + \":\" + this.parentOffset;\n    }\n    /**\n    @internal\n    */\n    static resolve(doc, pos) {\n        if (!(pos >= 0 && pos <= doc.content.size))\n            throw new RangeError(\"Position \" + pos + \" out of range\");\n        let path = [];\n        let start = 0, parentOffset = pos;\n        for (let node = doc;;) {\n            let { index, offset } = node.content.findIndex(parentOffset);\n            let rem = parentOffset - offset;\n            path.push(node, index, start + offset);\n            if (!rem)\n                break;\n            node = node.child(index);\n            if (node.isText)\n                break;\n            parentOffset = rem - 1;\n            start += offset + 1;\n        }\n        return new ResolvedPos(pos, path, parentOffset);\n    }\n    /**\n    @internal\n    */\n    static resolveCached(doc, pos) {\n        let cache = resolveCache.get(doc);\n        if (cache) {\n            for (let i = 0; i < cache.elts.length; i++) {\n                let elt = cache.elts[i];\n                if (elt.pos == pos)\n                    return elt;\n            }\n        }\n        else {\n            resolveCache.set(doc, cache = new ResolveCache);\n        }\n        let result = cache.elts[cache.i] = ResolvedPos.resolve(doc, pos);\n        cache.i = (cache.i + 1) % resolveCacheSize;\n        return result;\n    }\n}\nclass ResolveCache {\n    constructor() {\n        this.elts = [];\n        this.i = 0;\n    }\n}\nconst resolveCacheSize = 12, resolveCache = new WeakMap();\n/**\nRepresents a flat range of content, i.e. one that starts and\nends in the same node.\n*/\nclass NodeRange {\n    /**\n    Construct a node range. `$from` and `$to` should point into the\n    same node until at least the given `depth`, since a node range\n    denotes an adjacent set of nodes in a single parent node.\n    */\n    constructor(\n    /**\n    A resolved position along the start of the content. May have a\n    `depth` greater than this object's `depth` property, since\n    these are the positions that were used to compute the range,\n    not re-resolved positions directly at its boundaries.\n    */\n    $from, \n    /**\n    A position along the end of the content. See\n    caveat for [`$from`](https://prosemirror.net/docs/ref/#model.NodeRange.$from).\n    */\n    $to, \n    /**\n    The depth of the node that this range points into.\n    */\n    depth) {\n        this.$from = $from;\n        this.$to = $to;\n        this.depth = depth;\n    }\n    /**\n    The position at the start of the range.\n    */\n    get start() { return this.$from.before(this.depth + 1); }\n    /**\n    The position at the end of the range.\n    */\n    get end() { return this.$to.after(this.depth + 1); }\n    /**\n    The parent node that the range points into.\n    */\n    get parent() { return this.$from.node(this.depth); }\n    /**\n    The start index of the range in the parent node.\n    */\n    get startIndex() { return this.$from.index(this.depth); }\n    /**\n    The end index of the range in the parent node.\n    */\n    get endIndex() { return this.$to.indexAfter(this.depth); }\n}\n\nconst emptyAttrs = Object.create(null);\n/**\nThis class represents a node in the tree that makes up a\nProseMirror document. So a document is an instance of `Node`, with\nchildren that are also instances of `Node`.\n\nNodes are persistent data structures. Instead of changing them, you\ncreate new ones with the content you want. Old ones keep pointing\nat the old document shape. This is made cheaper by sharing\nstructure between the old and new data as much as possible, which a\ntree shape like this (without back pointers) makes easy.\n\n**Do not** directly mutate the properties of a `Node` object. See\n[the guide](https://prosemirror.net/docs/guide/#doc) for more information.\n*/\nclass Node {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The type of node that this is.\n    */\n    type, \n    /**\n    An object mapping attribute names to values. The kind of\n    attributes allowed and required are\n    [determined](https://prosemirror.net/docs/ref/#model.NodeSpec.attrs) by the node type.\n    */\n    attrs, \n    // A fragment holding the node's children.\n    content, \n    /**\n    The marks (things like whether it is emphasized or part of a\n    link) applied to this node.\n    */\n    marks = Mark.none) {\n        this.type = type;\n        this.attrs = attrs;\n        this.marks = marks;\n        this.content = content || Fragment.empty;\n    }\n    /**\n    The array of this node's child nodes.\n    */\n    get children() { return this.content.content; }\n    /**\n    The size of this node, as defined by the integer-based [indexing\n    scheme](https://prosemirror.net/docs/guide/#doc.indexing). For text nodes, this is the\n    amount of characters. For other leaf nodes, it is one. For\n    non-leaf nodes, it is the size of the content plus two (the\n    start and end token).\n    */\n    get nodeSize() { return this.isLeaf ? 1 : 2 + this.content.size; }\n    /**\n    The number of children that the node has.\n    */\n    get childCount() { return this.content.childCount; }\n    /**\n    Get the child node at the given index. Raises an error when the\n    index is out of range.\n    */\n    child(index) { return this.content.child(index); }\n    /**\n    Get the child node at the given index, if it exists.\n    */\n    maybeChild(index) { return this.content.maybeChild(index); }\n    /**\n    Call `f` for every child node, passing the node, its offset\n    into this parent node, and its index.\n    */\n    forEach(f) { this.content.forEach(f); }\n    /**\n    Invoke a callback for all descendant nodes recursively between\n    the given two positions that are relative to start of this\n    node's content. The callback is invoked with the node, its\n    position relative to the original node (method receiver),\n    its parent node, and its child index. When the callback returns\n    false for a given node, that node's children will not be\n    recursed over. The last parameter can be used to specify a\n    starting position to count from.\n    */\n    nodesBetween(from, to, f, startPos = 0) {\n        this.content.nodesBetween(from, to, f, startPos, this);\n    }\n    /**\n    Call the given callback for every descendant node. Doesn't\n    descend into a node when the callback returns `false`.\n    */\n    descendants(f) {\n        this.nodesBetween(0, this.content.size, f);\n    }\n    /**\n    Concatenates all the text nodes found in this fragment and its\n    children.\n    */\n    get textContent() {\n        return (this.isLeaf && this.type.spec.leafText)\n            ? this.type.spec.leafText(this)\n            : this.textBetween(0, this.content.size, \"\");\n    }\n    /**\n    Get all text between positions `from` and `to`. When\n    `blockSeparator` is given, it will be inserted to separate text\n    from different block nodes. If `leafText` is given, it'll be\n    inserted for every non-text leaf node encountered, otherwise\n    [`leafText`](https://prosemirror.net/docs/ref/#model.NodeSpec^leafText) will be used.\n    */\n    textBetween(from, to, blockSeparator, leafText) {\n        return this.content.textBetween(from, to, blockSeparator, leafText);\n    }\n    /**\n    Returns this node's first child, or `null` if there are no\n    children.\n    */\n    get firstChild() { return this.content.firstChild; }\n    /**\n    Returns this node's last child, or `null` if there are no\n    children.\n    */\n    get lastChild() { return this.content.lastChild; }\n    /**\n    Test whether two nodes represent the same piece of document.\n    */\n    eq(other) {\n        return this == other || (this.sameMarkup(other) && this.content.eq(other.content));\n    }\n    /**\n    Compare the markup (type, attributes, and marks) of this node to\n    those of another. Returns `true` if both have the same markup.\n    */\n    sameMarkup(other) {\n        return this.hasMarkup(other.type, other.attrs, other.marks);\n    }\n    /**\n    Check whether this node's markup correspond to the given type,\n    attributes, and marks.\n    */\n    hasMarkup(type, attrs, marks) {\n        return this.type == type &&\n            compareDeep(this.attrs, attrs || type.defaultAttrs || emptyAttrs) &&\n            Mark.sameSet(this.marks, marks || Mark.none);\n    }\n    /**\n    Create a new node with the same markup as this node, containing\n    the given content (or empty, if no content is given).\n    */\n    copy(content = null) {\n        if (content == this.content)\n            return this;\n        return new Node(this.type, this.attrs, content, this.marks);\n    }\n    /**\n    Create a copy of this node, with the given set of marks instead\n    of the node's own marks.\n    */\n    mark(marks) {\n        return marks == this.marks ? this : new Node(this.type, this.attrs, this.content, marks);\n    }\n    /**\n    Create a copy of this node with only the content between the\n    given positions. If `to` is not given, it defaults to the end of\n    the node.\n    */\n    cut(from, to = this.content.size) {\n        if (from == 0 && to == this.content.size)\n            return this;\n        return this.copy(this.content.cut(from, to));\n    }\n    /**\n    Cut out the part of the document between the given positions, and\n    return it as a `Slice` object.\n    */\n    slice(from, to = this.content.size, includeParents = false) {\n        if (from == to)\n            return Slice.empty;\n        let $from = this.resolve(from), $to = this.resolve(to);\n        let depth = includeParents ? 0 : $from.sharedDepth(to);\n        let start = $from.start(depth), node = $from.node(depth);\n        let content = node.content.cut($from.pos - start, $to.pos - start);\n        return new Slice(content, $from.depth - depth, $to.depth - depth);\n    }\n    /**\n    Replace the part of the document between the given positions with\n    the given slice. The slice must 'fit', meaning its open sides\n    must be able to connect to the surrounding content, and its\n    content nodes must be valid children for the node they are placed\n    into. If any of this is violated, an error of type\n    [`ReplaceError`](https://prosemirror.net/docs/ref/#model.ReplaceError) is thrown.\n    */\n    replace(from, to, slice) {\n        return replace(this.resolve(from), this.resolve(to), slice);\n    }\n    /**\n    Find the node directly after the given position.\n    */\n    nodeAt(pos) {\n        for (let node = this;;) {\n            let { index, offset } = node.content.findIndex(pos);\n            node = node.maybeChild(index);\n            if (!node)\n                return null;\n            if (offset == pos || node.isText)\n                return node;\n            pos -= offset + 1;\n        }\n    }\n    /**\n    Find the (direct) child node after the given offset, if any,\n    and return it along with its index and offset relative to this\n    node.\n    */\n    childAfter(pos) {\n        let { index, offset } = this.content.findIndex(pos);\n        return { node: this.content.maybeChild(index), index, offset };\n    }\n    /**\n    Find the (direct) child node before the given offset, if any,\n    and return it along with its index and offset relative to this\n    node.\n    */\n    childBefore(pos) {\n        if (pos == 0)\n            return { node: null, index: 0, offset: 0 };\n        let { index, offset } = this.content.findIndex(pos);\n        if (offset < pos)\n            return { node: this.content.child(index), index, offset };\n        let node = this.content.child(index - 1);\n        return { node, index: index - 1, offset: offset - node.nodeSize };\n    }\n    /**\n    Resolve the given position in the document, returning an\n    [object](https://prosemirror.net/docs/ref/#model.ResolvedPos) with information about its context.\n    */\n    resolve(pos) { return ResolvedPos.resolveCached(this, pos); }\n    /**\n    @internal\n    */\n    resolveNoCache(pos) { return ResolvedPos.resolve(this, pos); }\n    /**\n    Test whether a given mark or mark type occurs in this document\n    between the two given positions.\n    */\n    rangeHasMark(from, to, type) {\n        let found = false;\n        if (to > from)\n            this.nodesBetween(from, to, node => {\n                if (type.isInSet(node.marks))\n                    found = true;\n                return !found;\n            });\n        return found;\n    }\n    /**\n    True when this is a block (non-inline node)\n    */\n    get isBlock() { return this.type.isBlock; }\n    /**\n    True when this is a textblock node, a block node with inline\n    content.\n    */\n    get isTextblock() { return this.type.isTextblock; }\n    /**\n    True when this node allows inline content.\n    */\n    get inlineContent() { return this.type.inlineContent; }\n    /**\n    True when this is an inline node (a text node or a node that can\n    appear among text).\n    */\n    get isInline() { return this.type.isInline; }\n    /**\n    True when this is a text node.\n    */\n    get isText() { return this.type.isText; }\n    /**\n    True when this is a leaf node.\n    */\n    get isLeaf() { return this.type.isLeaf; }\n    /**\n    True when this is an atom, i.e. when it does not have directly\n    editable content. This is usually the same as `isLeaf`, but can\n    be configured with the [`atom` property](https://prosemirror.net/docs/ref/#model.NodeSpec.atom)\n    on a node's spec (typically used when the node is displayed as\n    an uneditable [node view](https://prosemirror.net/docs/ref/#view.NodeView)).\n    */\n    get isAtom() { return this.type.isAtom; }\n    /**\n    Return a string representation of this node for debugging\n    purposes.\n    */\n    toString() {\n        if (this.type.spec.toDebugString)\n            return this.type.spec.toDebugString(this);\n        let name = this.type.name;\n        if (this.content.size)\n            name += \"(\" + this.content.toStringInner() + \")\";\n        return wrapMarks(this.marks, name);\n    }\n    /**\n    Get the content match in this node at the given index.\n    */\n    contentMatchAt(index) {\n        let match = this.type.contentMatch.matchFragment(this.content, 0, index);\n        if (!match)\n            throw new Error(\"Called contentMatchAt on a node with invalid content\");\n        return match;\n    }\n    /**\n    Test whether replacing the range between `from` and `to` (by\n    child index) with the given replacement fragment (which defaults\n    to the empty fragment) would leave the node's content valid. You\n    can optionally pass `start` and `end` indices into the\n    replacement fragment.\n    */\n    canReplace(from, to, replacement = Fragment.empty, start = 0, end = replacement.childCount) {\n        let one = this.contentMatchAt(from).matchFragment(replacement, start, end);\n        let two = one && one.matchFragment(this.content, to);\n        if (!two || !two.validEnd)\n            return false;\n        for (let i = start; i < end; i++)\n            if (!this.type.allowsMarks(replacement.child(i).marks))\n                return false;\n        return true;\n    }\n    /**\n    Test whether replacing the range `from` to `to` (by index) with\n    a node of the given type would leave the node's content valid.\n    */\n    canReplaceWith(from, to, type, marks) {\n        if (marks && !this.type.allowsMarks(marks))\n            return false;\n        let start = this.contentMatchAt(from).matchType(type);\n        let end = start && start.matchFragment(this.content, to);\n        return end ? end.validEnd : false;\n    }\n    /**\n    Test whether the given node's content could be appended to this\n    node. If that node is empty, this will only return true if there\n    is at least one node type that can appear in both nodes (to avoid\n    merging completely incompatible nodes).\n    */\n    canAppend(other) {\n        if (other.content.size)\n            return this.canReplace(this.childCount, this.childCount, other.content);\n        else\n            return this.type.compatibleContent(other.type);\n    }\n    /**\n    Check whether this node and its descendants conform to the\n    schema, and raise an exception when they do not.\n    */\n    check() {\n        this.type.checkContent(this.content);\n        this.type.checkAttrs(this.attrs);\n        let copy = Mark.none;\n        for (let i = 0; i < this.marks.length; i++) {\n            let mark = this.marks[i];\n            mark.type.checkAttrs(mark.attrs);\n            copy = mark.addToSet(copy);\n        }\n        if (!Mark.sameSet(copy, this.marks))\n            throw new RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(m => m.type.name)}`);\n        this.content.forEach(node => node.check());\n    }\n    /**\n    Return a JSON-serializeable representation of this node.\n    */\n    toJSON() {\n        let obj = { type: this.type.name };\n        for (let _ in this.attrs) {\n            obj.attrs = this.attrs;\n            break;\n        }\n        if (this.content.size)\n            obj.content = this.content.toJSON();\n        if (this.marks.length)\n            obj.marks = this.marks.map(n => n.toJSON());\n        return obj;\n    }\n    /**\n    Deserialize a node from its JSON representation.\n    */\n    static fromJSON(schema, json) {\n        if (!json)\n            throw new RangeError(\"Invalid input for Node.fromJSON\");\n        let marks = undefined;\n        if (json.marks) {\n            if (!Array.isArray(json.marks))\n                throw new RangeError(\"Invalid mark data for Node.fromJSON\");\n            marks = json.marks.map(schema.markFromJSON);\n        }\n        if (json.type == \"text\") {\n            if (typeof json.text != \"string\")\n                throw new RangeError(\"Invalid text node in JSON\");\n            return schema.text(json.text, marks);\n        }\n        let content = Fragment.fromJSON(schema, json.content);\n        let node = schema.nodeType(json.type).create(json.attrs, content, marks);\n        node.type.checkAttrs(node.attrs);\n        return node;\n    }\n}\nNode.prototype.text = undefined;\nclass TextNode extends Node {\n    /**\n    @internal\n    */\n    constructor(type, attrs, content, marks) {\n        super(type, attrs, null, marks);\n        if (!content)\n            throw new RangeError(\"Empty text nodes are not allowed\");\n        this.text = content;\n    }\n    toString() {\n        if (this.type.spec.toDebugString)\n            return this.type.spec.toDebugString(this);\n        return wrapMarks(this.marks, JSON.stringify(this.text));\n    }\n    get textContent() { return this.text; }\n    textBetween(from, to) { return this.text.slice(from, to); }\n    get nodeSize() { return this.text.length; }\n    mark(marks) {\n        return marks == this.marks ? this : new TextNode(this.type, this.attrs, this.text, marks);\n    }\n    withText(text) {\n        if (text == this.text)\n            return this;\n        return new TextNode(this.type, this.attrs, text, this.marks);\n    }\n    cut(from = 0, to = this.text.length) {\n        if (from == 0 && to == this.text.length)\n            return this;\n        return this.withText(this.text.slice(from, to));\n    }\n    eq(other) {\n        return this.sameMarkup(other) && this.text == other.text;\n    }\n    toJSON() {\n        let base = super.toJSON();\n        base.text = this.text;\n        return base;\n    }\n}\nfunction wrapMarks(marks, str) {\n    for (let i = marks.length - 1; i >= 0; i--)\n        str = marks[i].type.name + \"(\" + str + \")\";\n    return str;\n}\n\n/**\nInstances of this class represent a match state of a node type's\n[content expression](https://prosemirror.net/docs/ref/#model.NodeSpec.content), and can be used to\nfind out whether further content matches here, and whether a given\nposition is a valid end of the node.\n*/\nclass ContentMatch {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    True when this match state represents a valid end of the node.\n    */\n    validEnd) {\n        this.validEnd = validEnd;\n        /**\n        @internal\n        */\n        this.next = [];\n        /**\n        @internal\n        */\n        this.wrapCache = [];\n    }\n    /**\n    @internal\n    */\n    static parse(string, nodeTypes) {\n        let stream = new TokenStream(string, nodeTypes);\n        if (stream.next == null)\n            return ContentMatch.empty;\n        let expr = parseExpr(stream);\n        if (stream.next)\n            stream.err(\"Unexpected trailing text\");\n        let match = dfa(nfa(expr));\n        checkForDeadEnds(match, stream);\n        return match;\n    }\n    /**\n    Match a node type, returning a match after that node if\n    successful.\n    */\n    matchType(type) {\n        for (let i = 0; i < this.next.length; i++)\n            if (this.next[i].type == type)\n                return this.next[i].next;\n        return null;\n    }\n    /**\n    Try to match a fragment. Returns the resulting match when\n    successful.\n    */\n    matchFragment(frag, start = 0, end = frag.childCount) {\n        let cur = this;\n        for (let i = start; cur && i < end; i++)\n            cur = cur.matchType(frag.child(i).type);\n        return cur;\n    }\n    /**\n    @internal\n    */\n    get inlineContent() {\n        return this.next.length != 0 && this.next[0].type.isInline;\n    }\n    /**\n    Get the first matching node type at this match position that can\n    be generated.\n    */\n    get defaultType() {\n        for (let i = 0; i < this.next.length; i++) {\n            let { type } = this.next[i];\n            if (!(type.isText || type.hasRequiredAttrs()))\n                return type;\n        }\n        return null;\n    }\n    /**\n    @internal\n    */\n    compatible(other) {\n        for (let i = 0; i < this.next.length; i++)\n            for (let j = 0; j < other.next.length; j++)\n                if (this.next[i].type == other.next[j].type)\n                    return true;\n        return false;\n    }\n    /**\n    Try to match the given fragment, and if that fails, see if it can\n    be made to match by inserting nodes in front of it. When\n    successful, return a fragment of inserted nodes (which may be\n    empty if nothing had to be inserted). When `toEnd` is true, only\n    return a fragment if the resulting match goes to the end of the\n    content expression.\n    */\n    fillBefore(after, toEnd = false, startIndex = 0) {\n        let seen = [this];\n        function search(match, types) {\n            let finished = match.matchFragment(after, startIndex);\n            if (finished && (!toEnd || finished.validEnd))\n                return Fragment.from(types.map(tp => tp.createAndFill()));\n            for (let i = 0; i < match.next.length; i++) {\n                let { type, next } = match.next[i];\n                if (!(type.isText || type.hasRequiredAttrs()) && seen.indexOf(next) == -1) {\n                    seen.push(next);\n                    let found = search(next, types.concat(type));\n                    if (found)\n                        return found;\n                }\n            }\n            return null;\n        }\n        return search(this, []);\n    }\n    /**\n    Find a set of wrapping node types that would allow a node of the\n    given type to appear at this position. The result may be empty\n    (when it fits directly) and will be null when no such wrapping\n    exists.\n    */\n    findWrapping(target) {\n        for (let i = 0; i < this.wrapCache.length; i += 2)\n            if (this.wrapCache[i] == target)\n                return this.wrapCache[i + 1];\n        let computed = this.computeWrapping(target);\n        this.wrapCache.push(target, computed);\n        return computed;\n    }\n    /**\n    @internal\n    */\n    computeWrapping(target) {\n        let seen = Object.create(null), active = [{ match: this, type: null, via: null }];\n        while (active.length) {\n            let current = active.shift(), match = current.match;\n            if (match.matchType(target)) {\n                let result = [];\n                for (let obj = current; obj.type; obj = obj.via)\n                    result.push(obj.type);\n                return result.reverse();\n            }\n            for (let i = 0; i < match.next.length; i++) {\n                let { type, next } = match.next[i];\n                if (!type.isLeaf && !type.hasRequiredAttrs() && !(type.name in seen) && (!current.type || next.validEnd)) {\n                    active.push({ match: type.contentMatch, type, via: current });\n                    seen[type.name] = true;\n                }\n            }\n        }\n        return null;\n    }\n    /**\n    The number of outgoing edges this node has in the finite\n    automaton that describes the content expression.\n    */\n    get edgeCount() {\n        return this.next.length;\n    }\n    /**\n    Get the _n_​th outgoing edge from this node in the finite\n    automaton that describes the content expression.\n    */\n    edge(n) {\n        if (n >= this.next.length)\n            throw new RangeError(`There's no ${n}th edge in this content match`);\n        return this.next[n];\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let seen = [];\n        function scan(m) {\n            seen.push(m);\n            for (let i = 0; i < m.next.length; i++)\n                if (seen.indexOf(m.next[i].next) == -1)\n                    scan(m.next[i].next);\n        }\n        scan(this);\n        return seen.map((m, i) => {\n            let out = i + (m.validEnd ? \"*\" : \" \") + \" \";\n            for (let i = 0; i < m.next.length; i++)\n                out += (i ? \", \" : \"\") + m.next[i].type.name + \"->\" + seen.indexOf(m.next[i].next);\n            return out;\n        }).join(\"\\n\");\n    }\n}\n/**\n@internal\n*/\nContentMatch.empty = new ContentMatch(true);\nclass TokenStream {\n    constructor(string, nodeTypes) {\n        this.string = string;\n        this.nodeTypes = nodeTypes;\n        this.inline = null;\n        this.pos = 0;\n        this.tokens = string.split(/\\s*(?=\\b|\\W|$)/);\n        if (this.tokens[this.tokens.length - 1] == \"\")\n            this.tokens.pop();\n        if (this.tokens[0] == \"\")\n            this.tokens.shift();\n    }\n    get next() { return this.tokens[this.pos]; }\n    eat(tok) { return this.next == tok && (this.pos++ || true); }\n    err(str) { throw new SyntaxError(str + \" (in content expression '\" + this.string + \"')\"); }\n}\nfunction parseExpr(stream) {\n    let exprs = [];\n    do {\n        exprs.push(parseExprSeq(stream));\n    } while (stream.eat(\"|\"));\n    return exprs.length == 1 ? exprs[0] : { type: \"choice\", exprs };\n}\nfunction parseExprSeq(stream) {\n    let exprs = [];\n    do {\n        exprs.push(parseExprSubscript(stream));\n    } while (stream.next && stream.next != \")\" && stream.next != \"|\");\n    return exprs.length == 1 ? exprs[0] : { type: \"seq\", exprs };\n}\nfunction parseExprSubscript(stream) {\n    let expr = parseExprAtom(stream);\n    for (;;) {\n        if (stream.eat(\"+\"))\n            expr = { type: \"plus\", expr };\n        else if (stream.eat(\"*\"))\n            expr = { type: \"star\", expr };\n        else if (stream.eat(\"?\"))\n            expr = { type: \"opt\", expr };\n        else if (stream.eat(\"{\"))\n            expr = parseExprRange(stream, expr);\n        else\n            break;\n    }\n    return expr;\n}\nfunction parseNum(stream) {\n    if (/\\D/.test(stream.next))\n        stream.err(\"Expected number, got '\" + stream.next + \"'\");\n    let result = Number(stream.next);\n    stream.pos++;\n    return result;\n}\nfunction parseExprRange(stream, expr) {\n    let min = parseNum(stream), max = min;\n    if (stream.eat(\",\")) {\n        if (stream.next != \"}\")\n            max = parseNum(stream);\n        else\n            max = -1;\n    }\n    if (!stream.eat(\"}\"))\n        stream.err(\"Unclosed braced range\");\n    return { type: \"range\", min, max, expr };\n}\nfunction resolveName(stream, name) {\n    let types = stream.nodeTypes, type = types[name];\n    if (type)\n        return [type];\n    let result = [];\n    for (let typeName in types) {\n        let type = types[typeName];\n        if (type.isInGroup(name))\n            result.push(type);\n    }\n    if (result.length == 0)\n        stream.err(\"No node type or group '\" + name + \"' found\");\n    return result;\n}\nfunction parseExprAtom(stream) {\n    if (stream.eat(\"(\")) {\n        let expr = parseExpr(stream);\n        if (!stream.eat(\")\"))\n            stream.err(\"Missing closing paren\");\n        return expr;\n    }\n    else if (!/\\W/.test(stream.next)) {\n        let exprs = resolveName(stream, stream.next).map(type => {\n            if (stream.inline == null)\n                stream.inline = type.isInline;\n            else if (stream.inline != type.isInline)\n                stream.err(\"Mixing inline and block content\");\n            return { type: \"name\", value: type };\n        });\n        stream.pos++;\n        return exprs.length == 1 ? exprs[0] : { type: \"choice\", exprs };\n    }\n    else {\n        stream.err(\"Unexpected token '\" + stream.next + \"'\");\n    }\n}\n// Construct an NFA from an expression as returned by the parser. The\n// NFA is represented as an array of states, which are themselves\n// arrays of edges, which are `{term, to}` objects. The first state is\n// the entry state and the last node is the success state.\n//\n// Note that unlike typical NFAs, the edge ordering in this one is\n// significant, in that it is used to contruct filler content when\n// necessary.\nfunction nfa(expr) {\n    let nfa = [[]];\n    connect(compile(expr, 0), node());\n    return nfa;\n    function node() { return nfa.push([]) - 1; }\n    function edge(from, to, term) {\n        let edge = { term, to };\n        nfa[from].push(edge);\n        return edge;\n    }\n    function connect(edges, to) {\n        edges.forEach(edge => edge.to = to);\n    }\n    function compile(expr, from) {\n        if (expr.type == \"choice\") {\n            return expr.exprs.reduce((out, expr) => out.concat(compile(expr, from)), []);\n        }\n        else if (expr.type == \"seq\") {\n            for (let i = 0;; i++) {\n                let next = compile(expr.exprs[i], from);\n                if (i == expr.exprs.length - 1)\n                    return next;\n                connect(next, from = node());\n            }\n        }\n        else if (expr.type == \"star\") {\n            let loop = node();\n            edge(from, loop);\n            connect(compile(expr.expr, loop), loop);\n            return [edge(loop)];\n        }\n        else if (expr.type == \"plus\") {\n            let loop = node();\n            connect(compile(expr.expr, from), loop);\n            connect(compile(expr.expr, loop), loop);\n            return [edge(loop)];\n        }\n        else if (expr.type == \"opt\") {\n            return [edge(from)].concat(compile(expr.expr, from));\n        }\n        else if (expr.type == \"range\") {\n            let cur = from;\n            for (let i = 0; i < expr.min; i++) {\n                let next = node();\n                connect(compile(expr.expr, cur), next);\n                cur = next;\n            }\n            if (expr.max == -1) {\n                connect(compile(expr.expr, cur), cur);\n            }\n            else {\n                for (let i = expr.min; i < expr.max; i++) {\n                    let next = node();\n                    edge(cur, next);\n                    connect(compile(expr.expr, cur), next);\n                    cur = next;\n                }\n            }\n            return [edge(cur)];\n        }\n        else if (expr.type == \"name\") {\n            return [edge(from, undefined, expr.value)];\n        }\n        else {\n            throw new Error(\"Unknown expr type\");\n        }\n    }\n}\nfunction cmp(a, b) { return b - a; }\n// Get the set of nodes reachable by null edges from `node`. Omit\n// nodes with only a single null-out-edge, since they may lead to\n// needless duplicated nodes.\nfunction nullFrom(nfa, node) {\n    let result = [];\n    scan(node);\n    return result.sort(cmp);\n    function scan(node) {\n        let edges = nfa[node];\n        if (edges.length == 1 && !edges[0].term)\n            return scan(edges[0].to);\n        result.push(node);\n        for (let i = 0; i < edges.length; i++) {\n            let { term, to } = edges[i];\n            if (!term && result.indexOf(to) == -1)\n                scan(to);\n        }\n    }\n}\n// Compiles an NFA as produced by `nfa` into a DFA, modeled as a set\n// of state objects (`ContentMatch` instances) with transitions\n// between them.\nfunction dfa(nfa) {\n    let labeled = Object.create(null);\n    return explore(nullFrom(nfa, 0));\n    function explore(states) {\n        let out = [];\n        states.forEach(node => {\n            nfa[node].forEach(({ term, to }) => {\n                if (!term)\n                    return;\n                let set;\n                for (let i = 0; i < out.length; i++)\n                    if (out[i][0] == term)\n                        set = out[i][1];\n                nullFrom(nfa, to).forEach(node => {\n                    if (!set)\n                        out.push([term, set = []]);\n                    if (set.indexOf(node) == -1)\n                        set.push(node);\n                });\n            });\n        });\n        let state = labeled[states.join(\",\")] = new ContentMatch(states.indexOf(nfa.length - 1) > -1);\n        for (let i = 0; i < out.length; i++) {\n            let states = out[i][1].sort(cmp);\n            state.next.push({ type: out[i][0], next: labeled[states.join(\",\")] || explore(states) });\n        }\n        return state;\n    }\n}\nfunction checkForDeadEnds(match, stream) {\n    for (let i = 0, work = [match]; i < work.length; i++) {\n        let state = work[i], dead = !state.validEnd, nodes = [];\n        for (let j = 0; j < state.next.length; j++) {\n            let { type, next } = state.next[j];\n            nodes.push(type.name);\n            if (dead && !(type.isText || type.hasRequiredAttrs()))\n                dead = false;\n            if (work.indexOf(next) == -1)\n                work.push(next);\n        }\n        if (dead)\n            stream.err(\"Only non-generatable nodes (\" + nodes.join(\", \") + \") in a required position (see https://prosemirror.net/docs/guide/#generatable)\");\n    }\n}\n\n// For node types where all attrs have a default value (or which don't\n// have any attributes), build up a single reusable default attribute\n// object, and use it for all nodes that don't specify specific\n// attributes.\nfunction defaultAttrs(attrs) {\n    let defaults = Object.create(null);\n    for (let attrName in attrs) {\n        let attr = attrs[attrName];\n        if (!attr.hasDefault)\n            return null;\n        defaults[attrName] = attr.default;\n    }\n    return defaults;\n}\nfunction computeAttrs(attrs, value) {\n    let built = Object.create(null);\n    for (let name in attrs) {\n        let given = value && value[name];\n        if (given === undefined) {\n            let attr = attrs[name];\n            if (attr.hasDefault)\n                given = attr.default;\n            else\n                throw new RangeError(\"No value supplied for attribute \" + name);\n        }\n        built[name] = given;\n    }\n    return built;\n}\nfunction checkAttrs(attrs, values, type, name) {\n    for (let name in values)\n        if (!(name in attrs))\n            throw new RangeError(`Unsupported attribute ${name} for ${type} of type ${name}`);\n    for (let name in attrs) {\n        let attr = attrs[name];\n        if (attr.validate)\n            attr.validate(values[name]);\n    }\n}\nfunction initAttrs(typeName, attrs) {\n    let result = Object.create(null);\n    if (attrs)\n        for (let name in attrs)\n            result[name] = new Attribute(typeName, name, attrs[name]);\n    return result;\n}\n/**\nNode types are objects allocated once per `Schema` and used to\n[tag](https://prosemirror.net/docs/ref/#model.Node.type) `Node` instances. They contain information\nabout the node type, such as its name and what kind of node it\nrepresents.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name the node type has in this schema.\n    */\n    name, \n    /**\n    A link back to the `Schema` the node type belongs to.\n    */\n    schema, \n    /**\n    The spec that this type is based on\n    */\n    spec) {\n        this.name = name;\n        this.schema = schema;\n        this.spec = spec;\n        /**\n        The set of marks allowed in this node. `null` means all marks\n        are allowed.\n        */\n        this.markSet = null;\n        this.groups = spec.group ? spec.group.split(\" \") : [];\n        this.attrs = initAttrs(name, spec.attrs);\n        this.defaultAttrs = defaultAttrs(this.attrs);\n        this.contentMatch = null;\n        this.inlineContent = null;\n        this.isBlock = !(spec.inline || name == \"text\");\n        this.isText = name == \"text\";\n    }\n    /**\n    True if this is an inline type.\n    */\n    get isInline() { return !this.isBlock; }\n    /**\n    True if this is a textblock type, a block that contains inline\n    content.\n    */\n    get isTextblock() { return this.isBlock && this.inlineContent; }\n    /**\n    True for node types that allow no content.\n    */\n    get isLeaf() { return this.contentMatch == ContentMatch.empty; }\n    /**\n    True when this node is an atom, i.e. when it does not have\n    directly editable content.\n    */\n    get isAtom() { return this.isLeaf || !!this.spec.atom; }\n    /**\n    Return true when this node type is part of the given\n    [group](https://prosemirror.net/docs/ref/#model.NodeSpec.group).\n    */\n    isInGroup(group) {\n        return this.groups.indexOf(group) > -1;\n    }\n    /**\n    The node type's [whitespace](https://prosemirror.net/docs/ref/#model.NodeSpec.whitespace) option.\n    */\n    get whitespace() {\n        return this.spec.whitespace || (this.spec.code ? \"pre\" : \"normal\");\n    }\n    /**\n    Tells you whether this node type has any required attributes.\n    */\n    hasRequiredAttrs() {\n        for (let n in this.attrs)\n            if (this.attrs[n].isRequired)\n                return true;\n        return false;\n    }\n    /**\n    Indicates whether this node allows some of the same content as\n    the given node type.\n    */\n    compatibleContent(other) {\n        return this == other || this.contentMatch.compatible(other.contentMatch);\n    }\n    /**\n    @internal\n    */\n    computeAttrs(attrs) {\n        if (!attrs && this.defaultAttrs)\n            return this.defaultAttrs;\n        else\n            return computeAttrs(this.attrs, attrs);\n    }\n    /**\n    Create a `Node` of this type. The given attributes are\n    checked and defaulted (you can pass `null` to use the type's\n    defaults entirely, if no required attributes exist). `content`\n    may be a `Fragment`, a node, an array of nodes, or\n    `null`. Similarly `marks` may be `null` to default to the empty\n    set of marks.\n    */\n    create(attrs = null, content, marks) {\n        if (this.isText)\n            throw new Error(\"NodeType.create can't construct text nodes\");\n        return new Node(this, this.computeAttrs(attrs), Fragment.from(content), Mark.setFrom(marks));\n    }\n    /**\n    Like [`create`](https://prosemirror.net/docs/ref/#model.NodeType.create), but check the given content\n    against the node type's content restrictions, and throw an error\n    if it doesn't match.\n    */\n    createChecked(attrs = null, content, marks) {\n        content = Fragment.from(content);\n        this.checkContent(content);\n        return new Node(this, this.computeAttrs(attrs), content, Mark.setFrom(marks));\n    }\n    /**\n    Like [`create`](https://prosemirror.net/docs/ref/#model.NodeType.create), but see if it is\n    necessary to add nodes to the start or end of the given fragment\n    to make it fit the node. If no fitting wrapping can be found,\n    return null. Note that, due to the fact that required nodes can\n    always be created, this will always succeed if you pass null or\n    `Fragment.empty` as content.\n    */\n    createAndFill(attrs = null, content, marks) {\n        attrs = this.computeAttrs(attrs);\n        content = Fragment.from(content);\n        if (content.size) {\n            let before = this.contentMatch.fillBefore(content);\n            if (!before)\n                return null;\n            content = before.append(content);\n        }\n        let matched = this.contentMatch.matchFragment(content);\n        let after = matched && matched.fillBefore(Fragment.empty, true);\n        if (!after)\n            return null;\n        return new Node(this, attrs, content.append(after), Mark.setFrom(marks));\n    }\n    /**\n    Returns true if the given fragment is valid content for this node\n    type.\n    */\n    validContent(content) {\n        let result = this.contentMatch.matchFragment(content);\n        if (!result || !result.validEnd)\n            return false;\n        for (let i = 0; i < content.childCount; i++)\n            if (!this.allowsMarks(content.child(i).marks))\n                return false;\n        return true;\n    }\n    /**\n    Throws a RangeError if the given fragment is not valid content for this\n    node type.\n    @internal\n    */\n    checkContent(content) {\n        if (!this.validContent(content))\n            throw new RangeError(`Invalid content for node ${this.name}: ${content.toString().slice(0, 50)}`);\n    }\n    /**\n    @internal\n    */\n    checkAttrs(attrs) {\n        checkAttrs(this.attrs, attrs, \"node\", this.name);\n    }\n    /**\n    Check whether the given mark type is allowed in this node.\n    */\n    allowsMarkType(markType) {\n        return this.markSet == null || this.markSet.indexOf(markType) > -1;\n    }\n    /**\n    Test whether the given set of marks are allowed in this node.\n    */\n    allowsMarks(marks) {\n        if (this.markSet == null)\n            return true;\n        for (let i = 0; i < marks.length; i++)\n            if (!this.allowsMarkType(marks[i].type))\n                return false;\n        return true;\n    }\n    /**\n    Removes the marks that are not allowed in this node from the given set.\n    */\n    allowedMarks(marks) {\n        if (this.markSet == null)\n            return marks;\n        let copy;\n        for (let i = 0; i < marks.length; i++) {\n            if (!this.allowsMarkType(marks[i].type)) {\n                if (!copy)\n                    copy = marks.slice(0, i);\n            }\n            else if (copy) {\n                copy.push(marks[i]);\n            }\n        }\n        return !copy ? marks : copy.length ? copy : Mark.none;\n    }\n    /**\n    @internal\n    */\n    static compile(nodes, schema) {\n        let result = Object.create(null);\n        nodes.forEach((name, spec) => result[name] = new NodeType(name, schema, spec));\n        let topType = schema.spec.topNode || \"doc\";\n        if (!result[topType])\n            throw new RangeError(\"Schema is missing its top node type ('\" + topType + \"')\");\n        if (!result.text)\n            throw new RangeError(\"Every schema needs a 'text' type\");\n        for (let _ in result.text.attrs)\n            throw new RangeError(\"The text node type should not have attributes\");\n        return result;\n    }\n}\nfunction validateType(typeName, attrName, type) {\n    let types = type.split(\"|\");\n    return (value) => {\n        let name = value === null ? \"null\" : typeof value;\n        if (types.indexOf(name) < 0)\n            throw new RangeError(`Expected value of type ${types} for attribute ${attrName} on type ${typeName}, got ${name}`);\n    };\n}\n// Attribute descriptors\nclass Attribute {\n    constructor(typeName, attrName, options) {\n        this.hasDefault = Object.prototype.hasOwnProperty.call(options, \"default\");\n        this.default = options.default;\n        this.validate = typeof options.validate == \"string\" ? validateType(typeName, attrName, options.validate) : options.validate;\n    }\n    get isRequired() {\n        return !this.hasDefault;\n    }\n}\n// Marks\n/**\nLike nodes, marks (which are associated with nodes to signify\nthings like emphasis or being part of a link) are\n[tagged](https://prosemirror.net/docs/ref/#model.Mark.type) with type objects, which are\ninstantiated once per `Schema`.\n*/\nclass MarkType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the mark type.\n    */\n    name, \n    /**\n    @internal\n    */\n    rank, \n    /**\n    The schema that this mark type instance is part of.\n    */\n    schema, \n    /**\n    The spec on which the type is based.\n    */\n    spec) {\n        this.name = name;\n        this.rank = rank;\n        this.schema = schema;\n        this.spec = spec;\n        this.attrs = initAttrs(name, spec.attrs);\n        this.excluded = null;\n        let defaults = defaultAttrs(this.attrs);\n        this.instance = defaults ? new Mark(this, defaults) : null;\n    }\n    /**\n    Create a mark of this type. `attrs` may be `null` or an object\n    containing only some of the mark's attributes. The others, if\n    they have defaults, will be added.\n    */\n    create(attrs = null) {\n        if (!attrs && this.instance)\n            return this.instance;\n        return new Mark(this, computeAttrs(this.attrs, attrs));\n    }\n    /**\n    @internal\n    */\n    static compile(marks, schema) {\n        let result = Object.create(null), rank = 0;\n        marks.forEach((name, spec) => result[name] = new MarkType(name, rank++, schema, spec));\n        return result;\n    }\n    /**\n    When there is a mark of this type in the given set, a new set\n    without it is returned. Otherwise, the input set is returned.\n    */\n    removeFromSet(set) {\n        for (var i = 0; i < set.length; i++)\n            if (set[i].type == this) {\n                set = set.slice(0, i).concat(set.slice(i + 1));\n                i--;\n            }\n        return set;\n    }\n    /**\n    Tests whether there is a mark of this type in the given set.\n    */\n    isInSet(set) {\n        for (let i = 0; i < set.length; i++)\n            if (set[i].type == this)\n                return set[i];\n    }\n    /**\n    @internal\n    */\n    checkAttrs(attrs) {\n        checkAttrs(this.attrs, attrs, \"mark\", this.name);\n    }\n    /**\n    Queries whether a given mark type is\n    [excluded](https://prosemirror.net/docs/ref/#model.MarkSpec.excludes) by this one.\n    */\n    excludes(other) {\n        return this.excluded.indexOf(other) > -1;\n    }\n}\n/**\nA document schema. Holds [node](https://prosemirror.net/docs/ref/#model.NodeType) and [mark\ntype](https://prosemirror.net/docs/ref/#model.MarkType) objects for the nodes and marks that may\noccur in conforming documents, and provides functionality for\ncreating and deserializing such documents.\n\nWhen given, the type parameters provide the names of the nodes and\nmarks in this schema.\n*/\nclass Schema {\n    /**\n    Construct a schema from a schema [specification](https://prosemirror.net/docs/ref/#model.SchemaSpec).\n    */\n    constructor(spec) {\n        /**\n        The [linebreak\n        replacement](https://prosemirror.net/docs/ref/#model.NodeSpec.linebreakReplacement) node defined\n        in this schema, if any.\n        */\n        this.linebreakReplacement = null;\n        /**\n        An object for storing whatever values modules may want to\n        compute and cache per schema. (If you want to store something\n        in it, try to use property names unlikely to clash.)\n        */\n        this.cached = Object.create(null);\n        let instanceSpec = this.spec = {};\n        for (let prop in spec)\n            instanceSpec[prop] = spec[prop];\n        instanceSpec.nodes = OrderedMap.from(spec.nodes),\n            instanceSpec.marks = OrderedMap.from(spec.marks || {}),\n            this.nodes = NodeType.compile(this.spec.nodes, this);\n        this.marks = MarkType.compile(this.spec.marks, this);\n        let contentExprCache = Object.create(null);\n        for (let prop in this.nodes) {\n            if (prop in this.marks)\n                throw new RangeError(prop + \" can not be both a node and a mark\");\n            let type = this.nodes[prop], contentExpr = type.spec.content || \"\", markExpr = type.spec.marks;\n            type.contentMatch = contentExprCache[contentExpr] ||\n                (contentExprCache[contentExpr] = ContentMatch.parse(contentExpr, this.nodes));\n            type.inlineContent = type.contentMatch.inlineContent;\n            if (type.spec.linebreakReplacement) {\n                if (this.linebreakReplacement)\n                    throw new RangeError(\"Multiple linebreak nodes defined\");\n                if (!type.isInline || !type.isLeaf)\n                    throw new RangeError(\"Linebreak replacement nodes must be inline leaf nodes\");\n                this.linebreakReplacement = type;\n            }\n            type.markSet = markExpr == \"_\" ? null :\n                markExpr ? gatherMarks(this, markExpr.split(\" \")) :\n                    markExpr == \"\" || !type.inlineContent ? [] : null;\n        }\n        for (let prop in this.marks) {\n            let type = this.marks[prop], excl = type.spec.excludes;\n            type.excluded = excl == null ? [type] : excl == \"\" ? [] : gatherMarks(this, excl.split(\" \"));\n        }\n        this.nodeFromJSON = this.nodeFromJSON.bind(this);\n        this.markFromJSON = this.markFromJSON.bind(this);\n        this.topNodeType = this.nodes[this.spec.topNode || \"doc\"];\n        this.cached.wrappings = Object.create(null);\n    }\n    /**\n    Create a node in this schema. The `type` may be a string or a\n    `NodeType` instance. Attributes will be extended with defaults,\n    `content` may be a `Fragment`, `null`, a `Node`, or an array of\n    nodes.\n    */\n    node(type, attrs = null, content, marks) {\n        if (typeof type == \"string\")\n            type = this.nodeType(type);\n        else if (!(type instanceof NodeType))\n            throw new RangeError(\"Invalid node type: \" + type);\n        else if (type.schema != this)\n            throw new RangeError(\"Node type from different schema used (\" + type.name + \")\");\n        return type.createChecked(attrs, content, marks);\n    }\n    /**\n    Create a text node in the schema. Empty text nodes are not\n    allowed.\n    */\n    text(text, marks) {\n        let type = this.nodes.text;\n        return new TextNode(type, type.defaultAttrs, text, Mark.setFrom(marks));\n    }\n    /**\n    Create a mark with the given type and attributes.\n    */\n    mark(type, attrs) {\n        if (typeof type == \"string\")\n            type = this.marks[type];\n        return type.create(attrs);\n    }\n    /**\n    Deserialize a node from its JSON representation. This method is\n    bound.\n    */\n    nodeFromJSON(json) {\n        return Node.fromJSON(this, json);\n    }\n    /**\n    Deserialize a mark from its JSON representation. This method is\n    bound.\n    */\n    markFromJSON(json) {\n        return Mark.fromJSON(this, json);\n    }\n    /**\n    @internal\n    */\n    nodeType(name) {\n        let found = this.nodes[name];\n        if (!found)\n            throw new RangeError(\"Unknown node type: \" + name);\n        return found;\n    }\n}\nfunction gatherMarks(schema, marks) {\n    let found = [];\n    for (let i = 0; i < marks.length; i++) {\n        let name = marks[i], mark = schema.marks[name], ok = mark;\n        if (mark) {\n            found.push(mark);\n        }\n        else {\n            for (let prop in schema.marks) {\n                let mark = schema.marks[prop];\n                if (name == \"_\" || (mark.spec.group && mark.spec.group.split(\" \").indexOf(name) > -1))\n                    found.push(ok = mark);\n            }\n        }\n        if (!ok)\n            throw new SyntaxError(\"Unknown mark type: '\" + marks[i] + \"'\");\n    }\n    return found;\n}\n\nfunction isTagRule(rule) { return rule.tag != null; }\nfunction isStyleRule(rule) { return rule.style != null; }\n/**\nA DOM parser represents a strategy for parsing DOM content into a\nProseMirror document conforming to a given schema. Its behavior is\ndefined by an array of [rules](https://prosemirror.net/docs/ref/#model.ParseRule).\n*/\nclass DOMParser {\n    /**\n    Create a parser that targets the given schema, using the given\n    parsing rules.\n    */\n    constructor(\n    /**\n    The schema into which the parser parses.\n    */\n    schema, \n    /**\n    The set of [parse rules](https://prosemirror.net/docs/ref/#model.ParseRule) that the parser\n    uses, in order of precedence.\n    */\n    rules) {\n        this.schema = schema;\n        this.rules = rules;\n        /**\n        @internal\n        */\n        this.tags = [];\n        /**\n        @internal\n        */\n        this.styles = [];\n        let matchedStyles = this.matchedStyles = [];\n        rules.forEach(rule => {\n            if (isTagRule(rule)) {\n                this.tags.push(rule);\n            }\n            else if (isStyleRule(rule)) {\n                let prop = /[^=]*/.exec(rule.style)[0];\n                if (matchedStyles.indexOf(prop) < 0)\n                    matchedStyles.push(prop);\n                this.styles.push(rule);\n            }\n        });\n        // Only normalize list elements when lists in the schema can't directly contain themselves\n        this.normalizeLists = !this.tags.some(r => {\n            if (!/^(ul|ol)\\b/.test(r.tag) || !r.node)\n                return false;\n            let node = schema.nodes[r.node];\n            return node.contentMatch.matchType(node);\n        });\n    }\n    /**\n    Parse a document from the content of a DOM node.\n    */\n    parse(dom, options = {}) {\n        let context = new ParseContext(this, options, false);\n        context.addAll(dom, Mark.none, options.from, options.to);\n        return context.finish();\n    }\n    /**\n    Parses the content of the given DOM node, like\n    [`parse`](https://prosemirror.net/docs/ref/#model.DOMParser.parse), and takes the same set of\n    options. But unlike that method, which produces a whole node,\n    this one returns a slice that is open at the sides, meaning that\n    the schema constraints aren't applied to the start of nodes to\n    the left of the input and the end of nodes at the end.\n    */\n    parseSlice(dom, options = {}) {\n        let context = new ParseContext(this, options, true);\n        context.addAll(dom, Mark.none, options.from, options.to);\n        return Slice.maxOpen(context.finish());\n    }\n    /**\n    @internal\n    */\n    matchTag(dom, context, after) {\n        for (let i = after ? this.tags.indexOf(after) + 1 : 0; i < this.tags.length; i++) {\n            let rule = this.tags[i];\n            if (matches(dom, rule.tag) &&\n                (rule.namespace === undefined || dom.namespaceURI == rule.namespace) &&\n                (!rule.context || context.matchesContext(rule.context))) {\n                if (rule.getAttrs) {\n                    let result = rule.getAttrs(dom);\n                    if (result === false)\n                        continue;\n                    rule.attrs = result || undefined;\n                }\n                return rule;\n            }\n        }\n    }\n    /**\n    @internal\n    */\n    matchStyle(prop, value, context, after) {\n        for (let i = after ? this.styles.indexOf(after) + 1 : 0; i < this.styles.length; i++) {\n            let rule = this.styles[i], style = rule.style;\n            if (style.indexOf(prop) != 0 ||\n                rule.context && !context.matchesContext(rule.context) ||\n                // Test that the style string either precisely matches the prop,\n                // or has an '=' sign after the prop, followed by the given\n                // value.\n                style.length > prop.length &&\n                    (style.charCodeAt(prop.length) != 61 || style.slice(prop.length + 1) != value))\n                continue;\n            if (rule.getAttrs) {\n                let result = rule.getAttrs(value);\n                if (result === false)\n                    continue;\n                rule.attrs = result || undefined;\n            }\n            return rule;\n        }\n    }\n    /**\n    @internal\n    */\n    static schemaRules(schema) {\n        let result = [];\n        function insert(rule) {\n            let priority = rule.priority == null ? 50 : rule.priority, i = 0;\n            for (; i < result.length; i++) {\n                let next = result[i], nextPriority = next.priority == null ? 50 : next.priority;\n                if (nextPriority < priority)\n                    break;\n            }\n            result.splice(i, 0, rule);\n        }\n        for (let name in schema.marks) {\n            let rules = schema.marks[name].spec.parseDOM;\n            if (rules)\n                rules.forEach(rule => {\n                    insert(rule = copy(rule));\n                    if (!(rule.mark || rule.ignore || rule.clearMark))\n                        rule.mark = name;\n                });\n        }\n        for (let name in schema.nodes) {\n            let rules = schema.nodes[name].spec.parseDOM;\n            if (rules)\n                rules.forEach(rule => {\n                    insert(rule = copy(rule));\n                    if (!(rule.node || rule.ignore || rule.mark))\n                        rule.node = name;\n                });\n        }\n        return result;\n    }\n    /**\n    Construct a DOM parser using the parsing rules listed in a\n    schema's [node specs](https://prosemirror.net/docs/ref/#model.NodeSpec.parseDOM), reordered by\n    [priority](https://prosemirror.net/docs/ref/#model.ParseRule.priority).\n    */\n    static fromSchema(schema) {\n        return schema.cached.domParser ||\n            (schema.cached.domParser = new DOMParser(schema, DOMParser.schemaRules(schema)));\n    }\n}\nconst blockTags = {\n    address: true, article: true, aside: true, blockquote: true, canvas: true,\n    dd: true, div: true, dl: true, fieldset: true, figcaption: true, figure: true,\n    footer: true, form: true, h1: true, h2: true, h3: true, h4: true, h5: true,\n    h6: true, header: true, hgroup: true, hr: true, li: true, noscript: true, ol: true,\n    output: true, p: true, pre: true, section: true, table: true, tfoot: true, ul: true\n};\nconst ignoreTags = {\n    head: true, noscript: true, object: true, script: true, style: true, title: true\n};\nconst listTags = { ol: true, ul: true };\n// Using a bitfield for node context options\nconst OPT_PRESERVE_WS = 1, OPT_PRESERVE_WS_FULL = 2, OPT_OPEN_LEFT = 4;\nfunction wsOptionsFor(type, preserveWhitespace, base) {\n    if (preserveWhitespace != null)\n        return (preserveWhitespace ? OPT_PRESERVE_WS : 0) |\n            (preserveWhitespace === \"full\" ? OPT_PRESERVE_WS_FULL : 0);\n    return type && type.whitespace == \"pre\" ? OPT_PRESERVE_WS | OPT_PRESERVE_WS_FULL : base & ~OPT_OPEN_LEFT;\n}\nclass NodeContext {\n    constructor(type, attrs, marks, solid, match, options) {\n        this.type = type;\n        this.attrs = attrs;\n        this.marks = marks;\n        this.solid = solid;\n        this.options = options;\n        this.content = [];\n        // Marks applied to the node's children\n        this.activeMarks = Mark.none;\n        this.match = match || (options & OPT_OPEN_LEFT ? null : type.contentMatch);\n    }\n    findWrapping(node) {\n        if (!this.match) {\n            if (!this.type)\n                return [];\n            let fill = this.type.contentMatch.fillBefore(Fragment.from(node));\n            if (fill) {\n                this.match = this.type.contentMatch.matchFragment(fill);\n            }\n            else {\n                let start = this.type.contentMatch, wrap;\n                if (wrap = start.findWrapping(node.type)) {\n                    this.match = start;\n                    return wrap;\n                }\n                else {\n                    return null;\n                }\n            }\n        }\n        return this.match.findWrapping(node.type);\n    }\n    finish(openEnd) {\n        if (!(this.options & OPT_PRESERVE_WS)) { // Strip trailing whitespace\n            let last = this.content[this.content.length - 1], m;\n            if (last && last.isText && (m = /[ \\t\\r\\n\\u000c]+$/.exec(last.text))) {\n                let text = last;\n                if (last.text.length == m[0].length)\n                    this.content.pop();\n                else\n                    this.content[this.content.length - 1] = text.withText(text.text.slice(0, text.text.length - m[0].length));\n            }\n        }\n        let content = Fragment.from(this.content);\n        if (!openEnd && this.match)\n            content = content.append(this.match.fillBefore(Fragment.empty, true));\n        return this.type ? this.type.create(this.attrs, content, this.marks) : content;\n    }\n    inlineContext(node) {\n        if (this.type)\n            return this.type.inlineContent;\n        if (this.content.length)\n            return this.content[0].isInline;\n        return node.parentNode && !blockTags.hasOwnProperty(node.parentNode.nodeName.toLowerCase());\n    }\n}\nclass ParseContext {\n    constructor(\n    // The parser we are using.\n    parser, \n    // The options passed to this parse.\n    options, isOpen) {\n        this.parser = parser;\n        this.options = options;\n        this.isOpen = isOpen;\n        this.open = 0;\n        this.localPreserveWS = false;\n        let topNode = options.topNode, topContext;\n        let topOptions = wsOptionsFor(null, options.preserveWhitespace, 0) | (isOpen ? OPT_OPEN_LEFT : 0);\n        if (topNode)\n            topContext = new NodeContext(topNode.type, topNode.attrs, Mark.none, true, options.topMatch || topNode.type.contentMatch, topOptions);\n        else if (isOpen)\n            topContext = new NodeContext(null, null, Mark.none, true, null, topOptions);\n        else\n            topContext = new NodeContext(parser.schema.topNodeType, null, Mark.none, true, null, topOptions);\n        this.nodes = [topContext];\n        this.find = options.findPositions;\n        this.needsBlock = false;\n    }\n    get top() {\n        return this.nodes[this.open];\n    }\n    // Add a DOM node to the content. Text is inserted as text node,\n    // otherwise, the node is passed to `addElement` or, if it has a\n    // `style` attribute, `addElementWithStyles`.\n    addDOM(dom, marks) {\n        if (dom.nodeType == 3)\n            this.addTextNode(dom, marks);\n        else if (dom.nodeType == 1)\n            this.addElement(dom, marks);\n    }\n    addTextNode(dom, marks) {\n        let value = dom.nodeValue;\n        let top = this.top, preserveWS = (top.options & OPT_PRESERVE_WS_FULL) ? \"full\"\n            : this.localPreserveWS || (top.options & OPT_PRESERVE_WS) > 0;\n        if (preserveWS === \"full\" ||\n            top.inlineContext(dom) ||\n            /[^ \\t\\r\\n\\u000c]/.test(value)) {\n            if (!preserveWS) {\n                value = value.replace(/[ \\t\\r\\n\\u000c]+/g, \" \");\n                // If this starts with whitespace, and there is no node before it, or\n                // a hard break, or a text node that ends with whitespace, strip the\n                // leading space.\n                if (/^[ \\t\\r\\n\\u000c]/.test(value) && this.open == this.nodes.length - 1) {\n                    let nodeBefore = top.content[top.content.length - 1];\n                    let domNodeBefore = dom.previousSibling;\n                    if (!nodeBefore ||\n                        (domNodeBefore && domNodeBefore.nodeName == 'BR') ||\n                        (nodeBefore.isText && /[ \\t\\r\\n\\u000c]$/.test(nodeBefore.text)))\n                        value = value.slice(1);\n                }\n            }\n            else if (preserveWS !== \"full\") {\n                value = value.replace(/\\r?\\n|\\r/g, \" \");\n            }\n            else {\n                value = value.replace(/\\r\\n?/g, \"\\n\");\n            }\n            if (value)\n                this.insertNode(this.parser.schema.text(value), marks, !/\\S/.test(value));\n            this.findInText(dom);\n        }\n        else {\n            this.findInside(dom);\n        }\n    }\n    // Try to find a handler for the given tag and use that to parse. If\n    // none is found, the element's content nodes are added directly.\n    addElement(dom, marks, matchAfter) {\n        let outerWS = this.localPreserveWS, top = this.top;\n        if (dom.tagName == \"PRE\" || /pre/.test(dom.style && dom.style.whiteSpace))\n            this.localPreserveWS = true;\n        let name = dom.nodeName.toLowerCase(), ruleID;\n        if (listTags.hasOwnProperty(name) && this.parser.normalizeLists)\n            normalizeList(dom);\n        let rule = (this.options.ruleFromNode && this.options.ruleFromNode(dom)) ||\n            (ruleID = this.parser.matchTag(dom, this, matchAfter));\n        out: if (rule ? rule.ignore : ignoreTags.hasOwnProperty(name)) {\n            this.findInside(dom);\n            this.ignoreFallback(dom, marks);\n        }\n        else if (!rule || rule.skip || rule.closeParent) {\n            if (rule && rule.closeParent)\n                this.open = Math.max(0, this.open - 1);\n            else if (rule && rule.skip.nodeType)\n                dom = rule.skip;\n            let sync, oldNeedsBlock = this.needsBlock;\n            if (blockTags.hasOwnProperty(name)) {\n                if (top.content.length && top.content[0].isInline && this.open) {\n                    this.open--;\n                    top = this.top;\n                }\n                sync = true;\n                if (!top.type)\n                    this.needsBlock = true;\n            }\n            else if (!dom.firstChild) {\n                this.leafFallback(dom, marks);\n                break out;\n            }\n            let innerMarks = rule && rule.skip ? marks : this.readStyles(dom, marks);\n            if (innerMarks)\n                this.addAll(dom, innerMarks);\n            if (sync)\n                this.sync(top);\n            this.needsBlock = oldNeedsBlock;\n        }\n        else {\n            let innerMarks = this.readStyles(dom, marks);\n            if (innerMarks)\n                this.addElementByRule(dom, rule, innerMarks, rule.consuming === false ? ruleID : undefined);\n        }\n        this.localPreserveWS = outerWS;\n    }\n    // Called for leaf DOM nodes that would otherwise be ignored\n    leafFallback(dom, marks) {\n        if (dom.nodeName == \"BR\" && this.top.type && this.top.type.inlineContent)\n            this.addTextNode(dom.ownerDocument.createTextNode(\"\\n\"), marks);\n    }\n    // Called for ignored nodes\n    ignoreFallback(dom, marks) {\n        // Ignored BR nodes should at least create an inline context\n        if (dom.nodeName == \"BR\" && (!this.top.type || !this.top.type.inlineContent))\n            this.findPlace(this.parser.schema.text(\"-\"), marks, true);\n    }\n    // Run any style parser associated with the node's styles. Either\n    // return an updated array of marks, or null to indicate some of the\n    // styles had a rule with `ignore` set.\n    readStyles(dom, marks) {\n        let styles = dom.style;\n        // Because many properties will only show up in 'normalized' form\n        // in `style.item` (i.e. text-decoration becomes\n        // text-decoration-line, text-decoration-color, etc), we directly\n        // query the styles mentioned in our rules instead of iterating\n        // over the items.\n        if (styles && styles.length)\n            for (let i = 0; i < this.parser.matchedStyles.length; i++) {\n                let name = this.parser.matchedStyles[i], value = styles.getPropertyValue(name);\n                if (value)\n                    for (let after = undefined;;) {\n                        let rule = this.parser.matchStyle(name, value, this, after);\n                        if (!rule)\n                            break;\n                        if (rule.ignore)\n                            return null;\n                        if (rule.clearMark)\n                            marks = marks.filter(m => !rule.clearMark(m));\n                        else\n                            marks = marks.concat(this.parser.schema.marks[rule.mark].create(rule.attrs));\n                        if (rule.consuming === false)\n                            after = rule;\n                        else\n                            break;\n                    }\n            }\n        return marks;\n    }\n    // Look up a handler for the given node. If none are found, return\n    // false. Otherwise, apply it, use its return value to drive the way\n    // the node's content is wrapped, and return true.\n    addElementByRule(dom, rule, marks, continueAfter) {\n        let sync, nodeType;\n        if (rule.node) {\n            nodeType = this.parser.schema.nodes[rule.node];\n            if (!nodeType.isLeaf) {\n                let inner = this.enter(nodeType, rule.attrs || null, marks, rule.preserveWhitespace);\n                if (inner) {\n                    sync = true;\n                    marks = inner;\n                }\n            }\n            else if (!this.insertNode(nodeType.create(rule.attrs), marks, dom.nodeName == \"BR\")) {\n                this.leafFallback(dom, marks);\n            }\n        }\n        else {\n            let markType = this.parser.schema.marks[rule.mark];\n            marks = marks.concat(markType.create(rule.attrs));\n        }\n        let startIn = this.top;\n        if (nodeType && nodeType.isLeaf) {\n            this.findInside(dom);\n        }\n        else if (continueAfter) {\n            this.addElement(dom, marks, continueAfter);\n        }\n        else if (rule.getContent) {\n            this.findInside(dom);\n            rule.getContent(dom, this.parser.schema).forEach(node => this.insertNode(node, marks, false));\n        }\n        else {\n            let contentDOM = dom;\n            if (typeof rule.contentElement == \"string\")\n                contentDOM = dom.querySelector(rule.contentElement);\n            else if (typeof rule.contentElement == \"function\")\n                contentDOM = rule.contentElement(dom);\n            else if (rule.contentElement)\n                contentDOM = rule.contentElement;\n            this.findAround(dom, contentDOM, true);\n            this.addAll(contentDOM, marks);\n            this.findAround(dom, contentDOM, false);\n        }\n        if (sync && this.sync(startIn))\n            this.open--;\n    }\n    // Add all child nodes between `startIndex` and `endIndex` (or the\n    // whole node, if not given). If `sync` is passed, use it to\n    // synchronize after every block element.\n    addAll(parent, marks, startIndex, endIndex) {\n        let index = startIndex || 0;\n        for (let dom = startIndex ? parent.childNodes[startIndex] : parent.firstChild, end = endIndex == null ? null : parent.childNodes[endIndex]; dom != end; dom = dom.nextSibling, ++index) {\n            this.findAtPoint(parent, index);\n            this.addDOM(dom, marks);\n        }\n        this.findAtPoint(parent, index);\n    }\n    // Try to find a way to fit the given node type into the current\n    // context. May add intermediate wrappers and/or leave non-solid\n    // nodes that we're in.\n    findPlace(node, marks, cautious) {\n        let route, sync;\n        for (let depth = this.open, penalty = 0; depth >= 0; depth--) {\n            let cx = this.nodes[depth];\n            let found = cx.findWrapping(node);\n            if (found && (!route || route.length > found.length + penalty)) {\n                route = found;\n                sync = cx;\n                if (!found.length)\n                    break;\n            }\n            if (cx.solid) {\n                if (cautious)\n                    break;\n                penalty += 2;\n            }\n        }\n        if (!route)\n            return null;\n        this.sync(sync);\n        for (let i = 0; i < route.length; i++)\n            marks = this.enterInner(route[i], null, marks, false);\n        return marks;\n    }\n    // Try to insert the given node, adjusting the context when needed.\n    insertNode(node, marks, cautious) {\n        if (node.isInline && this.needsBlock && !this.top.type) {\n            let block = this.textblockFromContext();\n            if (block)\n                marks = this.enterInner(block, null, marks);\n        }\n        let innerMarks = this.findPlace(node, marks, cautious);\n        if (innerMarks) {\n            this.closeExtra();\n            let top = this.top;\n            if (top.match)\n                top.match = top.match.matchType(node.type);\n            let nodeMarks = Mark.none;\n            for (let m of innerMarks.concat(node.marks))\n                if (top.type ? top.type.allowsMarkType(m.type) : markMayApply(m.type, node.type))\n                    nodeMarks = m.addToSet(nodeMarks);\n            top.content.push(node.mark(nodeMarks));\n            return true;\n        }\n        return false;\n    }\n    // Try to start a node of the given type, adjusting the context when\n    // necessary.\n    enter(type, attrs, marks, preserveWS) {\n        let innerMarks = this.findPlace(type.create(attrs), marks, false);\n        if (innerMarks)\n            innerMarks = this.enterInner(type, attrs, marks, true, preserveWS);\n        return innerMarks;\n    }\n    // Open a node of the given type\n    enterInner(type, attrs, marks, solid = false, preserveWS) {\n        this.closeExtra();\n        let top = this.top;\n        top.match = top.match && top.match.matchType(type);\n        let options = wsOptionsFor(type, preserveWS, top.options);\n        if ((top.options & OPT_OPEN_LEFT) && top.content.length == 0)\n            options |= OPT_OPEN_LEFT;\n        let applyMarks = Mark.none;\n        marks = marks.filter(m => {\n            if (top.type ? top.type.allowsMarkType(m.type) : markMayApply(m.type, type)) {\n                applyMarks = m.addToSet(applyMarks);\n                return false;\n            }\n            return true;\n        });\n        this.nodes.push(new NodeContext(type, attrs, applyMarks, solid, null, options));\n        this.open++;\n        return marks;\n    }\n    // Make sure all nodes above this.open are finished and added to\n    // their parents\n    closeExtra(openEnd = false) {\n        let i = this.nodes.length - 1;\n        if (i > this.open) {\n            for (; i > this.open; i--)\n                this.nodes[i - 1].content.push(this.nodes[i].finish(openEnd));\n            this.nodes.length = this.open + 1;\n        }\n    }\n    finish() {\n        this.open = 0;\n        this.closeExtra(this.isOpen);\n        return this.nodes[0].finish(!!(this.isOpen || this.options.topOpen));\n    }\n    sync(to) {\n        for (let i = this.open; i >= 0; i--) {\n            if (this.nodes[i] == to) {\n                this.open = i;\n                return true;\n            }\n            else if (this.localPreserveWS) {\n                this.nodes[i].options |= OPT_PRESERVE_WS;\n            }\n        }\n        return false;\n    }\n    get currentPos() {\n        this.closeExtra();\n        let pos = 0;\n        for (let i = this.open; i >= 0; i--) {\n            let content = this.nodes[i].content;\n            for (let j = content.length - 1; j >= 0; j--)\n                pos += content[j].nodeSize;\n            if (i)\n                pos++;\n        }\n        return pos;\n    }\n    findAtPoint(parent, offset) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].node == parent && this.find[i].offset == offset)\n                    this.find[i].pos = this.currentPos;\n            }\n    }\n    findInside(parent) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].pos == null && parent.nodeType == 1 && parent.contains(this.find[i].node))\n                    this.find[i].pos = this.currentPos;\n            }\n    }\n    findAround(parent, content, before) {\n        if (parent != content && this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].pos == null && parent.nodeType == 1 && parent.contains(this.find[i].node)) {\n                    let pos = content.compareDocumentPosition(this.find[i].node);\n                    if (pos & (before ? 2 : 4))\n                        this.find[i].pos = this.currentPos;\n                }\n            }\n    }\n    findInText(textNode) {\n        if (this.find)\n            for (let i = 0; i < this.find.length; i++) {\n                if (this.find[i].node == textNode)\n                    this.find[i].pos = this.currentPos - (textNode.nodeValue.length - this.find[i].offset);\n            }\n    }\n    // Determines whether the given context string matches this context.\n    matchesContext(context) {\n        if (context.indexOf(\"|\") > -1)\n            return context.split(/\\s*\\|\\s*/).some(this.matchesContext, this);\n        let parts = context.split(\"/\");\n        let option = this.options.context;\n        let useRoot = !this.isOpen && (!option || option.parent.type == this.nodes[0].type);\n        let minDepth = -(option ? option.depth + 1 : 0) + (useRoot ? 0 : 1);\n        let match = (i, depth) => {\n            for (; i >= 0; i--) {\n                let part = parts[i];\n                if (part == \"\") {\n                    if (i == parts.length - 1 || i == 0)\n                        continue;\n                    for (; depth >= minDepth; depth--)\n                        if (match(i - 1, depth))\n                            return true;\n                    return false;\n                }\n                else {\n                    let next = depth > 0 || (depth == 0 && useRoot) ? this.nodes[depth].type\n                        : option && depth >= minDepth ? option.node(depth - minDepth).type\n                            : null;\n                    if (!next || (next.name != part && !next.isInGroup(part)))\n                        return false;\n                    depth--;\n                }\n            }\n            return true;\n        };\n        return match(parts.length - 1, this.open);\n    }\n    textblockFromContext() {\n        let $context = this.options.context;\n        if ($context)\n            for (let d = $context.depth; d >= 0; d--) {\n                let deflt = $context.node(d).contentMatchAt($context.indexAfter(d)).defaultType;\n                if (deflt && deflt.isTextblock && deflt.defaultAttrs)\n                    return deflt;\n            }\n        for (let name in this.parser.schema.nodes) {\n            let type = this.parser.schema.nodes[name];\n            if (type.isTextblock && type.defaultAttrs)\n                return type;\n        }\n    }\n}\n// Kludge to work around directly nested list nodes produced by some\n// tools and allowed by browsers to mean that the nested list is\n// actually part of the list item above it.\nfunction normalizeList(dom) {\n    for (let child = dom.firstChild, prevItem = null; child; child = child.nextSibling) {\n        let name = child.nodeType == 1 ? child.nodeName.toLowerCase() : null;\n        if (name && listTags.hasOwnProperty(name) && prevItem) {\n            prevItem.appendChild(child);\n            child = prevItem;\n        }\n        else if (name == \"li\") {\n            prevItem = child;\n        }\n        else if (name) {\n            prevItem = null;\n        }\n    }\n}\n// Apply a CSS selector.\nfunction matches(dom, selector) {\n    return (dom.matches || dom.msMatchesSelector || dom.webkitMatchesSelector || dom.mozMatchesSelector).call(dom, selector);\n}\nfunction copy(obj) {\n    let copy = {};\n    for (let prop in obj)\n        copy[prop] = obj[prop];\n    return copy;\n}\n// Used when finding a mark at the top level of a fragment parse.\n// Checks whether it would be reasonable to apply a given mark type to\n// a given node, by looking at the way the mark occurs in the schema.\nfunction markMayApply(markType, nodeType) {\n    let nodes = nodeType.schema.nodes;\n    for (let name in nodes) {\n        let parent = nodes[name];\n        if (!parent.allowsMarkType(markType))\n            continue;\n        let seen = [], scan = (match) => {\n            seen.push(match);\n            for (let i = 0; i < match.edgeCount; i++) {\n                let { type, next } = match.edge(i);\n                if (type == nodeType)\n                    return true;\n                if (seen.indexOf(next) < 0 && scan(next))\n                    return true;\n            }\n        };\n        if (scan(parent.contentMatch))\n            return true;\n    }\n}\n\n/**\nA DOM serializer knows how to convert ProseMirror nodes and\nmarks of various types to DOM nodes.\n*/\nclass DOMSerializer {\n    /**\n    Create a serializer. `nodes` should map node names to functions\n    that take a node and return a description of the corresponding\n    DOM. `marks` does the same for mark names, but also gets an\n    argument that tells it whether the mark's content is block or\n    inline content (for typical use, it'll always be inline). A mark\n    serializer may be `null` to indicate that marks of that type\n    should not be serialized.\n    */\n    constructor(\n    /**\n    The node serialization functions.\n    */\n    nodes, \n    /**\n    The mark serialization functions.\n    */\n    marks) {\n        this.nodes = nodes;\n        this.marks = marks;\n    }\n    /**\n    Serialize the content of this fragment to a DOM fragment. When\n    not in the browser, the `document` option, containing a DOM\n    document, should be passed so that the serializer can create\n    nodes.\n    */\n    serializeFragment(fragment, options = {}, target) {\n        if (!target)\n            target = doc(options).createDocumentFragment();\n        let top = target, active = [];\n        fragment.forEach(node => {\n            if (active.length || node.marks.length) {\n                let keep = 0, rendered = 0;\n                while (keep < active.length && rendered < node.marks.length) {\n                    let next = node.marks[rendered];\n                    if (!this.marks[next.type.name]) {\n                        rendered++;\n                        continue;\n                    }\n                    if (!next.eq(active[keep][0]) || next.type.spec.spanning === false)\n                        break;\n                    keep++;\n                    rendered++;\n                }\n                while (keep < active.length)\n                    top = active.pop()[1];\n                while (rendered < node.marks.length) {\n                    let add = node.marks[rendered++];\n                    let markDOM = this.serializeMark(add, node.isInline, options);\n                    if (markDOM) {\n                        active.push([add, top]);\n                        top.appendChild(markDOM.dom);\n                        top = markDOM.contentDOM || markDOM.dom;\n                    }\n                }\n            }\n            top.appendChild(this.serializeNodeInner(node, options));\n        });\n        return target;\n    }\n    /**\n    @internal\n    */\n    serializeNodeInner(node, options) {\n        let { dom, contentDOM } = renderSpec(doc(options), this.nodes[node.type.name](node), null, node.attrs);\n        if (contentDOM) {\n            if (node.isLeaf)\n                throw new RangeError(\"Content hole not allowed in a leaf node spec\");\n            this.serializeFragment(node.content, options, contentDOM);\n        }\n        return dom;\n    }\n    /**\n    Serialize this node to a DOM node. This can be useful when you\n    need to serialize a part of a document, as opposed to the whole\n    document. To serialize a whole document, use\n    [`serializeFragment`](https://prosemirror.net/docs/ref/#model.DOMSerializer.serializeFragment) on\n    its [content](https://prosemirror.net/docs/ref/#model.Node.content).\n    */\n    serializeNode(node, options = {}) {\n        let dom = this.serializeNodeInner(node, options);\n        for (let i = node.marks.length - 1; i >= 0; i--) {\n            let wrap = this.serializeMark(node.marks[i], node.isInline, options);\n            if (wrap) {\n                (wrap.contentDOM || wrap.dom).appendChild(dom);\n                dom = wrap.dom;\n            }\n        }\n        return dom;\n    }\n    /**\n    @internal\n    */\n    serializeMark(mark, inline, options = {}) {\n        let toDOM = this.marks[mark.type.name];\n        return toDOM && renderSpec(doc(options), toDOM(mark, inline), null, mark.attrs);\n    }\n    static renderSpec(doc, structure, xmlNS = null, blockArraysIn) {\n        return renderSpec(doc, structure, xmlNS, blockArraysIn);\n    }\n    /**\n    Build a serializer using the [`toDOM`](https://prosemirror.net/docs/ref/#model.NodeSpec.toDOM)\n    properties in a schema's node and mark specs.\n    */\n    static fromSchema(schema) {\n        return schema.cached.domSerializer ||\n            (schema.cached.domSerializer = new DOMSerializer(this.nodesFromSchema(schema), this.marksFromSchema(schema)));\n    }\n    /**\n    Gather the serializers in a schema's node specs into an object.\n    This can be useful as a base to build a custom serializer from.\n    */\n    static nodesFromSchema(schema) {\n        let result = gatherToDOM(schema.nodes);\n        if (!result.text)\n            result.text = node => node.text;\n        return result;\n    }\n    /**\n    Gather the serializers in a schema's mark specs into an object.\n    */\n    static marksFromSchema(schema) {\n        return gatherToDOM(schema.marks);\n    }\n}\nfunction gatherToDOM(obj) {\n    let result = {};\n    for (let name in obj) {\n        let toDOM = obj[name].spec.toDOM;\n        if (toDOM)\n            result[name] = toDOM;\n    }\n    return result;\n}\nfunction doc(options) {\n    return options.document || window.document;\n}\nconst suspiciousAttributeCache = new WeakMap();\nfunction suspiciousAttributes(attrs) {\n    let value = suspiciousAttributeCache.get(attrs);\n    if (value === undefined)\n        suspiciousAttributeCache.set(attrs, value = suspiciousAttributesInner(attrs));\n    return value;\n}\nfunction suspiciousAttributesInner(attrs) {\n    let result = null;\n    function scan(value) {\n        if (value && typeof value == \"object\") {\n            if (Array.isArray(value)) {\n                if (typeof value[0] == \"string\") {\n                    if (!result)\n                        result = [];\n                    result.push(value);\n                }\n                else {\n                    for (let i = 0; i < value.length; i++)\n                        scan(value[i]);\n                }\n            }\n            else {\n                for (let prop in value)\n                    scan(value[prop]);\n            }\n        }\n    }\n    scan(attrs);\n    return result;\n}\nfunction renderSpec(doc, structure, xmlNS, blockArraysIn) {\n    if (typeof structure == \"string\")\n        return { dom: doc.createTextNode(structure) };\n    if (structure.nodeType != null)\n        return { dom: structure };\n    if (structure.dom && structure.dom.nodeType != null)\n        return structure;\n    let tagName = structure[0], suspicious;\n    if (typeof tagName != \"string\")\n        throw new RangeError(\"Invalid array passed to renderSpec\");\n    if (blockArraysIn && (suspicious = suspiciousAttributes(blockArraysIn)) &&\n        suspicious.indexOf(structure) > -1)\n        throw new RangeError(\"Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.\");\n    let space = tagName.indexOf(\" \");\n    if (space > 0) {\n        xmlNS = tagName.slice(0, space);\n        tagName = tagName.slice(space + 1);\n    }\n    let contentDOM;\n    let dom = (xmlNS ? doc.createElementNS(xmlNS, tagName) : doc.createElement(tagName));\n    let attrs = structure[1], start = 1;\n    if (attrs && typeof attrs == \"object\" && attrs.nodeType == null && !Array.isArray(attrs)) {\n        start = 2;\n        for (let name in attrs)\n            if (attrs[name] != null) {\n                let space = name.indexOf(\" \");\n                if (space > 0)\n                    dom.setAttributeNS(name.slice(0, space), name.slice(space + 1), attrs[name]);\n                else\n                    dom.setAttribute(name, attrs[name]);\n            }\n    }\n    for (let i = start; i < structure.length; i++) {\n        let child = structure[i];\n        if (child === 0) {\n            if (i < structure.length - 1 || i > start)\n                throw new RangeError(\"Content hole must be the only child of its parent node\");\n            return { dom, contentDOM: dom };\n        }\n        else {\n            let { dom: inner, contentDOM: innerContent } = renderSpec(doc, child, xmlNS, blockArraysIn);\n            dom.appendChild(inner);\n            if (innerContent) {\n                if (contentDOM)\n                    throw new RangeError(\"Multiple content holes\");\n                contentDOM = innerContent;\n            }\n        }\n    }\n    return { dom, contentDOM };\n}\n\nexport { ContentMatch, DOMParser, DOMSerializer, Fragment, Mark, MarkType, Node, NodeRange, NodeType, ReplaceError, ResolvedPos, Schema, Slice };\n", "// ::- Persistent data structure representing an ordered mapping from\n// strings to values, with some convenient update methods.\nfunction OrderedMap(content) {\n  this.content = content;\n}\n\nOrderedMap.prototype = {\n  constructor: OrderedMap,\n\n  find: function(key) {\n    for (var i = 0; i < this.content.length; i += 2)\n      if (this.content[i] === key) return i\n    return -1\n  },\n\n  // :: (string) → ?any\n  // Retrieve the value stored under `key`, or return undefined when\n  // no such key exists.\n  get: function(key) {\n    var found = this.find(key);\n    return found == -1 ? undefined : this.content[found + 1]\n  },\n\n  // :: (string, any, ?string) → OrderedMap\n  // Create a new map by replacing the value of `key` with a new\n  // value, or adding a binding to the end of the map. If `newKey` is\n  // given, the key of the binding will be replaced with that key.\n  update: function(key, value, newKey) {\n    var self = newKey && newKey != key ? this.remove(newKey) : this;\n    var found = self.find(key), content = self.content.slice();\n    if (found == -1) {\n      content.push(newKey || key, value);\n    } else {\n      content[found + 1] = value;\n      if (newKey) content[found] = newKey;\n    }\n    return new OrderedMap(content)\n  },\n\n  // :: (string) → OrderedMap\n  // Return a map with the given key removed, if it existed.\n  remove: function(key) {\n    var found = this.find(key);\n    if (found == -1) return this\n    var content = this.content.slice();\n    content.splice(found, 2);\n    return new OrderedMap(content)\n  },\n\n  // :: (string, any) → OrderedMap\n  // Add a new key to the start of the map.\n  addToStart: function(key, value) {\n    return new OrderedMap([key, value].concat(this.remove(key).content))\n  },\n\n  // :: (string, any) → OrderedMap\n  // Add a new key to the end of the map.\n  addToEnd: function(key, value) {\n    var content = this.remove(key).content.slice();\n    content.push(key, value);\n    return new OrderedMap(content)\n  },\n\n  // :: (string, string, any) → OrderedMap\n  // Add a key after the given key. If `place` is not found, the new\n  // key is added to the end.\n  addBefore: function(place, key, value) {\n    var without = this.remove(key), content = without.content.slice();\n    var found = without.find(place);\n    content.splice(found == -1 ? content.length : found, 0, key, value);\n    return new OrderedMap(content)\n  },\n\n  // :: ((key: string, value: any))\n  // Call the given function for each key/value pair in the map, in\n  // order.\n  forEach: function(f) {\n    for (var i = 0; i < this.content.length; i += 2)\n      f(this.content[i], this.content[i + 1]);\n  },\n\n  // :: (union<Object, OrderedMap>) → OrderedMap\n  // Create a new map by prepending the keys in this map that don't\n  // appear in `map` before the keys in `map`.\n  prepend: function(map) {\n    map = OrderedMap.from(map);\n    if (!map.size) return this\n    return new OrderedMap(map.content.concat(this.subtract(map).content))\n  },\n\n  // :: (union<Object, OrderedMap>) → OrderedMap\n  // Create a new map by appending the keys in this map that don't\n  // appear in `map` after the keys in `map`.\n  append: function(map) {\n    map = OrderedMap.from(map);\n    if (!map.size) return this\n    return new OrderedMap(this.subtract(map).content.concat(map.content))\n  },\n\n  // :: (union<Object, OrderedMap>) → OrderedMap\n  // Create a map containing all the keys in this map that don't\n  // appear in `map`.\n  subtract: function(map) {\n    var result = this;\n    map = OrderedMap.from(map);\n    for (var i = 0; i < map.content.length; i += 2)\n      result = result.remove(map.content[i]);\n    return result\n  },\n\n  // :: () → Object\n  // Turn ordered map into a plain object.\n  toObject: function() {\n    var result = {};\n    this.forEach(function(key, value) { result[key] = value; });\n    return result\n  },\n\n  // :: number\n  // The amount of keys in this map.\n  get size() {\n    return this.content.length >> 1\n  }\n};\n\n// :: (?union<Object, OrderedMap>) → OrderedMap\n// Return a map with the given content. If null, create an empty\n// map. If given an ordered map, return that map itself. If given an\n// object, create a map from the object's properties.\nOrderedMap.from = function(value) {\n  if (value instanceof OrderedMap) return value\n  var content = [];\n  if (value) for (var prop in value) content.push(prop, value[prop]);\n  return new OrderedMap(content)\n};\n\nexport default OrderedMap;\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACAA;AAAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAEA,SAAS,WAAW,SAAS;AAC3B,OAAK,UAAU;AACjB;AAEA,WAAW,YAAY;AAAA,EACrB,aAAa;AAAA,EAEb,MAAM,SAAS,KAAK;AAClB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC5C,UAAI,KAAK,QAAQ,CAAC,MAAM,IAAK,QAAO;AACtC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,SAAS,KAAK;AACjB,QAAIC,SAAQ,KAAK,KAAK,GAAG;AACzB,WAAOA,UAAS,KAAK,SAAY,KAAK,QAAQA,SAAQ,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ,SAAS,KAAK,OAAO,QAAQ;AACnC,QAAI,OAAO,UAAU,UAAU,MAAM,KAAK,OAAO,MAAM,IAAI;AAC3D,QAAIA,SAAQ,KAAK,KAAK,GAAG,GAAG,UAAU,KAAK,QAAQ,MAAM;AACzD,QAAIA,UAAS,IAAI;AACf,cAAQ,KAAK,UAAU,KAAK,KAAK;AAAA,IACnC,OAAO;AACL,cAAQA,SAAQ,CAAC,IAAI;AACrB,UAAI,OAAQ,SAAQA,MAAK,IAAI;AAAA,IAC/B;AACA,WAAO,IAAI,WAAW,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA,EAIA,QAAQ,SAAS,KAAK;AACpB,QAAIA,SAAQ,KAAK,KAAK,GAAG;AACzB,QAAIA,UAAS,GAAI,QAAO;AACxB,QAAI,UAAU,KAAK,QAAQ,MAAM;AACjC,YAAQ,OAAOA,QAAO,CAAC;AACvB,WAAO,IAAI,WAAW,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA,EAIA,YAAY,SAAS,KAAK,OAAO;AAC/B,WAAO,IAAI,WAAW,CAAC,KAAK,KAAK,EAAE,OAAO,KAAK,OAAO,GAAG,EAAE,OAAO,CAAC;AAAA,EACrE;AAAA;AAAA;AAAA,EAIA,UAAU,SAAS,KAAK,OAAO;AAC7B,QAAI,UAAU,KAAK,OAAO,GAAG,EAAE,QAAQ,MAAM;AAC7C,YAAQ,KAAK,KAAK,KAAK;AACvB,WAAO,IAAI,WAAW,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,SAAS,OAAO,KAAK,OAAO;AACrC,QAAI,UAAU,KAAK,OAAO,GAAG,GAAG,UAAU,QAAQ,QAAQ,MAAM;AAChE,QAAIA,SAAQ,QAAQ,KAAK,KAAK;AAC9B,YAAQ,OAAOA,UAAS,KAAK,QAAQ,SAASA,QAAO,GAAG,KAAK,KAAK;AAClE,WAAO,IAAI,WAAW,OAAO;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,SAAS,GAAG;AACnB,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AAC5C,QAAE,KAAK,QAAQ,CAAC,GAAG,KAAK,QAAQ,IAAI,CAAC,CAAC;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,SAAS,KAAK;AACrB,UAAM,WAAW,KAAK,GAAG;AACzB,QAAI,CAAC,IAAI,KAAM,QAAO;AACtB,WAAO,IAAI,WAAW,IAAI,QAAQ,OAAO,KAAK,SAAS,GAAG,EAAE,OAAO,CAAC;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,SAAS,KAAK;AACpB,UAAM,WAAW,KAAK,GAAG;AACzB,QAAI,CAAC,IAAI,KAAM,QAAO;AACtB,WAAO,IAAI,WAAW,KAAK,SAAS,GAAG,EAAE,QAAQ,OAAO,IAAI,OAAO,CAAC;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,SAAS,KAAK;AACtB,QAAI,SAAS;AACb,UAAM,WAAW,KAAK,GAAG;AACzB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,QAAQ,KAAK;AAC3C,eAAS,OAAO,OAAO,IAAI,QAAQ,CAAC,CAAC;AACvC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAIA,UAAU,WAAW;AACnB,QAAI,SAAS,CAAC;AACd,SAAK,QAAQ,SAAS,KAAK,OAAO;AAAE,aAAO,GAAG,IAAI;AAAA,IAAO,CAAC;AAC1D,WAAO;AAAA,EACT;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACT,WAAO,KAAK,QAAQ,UAAU;AAAA,EAChC;AACF;AAMA,WAAW,OAAO,SAAS,OAAO;AAChC,MAAI,iBAAiB,WAAY,QAAO;AACxC,MAAI,UAAU,CAAC;AACf,MAAI,MAAO,UAAS,QAAQ,MAAO,SAAQ,KAAK,MAAM,MAAM,IAAI,CAAC;AACjE,SAAO,IAAI,WAAW,OAAO;AAC/B;AAEA,IAAO,eAAQ;;;ADtIf,SAAS,cAAc,GAAG,GAAG,KAAK;AAC9B,WAAS,IAAI,KAAI,KAAK;AAClB,QAAI,KAAK,EAAE,cAAc,KAAK,EAAE;AAC5B,aAAO,EAAE,cAAc,EAAE,aAAa,OAAO;AACjD,QAAI,SAAS,EAAE,MAAM,CAAC,GAAG,SAAS,EAAE,MAAM,CAAC;AAC3C,QAAI,UAAU,QAAQ;AAClB,aAAO,OAAO;AACd;AAAA,IACJ;AACA,QAAI,CAAC,OAAO,WAAW,MAAM;AACzB,aAAO;AACX,QAAI,OAAO,UAAU,OAAO,QAAQ,OAAO,MAAM;AAC7C,eAAS,IAAI,GAAG,OAAO,KAAK,CAAC,KAAK,OAAO,KAAK,CAAC,GAAG;AAC9C;AACJ,aAAO;AAAA,IACX;AACA,QAAI,OAAO,QAAQ,QAAQ,OAAO,QAAQ,MAAM;AAC5C,UAAI,QAAQ,cAAc,OAAO,SAAS,OAAO,SAAS,MAAM,CAAC;AACjE,UAAI,SAAS;AACT,eAAO;AAAA,IACf;AACA,WAAO,OAAO;AAAA,EAClB;AACJ;AACA,SAAS,YAAY,GAAG,GAAG,MAAM,MAAM;AACnC,WAAS,KAAK,EAAE,YAAY,KAAK,EAAE,gBAAc;AAC7C,QAAI,MAAM,KAAK,MAAM;AACjB,aAAO,MAAM,KAAK,OAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAChD,QAAI,SAAS,EAAE,MAAM,EAAE,EAAE,GAAG,SAAS,EAAE,MAAM,EAAE,EAAE,GAAG,OAAO,OAAO;AAClE,QAAI,UAAU,QAAQ;AAClB,cAAQ;AACR,cAAQ;AACR;AAAA,IACJ;AACA,QAAI,CAAC,OAAO,WAAW,MAAM;AACzB,aAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAC9B,QAAI,OAAO,UAAU,OAAO,QAAQ,OAAO,MAAM;AAC7C,UAAI,OAAO,GAAG,UAAU,KAAK,IAAI,OAAO,KAAK,QAAQ,OAAO,KAAK,MAAM;AACvE,aAAO,OAAO,WAAW,OAAO,KAAK,OAAO,KAAK,SAAS,OAAO,CAAC,KAAK,OAAO,KAAK,OAAO,KAAK,SAAS,OAAO,CAAC,GAAG;AAC/G;AACA;AACA;AAAA,MACJ;AACA,aAAO,EAAE,GAAG,MAAM,GAAG,KAAK;AAAA,IAC9B;AACA,QAAI,OAAO,QAAQ,QAAQ,OAAO,QAAQ,MAAM;AAC5C,UAAI,QAAQ,YAAY,OAAO,SAAS,OAAO,SAAS,OAAO,GAAG,OAAO,CAAC;AAC1E,UAAI;AACA,eAAO;AAAA,IACf;AACA,YAAQ;AACR,YAAQ;AAAA,EACZ;AACJ;AASA,IAAM,WAAN,MAAM,UAAS;AAAA;AAAA;AAAA;AAAA,EAIX,YAIA,SAAS,MAAM;AACX,SAAK,UAAU;AACf,SAAK,OAAO,QAAQ;AACpB,QAAI,QAAQ;AACR,eAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ;AAChC,aAAK,QAAQ,QAAQ,CAAC,EAAE;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,MAAM,IAAI,GAAG,YAAY,GAAG,QAAQ;AAC7C,aAAS,IAAI,GAAG,MAAM,GAAG,MAAM,IAAI,KAAK;AACpC,UAAI,QAAQ,KAAK,QAAQ,CAAC,GAAG,MAAM,MAAM,MAAM;AAC/C,UAAI,MAAM,QAAQ,EAAE,OAAO,YAAY,KAAK,UAAU,MAAM,CAAC,MAAM,SAAS,MAAM,QAAQ,MAAM;AAC5F,YAAI,QAAQ,MAAM;AAClB,cAAM,aAAa,KAAK,IAAI,GAAG,OAAO,KAAK,GAAG,KAAK,IAAI,MAAM,QAAQ,MAAM,KAAK,KAAK,GAAG,GAAG,YAAY,KAAK;AAAA,MAChH;AACA,YAAM;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,GAAG;AACX,SAAK,aAAa,GAAG,KAAK,MAAM,CAAC;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAM,IAAI,gBAAgB,UAAU;AAC5C,QAAI,OAAO,IAAI,QAAQ;AACvB,SAAK,aAAa,MAAM,IAAI,CAAC,MAAM,QAAQ;AACvC,UAAI,WAAW,KAAK,SAAS,KAAK,KAAK,MAAM,KAAK,IAAI,MAAM,GAAG,IAAI,KAAK,KAAK,GAAG,IAC1E,CAAC,KAAK,SAAS,KACX,WAAY,OAAO,aAAa,aAAa,SAAS,IAAI,IAAI,WAC1D,KAAK,KAAK,KAAK,WAAW,KAAK,KAAK,KAAK,SAAS,IAAI,IAClD;AAClB,UAAI,KAAK,YAAY,KAAK,UAAU,YAAY,KAAK,gBAAgB,gBAAgB;AACjF,YAAI;AACA,kBAAQ;AAAA;AAER,kBAAQ;AAAA,MAChB;AACA,cAAQ;AAAA,IACZ,GAAG,CAAC;AACJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,OAAO;AACV,QAAI,CAAC,MAAM;AACP,aAAO;AACX,QAAI,CAAC,KAAK;AACN,aAAO;AACX,QAAI,OAAO,KAAK,WAAW,QAAQ,MAAM,YAAY,UAAU,KAAK,QAAQ,MAAM,GAAG,IAAI;AACzF,QAAI,KAAK,UAAU,KAAK,WAAW,KAAK,GAAG;AACvC,cAAQ,QAAQ,SAAS,CAAC,IAAI,KAAK,SAAS,KAAK,OAAO,MAAM,IAAI;AAClE,UAAI;AAAA,IACR;AACA,WAAO,IAAI,MAAM,QAAQ,QAAQ;AAC7B,cAAQ,KAAK,MAAM,QAAQ,CAAC,CAAC;AACjC,WAAO,IAAI,UAAS,SAAS,KAAK,OAAO,MAAM,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,MAAM,KAAK,KAAK,MAAM;AACtB,QAAI,QAAQ,KAAK,MAAM,KAAK;AACxB,aAAO;AACX,QAAI,SAAS,CAAC,GAAG,OAAO;AACxB,QAAI,KAAK;AACL,eAAS,IAAI,GAAG,MAAM,GAAG,MAAM,IAAI,KAAK;AACpC,YAAI,QAAQ,KAAK,QAAQ,CAAC,GAAG,MAAM,MAAM,MAAM;AAC/C,YAAI,MAAM,MAAM;AACZ,cAAI,MAAM,QAAQ,MAAM,IAAI;AACxB,gBAAI,MAAM;AACN,sBAAQ,MAAM,IAAI,KAAK,IAAI,GAAG,OAAO,GAAG,GAAG,KAAK,IAAI,MAAM,KAAK,QAAQ,KAAK,GAAG,CAAC;AAAA;AAEhF,sBAAQ,MAAM,IAAI,KAAK,IAAI,GAAG,OAAO,MAAM,CAAC,GAAG,KAAK,IAAI,MAAM,QAAQ,MAAM,KAAK,MAAM,CAAC,CAAC;AAAA,UACjG;AACA,iBAAO,KAAK,KAAK;AACjB,kBAAQ,MAAM;AAAA,QAClB;AACA,cAAM;AAAA,MACV;AACJ,WAAO,IAAI,UAAS,QAAQ,IAAI;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM,IAAI;AACjB,QAAI,QAAQ;AACR,aAAO,UAAS;AACpB,QAAI,QAAQ,KAAK,MAAM,KAAK,QAAQ;AAChC,aAAO;AACX,WAAO,IAAI,UAAS,KAAK,QAAQ,MAAM,MAAM,EAAE,CAAC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,OAAO,MAAM;AACtB,QAAI,UAAU,KAAK,QAAQ,KAAK;AAChC,QAAI,WAAW;AACX,aAAO;AACX,QAAIC,QAAO,KAAK,QAAQ,MAAM;AAC9B,QAAI,OAAO,KAAK,OAAO,KAAK,WAAW,QAAQ;AAC/C,IAAAA,MAAK,KAAK,IAAI;AACd,WAAO,IAAI,UAASA,OAAM,IAAI;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,MAAM;AACb,WAAO,IAAI,UAAS,CAAC,IAAI,EAAE,OAAO,KAAK,OAAO,GAAG,KAAK,OAAO,KAAK,QAAQ;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,MAAM;AACX,WAAO,IAAI,UAAS,KAAK,QAAQ,OAAO,IAAI,GAAG,KAAK,OAAO,KAAK,QAAQ;AAAA,EAC5E;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO;AACN,QAAI,KAAK,QAAQ,UAAU,MAAM,QAAQ;AACrC,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ;AACrC,UAAI,CAAC,KAAK,QAAQ,CAAC,EAAE,GAAG,MAAM,QAAQ,CAAC,CAAC;AACpC,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,aAAa;AAAE,WAAO,KAAK,QAAQ,SAAS,KAAK,QAAQ,CAAC,IAAI;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA,EAIxE,IAAI,YAAY;AAAE,WAAO,KAAK,QAAQ,SAAS,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,IAAI;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA,EAI7F,IAAI,aAAa;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/C,MAAM,OAAO;AACT,QAAIC,SAAQ,KAAK,QAAQ,KAAK;AAC9B,QAAI,CAACA;AACD,YAAM,IAAI,WAAW,WAAW,QAAQ,uBAAuB,IAAI;AACvE,WAAOA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,WAAO,KAAK,QAAQ,KAAK,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,GAAG;AACP,aAAS,IAAI,GAAG,IAAI,GAAG,IAAI,KAAK,QAAQ,QAAQ,KAAK;AACjD,UAAI,QAAQ,KAAK,QAAQ,CAAC;AAC1B,QAAE,OAAO,GAAG,CAAC;AACb,WAAK,MAAM;AAAA,IACf;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,OAAO,MAAM,GAAG;AAC1B,WAAO,cAAc,MAAM,OAAO,GAAG;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,YAAY,OAAO,MAAM,KAAK,MAAM,WAAW,MAAM,MAAM;AACvD,WAAO,YAAY,MAAM,OAAO,KAAK,QAAQ;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,KAAK,QAAQ,IAAI;AACvB,QAAI,OAAO;AACP,aAAO,SAAS,GAAG,GAAG;AAC1B,QAAI,OAAO,KAAK;AACZ,aAAO,SAAS,KAAK,QAAQ,QAAQ,GAAG;AAC5C,QAAI,MAAM,KAAK,QAAQ,MAAM;AACzB,YAAM,IAAI,WAAW,YAAY,GAAG,yBAAyB,IAAI,GAAG;AACxE,aAAS,IAAI,GAAG,SAAS,KAAI,KAAK;AAC9B,UAAI,MAAM,KAAK,MAAM,CAAC,GAAG,MAAM,SAAS,IAAI;AAC5C,UAAI,OAAO,KAAK;AACZ,YAAI,OAAO,OAAO,QAAQ;AACtB,iBAAO,SAAS,IAAI,GAAG,GAAG;AAC9B,eAAO,SAAS,GAAG,MAAM;AAAA,MAC7B;AACA,eAAS;AAAA,IACb;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AAAE,WAAO,MAAM,KAAK,cAAc,IAAI;AAAA,EAAK;AAAA;AAAA;AAAA;AAAA,EAItD,gBAAgB;AAAE,WAAO,KAAK,QAAQ,KAAK,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIlD,SAAS;AACL,WAAO,KAAK,QAAQ,SAAS,KAAK,QAAQ,IAAI,OAAK,EAAE,OAAO,CAAC,IAAI;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,OAAO;AAC3B,QAAI,CAAC;AACD,aAAO,UAAS;AACpB,QAAI,CAAC,MAAM,QAAQ,KAAK;AACpB,YAAM,IAAI,WAAW,qCAAqC;AAC9D,WAAO,IAAI,UAAS,MAAM,IAAI,OAAO,YAAY,CAAC;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,UAAU,OAAO;AACpB,QAAI,CAAC,MAAM;AACP,aAAO,UAAS;AACpB,QAAI,QAAQ,OAAO;AACnB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,OAAO,MAAM,CAAC;AAClB,cAAQ,KAAK;AACb,UAAI,KAAK,KAAK,UAAU,MAAM,IAAI,CAAC,EAAE,WAAW,IAAI,GAAG;AACnD,YAAI,CAAC;AACD,mBAAS,MAAM,MAAM,GAAG,CAAC;AAC7B,eAAO,OAAO,SAAS,CAAC,IAAI,KACvB,SAAS,OAAO,OAAO,SAAS,CAAC,EAAE,OAAO,KAAK,IAAI;AAAA,MAC5D,WACS,QAAQ;AACb,eAAO,KAAK,IAAI;AAAA,MACpB;AAAA,IACJ;AACA,WAAO,IAAI,UAAS,UAAU,OAAO,IAAI;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,KAAK,OAAO;AACf,QAAI,CAAC;AACD,aAAO,UAAS;AACpB,QAAI,iBAAiB;AACjB,aAAO;AACX,QAAI,MAAM,QAAQ,KAAK;AACnB,aAAO,KAAK,UAAU,KAAK;AAC/B,QAAI,MAAM;AACN,aAAO,IAAI,UAAS,CAAC,KAAK,GAAG,MAAM,QAAQ;AAC/C,UAAM,IAAI,WAAW,qBAAqB,QAAQ,oBAC7C,MAAM,eAAe,qEAAqE,GAAG;AAAA,EACtG;AACJ;AAMA,SAAS,QAAQ,IAAI,SAAS,CAAC,GAAG,CAAC;AACnC,IAAM,QAAQ,EAAE,OAAO,GAAG,QAAQ,EAAE;AACpC,SAAS,SAAS,OAAO,QAAQ;AAC7B,QAAM,QAAQ;AACd,QAAM,SAAS;AACf,SAAO;AACX;AAEA,SAAS,YAAY,GAAG,GAAG;AACvB,MAAI,MAAM;AACN,WAAO;AACX,MAAI,EAAE,KAAK,OAAO,KAAK,aACnB,EAAE,KAAK,OAAO,KAAK;AACnB,WAAO;AACX,MAAI,QAAQ,MAAM,QAAQ,CAAC;AAC3B,MAAI,MAAM,QAAQ,CAAC,KAAK;AACpB,WAAO;AACX,MAAI,OAAO;AACP,QAAI,EAAE,UAAU,EAAE;AACd,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC1B,UAAI,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACvB,eAAO;AAAA,EACnB,OACK;AACD,aAAS,KAAK;AACV,UAAI,EAAE,KAAK,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;AACpC,eAAO;AACf,aAAS,KAAK;AACV,UAAI,EAAE,KAAK;AACP,eAAO;AAAA,EACnB;AACA,SAAO;AACX;AAUA,IAAM,OAAN,MAAM,MAAK;AAAA;AAAA;AAAA;AAAA,EAIP,YAIA,MAIA,OAAO;AACH,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,SAAS,KAAK;AACV,QAAID,OAAM,SAAS;AACnB,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,UAAI,QAAQ,IAAI,CAAC;AACjB,UAAI,KAAK,GAAG,KAAK;AACb,eAAO;AACX,UAAI,KAAK,KAAK,SAAS,MAAM,IAAI,GAAG;AAChC,YAAI,CAACA;AACD,UAAAA,QAAO,IAAI,MAAM,GAAG,CAAC;AAAA,MAC7B,WACS,MAAM,KAAK,SAAS,KAAK,IAAI,GAAG;AACrC,eAAO;AAAA,MACX,OACK;AACD,YAAI,CAAC,UAAU,MAAM,KAAK,OAAO,KAAK,KAAK,MAAM;AAC7C,cAAI,CAACA;AACD,YAAAA,QAAO,IAAI,MAAM,GAAG,CAAC;AACzB,UAAAA,MAAK,KAAK,IAAI;AACd,mBAAS;AAAA,QACb;AACA,YAAIA;AACA,UAAAA,MAAK,KAAK,KAAK;AAAA,MACvB;AAAA,IACJ;AACA,QAAI,CAACA;AACD,MAAAA,QAAO,IAAI,MAAM;AACrB,QAAI,CAAC;AACD,MAAAA,MAAK,KAAK,IAAI;AAClB,WAAOA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,KAAK;AACf,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC5B,UAAI,KAAK,GAAG,IAAI,CAAC,CAAC;AACd,eAAO,IAAI,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,MAAM,IAAI,CAAC,CAAC;AACtD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,KAAK;AACT,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC5B,UAAI,KAAK,GAAG,IAAI,CAAC,CAAC;AACd,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,GAAG,OAAO;AACN,WAAO,QAAQ,SACV,KAAK,QAAQ,MAAM,QAAQ,YAAY,KAAK,OAAO,MAAM,KAAK;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,QAAI,MAAM,EAAE,MAAM,KAAK,KAAK,KAAK;AACjC,aAAS,KAAK,KAAK,OAAO;AACtB,UAAI,QAAQ,KAAK;AACjB;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,iCAAiC;AAC1D,QAAI,OAAO,OAAO,MAAM,KAAK,IAAI;AACjC,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,yBAAyB,KAAK,IAAI,iBAAiB;AAC5E,QAAI,OAAO,KAAK,OAAO,KAAK,KAAK;AACjC,SAAK,WAAW,KAAK,KAAK;AAC1B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,QAAQ,GAAG,GAAG;AACjB,QAAI,KAAK;AACL,aAAO;AACX,QAAI,EAAE,UAAU,EAAE;AACd,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,EAAE,QAAQ;AAC1B,UAAI,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;AACb,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,QAAQ,OAAO;AAClB,QAAI,CAAC,SAAS,MAAM,QAAQ,KAAK,KAAK,MAAM,UAAU;AAClD,aAAO,MAAK;AAChB,QAAI,iBAAiB;AACjB,aAAO,CAAC,KAAK;AACjB,QAAIA,QAAO,MAAM,MAAM;AACvB,IAAAA,MAAK,KAAK,CAAC,GAAG,MAAM,EAAE,KAAK,OAAO,EAAE,KAAK,IAAI;AAC7C,WAAOA;AAAA,EACX;AACJ;AAIA,KAAK,OAAO,CAAC;AAMb,IAAM,eAAN,cAA2B,MAAM;AACjC;AAiBA,IAAM,QAAN,MAAM,OAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaR,YAIA,SAIA,WAIA,SAAS;AACL,SAAK,UAAU;AACf,SAAK,YAAY;AACjB,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACP,WAAO,KAAK,QAAQ,OAAO,KAAK,YAAY,KAAK;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,KAAK,UAAU;AACpB,QAAI,UAAU,WAAW,KAAK,SAAS,MAAM,KAAK,WAAW,QAAQ;AACrE,WAAO,WAAW,IAAI,OAAM,SAAS,KAAK,WAAW,KAAK,OAAO;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,MAAM,IAAI;AACpB,WAAO,IAAI,OAAM,YAAY,KAAK,SAAS,OAAO,KAAK,WAAW,KAAK,KAAK,SAAS,GAAG,KAAK,WAAW,KAAK,OAAO;AAAA,EACxH;AAAA;AAAA;AAAA;AAAA,EAIA,GAAG,OAAO;AACN,WAAO,KAAK,QAAQ,GAAG,MAAM,OAAO,KAAK,KAAK,aAAa,MAAM,aAAa,KAAK,WAAW,MAAM;AAAA,EACxG;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,WAAO,KAAK,UAAU,MAAM,KAAK,YAAY,MAAM,KAAK,UAAU;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,QAAI,CAAC,KAAK,QAAQ;AACd,aAAO;AACX,QAAI,OAAO,EAAE,SAAS,KAAK,QAAQ,OAAO,EAAE;AAC5C,QAAI,KAAK,YAAY;AACjB,WAAK,YAAY,KAAK;AAC1B,QAAI,KAAK,UAAU;AACf,WAAK,UAAU,KAAK;AACxB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,CAAC;AACD,aAAO,OAAM;AACjB,QAAI,YAAY,KAAK,aAAa,GAAG,UAAU,KAAK,WAAW;AAC/D,QAAI,OAAO,aAAa,YAAY,OAAO,WAAW;AAClD,YAAM,IAAI,WAAW,kCAAkC;AAC3D,WAAO,IAAI,OAAM,SAAS,SAAS,QAAQ,KAAK,OAAO,GAAG,WAAW,OAAO;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,QAAQ,UAAU,gBAAgB,MAAM;AAC3C,QAAI,YAAY,GAAG,UAAU;AAC7B,aAAS,IAAI,SAAS,YAAY,KAAK,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,KAAK,KAAK,YAAY,IAAI,EAAE;AACjG;AACJ,aAAS,IAAI,SAAS,WAAW,KAAK,CAAC,EAAE,WAAW,iBAAiB,CAAC,EAAE,KAAK,KAAK,YAAY,IAAI,EAAE;AAChG;AACJ,WAAO,IAAI,OAAM,UAAU,WAAW,OAAO;AAAA,EACjD;AACJ;AAIA,MAAM,QAAQ,IAAI,MAAM,SAAS,OAAO,GAAG,CAAC;AAC5C,SAAS,YAAY,SAAS,MAAM,IAAI;AACpC,MAAI,EAAE,OAAO,OAAO,IAAI,QAAQ,UAAU,IAAI,GAAG,QAAQ,QAAQ,WAAW,KAAK;AACjF,MAAI,EAAE,OAAO,SAAS,QAAQ,SAAS,IAAI,QAAQ,UAAU,EAAE;AAC/D,MAAI,UAAU,QAAQ,MAAM,QAAQ;AAChC,QAAI,YAAY,MAAM,CAAC,QAAQ,MAAM,OAAO,EAAE;AAC1C,YAAM,IAAI,WAAW,yBAAyB;AAClD,WAAO,QAAQ,IAAI,GAAG,IAAI,EAAE,OAAO,QAAQ,IAAI,EAAE,CAAC;AAAA,EACtD;AACA,MAAI,SAAS;AACT,UAAM,IAAI,WAAW,yBAAyB;AAClD,SAAO,QAAQ,aAAa,OAAO,MAAM,KAAK,YAAY,MAAM,SAAS,OAAO,SAAS,GAAG,KAAK,SAAS,CAAC,CAAC,CAAC;AACjH;AACA,SAAS,WAAW,SAAS,MAAM,QAAQ,QAAQ;AAC/C,MAAI,EAAE,OAAO,OAAO,IAAI,QAAQ,UAAU,IAAI,GAAG,QAAQ,QAAQ,WAAW,KAAK;AACjF,MAAI,UAAU,QAAQ,MAAM,QAAQ;AAChC,QAAI,UAAU,CAAC,OAAO,WAAW,OAAO,OAAO,MAAM;AACjD,aAAO;AACX,WAAO,QAAQ,IAAI,GAAG,IAAI,EAAE,OAAO,MAAM,EAAE,OAAO,QAAQ,IAAI,IAAI,CAAC;AAAA,EACvE;AACA,MAAI,QAAQ,WAAW,MAAM,SAAS,OAAO,SAAS,GAAG,MAAM;AAC/D,SAAO,SAAS,QAAQ,aAAa,OAAO,MAAM,KAAK,KAAK,CAAC;AACjE;AACA,SAAS,QAAQ,OAAO,KAAK,OAAO;AAChC,MAAI,MAAM,YAAY,MAAM;AACxB,UAAM,IAAI,aAAa,iDAAiD;AAC5E,MAAI,MAAM,QAAQ,MAAM,aAAa,IAAI,QAAQ,MAAM;AACnD,UAAM,IAAI,aAAa,0BAA0B;AACrD,SAAO,aAAa,OAAO,KAAK,OAAO,CAAC;AAC5C;AACA,SAAS,aAAa,OAAO,KAAK,OAAO,OAAO;AAC5C,MAAI,QAAQ,MAAM,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,KAAK;AACvD,MAAI,SAAS,IAAI,MAAM,KAAK,KAAK,QAAQ,MAAM,QAAQ,MAAM,WAAW;AACpE,QAAI,QAAQ,aAAa,OAAO,KAAK,OAAO,QAAQ,CAAC;AACrD,WAAO,KAAK,KAAK,KAAK,QAAQ,aAAa,OAAO,KAAK,CAAC;AAAA,EAC5D,WACS,CAAC,MAAM,QAAQ,MAAM;AAC1B,WAAO,MAAM,MAAM,cAAc,OAAO,KAAK,KAAK,CAAC;AAAA,EACvD,WACS,CAAC,MAAM,aAAa,CAAC,MAAM,WAAW,MAAM,SAAS,SAAS,IAAI,SAAS,OAAO;AACvF,QAAI,SAAS,MAAM,QAAQ,UAAU,OAAO;AAC5C,WAAO,MAAM,QAAQ,QAAQ,IAAI,GAAG,MAAM,YAAY,EAAE,OAAO,MAAM,OAAO,EAAE,OAAO,QAAQ,IAAI,IAAI,YAAY,CAAC,CAAC;AAAA,EACvH,OACK;AACD,QAAI,EAAE,OAAO,IAAI,IAAI,uBAAuB,OAAO,KAAK;AACxD,WAAO,MAAM,MAAM,gBAAgB,OAAO,OAAO,KAAK,KAAK,KAAK,CAAC;AAAA,EACrE;AACJ;AACA,SAAS,UAAU,MAAM,KAAK;AAC1B,MAAI,CAAC,IAAI,KAAK,kBAAkB,KAAK,IAAI;AACrC,UAAM,IAAI,aAAa,iBAAiB,IAAI,KAAK,OAAO,WAAW,KAAK,KAAK,IAAI;AACzF;AACA,SAAS,SAAS,SAAS,QAAQ,OAAO;AACtC,MAAI,OAAO,QAAQ,KAAK,KAAK;AAC7B,YAAU,MAAM,OAAO,KAAK,KAAK,CAAC;AAClC,SAAO;AACX;AACA,SAAS,QAAQ,OAAO,QAAQ;AAC5B,MAAI,OAAO,OAAO,SAAS;AAC3B,MAAI,QAAQ,KAAK,MAAM,UAAU,MAAM,WAAW,OAAO,IAAI,CAAC;AAC1D,WAAO,IAAI,IAAI,MAAM,SAAS,OAAO,IAAI,EAAE,OAAO,MAAM,IAAI;AAAA;AAE5D,WAAO,KAAK,KAAK;AACzB;AACA,SAAS,SAAS,QAAQ,MAAM,OAAO,QAAQ;AAC3C,MAAI,QAAQ,QAAQ,QAAQ,KAAK,KAAK;AACtC,MAAI,aAAa,GAAG,WAAW,OAAO,KAAK,MAAM,KAAK,IAAI,KAAK;AAC/D,MAAI,QAAQ;AACR,iBAAa,OAAO,MAAM,KAAK;AAC/B,QAAI,OAAO,QAAQ,OAAO;AACtB;AAAA,IACJ,WACS,OAAO,YAAY;AACxB,cAAQ,OAAO,WAAW,MAAM;AAChC;AAAA,IACJ;AAAA,EACJ;AACA,WAAS,IAAI,YAAY,IAAI,UAAU;AACnC,YAAQ,KAAK,MAAM,CAAC,GAAG,MAAM;AACjC,MAAI,QAAQ,KAAK,SAAS,SAAS,KAAK;AACpC,YAAQ,KAAK,YAAY,MAAM;AACvC;AACA,SAAS,MAAM,MAAM,SAAS;AAC1B,OAAK,KAAK,aAAa,OAAO;AAC9B,SAAO,KAAK,KAAK,OAAO;AAC5B;AACA,SAAS,gBAAgB,OAAO,QAAQ,MAAM,KAAK,OAAO;AACtD,MAAI,YAAY,MAAM,QAAQ,SAAS,SAAS,OAAO,QAAQ,QAAQ,CAAC;AACxE,MAAI,UAAU,IAAI,QAAQ,SAAS,SAAS,MAAM,KAAK,QAAQ,CAAC;AAChE,MAAI,UAAU,CAAC;AACf,WAAS,MAAM,OAAO,OAAO,OAAO;AACpC,MAAI,aAAa,WAAW,OAAO,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,GAAG;AAClE,cAAU,WAAW,OAAO;AAC5B,YAAQ,MAAM,WAAW,gBAAgB,OAAO,QAAQ,MAAM,KAAK,QAAQ,CAAC,CAAC,GAAG,OAAO;AAAA,EAC3F,OACK;AACD,QAAI;AACA,cAAQ,MAAM,WAAW,cAAc,OAAO,QAAQ,QAAQ,CAAC,CAAC,GAAG,OAAO;AAC9E,aAAS,QAAQ,MAAM,OAAO,OAAO;AACrC,QAAI;AACA,cAAQ,MAAM,SAAS,cAAc,MAAM,KAAK,QAAQ,CAAC,CAAC,GAAG,OAAO;AAAA,EAC5E;AACA,WAAS,KAAK,MAAM,OAAO,OAAO;AAClC,SAAO,IAAI,SAAS,OAAO;AAC/B;AACA,SAAS,cAAc,OAAO,KAAK,OAAO;AACtC,MAAI,UAAU,CAAC;AACf,WAAS,MAAM,OAAO,OAAO,OAAO;AACpC,MAAI,MAAM,QAAQ,OAAO;AACrB,QAAI,OAAO,SAAS,OAAO,KAAK,QAAQ,CAAC;AACzC,YAAQ,MAAM,MAAM,cAAc,OAAO,KAAK,QAAQ,CAAC,CAAC,GAAG,OAAO;AAAA,EACtE;AACA,WAAS,KAAK,MAAM,OAAO,OAAO;AAClC,SAAO,IAAI,SAAS,OAAO;AAC/B;AACA,SAAS,uBAAuB,OAAO,QAAQ;AAC3C,MAAI,QAAQ,OAAO,QAAQ,MAAM,WAAW,SAAS,OAAO,KAAK,KAAK;AACtE,MAAI,OAAO,OAAO,KAAK,MAAM,OAAO;AACpC,WAAS,IAAI,QAAQ,GAAG,KAAK,GAAG;AAC5B,WAAO,OAAO,KAAK,CAAC,EAAE,KAAK,SAAS,KAAK,IAAI,CAAC;AAClD,SAAO;AAAA,IAAE,OAAO,KAAK,eAAe,MAAM,YAAY,KAAK;AAAA,IACvD,KAAK,KAAK,eAAe,KAAK,QAAQ,OAAO,MAAM,UAAU,KAAK;AAAA,EAAE;AAC5E;AAYA,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA,EAId,YAIA,KAIA,MAIA,cAAc;AACV,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,eAAe;AACpB,SAAK,QAAQ,KAAK,SAAS,IAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,KAAK;AACd,QAAI,OAAO;AACP,aAAO,KAAK;AAChB,QAAI,MAAM;AACN,aAAO,KAAK,QAAQ;AACxB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,SAAS;AAAE,WAAO,KAAK,KAAK,KAAK,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAI7C,IAAI,MAAM;AAAE,WAAO,KAAK,KAAK,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKjC,KAAK,OAAO;AAAE,WAAO,KAAK,KAAK,KAAK,aAAa,KAAK,IAAI,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9D,MAAM,OAAO;AAAE,WAAO,KAAK,KAAK,KAAK,aAAa,KAAK,IAAI,IAAI,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnE,WAAW,OAAO;AACd,YAAQ,KAAK,aAAa,KAAK;AAC/B,WAAO,KAAK,MAAM,KAAK,KAAK,SAAS,KAAK,SAAS,CAAC,KAAK,aAAa,IAAI;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACT,YAAQ,KAAK,aAAa,KAAK;AAC/B,WAAO,SAAS,IAAI,IAAI,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,OAAO;AACP,YAAQ,KAAK,aAAa,KAAK;AAC/B,WAAO,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK,KAAK,EAAE,QAAQ;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO;AACV,YAAQ,KAAK,aAAa,KAAK;AAC/B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,gDAAgD;AACzE,WAAO,SAAS,KAAK,QAAQ,IAAI,KAAK,MAAM,KAAK,KAAK,QAAQ,IAAI,CAAC;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,OAAO;AACT,YAAQ,KAAK,aAAa,KAAK;AAC/B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,+CAA+C;AACxE,WAAO,SAAS,KAAK,QAAQ,IAAI,KAAK,MAAM,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK,KAAK,QAAQ,CAAC,EAAE;AAAA,EAChG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aAAa;AAAE,WAAO,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMtE,IAAI,YAAY;AACZ,QAAI,SAAS,KAAK,QAAQ,QAAQ,KAAK,MAAM,KAAK,KAAK;AACvD,QAAI,SAAS,OAAO;AAChB,aAAO;AACX,QAAI,OAAO,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC,GAAG,QAAQ,OAAO,MAAM,KAAK;AACjF,WAAO,OAAO,OAAO,MAAM,KAAK,EAAE,IAAI,IAAI,IAAI;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,aAAa;AACb,QAAI,QAAQ,KAAK,MAAM,KAAK,KAAK;AACjC,QAAI,OAAO,KAAK,MAAM,KAAK,KAAK,KAAK,KAAK,SAAS,CAAC;AACpD,QAAI;AACA,aAAO,KAAK,OAAO,MAAM,KAAK,EAAE,IAAI,GAAG,IAAI;AAC/C,WAAO,SAAS,IAAI,OAAO,KAAK,OAAO,MAAM,QAAQ,CAAC;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO,OAAO;AACrB,YAAQ,KAAK,aAAa,KAAK;AAC/B,QAAI,OAAO,KAAK,KAAK,QAAQ,CAAC,GAAG,MAAM,SAAS,IAAI,IAAI,KAAK,KAAK,QAAQ,IAAI,CAAC,IAAI;AACnF,aAAS,IAAI,GAAG,IAAI,OAAO;AACvB,aAAO,KAAK,MAAM,CAAC,EAAE;AACzB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACJ,QAAI,SAAS,KAAK,QAAQ,QAAQ,KAAK,MAAM;AAE7C,QAAI,OAAO,QAAQ,QAAQ;AACvB,aAAO,KAAK;AAEhB,QAAI,KAAK;AACL,aAAO,OAAO,MAAM,KAAK,EAAE;AAC/B,QAAI,OAAO,OAAO,WAAW,QAAQ,CAAC,GAAG,QAAQ,OAAO,WAAW,KAAK;AAGxE,QAAI,CAAC,MAAM;AACP,UAAI,MAAM;AACV,aAAO;AACP,cAAQ;AAAA,IACZ;AAGA,QAAI,QAAQ,KAAK;AACjB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,UAAI,MAAM,CAAC,EAAE,KAAK,KAAK,cAAc,UAAU,CAAC,SAAS,CAAC,MAAM,CAAC,EAAE,QAAQ,MAAM,KAAK;AAClF,gBAAQ,MAAM,GAAG,EAAE,cAAc,KAAK;AAC9C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,MAAM;AACd,QAAI,QAAQ,KAAK,OAAO,WAAW,KAAK,MAAM,CAAC;AAC/C,QAAI,CAAC,SAAS,CAAC,MAAM;AACjB,aAAO;AACX,QAAI,QAAQ,MAAM,OAAO,OAAO,KAAK,OAAO,WAAW,KAAK,MAAM,CAAC;AACnE,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,UAAI,MAAM,CAAC,EAAE,KAAK,KAAK,cAAc,UAAU,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,QAAQ,KAAK,KAAK;AAChF,gBAAQ,MAAM,GAAG,EAAE,cAAc,KAAK;AAC9C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,KAAK;AACb,aAAS,QAAQ,KAAK,OAAO,QAAQ,GAAG;AACpC,UAAI,KAAK,MAAM,KAAK,KAAK,OAAO,KAAK,IAAI,KAAK,KAAK;AAC/C,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,WAAW,QAAQ,MAAM,MAAM;AAC3B,QAAI,MAAM,MAAM,KAAK;AACjB,aAAO,MAAM,WAAW,IAAI;AAChC,aAAS,IAAI,KAAK,SAAS,KAAK,OAAO,iBAAiB,KAAK,OAAO,MAAM,MAAM,IAAI,IAAI,KAAK,GAAG;AAC5F,UAAI,MAAM,OAAO,KAAK,IAAI,CAAC,MAAM,CAAC,QAAQ,KAAK,KAAK,KAAK,CAAC,CAAC;AACvD,eAAO,IAAI,UAAU,MAAM,OAAO,CAAC;AAC3C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,WAAO,KAAK,MAAM,KAAK,gBAAgB,MAAM,MAAM,MAAM;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACP,WAAO,MAAM,MAAM,KAAK,MAAM,QAAQ;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AACP,WAAO,MAAM,MAAM,KAAK,MAAM,QAAQ;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,QAAI,MAAM;AACV,aAAS,IAAI,GAAG,KAAK,KAAK,OAAO;AAC7B,cAAQ,MAAM,MAAM,MAAM,KAAK,KAAK,CAAC,EAAE,KAAK,OAAO,MAAM,KAAK,MAAM,IAAI,CAAC;AAC7E,WAAO,MAAM,MAAM,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,QAAQE,MAAK,KAAK;AACrB,QAAI,EAAE,OAAO,KAAK,OAAOA,KAAI,QAAQ;AACjC,YAAM,IAAI,WAAW,cAAc,MAAM,eAAe;AAC5D,QAAI,OAAO,CAAC;AACZ,QAAI,QAAQ,GAAG,eAAe;AAC9B,aAAS,OAAOA,UAAO;AACnB,UAAI,EAAE,OAAO,OAAO,IAAI,KAAK,QAAQ,UAAU,YAAY;AAC3D,UAAI,MAAM,eAAe;AACzB,WAAK,KAAK,MAAM,OAAO,QAAQ,MAAM;AACrC,UAAI,CAAC;AACD;AACJ,aAAO,KAAK,MAAM,KAAK;AACvB,UAAI,KAAK;AACL;AACJ,qBAAe,MAAM;AACrB,eAAS,SAAS;AAAA,IACtB;AACA,WAAO,IAAI,aAAY,KAAK,MAAM,YAAY;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,cAAcA,MAAK,KAAK;AAC3B,QAAI,QAAQ,aAAa,IAAIA,IAAG;AAChC,QAAI,OAAO;AACP,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AACxC,YAAI,MAAM,MAAM,KAAK,CAAC;AACtB,YAAI,IAAI,OAAO;AACX,iBAAO;AAAA,MACf;AAAA,IACJ,OACK;AACD,mBAAa,IAAIA,MAAK,QAAQ,IAAI,cAAY;AAAA,IAClD;AACA,QAAI,SAAS,MAAM,KAAK,MAAM,CAAC,IAAI,aAAY,QAAQA,MAAK,GAAG;AAC/D,UAAM,KAAK,MAAM,IAAI,KAAK;AAC1B,WAAO;AAAA,EACX;AACJ;AACA,IAAM,eAAN,MAAmB;AAAA,EACf,cAAc;AACV,SAAK,OAAO,CAAC;AACb,SAAK,IAAI;AAAA,EACb;AACJ;AACA,IAAM,mBAAmB;AAAzB,IAA6B,eAAe,oBAAI,QAAQ;AAKxD,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,YAOA,OAKA,KAIA,OAAO;AACH,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AAAE,WAAO,KAAK,MAAM,OAAO,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIxD,IAAI,MAAM;AAAE,WAAO,KAAK,IAAI,MAAM,KAAK,QAAQ,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAInD,IAAI,SAAS;AAAE,WAAO,KAAK,MAAM,KAAK,KAAK,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAInD,IAAI,aAAa;AAAE,WAAO,KAAK,MAAM,MAAM,KAAK,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIxD,IAAI,WAAW;AAAE,WAAO,KAAK,IAAI,WAAW,KAAK,KAAK;AAAA,EAAG;AAC7D;AAEA,IAAM,aAAa,uBAAO,OAAO,IAAI;AAerC,IAAM,OAAN,MAAM,MAAK;AAAA;AAAA;AAAA;AAAA,EAIP,YAIA,MAMA,OAEA,SAKA,QAAQ,KAAK,MAAM;AACf,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,UAAU,WAAW,SAAS;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,WAAW;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQ9C,IAAI,WAAW;AAAE,WAAO,KAAK,SAAS,IAAI,IAAI,KAAK,QAAQ;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA,EAIjE,IAAI,aAAa;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnD,MAAM,OAAO;AAAE,WAAO,KAAK,QAAQ,MAAM,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIjD,WAAW,OAAO;AAAE,WAAO,KAAK,QAAQ,WAAW,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAK3D,QAAQ,GAAG;AAAE,SAAK,QAAQ,QAAQ,CAAC;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWtC,aAAa,MAAM,IAAI,GAAG,WAAW,GAAG;AACpC,SAAK,QAAQ,aAAa,MAAM,IAAI,GAAG,UAAU,IAAI;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,GAAG;AACX,SAAK,aAAa,GAAG,KAAK,QAAQ,MAAM,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AACd,WAAQ,KAAK,UAAU,KAAK,KAAK,KAAK,WAChC,KAAK,KAAK,KAAK,SAAS,IAAI,IAC5B,KAAK,YAAY,GAAG,KAAK,QAAQ,MAAM,EAAE;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,MAAM,IAAI,gBAAgB,UAAU;AAC5C,WAAO,KAAK,QAAQ,YAAY,MAAM,IAAI,gBAAgB,QAAQ;AAAA,EACtE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAY;AAAA;AAAA;AAAA;AAAA;AAAA,EAKnD,IAAI,YAAY;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAW;AAAA;AAAA;AAAA;AAAA,EAIjD,GAAG,OAAO;AACN,WAAO,QAAQ,SAAU,KAAK,WAAW,KAAK,KAAK,KAAK,QAAQ,GAAG,MAAM,OAAO;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,OAAO;AACd,WAAO,KAAK,UAAU,MAAM,MAAM,MAAM,OAAO,MAAM,KAAK;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,MAAM,OAAO,OAAO;AAC1B,WAAO,KAAK,QAAQ,QAChB,YAAY,KAAK,OAAO,SAAS,KAAK,gBAAgB,UAAU,KAChE,KAAK,QAAQ,KAAK,OAAO,SAAS,KAAK,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,UAAU,MAAM;AACjB,QAAI,WAAW,KAAK;AAChB,aAAO;AACX,WAAO,IAAI,MAAK,KAAK,MAAM,KAAK,OAAO,SAAS,KAAK,KAAK;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,OAAO;AACR,WAAO,SAAS,KAAK,QAAQ,OAAO,IAAI,MAAK,KAAK,MAAM,KAAK,OAAO,KAAK,SAAS,KAAK;AAAA,EAC3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,MAAM,KAAK,KAAK,QAAQ,MAAM;AAC9B,QAAI,QAAQ,KAAK,MAAM,KAAK,QAAQ;AAChC,aAAO;AACX,WAAO,KAAK,KAAK,KAAK,QAAQ,IAAI,MAAM,EAAE,CAAC;AAAA,EAC/C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,MAAM,MAAM,KAAK,KAAK,QAAQ,MAAM,iBAAiB,OAAO;AACxD,QAAI,QAAQ;AACR,aAAO,MAAM;AACjB,QAAI,QAAQ,KAAK,QAAQ,IAAI,GAAG,MAAM,KAAK,QAAQ,EAAE;AACrD,QAAI,QAAQ,iBAAiB,IAAI,MAAM,YAAY,EAAE;AACrD,QAAI,QAAQ,MAAM,MAAM,KAAK,GAAG,OAAO,MAAM,KAAK,KAAK;AACvD,QAAI,UAAU,KAAK,QAAQ,IAAI,MAAM,MAAM,OAAO,IAAI,MAAM,KAAK;AACjE,WAAO,IAAI,MAAM,SAAS,MAAM,QAAQ,OAAO,IAAI,QAAQ,KAAK;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,MAAM,IAAI,OAAO;AACrB,WAAO,QAAQ,KAAK,QAAQ,IAAI,GAAG,KAAK,QAAQ,EAAE,GAAG,KAAK;AAAA,EAC9D;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,KAAK;AACR,aAAS,OAAO,UAAQ;AACpB,UAAI,EAAE,OAAO,OAAO,IAAI,KAAK,QAAQ,UAAU,GAAG;AAClD,aAAO,KAAK,WAAW,KAAK;AAC5B,UAAI,CAAC;AACD,eAAO;AACX,UAAI,UAAU,OAAO,KAAK;AACtB,eAAO;AACX,aAAO,SAAS;AAAA,IACpB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW,KAAK;AACZ,QAAI,EAAE,OAAO,OAAO,IAAI,KAAK,QAAQ,UAAU,GAAG;AAClD,WAAO,EAAE,MAAM,KAAK,QAAQ,WAAW,KAAK,GAAG,OAAO,OAAO;AAAA,EACjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,KAAK;AACb,QAAI,OAAO;AACP,aAAO,EAAE,MAAM,MAAM,OAAO,GAAG,QAAQ,EAAE;AAC7C,QAAI,EAAE,OAAO,OAAO,IAAI,KAAK,QAAQ,UAAU,GAAG;AAClD,QAAI,SAAS;AACT,aAAO,EAAE,MAAM,KAAK,QAAQ,MAAM,KAAK,GAAG,OAAO,OAAO;AAC5D,QAAI,OAAO,KAAK,QAAQ,MAAM,QAAQ,CAAC;AACvC,WAAO,EAAE,MAAM,OAAO,QAAQ,GAAG,QAAQ,SAAS,KAAK,SAAS;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,KAAK;AAAE,WAAO,YAAY,cAAc,MAAM,GAAG;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAI5D,eAAe,KAAK;AAAE,WAAO,YAAY,QAAQ,MAAM,GAAG;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAK7D,aAAa,MAAM,IAAI,MAAM;AACzB,QAAID,SAAQ;AACZ,QAAI,KAAK;AACL,WAAK,aAAa,MAAM,IAAI,UAAQ;AAChC,YAAI,KAAK,QAAQ,KAAK,KAAK;AACvB,UAAAA,SAAQ;AACZ,eAAO,CAACA;AAAA,MACZ,CAAC;AACL,WAAOA;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AAAE,WAAO,KAAK,KAAK;AAAA,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAK1C,IAAI,cAAc;AAAE,WAAO,KAAK,KAAK;AAAA,EAAa;AAAA;AAAA;AAAA;AAAA,EAIlD,IAAI,gBAAgB;AAAE,WAAO,KAAK,KAAK;AAAA,EAAe;AAAA;AAAA;AAAA;AAAA;AAAA,EAKtD,IAAI,WAAW;AAAE,WAAO,KAAK,KAAK;AAAA,EAAU;AAAA;AAAA;AAAA;AAAA,EAI5C,IAAI,SAAS;AAAE,WAAO,KAAK,KAAK;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA,EAIxC,IAAI,SAAS;AAAE,WAAO,KAAK,KAAK;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQxC,IAAI,SAAS;AAAE,WAAO,KAAK,KAAK;AAAA,EAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxC,WAAW;AACP,QAAI,KAAK,KAAK,KAAK;AACf,aAAO,KAAK,KAAK,KAAK,cAAc,IAAI;AAC5C,QAAI,OAAO,KAAK,KAAK;AACrB,QAAI,KAAK,QAAQ;AACb,cAAQ,MAAM,KAAK,QAAQ,cAAc,IAAI;AACjD,WAAO,UAAU,KAAK,OAAO,IAAI;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,OAAO;AAClB,QAAI,QAAQ,KAAK,KAAK,aAAa,cAAc,KAAK,SAAS,GAAG,KAAK;AACvE,QAAI,CAAC;AACD,YAAM,IAAI,MAAM,sDAAsD;AAC1E,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,WAAW,MAAM,IAAI,cAAc,SAAS,OAAO,QAAQ,GAAG,MAAM,YAAY,YAAY;AACxF,QAAI,MAAM,KAAK,eAAe,IAAI,EAAE,cAAc,aAAa,OAAO,GAAG;AACzE,QAAI,MAAM,OAAO,IAAI,cAAc,KAAK,SAAS,EAAE;AACnD,QAAI,CAAC,OAAO,CAAC,IAAI;AACb,aAAO;AACX,aAAS,IAAI,OAAO,IAAI,KAAK;AACzB,UAAI,CAAC,KAAK,KAAK,YAAY,YAAY,MAAM,CAAC,EAAE,KAAK;AACjD,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,MAAM,IAAI,MAAM,OAAO;AAClC,QAAI,SAAS,CAAC,KAAK,KAAK,YAAY,KAAK;AACrC,aAAO;AACX,QAAI,QAAQ,KAAK,eAAe,IAAI,EAAE,UAAU,IAAI;AACpD,QAAI,MAAM,SAAS,MAAM,cAAc,KAAK,SAAS,EAAE;AACvD,WAAO,MAAM,IAAI,WAAW;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,UAAU,OAAO;AACb,QAAI,MAAM,QAAQ;AACd,aAAO,KAAK,WAAW,KAAK,YAAY,KAAK,YAAY,MAAM,OAAO;AAAA;AAEtE,aAAO,KAAK,KAAK,kBAAkB,MAAM,IAAI;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ;AACJ,SAAK,KAAK,aAAa,KAAK,OAAO;AACnC,SAAK,KAAK,WAAW,KAAK,KAAK;AAC/B,QAAID,QAAO,KAAK;AAChB,aAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ,KAAK;AACxC,UAAI,OAAO,KAAK,MAAM,CAAC;AACvB,WAAK,KAAK,WAAW,KAAK,KAAK;AAC/B,MAAAA,QAAO,KAAK,SAASA,KAAI;AAAA,IAC7B;AACA,QAAI,CAAC,KAAK,QAAQA,OAAM,KAAK,KAAK;AAC9B,YAAM,IAAI,WAAW,wCAAwC,KAAK,KAAK,IAAI,KAAK,KAAK,MAAM,IAAI,OAAK,EAAE,KAAK,IAAI,CAAC,EAAE;AACtH,SAAK,QAAQ,QAAQ,UAAQ,KAAK,MAAM,CAAC;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,QAAI,MAAM,EAAE,MAAM,KAAK,KAAK,KAAK;AACjC,aAAS,KAAK,KAAK,OAAO;AACtB,UAAI,QAAQ,KAAK;AACjB;AAAA,IACJ;AACA,QAAI,KAAK,QAAQ;AACb,UAAI,UAAU,KAAK,QAAQ,OAAO;AACtC,QAAI,KAAK,MAAM;AACX,UAAI,QAAQ,KAAK,MAAM,IAAI,OAAK,EAAE,OAAO,CAAC;AAC9C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,iCAAiC;AAC1D,QAAI,QAAQ;AACZ,QAAI,KAAK,OAAO;AACZ,UAAI,CAAC,MAAM,QAAQ,KAAK,KAAK;AACzB,cAAM,IAAI,WAAW,qCAAqC;AAC9D,cAAQ,KAAK,MAAM,IAAI,OAAO,YAAY;AAAA,IAC9C;AACA,QAAI,KAAK,QAAQ,QAAQ;AACrB,UAAI,OAAO,KAAK,QAAQ;AACpB,cAAM,IAAI,WAAW,2BAA2B;AACpD,aAAO,OAAO,KAAK,KAAK,MAAM,KAAK;AAAA,IACvC;AACA,QAAI,UAAU,SAAS,SAAS,QAAQ,KAAK,OAAO;AACpD,QAAI,OAAO,OAAO,SAAS,KAAK,IAAI,EAAE,OAAO,KAAK,OAAO,SAAS,KAAK;AACvE,SAAK,KAAK,WAAW,KAAK,KAAK;AAC/B,WAAO;AAAA,EACX;AACJ;AACA,KAAK,UAAU,OAAO;AACtB,IAAM,WAAN,MAAM,kBAAiB,KAAK;AAAA;AAAA;AAAA;AAAA,EAIxB,YAAY,MAAM,OAAO,SAAS,OAAO;AACrC,UAAM,MAAM,OAAO,MAAM,KAAK;AAC9B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,kCAAkC;AAC3D,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,WAAW;AACP,QAAI,KAAK,KAAK,KAAK;AACf,aAAO,KAAK,KAAK,KAAK,cAAc,IAAI;AAC5C,WAAO,UAAU,KAAK,OAAO,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,EAC1D;AAAA,EACA,IAAI,cAAc;AAAE,WAAO,KAAK;AAAA,EAAM;AAAA,EACtC,YAAY,MAAM,IAAI;AAAE,WAAO,KAAK,KAAK,MAAM,MAAM,EAAE;AAAA,EAAG;AAAA,EAC1D,IAAI,WAAW;AAAE,WAAO,KAAK,KAAK;AAAA,EAAQ;AAAA,EAC1C,KAAK,OAAO;AACR,WAAO,SAAS,KAAK,QAAQ,OAAO,IAAI,UAAS,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,KAAK;AAAA,EAC5F;AAAA,EACA,SAAS,MAAM;AACX,QAAI,QAAQ,KAAK;AACb,aAAO;AACX,WAAO,IAAI,UAAS,KAAK,MAAM,KAAK,OAAO,MAAM,KAAK,KAAK;AAAA,EAC/D;AAAA,EACA,IAAI,OAAO,GAAG,KAAK,KAAK,KAAK,QAAQ;AACjC,QAAI,QAAQ,KAAK,MAAM,KAAK,KAAK;AAC7B,aAAO;AACX,WAAO,KAAK,SAAS,KAAK,KAAK,MAAM,MAAM,EAAE,CAAC;AAAA,EAClD;AAAA,EACA,GAAG,OAAO;AACN,WAAO,KAAK,WAAW,KAAK,KAAK,KAAK,QAAQ,MAAM;AAAA,EACxD;AAAA,EACA,SAAS;AACL,QAAI,OAAO,MAAM,OAAO;AACxB,SAAK,OAAO,KAAK;AACjB,WAAO;AAAA,EACX;AACJ;AACA,SAAS,UAAU,OAAO,KAAK;AAC3B,WAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG;AACnC,UAAM,MAAM,CAAC,EAAE,KAAK,OAAO,MAAM,MAAM;AAC3C,SAAO;AACX;AAQA,IAAM,eAAN,MAAM,cAAa;AAAA;AAAA;AAAA;AAAA,EAIf,YAIA,UAAU;AACN,SAAK,WAAW;AAIhB,SAAK,OAAO,CAAC;AAIb,SAAK,YAAY,CAAC;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,QAAQ,WAAW;AAC5B,QAAI,SAAS,IAAI,YAAY,QAAQ,SAAS;AAC9C,QAAI,OAAO,QAAQ;AACf,aAAO,cAAa;AACxB,QAAI,OAAO,UAAU,MAAM;AAC3B,QAAI,OAAO;AACP,aAAO,IAAI,0BAA0B;AACzC,QAAI,QAAQ,IAAI,IAAI,IAAI,CAAC;AACzB,qBAAiB,OAAO,MAAM;AAC9B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,MAAM;AACZ,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ;AAClC,UAAI,KAAK,KAAK,CAAC,EAAE,QAAQ;AACrB,eAAO,KAAK,KAAK,CAAC,EAAE;AAC5B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,MAAM,QAAQ,GAAG,MAAM,KAAK,YAAY;AAClD,QAAI,MAAM;AACV,aAAS,IAAI,OAAO,OAAO,IAAI,KAAK;AAChC,YAAM,IAAI,UAAU,KAAK,MAAM,CAAC,EAAE,IAAI;AAC1C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,gBAAgB;AAChB,WAAO,KAAK,KAAK,UAAU,KAAK,KAAK,KAAK,CAAC,EAAE,KAAK;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,cAAc;AACd,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,UAAI,EAAE,KAAK,IAAI,KAAK,KAAK,CAAC;AAC1B,UAAI,EAAE,KAAK,UAAU,KAAK,iBAAiB;AACvC,eAAO;AAAA,IACf;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,aAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ;AAClC,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ;AACnC,YAAI,KAAK,KAAK,CAAC,EAAE,QAAQ,MAAM,KAAK,CAAC,EAAE;AACnC,iBAAO;AACnB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,OAAO,QAAQ,OAAO,aAAa,GAAG;AAC7C,QAAI,OAAO,CAAC,IAAI;AAChB,aAAS,OAAO,OAAO,OAAO;AAC1B,UAAI,WAAW,MAAM,cAAc,OAAO,UAAU;AACpD,UAAI,aAAa,CAAC,SAAS,SAAS;AAChC,eAAO,SAAS,KAAK,MAAM,IAAI,QAAM,GAAG,cAAc,CAAC,CAAC;AAC5D,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AACxC,YAAI,EAAE,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC;AACjC,YAAI,EAAE,KAAK,UAAU,KAAK,iBAAiB,MAAM,KAAK,QAAQ,IAAI,KAAK,IAAI;AACvE,eAAK,KAAK,IAAI;AACd,cAAIC,SAAQ,OAAO,MAAM,MAAM,OAAO,IAAI,CAAC;AAC3C,cAAIA;AACA,mBAAOA;AAAA,QACf;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,WAAO,OAAO,MAAM,CAAC,CAAC;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,aAAa,QAAQ;AACjB,aAAS,IAAI,GAAG,IAAI,KAAK,UAAU,QAAQ,KAAK;AAC5C,UAAI,KAAK,UAAU,CAAC,KAAK;AACrB,eAAO,KAAK,UAAU,IAAI,CAAC;AACnC,QAAI,WAAW,KAAK,gBAAgB,MAAM;AAC1C,SAAK,UAAU,KAAK,QAAQ,QAAQ;AACpC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,QAAQ;AACpB,QAAI,OAAO,uBAAO,OAAO,IAAI,GAAG,SAAS,CAAC,EAAE,OAAO,MAAM,MAAM,MAAM,KAAK,KAAK,CAAC;AAChF,WAAO,OAAO,QAAQ;AAClB,UAAI,UAAU,OAAO,MAAM,GAAG,QAAQ,QAAQ;AAC9C,UAAI,MAAM,UAAU,MAAM,GAAG;AACzB,YAAI,SAAS,CAAC;AACd,iBAAS,MAAM,SAAS,IAAI,MAAM,MAAM,IAAI;AACxC,iBAAO,KAAK,IAAI,IAAI;AACxB,eAAO,OAAO,QAAQ;AAAA,MAC1B;AACA,eAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AACxC,YAAI,EAAE,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC;AACjC,YAAI,CAAC,KAAK,UAAU,CAAC,KAAK,iBAAiB,KAAK,EAAE,KAAK,QAAQ,UAAU,CAAC,QAAQ,QAAQ,KAAK,WAAW;AACtG,iBAAO,KAAK,EAAE,OAAO,KAAK,cAAc,MAAM,KAAK,QAAQ,CAAC;AAC5D,eAAK,KAAK,IAAI,IAAI;AAAA,QACtB;AAAA,MACJ;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACZ,WAAO,KAAK,KAAK;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,GAAG;AACJ,QAAI,KAAK,KAAK,KAAK;AACf,YAAM,IAAI,WAAW,cAAc,CAAC,+BAA+B;AACvE,WAAO,KAAK,KAAK,CAAC;AAAA,EACtB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG;AACb,WAAK,KAAK,CAAC;AACX,eAAS,IAAI,GAAG,IAAI,EAAE,KAAK,QAAQ;AAC/B,YAAI,KAAK,QAAQ,EAAE,KAAK,CAAC,EAAE,IAAI,KAAK;AAChC,eAAK,EAAE,KAAK,CAAC,EAAE,IAAI;AAAA,IAC/B;AACA,SAAK,IAAI;AACT,WAAO,KAAK,IAAI,CAAC,GAAG,MAAM;AACtB,UAAI,MAAM,KAAK,EAAE,WAAW,MAAM,OAAO;AACzC,eAASE,KAAI,GAAGA,KAAI,EAAE,KAAK,QAAQA;AAC/B,gBAAQA,KAAI,OAAO,MAAM,EAAE,KAAKA,EAAC,EAAE,KAAK,OAAO,OAAO,KAAK,QAAQ,EAAE,KAAKA,EAAC,EAAE,IAAI;AACrF,aAAO;AAAA,IACX,CAAC,EAAE,KAAK,IAAI;AAAA,EAChB;AACJ;AAIA,aAAa,QAAQ,IAAI,aAAa,IAAI;AAC1C,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,QAAQ,WAAW;AAC3B,SAAK,SAAS;AACd,SAAK,YAAY;AACjB,SAAK,SAAS;AACd,SAAK,MAAM;AACX,SAAK,SAAS,OAAO,MAAM,gBAAgB;AAC3C,QAAI,KAAK,OAAO,KAAK,OAAO,SAAS,CAAC,KAAK;AACvC,WAAK,OAAO,IAAI;AACpB,QAAI,KAAK,OAAO,CAAC,KAAK;AAClB,WAAK,OAAO,MAAM;AAAA,EAC1B;AAAA,EACA,IAAI,OAAO;AAAE,WAAO,KAAK,OAAO,KAAK,GAAG;AAAA,EAAG;AAAA,EAC3C,IAAI,KAAK;AAAE,WAAO,KAAK,QAAQ,QAAQ,KAAK,SAAS;AAAA,EAAO;AAAA,EAC5D,IAAI,KAAK;AAAE,UAAM,IAAI,YAAY,MAAM,8BAA8B,KAAK,SAAS,IAAI;AAAA,EAAG;AAC9F;AACA,SAAS,UAAU,QAAQ;AACvB,MAAI,QAAQ,CAAC;AACb,KAAG;AACC,UAAM,KAAK,aAAa,MAAM,CAAC;AAAA,EACnC,SAAS,OAAO,IAAI,GAAG;AACvB,SAAO,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,EAAE,MAAM,UAAU,MAAM;AAClE;AACA,SAAS,aAAa,QAAQ;AAC1B,MAAI,QAAQ,CAAC;AACb,KAAG;AACC,UAAM,KAAK,mBAAmB,MAAM,CAAC;AAAA,EACzC,SAAS,OAAO,QAAQ,OAAO,QAAQ,OAAO,OAAO,QAAQ;AAC7D,SAAO,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,EAAE,MAAM,OAAO,MAAM;AAC/D;AACA,SAAS,mBAAmB,QAAQ;AAChC,MAAI,OAAO,cAAc,MAAM;AAC/B,aAAS;AACL,QAAI,OAAO,IAAI,GAAG;AACd,aAAO,EAAE,MAAM,QAAQ,KAAK;AAAA,aACvB,OAAO,IAAI,GAAG;AACnB,aAAO,EAAE,MAAM,QAAQ,KAAK;AAAA,aACvB,OAAO,IAAI,GAAG;AACnB,aAAO,EAAE,MAAM,OAAO,KAAK;AAAA,aACtB,OAAO,IAAI,GAAG;AACnB,aAAO,eAAe,QAAQ,IAAI;AAAA;AAElC;AAAA,EACR;AACA,SAAO;AACX;AACA,SAAS,SAAS,QAAQ;AACtB,MAAI,KAAK,KAAK,OAAO,IAAI;AACrB,WAAO,IAAI,2BAA2B,OAAO,OAAO,GAAG;AAC3D,MAAI,SAAS,OAAO,OAAO,IAAI;AAC/B,SAAO;AACP,SAAO;AACX;AACA,SAAS,eAAe,QAAQ,MAAM;AAClC,MAAI,MAAM,SAAS,MAAM,GAAG,MAAM;AAClC,MAAI,OAAO,IAAI,GAAG,GAAG;AACjB,QAAI,OAAO,QAAQ;AACf,YAAM,SAAS,MAAM;AAAA;AAErB,YAAM;AAAA,EACd;AACA,MAAI,CAAC,OAAO,IAAI,GAAG;AACf,WAAO,IAAI,uBAAuB;AACtC,SAAO,EAAE,MAAM,SAAS,KAAK,KAAK,KAAK;AAC3C;AACA,SAAS,YAAY,QAAQ,MAAM;AAC/B,MAAI,QAAQ,OAAO,WAAW,OAAO,MAAM,IAAI;AAC/C,MAAI;AACA,WAAO,CAAC,IAAI;AAChB,MAAI,SAAS,CAAC;AACd,WAAS,YAAY,OAAO;AACxB,QAAIC,QAAO,MAAM,QAAQ;AACzB,QAAIA,MAAK,UAAU,IAAI;AACnB,aAAO,KAAKA,KAAI;AAAA,EACxB;AACA,MAAI,OAAO,UAAU;AACjB,WAAO,IAAI,4BAA4B,OAAO,SAAS;AAC3D,SAAO;AACX;AACA,SAAS,cAAc,QAAQ;AAC3B,MAAI,OAAO,IAAI,GAAG,GAAG;AACjB,QAAI,OAAO,UAAU,MAAM;AAC3B,QAAI,CAAC,OAAO,IAAI,GAAG;AACf,aAAO,IAAI,uBAAuB;AACtC,WAAO;AAAA,EACX,WACS,CAAC,KAAK,KAAK,OAAO,IAAI,GAAG;AAC9B,QAAI,QAAQ,YAAY,QAAQ,OAAO,IAAI,EAAE,IAAI,UAAQ;AACrD,UAAI,OAAO,UAAU;AACjB,eAAO,SAAS,KAAK;AAAA,eAChB,OAAO,UAAU,KAAK;AAC3B,eAAO,IAAI,iCAAiC;AAChD,aAAO,EAAE,MAAM,QAAQ,OAAO,KAAK;AAAA,IACvC,CAAC;AACD,WAAO;AACP,WAAO,MAAM,UAAU,IAAI,MAAM,CAAC,IAAI,EAAE,MAAM,UAAU,MAAM;AAAA,EAClE,OACK;AACD,WAAO,IAAI,uBAAuB,OAAO,OAAO,GAAG;AAAA,EACvD;AACJ;AASA,SAAS,IAAI,MAAM;AACf,MAAIC,OAAM,CAAC,CAAC,CAAC;AACb,UAAQ,QAAQ,MAAM,CAAC,GAAG,KAAK,CAAC;AAChC,SAAOA;AACP,WAAS,OAAO;AAAE,WAAOA,KAAI,KAAK,CAAC,CAAC,IAAI;AAAA,EAAG;AAC3C,WAAS,KAAK,MAAM,IAAI,MAAM;AAC1B,QAAIC,QAAO,EAAE,MAAM,GAAG;AACtB,IAAAD,KAAI,IAAI,EAAE,KAAKC,KAAI;AACnB,WAAOA;AAAA,EACX;AACA,WAAS,QAAQ,OAAO,IAAI;AACxB,UAAM,QAAQ,CAAAA,UAAQA,MAAK,KAAK,EAAE;AAAA,EACtC;AACA,WAAS,QAAQC,OAAM,MAAM;AACzB,QAAIA,MAAK,QAAQ,UAAU;AACvB,aAAOA,MAAK,MAAM,OAAO,CAAC,KAAKA,UAAS,IAAI,OAAO,QAAQA,OAAM,IAAI,CAAC,GAAG,CAAC,CAAC;AAAA,IAC/E,WACSA,MAAK,QAAQ,OAAO;AACzB,eAAS,IAAI,KAAI,KAAK;AAClB,YAAI,OAAO,QAAQA,MAAK,MAAM,CAAC,GAAG,IAAI;AACtC,YAAI,KAAKA,MAAK,MAAM,SAAS;AACzB,iBAAO;AACX,gBAAQ,MAAM,OAAO,KAAK,CAAC;AAAA,MAC/B;AAAA,IACJ,WACSA,MAAK,QAAQ,QAAQ;AAC1B,UAAI,OAAO,KAAK;AAChB,WAAK,MAAM,IAAI;AACf,cAAQ,QAAQA,MAAK,MAAM,IAAI,GAAG,IAAI;AACtC,aAAO,CAAC,KAAK,IAAI,CAAC;AAAA,IACtB,WACSA,MAAK,QAAQ,QAAQ;AAC1B,UAAI,OAAO,KAAK;AAChB,cAAQ,QAAQA,MAAK,MAAM,IAAI,GAAG,IAAI;AACtC,cAAQ,QAAQA,MAAK,MAAM,IAAI,GAAG,IAAI;AACtC,aAAO,CAAC,KAAK,IAAI,CAAC;AAAA,IACtB,WACSA,MAAK,QAAQ,OAAO;AACzB,aAAO,CAAC,KAAK,IAAI,CAAC,EAAE,OAAO,QAAQA,MAAK,MAAM,IAAI,CAAC;AAAA,IACvD,WACSA,MAAK,QAAQ,SAAS;AAC3B,UAAI,MAAM;AACV,eAAS,IAAI,GAAG,IAAIA,MAAK,KAAK,KAAK;AAC/B,YAAI,OAAO,KAAK;AAChB,gBAAQ,QAAQA,MAAK,MAAM,GAAG,GAAG,IAAI;AACrC,cAAM;AAAA,MACV;AACA,UAAIA,MAAK,OAAO,IAAI;AAChB,gBAAQ,QAAQA,MAAK,MAAM,GAAG,GAAG,GAAG;AAAA,MACxC,OACK;AACD,iBAAS,IAAIA,MAAK,KAAK,IAAIA,MAAK,KAAK,KAAK;AACtC,cAAI,OAAO,KAAK;AAChB,eAAK,KAAK,IAAI;AACd,kBAAQ,QAAQA,MAAK,MAAM,GAAG,GAAG,IAAI;AACrC,gBAAM;AAAA,QACV;AAAA,MACJ;AACA,aAAO,CAAC,KAAK,GAAG,CAAC;AAAA,IACrB,WACSA,MAAK,QAAQ,QAAQ;AAC1B,aAAO,CAAC,KAAK,MAAM,QAAWA,MAAK,KAAK,CAAC;AAAA,IAC7C,OACK;AACD,YAAM,IAAI,MAAM,mBAAmB;AAAA,IACvC;AAAA,EACJ;AACJ;AACA,SAAS,IAAI,GAAG,GAAG;AAAE,SAAO,IAAI;AAAG;AAInC,SAAS,SAASF,MAAK,MAAM;AACzB,MAAI,SAAS,CAAC;AACd,OAAK,IAAI;AACT,SAAO,OAAO,KAAK,GAAG;AACtB,WAAS,KAAKG,OAAM;AAChB,QAAI,QAAQH,KAAIG,KAAI;AACpB,QAAI,MAAM,UAAU,KAAK,CAAC,MAAM,CAAC,EAAE;AAC/B,aAAO,KAAK,MAAM,CAAC,EAAE,EAAE;AAC3B,WAAO,KAAKA,KAAI;AAChB,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,EAAE,MAAM,GAAG,IAAI,MAAM,CAAC;AAC1B,UAAI,CAAC,QAAQ,OAAO,QAAQ,EAAE,KAAK;AAC/B,aAAK,EAAE;AAAA,IACf;AAAA,EACJ;AACJ;AAIA,SAAS,IAAIH,MAAK;AACd,MAAI,UAAU,uBAAO,OAAO,IAAI;AAChC,SAAO,QAAQ,SAASA,MAAK,CAAC,CAAC;AAC/B,WAAS,QAAQ,QAAQ;AACrB,QAAI,MAAM,CAAC;AACX,WAAO,QAAQ,UAAQ;AACnB,MAAAA,KAAI,IAAI,EAAE,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;AAChC,YAAI,CAAC;AACD;AACJ,YAAI;AACJ,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC5B,cAAI,IAAI,CAAC,EAAE,CAAC,KAAK;AACb,kBAAM,IAAI,CAAC,EAAE,CAAC;AACtB,iBAASA,MAAK,EAAE,EAAE,QAAQ,CAAAG,UAAQ;AAC9B,cAAI,CAAC;AACD,gBAAI,KAAK,CAAC,MAAM,MAAM,CAAC,CAAC,CAAC;AAC7B,cAAI,IAAI,QAAQA,KAAI,KAAK;AACrB,gBAAI,KAAKA,KAAI;AAAA,QACrB,CAAC;AAAA,MACL,CAAC;AAAA,IACL,CAAC;AACD,QAAI,QAAQ,QAAQ,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,aAAa,OAAO,QAAQH,KAAI,SAAS,CAAC,IAAI,EAAE;AAC5F,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACjC,UAAII,UAAS,IAAI,CAAC,EAAE,CAAC,EAAE,KAAK,GAAG;AAC/B,YAAM,KAAK,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,CAAC,GAAG,MAAM,QAAQA,QAAO,KAAK,GAAG,CAAC,KAAK,QAAQA,OAAM,EAAE,CAAC;AAAA,IAC3F;AACA,WAAO;AAAA,EACX;AACJ;AACA,SAAS,iBAAiB,OAAO,QAAQ;AACrC,WAAS,IAAI,GAAG,OAAO,CAAC,KAAK,GAAG,IAAI,KAAK,QAAQ,KAAK;AAClD,QAAI,QAAQ,KAAK,CAAC,GAAG,OAAO,CAAC,MAAM,UAAU,QAAQ,CAAC;AACtD,aAAS,IAAI,GAAG,IAAI,MAAM,KAAK,QAAQ,KAAK;AACxC,UAAI,EAAE,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC;AACjC,YAAM,KAAK,KAAK,IAAI;AACpB,UAAI,QAAQ,EAAE,KAAK,UAAU,KAAK,iBAAiB;AAC/C,eAAO;AACX,UAAI,KAAK,QAAQ,IAAI,KAAK;AACtB,aAAK,KAAK,IAAI;AAAA,IACtB;AACA,QAAI;AACA,aAAO,IAAI,iCAAiC,MAAM,KAAK,IAAI,IAAI,gFAAgF;AAAA,EACvJ;AACJ;AAMA,SAAS,aAAa,OAAO;AACzB,MAAI,WAAW,uBAAO,OAAO,IAAI;AACjC,WAAS,YAAY,OAAO;AACxB,QAAI,OAAO,MAAM,QAAQ;AACzB,QAAI,CAAC,KAAK;AACN,aAAO;AACX,aAAS,QAAQ,IAAI,KAAK;AAAA,EAC9B;AACA,SAAO;AACX;AACA,SAAS,aAAa,OAAO,OAAO;AAChC,MAAI,QAAQ,uBAAO,OAAO,IAAI;AAC9B,WAAS,QAAQ,OAAO;AACpB,QAAI,QAAQ,SAAS,MAAM,IAAI;AAC/B,QAAI,UAAU,QAAW;AACrB,UAAI,OAAO,MAAM,IAAI;AACrB,UAAI,KAAK;AACL,gBAAQ,KAAK;AAAA;AAEb,cAAM,IAAI,WAAW,qCAAqC,IAAI;AAAA,IACtE;AACA,UAAM,IAAI,IAAI;AAAA,EAClB;AACA,SAAO;AACX;AACA,SAAS,WAAW,OAAO,QAAQ,MAAM,MAAM;AAC3C,WAASC,SAAQ;AACb,QAAI,EAAEA,SAAQ;AACV,YAAM,IAAI,WAAW,yBAAyBA,KAAI,QAAQ,IAAI,YAAYA,KAAI,EAAE;AACxF,WAASA,SAAQ,OAAO;AACpB,QAAI,OAAO,MAAMA,KAAI;AACrB,QAAI,KAAK;AACL,WAAK,SAAS,OAAOA,KAAI,CAAC;AAAA,EAClC;AACJ;AACA,SAAS,UAAU,UAAU,OAAO;AAChC,MAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,MAAI;AACA,aAAS,QAAQ;AACb,aAAO,IAAI,IAAI,IAAI,UAAU,UAAU,MAAM,MAAM,IAAI,CAAC;AAChE,SAAO;AACX;AAOA,IAAM,WAAN,MAAM,UAAS;AAAA;AAAA;AAAA;AAAA,EAIX,YAIA,MAIA,QAIA,MAAM;AACF,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,OAAO;AAKZ,SAAK,UAAU;AACf,SAAK,SAAS,KAAK,QAAQ,KAAK,MAAM,MAAM,GAAG,IAAI,CAAC;AACpD,SAAK,QAAQ,UAAU,MAAM,KAAK,KAAK;AACvC,SAAK,eAAe,aAAa,KAAK,KAAK;AAC3C,SAAK,eAAe;AACpB,SAAK,gBAAgB;AACrB,SAAK,UAAU,EAAE,KAAK,UAAU,QAAQ;AACxC,SAAK,SAAS,QAAQ;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,WAAW;AAAE,WAAO,CAAC,KAAK;AAAA,EAAS;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvC,IAAI,cAAc;AAAE,WAAO,KAAK,WAAW,KAAK;AAAA,EAAe;AAAA;AAAA;AAAA;AAAA,EAI/D,IAAI,SAAS;AAAE,WAAO,KAAK,gBAAgB,aAAa;AAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAK/D,IAAI,SAAS;AAAE,WAAO,KAAK,UAAU,CAAC,CAAC,KAAK,KAAK;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAKvD,UAAU,OAAO;AACb,WAAO,KAAK,OAAO,QAAQ,KAAK,IAAI;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,aAAa;AACb,WAAO,KAAK,KAAK,eAAe,KAAK,KAAK,OAAO,QAAQ;AAAA,EAC7D;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB;AACf,aAAS,KAAK,KAAK;AACf,UAAI,KAAK,MAAM,CAAC,EAAE;AACd,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkB,OAAO;AACrB,WAAO,QAAQ,SAAS,KAAK,aAAa,WAAW,MAAM,YAAY;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,OAAO;AAChB,QAAI,CAAC,SAAS,KAAK;AACf,aAAO,KAAK;AAAA;AAEZ,aAAO,aAAa,KAAK,OAAO,KAAK;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,QAAQ,MAAM,SAAS,OAAO;AACjC,QAAI,KAAK;AACL,YAAM,IAAI,MAAM,4CAA4C;AAChE,WAAO,IAAI,KAAK,MAAM,KAAK,aAAa,KAAK,GAAG,SAAS,KAAK,OAAO,GAAG,KAAK,QAAQ,KAAK,CAAC;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,cAAc,QAAQ,MAAM,SAAS,OAAO;AACxC,cAAU,SAAS,KAAK,OAAO;AAC/B,SAAK,aAAa,OAAO;AACzB,WAAO,IAAI,KAAK,MAAM,KAAK,aAAa,KAAK,GAAG,SAAS,KAAK,QAAQ,KAAK,CAAC;AAAA,EAChF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,QAAQ,MAAM,SAAS,OAAO;AACxC,YAAQ,KAAK,aAAa,KAAK;AAC/B,cAAU,SAAS,KAAK,OAAO;AAC/B,QAAI,QAAQ,MAAM;AACd,UAAI,SAAS,KAAK,aAAa,WAAW,OAAO;AACjD,UAAI,CAAC;AACD,eAAO;AACX,gBAAU,OAAO,OAAO,OAAO;AAAA,IACnC;AACA,QAAI,UAAU,KAAK,aAAa,cAAc,OAAO;AACrD,QAAI,QAAQ,WAAW,QAAQ,WAAW,SAAS,OAAO,IAAI;AAC9D,QAAI,CAAC;AACD,aAAO;AACX,WAAO,IAAI,KAAK,MAAM,OAAO,QAAQ,OAAO,KAAK,GAAG,KAAK,QAAQ,KAAK,CAAC;AAAA,EAC3E;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,SAAS;AAClB,QAAI,SAAS,KAAK,aAAa,cAAc,OAAO;AACpD,QAAI,CAAC,UAAU,CAAC,OAAO;AACnB,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,QAAQ,YAAY;AACpC,UAAI,CAAC,KAAK,YAAY,QAAQ,MAAM,CAAC,EAAE,KAAK;AACxC,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,aAAa,SAAS;AAClB,QAAI,CAAC,KAAK,aAAa,OAAO;AAC1B,YAAM,IAAI,WAAW,4BAA4B,KAAK,IAAI,KAAK,QAAQ,SAAS,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE;AAAA,EACxG;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,eAAW,KAAK,OAAO,OAAO,QAAQ,KAAK,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,UAAU;AACrB,WAAO,KAAK,WAAW,QAAQ,KAAK,QAAQ,QAAQ,QAAQ,IAAI;AAAA,EACpE;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,OAAO;AACf,QAAI,KAAK,WAAW;AAChB,aAAO;AACX,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,UAAI,CAAC,KAAK,eAAe,MAAM,CAAC,EAAE,IAAI;AAClC,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,aAAa,OAAO;AAChB,QAAI,KAAK,WAAW;AAChB,aAAO;AACX,QAAIV;AACJ,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,UAAI,CAAC,KAAK,eAAe,MAAM,CAAC,EAAE,IAAI,GAAG;AACrC,YAAI,CAACA;AACD,UAAAA,QAAO,MAAM,MAAM,GAAG,CAAC;AAAA,MAC/B,WACSA,OAAM;AACX,QAAAA,MAAK,KAAK,MAAM,CAAC,CAAC;AAAA,MACtB;AAAA,IACJ;AACA,WAAO,CAACA,QAAO,QAAQA,MAAK,SAASA,QAAO,KAAK;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,QAAQ,OAAO,QAAQ;AAC1B,QAAI,SAAS,uBAAO,OAAO,IAAI;AAC/B,UAAM,QAAQ,CAAC,MAAM,SAAS,OAAO,IAAI,IAAI,IAAI,UAAS,MAAM,QAAQ,IAAI,CAAC;AAC7E,QAAI,UAAU,OAAO,KAAK,WAAW;AACrC,QAAI,CAAC,OAAO,OAAO;AACf,YAAM,IAAI,WAAW,2CAA2C,UAAU,IAAI;AAClF,QAAI,CAAC,OAAO;AACR,YAAM,IAAI,WAAW,kCAAkC;AAC3D,aAAS,KAAK,OAAO,KAAK;AACtB,YAAM,IAAI,WAAW,+CAA+C;AACxE,WAAO;AAAA,EACX;AACJ;AACA,SAAS,aAAa,UAAU,UAAU,MAAM;AAC5C,MAAI,QAAQ,KAAK,MAAM,GAAG;AAC1B,SAAO,CAAC,UAAU;AACd,QAAI,OAAO,UAAU,OAAO,SAAS,OAAO;AAC5C,QAAI,MAAM,QAAQ,IAAI,IAAI;AACtB,YAAM,IAAI,WAAW,0BAA0B,KAAK,kBAAkB,QAAQ,YAAY,QAAQ,SAAS,IAAI,EAAE;AAAA,EACzH;AACJ;AAEA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,UAAU,UAAU,SAAS;AACrC,SAAK,aAAa,OAAO,UAAU,eAAe,KAAK,SAAS,SAAS;AACzE,SAAK,UAAU,QAAQ;AACvB,SAAK,WAAW,OAAO,QAAQ,YAAY,WAAW,aAAa,UAAU,UAAU,QAAQ,QAAQ,IAAI,QAAQ;AAAA,EACvH;AAAA,EACA,IAAI,aAAa;AACb,WAAO,CAAC,KAAK;AAAA,EACjB;AACJ;AAQA,IAAM,WAAN,MAAM,UAAS;AAAA;AAAA;AAAA;AAAA,EAIX,YAIA,MAIA,MAIA,QAIA,MAAM;AACF,SAAK,OAAO;AACZ,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,QAAQ,UAAU,MAAM,KAAK,KAAK;AACvC,SAAK,WAAW;AAChB,QAAI,WAAW,aAAa,KAAK,KAAK;AACtC,SAAK,WAAW,WAAW,IAAI,KAAK,MAAM,QAAQ,IAAI;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,QAAQ,MAAM;AACjB,QAAI,CAAC,SAAS,KAAK;AACf,aAAO,KAAK;AAChB,WAAO,IAAI,KAAK,MAAM,aAAa,KAAK,OAAO,KAAK,CAAC;AAAA,EACzD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,QAAQ,OAAO,QAAQ;AAC1B,QAAI,SAAS,uBAAO,OAAO,IAAI,GAAG,OAAO;AACzC,UAAM,QAAQ,CAAC,MAAM,SAAS,OAAO,IAAI,IAAI,IAAI,UAAS,MAAM,QAAQ,QAAQ,IAAI,CAAC;AACrF,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,KAAK;AACf,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC5B,UAAI,IAAI,CAAC,EAAE,QAAQ,MAAM;AACrB,cAAM,IAAI,MAAM,GAAG,CAAC,EAAE,OAAO,IAAI,MAAM,IAAI,CAAC,CAAC;AAC7C;AAAA,MACJ;AACJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,KAAK;AACT,aAAS,IAAI,GAAG,IAAI,IAAI,QAAQ;AAC5B,UAAI,IAAI,CAAC,EAAE,QAAQ;AACf,eAAO,IAAI,CAAC;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,OAAO;AACd,eAAW,KAAK,OAAO,OAAO,QAAQ,KAAK,IAAI;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS,OAAO;AACZ,WAAO,KAAK,SAAS,QAAQ,KAAK,IAAI;AAAA,EAC1C;AACJ;AAUA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA,EAIT,YAAY,MAAM;AAMd,SAAK,uBAAuB;AAM5B,SAAK,SAAS,uBAAO,OAAO,IAAI;AAChC,QAAI,eAAe,KAAK,OAAO,CAAC;AAChC,aAAS,QAAQ;AACb,mBAAa,IAAI,IAAI,KAAK,IAAI;AAClC,iBAAa,QAAQ,aAAW,KAAK,KAAK,KAAK,GAC3C,aAAa,QAAQ,aAAW,KAAK,KAAK,SAAS,CAAC,CAAC,GACrD,KAAK,QAAQ,SAAS,QAAQ,KAAK,KAAK,OAAO,IAAI;AACvD,SAAK,QAAQ,SAAS,QAAQ,KAAK,KAAK,OAAO,IAAI;AACnD,QAAI,mBAAmB,uBAAO,OAAO,IAAI;AACzC,aAAS,QAAQ,KAAK,OAAO;AACzB,UAAI,QAAQ,KAAK;AACb,cAAM,IAAI,WAAW,OAAO,oCAAoC;AACpE,UAAI,OAAO,KAAK,MAAM,IAAI,GAAG,cAAc,KAAK,KAAK,WAAW,IAAI,WAAW,KAAK,KAAK;AACzF,WAAK,eAAe,iBAAiB,WAAW,MAC3C,iBAAiB,WAAW,IAAI,aAAa,MAAM,aAAa,KAAK,KAAK;AAC/E,WAAK,gBAAgB,KAAK,aAAa;AACvC,UAAI,KAAK,KAAK,sBAAsB;AAChC,YAAI,KAAK;AACL,gBAAM,IAAI,WAAW,kCAAkC;AAC3D,YAAI,CAAC,KAAK,YAAY,CAAC,KAAK;AACxB,gBAAM,IAAI,WAAW,uDAAuD;AAChF,aAAK,uBAAuB;AAAA,MAChC;AACA,WAAK,UAAU,YAAY,MAAM,OAC7B,WAAW,YAAY,MAAM,SAAS,MAAM,GAAG,CAAC,IAC5C,YAAY,MAAM,CAAC,KAAK,gBAAgB,CAAC,IAAI;AAAA,IACzD;AACA,aAAS,QAAQ,KAAK,OAAO;AACzB,UAAI,OAAO,KAAK,MAAM,IAAI,GAAG,OAAO,KAAK,KAAK;AAC9C,WAAK,WAAW,QAAQ,OAAO,CAAC,IAAI,IAAI,QAAQ,KAAK,CAAC,IAAI,YAAY,MAAM,KAAK,MAAM,GAAG,CAAC;AAAA,IAC/F;AACA,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,eAAe,KAAK,aAAa,KAAK,IAAI;AAC/C,SAAK,cAAc,KAAK,MAAM,KAAK,KAAK,WAAW,KAAK;AACxD,SAAK,OAAO,YAAY,uBAAO,OAAO,IAAI;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,MAAM,QAAQ,MAAM,SAAS,OAAO;AACrC,QAAI,OAAO,QAAQ;AACf,aAAO,KAAK,SAAS,IAAI;AAAA,aACpB,EAAE,gBAAgB;AACvB,YAAM,IAAI,WAAW,wBAAwB,IAAI;AAAA,aAC5C,KAAK,UAAU;AACpB,YAAM,IAAI,WAAW,2CAA2C,KAAK,OAAO,GAAG;AACnF,WAAO,KAAK,cAAc,OAAO,SAAS,KAAK;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,MAAM,OAAO;AACd,QAAI,OAAO,KAAK,MAAM;AACtB,WAAO,IAAI,SAAS,MAAM,KAAK,cAAc,MAAM,KAAK,QAAQ,KAAK,CAAC;AAAA,EAC1E;AAAA;AAAA;AAAA;AAAA,EAIA,KAAK,MAAM,OAAO;AACd,QAAI,OAAO,QAAQ;AACf,aAAO,KAAK,MAAM,IAAI;AAC1B,WAAO,KAAK,OAAO,KAAK;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,MAAM;AACf,WAAO,KAAK,SAAS,MAAM,IAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,MAAM;AACf,WAAO,KAAK,SAAS,MAAM,IAAI;AAAA,EACnC;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,MAAM;AACX,QAAIC,SAAQ,KAAK,MAAM,IAAI;AAC3B,QAAI,CAACA;AACD,YAAM,IAAI,WAAW,wBAAwB,IAAI;AACrD,WAAOA;AAAA,EACX;AACJ;AACA,SAAS,YAAY,QAAQ,OAAO;AAChC,MAAIA,SAAQ,CAAC;AACb,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,QAAI,OAAO,MAAM,CAAC,GAAG,OAAO,OAAO,MAAM,IAAI,GAAG,KAAK;AACrD,QAAI,MAAM;AACN,MAAAA,OAAM,KAAK,IAAI;AAAA,IACnB,OACK;AACD,eAAS,QAAQ,OAAO,OAAO;AAC3B,YAAIU,QAAO,OAAO,MAAM,IAAI;AAC5B,YAAI,QAAQ,OAAQA,MAAK,KAAK,SAASA,MAAK,KAAK,MAAM,MAAM,GAAG,EAAE,QAAQ,IAAI,IAAI;AAC9E,UAAAV,OAAM,KAAK,KAAKU,KAAI;AAAA,MAC5B;AAAA,IACJ;AACA,QAAI,CAAC;AACD,YAAM,IAAI,YAAY,yBAAyB,MAAM,CAAC,IAAI,GAAG;AAAA,EACrE;AACA,SAAOV;AACX;AAEA,SAAS,UAAU,MAAM;AAAE,SAAO,KAAK,OAAO;AAAM;AACpD,SAAS,YAAY,MAAM;AAAE,SAAO,KAAK,SAAS;AAAM;AAMxD,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKZ,YAIA,QAKA,OAAO;AACH,SAAK,SAAS;AACd,SAAK,QAAQ;AAIb,SAAK,OAAO,CAAC;AAIb,SAAK,SAAS,CAAC;AACf,QAAI,gBAAgB,KAAK,gBAAgB,CAAC;AAC1C,UAAM,QAAQ,UAAQ;AAClB,UAAI,UAAU,IAAI,GAAG;AACjB,aAAK,KAAK,KAAK,IAAI;AAAA,MACvB,WACS,YAAY,IAAI,GAAG;AACxB,YAAI,OAAO,QAAQ,KAAK,KAAK,KAAK,EAAE,CAAC;AACrC,YAAI,cAAc,QAAQ,IAAI,IAAI;AAC9B,wBAAc,KAAK,IAAI;AAC3B,aAAK,OAAO,KAAK,IAAI;AAAA,MACzB;AAAA,IACJ,CAAC;AAED,SAAK,iBAAiB,CAAC,KAAK,KAAK,KAAK,OAAK;AACvC,UAAI,CAAC,aAAa,KAAK,EAAE,GAAG,KAAK,CAAC,EAAE;AAChC,eAAO;AACX,UAAI,OAAO,OAAO,MAAM,EAAE,IAAI;AAC9B,aAAO,KAAK,aAAa,UAAU,IAAI;AAAA,IAC3C,CAAC;AAAA,EACL;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,KAAK,UAAU,CAAC,GAAG;AACrB,QAAI,UAAU,IAAI,aAAa,MAAM,SAAS,KAAK;AACnD,YAAQ,OAAO,KAAK,KAAK,MAAM,QAAQ,MAAM,QAAQ,EAAE;AACvD,WAAO,QAAQ,OAAO;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,KAAK,UAAU,CAAC,GAAG;AAC1B,QAAI,UAAU,IAAI,aAAa,MAAM,SAAS,IAAI;AAClD,YAAQ,OAAO,KAAK,KAAK,MAAM,QAAQ,MAAM,QAAQ,EAAE;AACvD,WAAO,MAAM,QAAQ,QAAQ,OAAO,CAAC;AAAA,EACzC;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,KAAK,SAAS,OAAO;AAC1B,aAAS,IAAI,QAAQ,KAAK,KAAK,QAAQ,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AAC9E,UAAI,OAAO,KAAK,KAAK,CAAC;AACtB,UAAI,QAAQ,KAAK,KAAK,GAAG,MACpB,KAAK,cAAc,UAAa,IAAI,gBAAgB,KAAK,eACzD,CAAC,KAAK,WAAW,QAAQ,eAAe,KAAK,OAAO,IAAI;AACzD,YAAI,KAAK,UAAU;AACf,cAAI,SAAS,KAAK,SAAS,GAAG;AAC9B,cAAI,WAAW;AACX;AACJ,eAAK,QAAQ,UAAU;AAAA,QAC3B;AACA,eAAO;AAAA,MACX;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,MAAM,OAAO,SAAS,OAAO;AACpC,aAAS,IAAI,QAAQ,KAAK,OAAO,QAAQ,KAAK,IAAI,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK;AAClF,UAAI,OAAO,KAAK,OAAO,CAAC,GAAG,QAAQ,KAAK;AACxC,UAAI,MAAM,QAAQ,IAAI,KAAK,KACvB,KAAK,WAAW,CAAC,QAAQ,eAAe,KAAK,OAAO;AAAA;AAAA;AAAA,MAIpD,MAAM,SAAS,KAAK,WACf,MAAM,WAAW,KAAK,MAAM,KAAK,MAAM,MAAM,MAAM,KAAK,SAAS,CAAC,KAAK;AAC5E;AACJ,UAAI,KAAK,UAAU;AACf,YAAI,SAAS,KAAK,SAAS,KAAK;AAChC,YAAI,WAAW;AACX;AACJ,aAAK,QAAQ,UAAU;AAAA,MAC3B;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,YAAY,QAAQ;AACvB,QAAI,SAAS,CAAC;AACd,aAAS,OAAO,MAAM;AAClB,UAAI,WAAW,KAAK,YAAY,OAAO,KAAK,KAAK,UAAU,IAAI;AAC/D,aAAO,IAAI,OAAO,QAAQ,KAAK;AAC3B,YAAI,OAAO,OAAO,CAAC,GAAG,eAAe,KAAK,YAAY,OAAO,KAAK,KAAK;AACvE,YAAI,eAAe;AACf;AAAA,MACR;AACA,aAAO,OAAO,GAAG,GAAG,IAAI;AAAA,IAC5B;AACA,aAAS,QAAQ,OAAO,OAAO;AAC3B,UAAI,QAAQ,OAAO,MAAM,IAAI,EAAE,KAAK;AACpC,UAAI;AACA,cAAM,QAAQ,UAAQ;AAClB,iBAAO,OAAO,KAAK,IAAI,CAAC;AACxB,cAAI,EAAE,KAAK,QAAQ,KAAK,UAAU,KAAK;AACnC,iBAAK,OAAO;AAAA,QACpB,CAAC;AAAA,IACT;AACA,aAAS,QAAQ,OAAO,OAAO;AAC3B,UAAI,QAAQ,OAAO,MAAM,IAAI,EAAE,KAAK;AACpC,UAAI;AACA,cAAM,QAAQ,UAAQ;AAClB,iBAAO,OAAO,KAAK,IAAI,CAAC;AACxB,cAAI,EAAE,KAAK,QAAQ,KAAK,UAAU,KAAK;AACnC,iBAAK,OAAO;AAAA,QACpB,CAAC;AAAA,IACT;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,WAAW,QAAQ;AACtB,WAAO,OAAO,OAAO,cAChB,OAAO,OAAO,YAAY,IAAI,WAAU,QAAQ,WAAU,YAAY,MAAM,CAAC;AAAA,EACtF;AACJ;AACA,IAAM,YAAY;AAAA,EACd,SAAS;AAAA,EAAM,SAAS;AAAA,EAAM,OAAO;AAAA,EAAM,YAAY;AAAA,EAAM,QAAQ;AAAA,EACrE,IAAI;AAAA,EAAM,KAAK;AAAA,EAAM,IAAI;AAAA,EAAM,UAAU;AAAA,EAAM,YAAY;AAAA,EAAM,QAAQ;AAAA,EACzE,QAAQ;AAAA,EAAM,MAAM;AAAA,EAAM,IAAI;AAAA,EAAM,IAAI;AAAA,EAAM,IAAI;AAAA,EAAM,IAAI;AAAA,EAAM,IAAI;AAAA,EACtE,IAAI;AAAA,EAAM,QAAQ;AAAA,EAAM,QAAQ;AAAA,EAAM,IAAI;AAAA,EAAM,IAAI;AAAA,EAAM,UAAU;AAAA,EAAM,IAAI;AAAA,EAC9E,QAAQ;AAAA,EAAM,GAAG;AAAA,EAAM,KAAK;AAAA,EAAM,SAAS;AAAA,EAAM,OAAO;AAAA,EAAM,OAAO;AAAA,EAAM,IAAI;AACnF;AACA,IAAM,aAAa;AAAA,EACf,MAAM;AAAA,EAAM,UAAU;AAAA,EAAM,QAAQ;AAAA,EAAM,QAAQ;AAAA,EAAM,OAAO;AAAA,EAAM,OAAO;AAChF;AACA,IAAM,WAAW,EAAE,IAAI,MAAM,IAAI,KAAK;AAEtC,IAAM,kBAAkB;AAAxB,IAA2B,uBAAuB;AAAlD,IAAqD,gBAAgB;AACrE,SAAS,aAAa,MAAM,oBAAoB,MAAM;AAClD,MAAI,sBAAsB;AACtB,YAAQ,qBAAqB,kBAAkB,MAC1C,uBAAuB,SAAS,uBAAuB;AAChE,SAAO,QAAQ,KAAK,cAAc,QAAQ,kBAAkB,uBAAuB,OAAO,CAAC;AAC/F;AACA,IAAM,cAAN,MAAkB;AAAA,EACd,YAAY,MAAM,OAAO,OAAO,OAAO,OAAO,SAAS;AACnD,SAAK,OAAO;AACZ,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,UAAU;AACf,SAAK,UAAU,CAAC;AAEhB,SAAK,cAAc,KAAK;AACxB,SAAK,QAAQ,UAAU,UAAU,gBAAgB,OAAO,KAAK;AAAA,EACjE;AAAA,EACA,aAAa,MAAM;AACf,QAAI,CAAC,KAAK,OAAO;AACb,UAAI,CAAC,KAAK;AACN,eAAO,CAAC;AACZ,UAAI,OAAO,KAAK,KAAK,aAAa,WAAW,SAAS,KAAK,IAAI,CAAC;AAChE,UAAI,MAAM;AACN,aAAK,QAAQ,KAAK,KAAK,aAAa,cAAc,IAAI;AAAA,MAC1D,OACK;AACD,YAAI,QAAQ,KAAK,KAAK,cAAc;AACpC,YAAI,OAAO,MAAM,aAAa,KAAK,IAAI,GAAG;AACtC,eAAK,QAAQ;AACb,iBAAO;AAAA,QACX,OACK;AACD,iBAAO;AAAA,QACX;AAAA,MACJ;AAAA,IACJ;AACA,WAAO,KAAK,MAAM,aAAa,KAAK,IAAI;AAAA,EAC5C;AAAA,EACA,OAAO,SAAS;AACZ,QAAI,EAAE,KAAK,UAAU,kBAAkB;AACnC,UAAI,OAAO,KAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,GAAG;AAClD,UAAI,QAAQ,KAAK,WAAW,IAAI,oBAAoB,KAAK,KAAK,IAAI,IAAI;AAClE,YAAI,OAAO;AACX,YAAI,KAAK,KAAK,UAAU,EAAE,CAAC,EAAE;AACzB,eAAK,QAAQ,IAAI;AAAA;AAEjB,eAAK,QAAQ,KAAK,QAAQ,SAAS,CAAC,IAAI,KAAK,SAAS,KAAK,KAAK,MAAM,GAAG,KAAK,KAAK,SAAS,EAAE,CAAC,EAAE,MAAM,CAAC;AAAA,MAChH;AAAA,IACJ;AACA,QAAI,UAAU,SAAS,KAAK,KAAK,OAAO;AACxC,QAAI,CAAC,WAAW,KAAK;AACjB,gBAAU,QAAQ,OAAO,KAAK,MAAM,WAAW,SAAS,OAAO,IAAI,CAAC;AACxE,WAAO,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,OAAO,SAAS,KAAK,KAAK,IAAI;AAAA,EAC3E;AAAA,EACA,cAAc,MAAM;AAChB,QAAI,KAAK;AACL,aAAO,KAAK,KAAK;AACrB,QAAI,KAAK,QAAQ;AACb,aAAO,KAAK,QAAQ,CAAC,EAAE;AAC3B,WAAO,KAAK,cAAc,CAAC,UAAU,eAAe,KAAK,WAAW,SAAS,YAAY,CAAC;AAAA,EAC9F;AACJ;AACA,IAAM,eAAN,MAAmB;AAAA,EACf,YAEA,QAEA,SAAS,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,kBAAkB;AACvB,QAAI,UAAU,QAAQ,SAAS;AAC/B,QAAI,aAAa,aAAa,MAAM,QAAQ,oBAAoB,CAAC,KAAK,SAAS,gBAAgB;AAC/F,QAAI;AACA,mBAAa,IAAI,YAAY,QAAQ,MAAM,QAAQ,OAAO,KAAK,MAAM,MAAM,QAAQ,YAAY,QAAQ,KAAK,cAAc,UAAU;AAAA,aAC/H;AACL,mBAAa,IAAI,YAAY,MAAM,MAAM,KAAK,MAAM,MAAM,MAAM,UAAU;AAAA;AAE1E,mBAAa,IAAI,YAAY,OAAO,OAAO,aAAa,MAAM,KAAK,MAAM,MAAM,MAAM,UAAU;AACnG,SAAK,QAAQ,CAAC,UAAU;AACxB,SAAK,OAAO,QAAQ;AACpB,SAAK,aAAa;AAAA,EACtB;AAAA,EACA,IAAI,MAAM;AACN,WAAO,KAAK,MAAM,KAAK,IAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,KAAK,OAAO;AACf,QAAI,IAAI,YAAY;AAChB,WAAK,YAAY,KAAK,KAAK;AAAA,aACtB,IAAI,YAAY;AACrB,WAAK,WAAW,KAAK,KAAK;AAAA,EAClC;AAAA,EACA,YAAY,KAAK,OAAO;AACpB,QAAI,QAAQ,IAAI;AAChB,QAAI,MAAM,KAAK,KAAK,aAAc,IAAI,UAAU,uBAAwB,SAClE,KAAK,oBAAoB,IAAI,UAAU,mBAAmB;AAChE,QAAI,eAAe,UACf,IAAI,cAAc,GAAG,KACrB,mBAAmB,KAAK,KAAK,GAAG;AAChC,UAAI,CAAC,YAAY;AACb,gBAAQ,MAAM,QAAQ,qBAAqB,GAAG;AAI9C,YAAI,mBAAmB,KAAK,KAAK,KAAK,KAAK,QAAQ,KAAK,MAAM,SAAS,GAAG;AACtE,cAAI,aAAa,IAAI,QAAQ,IAAI,QAAQ,SAAS,CAAC;AACnD,cAAI,gBAAgB,IAAI;AACxB,cAAI,CAAC,cACA,iBAAiB,cAAc,YAAY,QAC3C,WAAW,UAAU,mBAAmB,KAAK,WAAW,IAAI;AAC7D,oBAAQ,MAAM,MAAM,CAAC;AAAA,QAC7B;AAAA,MACJ,WACS,eAAe,QAAQ;AAC5B,gBAAQ,MAAM,QAAQ,aAAa,GAAG;AAAA,MAC1C,OACK;AACD,gBAAQ,MAAM,QAAQ,UAAU,IAAI;AAAA,MACxC;AACA,UAAI;AACA,aAAK,WAAW,KAAK,OAAO,OAAO,KAAK,KAAK,GAAG,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC;AAC5E,WAAK,WAAW,GAAG;AAAA,IACvB,OACK;AACD,WAAK,WAAW,GAAG;AAAA,IACvB;AAAA,EACJ;AAAA;AAAA;AAAA,EAGA,WAAW,KAAK,OAAO,YAAY;AAC/B,QAAI,UAAU,KAAK,iBAAiB,MAAM,KAAK;AAC/C,QAAI,IAAI,WAAW,SAAS,MAAM,KAAK,IAAI,SAAS,IAAI,MAAM,UAAU;AACpE,WAAK,kBAAkB;AAC3B,QAAI,OAAO,IAAI,SAAS,YAAY,GAAG;AACvC,QAAI,SAAS,eAAe,IAAI,KAAK,KAAK,OAAO;AAC7C,oBAAc,GAAG;AACrB,QAAI,OAAQ,KAAK,QAAQ,gBAAgB,KAAK,QAAQ,aAAa,GAAG,MACjE,SAAS,KAAK,OAAO,SAAS,KAAK,MAAM,UAAU;AACxD,QAAK,KAAI,OAAO,KAAK,SAAS,WAAW,eAAe,IAAI,GAAG;AAC3D,WAAK,WAAW,GAAG;AACnB,WAAK,eAAe,KAAK,KAAK;AAAA,IAClC,WACS,CAAC,QAAQ,KAAK,QAAQ,KAAK,aAAa;AAC7C,UAAI,QAAQ,KAAK;AACb,aAAK,OAAO,KAAK,IAAI,GAAG,KAAK,OAAO,CAAC;AAAA,eAChC,QAAQ,KAAK,KAAK;AACvB,cAAM,KAAK;AACf,UAAI,MAAM,gBAAgB,KAAK;AAC/B,UAAI,UAAU,eAAe,IAAI,GAAG;AAChC,YAAI,IAAI,QAAQ,UAAU,IAAI,QAAQ,CAAC,EAAE,YAAY,KAAK,MAAM;AAC5D,eAAK;AACL,gBAAM,KAAK;AAAA,QACf;AACA,eAAO;AACP,YAAI,CAAC,IAAI;AACL,eAAK,aAAa;AAAA,MAC1B,WACS,CAAC,IAAI,YAAY;AACtB,aAAK,aAAa,KAAK,KAAK;AAC5B,cAAM;AAAA,MACV;AACA,UAAI,aAAa,QAAQ,KAAK,OAAO,QAAQ,KAAK,WAAW,KAAK,KAAK;AACvE,UAAI;AACA,aAAK,OAAO,KAAK,UAAU;AAC/B,UAAI;AACA,aAAK,KAAK,GAAG;AACjB,WAAK,aAAa;AAAA,IACtB,OACK;AACD,UAAI,aAAa,KAAK,WAAW,KAAK,KAAK;AAC3C,UAAI;AACA,aAAK,iBAAiB,KAAK,MAAM,YAAY,KAAK,cAAc,QAAQ,SAAS,MAAS;AAAA,IAClG;AACA,SAAK,kBAAkB;AAAA,EAC3B;AAAA;AAAA,EAEA,aAAa,KAAK,OAAO;AACrB,QAAI,IAAI,YAAY,QAAQ,KAAK,IAAI,QAAQ,KAAK,IAAI,KAAK;AACvD,WAAK,YAAY,IAAI,cAAc,eAAe,IAAI,GAAG,KAAK;AAAA,EACtE;AAAA;AAAA,EAEA,eAAe,KAAK,OAAO;AAEvB,QAAI,IAAI,YAAY,SAAS,CAAC,KAAK,IAAI,QAAQ,CAAC,KAAK,IAAI,KAAK;AAC1D,WAAK,UAAU,KAAK,OAAO,OAAO,KAAK,GAAG,GAAG,OAAO,IAAI;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,KAAK,OAAO;AACnB,QAAI,SAAS,IAAI;AAMjB,QAAI,UAAU,OAAO;AACjB,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,cAAc,QAAQ,KAAK;AACvD,YAAI,OAAO,KAAK,OAAO,cAAc,CAAC,GAAG,QAAQ,OAAO,iBAAiB,IAAI;AAC7E,YAAI;AACA,mBAAS,QAAQ,YAAa;AAC1B,gBAAI,OAAO,KAAK,OAAO,WAAW,MAAM,OAAO,MAAM,KAAK;AAC1D,gBAAI,CAAC;AACD;AACJ,gBAAI,KAAK;AACL,qBAAO;AACX,gBAAI,KAAK;AACL,sBAAQ,MAAM,OAAO,OAAK,CAAC,KAAK,UAAU,CAAC,CAAC;AAAA;AAE5C,sBAAQ,MAAM,OAAO,KAAK,OAAO,OAAO,MAAM,KAAK,IAAI,EAAE,OAAO,KAAK,KAAK,CAAC;AAC/E,gBAAI,KAAK,cAAc;AACnB,sBAAQ;AAAA;AAER;AAAA,UACR;AAAA,MACR;AACJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,KAAK,MAAM,OAAO,eAAe;AAC9C,QAAI,MAAM;AACV,QAAI,KAAK,MAAM;AACX,iBAAW,KAAK,OAAO,OAAO,MAAM,KAAK,IAAI;AAC7C,UAAI,CAAC,SAAS,QAAQ;AAClB,YAAI,QAAQ,KAAK,MAAM,UAAU,KAAK,SAAS,MAAM,OAAO,KAAK,kBAAkB;AACnF,YAAI,OAAO;AACP,iBAAO;AACP,kBAAQ;AAAA,QACZ;AAAA,MACJ,WACS,CAAC,KAAK,WAAW,SAAS,OAAO,KAAK,KAAK,GAAG,OAAO,IAAI,YAAY,IAAI,GAAG;AACjF,aAAK,aAAa,KAAK,KAAK;AAAA,MAChC;AAAA,IACJ,OACK;AACD,UAAI,WAAW,KAAK,OAAO,OAAO,MAAM,KAAK,IAAI;AACjD,cAAQ,MAAM,OAAO,SAAS,OAAO,KAAK,KAAK,CAAC;AAAA,IACpD;AACA,QAAI,UAAU,KAAK;AACnB,QAAI,YAAY,SAAS,QAAQ;AAC7B,WAAK,WAAW,GAAG;AAAA,IACvB,WACS,eAAe;AACpB,WAAK,WAAW,KAAK,OAAO,aAAa;AAAA,IAC7C,WACS,KAAK,YAAY;AACtB,WAAK,WAAW,GAAG;AACnB,WAAK,WAAW,KAAK,KAAK,OAAO,MAAM,EAAE,QAAQ,UAAQ,KAAK,WAAW,MAAM,OAAO,KAAK,CAAC;AAAA,IAChG,OACK;AACD,UAAI,aAAa;AACjB,UAAI,OAAO,KAAK,kBAAkB;AAC9B,qBAAa,IAAI,cAAc,KAAK,cAAc;AAAA,eAC7C,OAAO,KAAK,kBAAkB;AACnC,qBAAa,KAAK,eAAe,GAAG;AAAA,eAC/B,KAAK;AACV,qBAAa,KAAK;AACtB,WAAK,WAAW,KAAK,YAAY,IAAI;AACrC,WAAK,OAAO,YAAY,KAAK;AAC7B,WAAK,WAAW,KAAK,YAAY,KAAK;AAAA,IAC1C;AACA,QAAI,QAAQ,KAAK,KAAK,OAAO;AACzB,WAAK;AAAA,EACb;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,QAAQ,OAAO,YAAY,UAAU;AACxC,QAAI,QAAQ,cAAc;AAC1B,aAAS,MAAM,aAAa,OAAO,WAAW,UAAU,IAAI,OAAO,YAAY,MAAM,YAAY,OAAO,OAAO,OAAO,WAAW,QAAQ,GAAG,OAAO,KAAK,MAAM,IAAI,aAAa,EAAE,OAAO;AACpL,WAAK,YAAY,QAAQ,KAAK;AAC9B,WAAK,OAAO,KAAK,KAAK;AAAA,IAC1B;AACA,SAAK,YAAY,QAAQ,KAAK;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,MAAM,OAAO,UAAU;AAC7B,QAAI,OAAO;AACX,aAAS,QAAQ,KAAK,MAAM,UAAU,GAAG,SAAS,GAAG,SAAS;AAC1D,UAAI,KAAK,KAAK,MAAM,KAAK;AACzB,UAAIA,SAAQ,GAAG,aAAa,IAAI;AAChC,UAAIA,WAAU,CAAC,SAAS,MAAM,SAASA,OAAM,SAAS,UAAU;AAC5D,gBAAQA;AACR,eAAO;AACP,YAAI,CAACA,OAAM;AACP;AAAA,MACR;AACA,UAAI,GAAG,OAAO;AACV,YAAI;AACA;AACJ,mBAAW;AAAA,MACf;AAAA,IACJ;AACA,QAAI,CAAC;AACD,aAAO;AACX,SAAK,KAAK,IAAI;AACd,aAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,cAAQ,KAAK,WAAW,MAAM,CAAC,GAAG,MAAM,OAAO,KAAK;AACxD,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,WAAW,MAAM,OAAO,UAAU;AAC9B,QAAI,KAAK,YAAY,KAAK,cAAc,CAAC,KAAK,IAAI,MAAM;AACpD,UAAI,QAAQ,KAAK,qBAAqB;AACtC,UAAI;AACA,gBAAQ,KAAK,WAAW,OAAO,MAAM,KAAK;AAAA,IAClD;AACA,QAAI,aAAa,KAAK,UAAU,MAAM,OAAO,QAAQ;AACrD,QAAI,YAAY;AACZ,WAAK,WAAW;AAChB,UAAI,MAAM,KAAK;AACf,UAAI,IAAI;AACJ,YAAI,QAAQ,IAAI,MAAM,UAAU,KAAK,IAAI;AAC7C,UAAI,YAAY,KAAK;AACrB,eAAS,KAAK,WAAW,OAAO,KAAK,KAAK;AACtC,YAAI,IAAI,OAAO,IAAI,KAAK,eAAe,EAAE,IAAI,IAAI,aAAa,EAAE,MAAM,KAAK,IAAI;AAC3E,sBAAY,EAAE,SAAS,SAAS;AACxC,UAAI,QAAQ,KAAK,KAAK,KAAK,SAAS,CAAC;AACrC,aAAO;AAAA,IACX;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,MAAM,MAAM,OAAO,OAAO,YAAY;AAClC,QAAI,aAAa,KAAK,UAAU,KAAK,OAAO,KAAK,GAAG,OAAO,KAAK;AAChE,QAAI;AACA,mBAAa,KAAK,WAAW,MAAM,OAAO,OAAO,MAAM,UAAU;AACrE,WAAO;AAAA,EACX;AAAA;AAAA,EAEA,WAAW,MAAM,OAAO,OAAO,QAAQ,OAAO,YAAY;AACtD,SAAK,WAAW;AAChB,QAAI,MAAM,KAAK;AACf,QAAI,QAAQ,IAAI,SAAS,IAAI,MAAM,UAAU,IAAI;AACjD,QAAI,UAAU,aAAa,MAAM,YAAY,IAAI,OAAO;AACxD,QAAK,IAAI,UAAU,iBAAkB,IAAI,QAAQ,UAAU;AACvD,iBAAW;AACf,QAAI,aAAa,KAAK;AACtB,YAAQ,MAAM,OAAO,OAAK;AACtB,UAAI,IAAI,OAAO,IAAI,KAAK,eAAe,EAAE,IAAI,IAAI,aAAa,EAAE,MAAM,IAAI,GAAG;AACzE,qBAAa,EAAE,SAAS,UAAU;AAClC,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX,CAAC;AACD,SAAK,MAAM,KAAK,IAAI,YAAY,MAAM,OAAO,YAAY,OAAO,MAAM,OAAO,CAAC;AAC9E,SAAK;AACL,WAAO;AAAA,EACX;AAAA;AAAA;AAAA,EAGA,WAAW,UAAU,OAAO;AACxB,QAAI,IAAI,KAAK,MAAM,SAAS;AAC5B,QAAI,IAAI,KAAK,MAAM;AACf,aAAO,IAAI,KAAK,MAAM;AAClB,aAAK,MAAM,IAAI,CAAC,EAAE,QAAQ,KAAK,KAAK,MAAM,CAAC,EAAE,OAAO,OAAO,CAAC;AAChE,WAAK,MAAM,SAAS,KAAK,OAAO;AAAA,IACpC;AAAA,EACJ;AAAA,EACA,SAAS;AACL,SAAK,OAAO;AACZ,SAAK,WAAW,KAAK,MAAM;AAC3B,WAAO,KAAK,MAAM,CAAC,EAAE,OAAO,CAAC,EAAE,KAAK,UAAU,KAAK,QAAQ,QAAQ;AAAA,EACvE;AAAA,EACA,KAAK,IAAI;AACL,aAAS,IAAI,KAAK,MAAM,KAAK,GAAG,KAAK;AACjC,UAAI,KAAK,MAAM,CAAC,KAAK,IAAI;AACrB,aAAK,OAAO;AACZ,eAAO;AAAA,MACX,WACS,KAAK,iBAAiB;AAC3B,aAAK,MAAM,CAAC,EAAE,WAAW;AAAA,MAC7B;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA,EACA,IAAI,aAAa;AACb,SAAK,WAAW;AAChB,QAAI,MAAM;AACV,aAAS,IAAI,KAAK,MAAM,KAAK,GAAG,KAAK;AACjC,UAAI,UAAU,KAAK,MAAM,CAAC,EAAE;AAC5B,eAAS,IAAI,QAAQ,SAAS,GAAG,KAAK,GAAG;AACrC,eAAO,QAAQ,CAAC,EAAE;AACtB,UAAI;AACA;AAAA,IACR;AACA,WAAO;AAAA,EACX;AAAA,EACA,YAAY,QAAQ,QAAQ;AACxB,QAAI,KAAK;AACL,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,YAAI,KAAK,KAAK,CAAC,EAAE,QAAQ,UAAU,KAAK,KAAK,CAAC,EAAE,UAAU;AACtD,eAAK,KAAK,CAAC,EAAE,MAAM,KAAK;AAAA,MAChC;AAAA,EACR;AAAA,EACA,WAAW,QAAQ;AACf,QAAI,KAAK;AACL,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,YAAI,KAAK,KAAK,CAAC,EAAE,OAAO,QAAQ,OAAO,YAAY,KAAK,OAAO,SAAS,KAAK,KAAK,CAAC,EAAE,IAAI;AACrF,eAAK,KAAK,CAAC,EAAE,MAAM,KAAK;AAAA,MAChC;AAAA,EACR;AAAA,EACA,WAAW,QAAQ,SAAS,QAAQ;AAChC,QAAI,UAAU,WAAW,KAAK;AAC1B,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,YAAI,KAAK,KAAK,CAAC,EAAE,OAAO,QAAQ,OAAO,YAAY,KAAK,OAAO,SAAS,KAAK,KAAK,CAAC,EAAE,IAAI,GAAG;AACxF,cAAI,MAAM,QAAQ,wBAAwB,KAAK,KAAK,CAAC,EAAE,IAAI;AAC3D,cAAI,OAAO,SAAS,IAAI;AACpB,iBAAK,KAAK,CAAC,EAAE,MAAM,KAAK;AAAA,QAChC;AAAA,MACJ;AAAA,EACR;AAAA,EACA,WAAW,UAAU;AACjB,QAAI,KAAK;AACL,eAAS,IAAI,GAAG,IAAI,KAAK,KAAK,QAAQ,KAAK;AACvC,YAAI,KAAK,KAAK,CAAC,EAAE,QAAQ;AACrB,eAAK,KAAK,CAAC,EAAE,MAAM,KAAK,cAAc,SAAS,UAAU,SAAS,KAAK,KAAK,CAAC,EAAE;AAAA,MACvF;AAAA,EACR;AAAA;AAAA,EAEA,eAAe,SAAS;AACpB,QAAI,QAAQ,QAAQ,GAAG,IAAI;AACvB,aAAO,QAAQ,MAAM,UAAU,EAAE,KAAK,KAAK,gBAAgB,IAAI;AACnE,QAAI,QAAQ,QAAQ,MAAM,GAAG;AAC7B,QAAI,SAAS,KAAK,QAAQ;AAC1B,QAAI,UAAU,CAAC,KAAK,WAAW,CAAC,UAAU,OAAO,OAAO,QAAQ,KAAK,MAAM,CAAC,EAAE;AAC9E,QAAI,WAAW,EAAE,SAAS,OAAO,QAAQ,IAAI,MAAM,UAAU,IAAI;AACjE,QAAI,QAAQ,CAAC,GAAG,UAAU;AACtB,aAAO,KAAK,GAAG,KAAK;AAChB,YAAI,OAAO,MAAM,CAAC;AAClB,YAAI,QAAQ,IAAI;AACZ,cAAI,KAAK,MAAM,SAAS,KAAK,KAAK;AAC9B;AACJ,iBAAO,SAAS,UAAU;AACtB,gBAAI,MAAM,IAAI,GAAG,KAAK;AAClB,qBAAO;AACf,iBAAO;AAAA,QACX,OACK;AACD,cAAI,OAAO,QAAQ,KAAM,SAAS,KAAK,UAAW,KAAK,MAAM,KAAK,EAAE,OAC9D,UAAU,SAAS,WAAW,OAAO,KAAK,QAAQ,QAAQ,EAAE,OACxD;AACV,cAAI,CAAC,QAAS,KAAK,QAAQ,QAAQ,CAAC,KAAK,UAAU,IAAI;AACnD,mBAAO;AACX;AAAA,QACJ;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,WAAO,MAAM,MAAM,SAAS,GAAG,KAAK,IAAI;AAAA,EAC5C;AAAA,EACA,uBAAuB;AACnB,QAAI,WAAW,KAAK,QAAQ;AAC5B,QAAI;AACA,eAAS,IAAI,SAAS,OAAO,KAAK,GAAG,KAAK;AACtC,YAAI,QAAQ,SAAS,KAAK,CAAC,EAAE,eAAe,SAAS,WAAW,CAAC,CAAC,EAAE;AACpE,YAAI,SAAS,MAAM,eAAe,MAAM;AACpC,iBAAO;AAAA,MACf;AACJ,aAAS,QAAQ,KAAK,OAAO,OAAO,OAAO;AACvC,UAAI,OAAO,KAAK,OAAO,OAAO,MAAM,IAAI;AACxC,UAAI,KAAK,eAAe,KAAK;AACzB,eAAO;AAAA,IACf;AAAA,EACJ;AACJ;AAIA,SAAS,cAAc,KAAK;AACxB,WAAS,QAAQ,IAAI,YAAY,WAAW,MAAM,OAAO,QAAQ,MAAM,aAAa;AAChF,QAAI,OAAO,MAAM,YAAY,IAAI,MAAM,SAAS,YAAY,IAAI;AAChE,QAAI,QAAQ,SAAS,eAAe,IAAI,KAAK,UAAU;AACnD,eAAS,YAAY,KAAK;AAC1B,cAAQ;AAAA,IACZ,WACS,QAAQ,MAAM;AACnB,iBAAW;AAAA,IACf,WACS,MAAM;AACX,iBAAW;AAAA,IACf;AAAA,EACJ;AACJ;AAEA,SAAS,QAAQ,KAAK,UAAU;AAC5B,UAAQ,IAAI,WAAW,IAAI,qBAAqB,IAAI,yBAAyB,IAAI,oBAAoB,KAAK,KAAK,QAAQ;AAC3H;AACA,SAAS,KAAK,KAAK;AACf,MAAID,QAAO,CAAC;AACZ,WAAS,QAAQ;AACb,IAAAA,MAAK,IAAI,IAAI,IAAI,IAAI;AACzB,SAAOA;AACX;AAIA,SAAS,aAAa,UAAU,UAAU;AACtC,MAAI,QAAQ,SAAS,OAAO;AAC5B,WAAS,QAAQ,OAAO;AACpB,QAAI,SAAS,MAAM,IAAI;AACvB,QAAI,CAAC,OAAO,eAAe,QAAQ;AAC/B;AACJ,QAAI,OAAO,CAAC,GAAG,OAAO,CAAC,UAAU;AAC7B,WAAK,KAAK,KAAK;AACf,eAAS,IAAI,GAAG,IAAI,MAAM,WAAW,KAAK;AACtC,YAAI,EAAE,MAAM,KAAK,IAAI,MAAM,KAAK,CAAC;AACjC,YAAI,QAAQ;AACR,iBAAO;AACX,YAAI,KAAK,QAAQ,IAAI,IAAI,KAAK,KAAK,IAAI;AACnC,iBAAO;AAAA,MACf;AAAA,IACJ;AACA,QAAI,KAAK,OAAO,YAAY;AACxB,aAAO;AAAA,EACf;AACJ;AAMA,IAAM,gBAAN,MAAM,eAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUhB,YAIA,OAIA,OAAO;AACH,SAAK,QAAQ;AACb,SAAK,QAAQ;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,UAAU,UAAU,CAAC,GAAG,QAAQ;AAC9C,QAAI,CAAC;AACD,eAAS,IAAI,OAAO,EAAE,uBAAuB;AACjD,QAAI,MAAM,QAAQ,SAAS,CAAC;AAC5B,aAAS,QAAQ,UAAQ;AACrB,UAAI,OAAO,UAAU,KAAK,MAAM,QAAQ;AACpC,YAAI,OAAO,GAAG,WAAW;AACzB,eAAO,OAAO,OAAO,UAAU,WAAW,KAAK,MAAM,QAAQ;AACzD,cAAI,OAAO,KAAK,MAAM,QAAQ;AAC9B,cAAI,CAAC,KAAK,MAAM,KAAK,KAAK,IAAI,GAAG;AAC7B;AACA;AAAA,UACJ;AACA,cAAI,CAAC,KAAK,GAAG,OAAO,IAAI,EAAE,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK,aAAa;AACzD;AACJ;AACA;AAAA,QACJ;AACA,eAAO,OAAO,OAAO;AACjB,gBAAM,OAAO,IAAI,EAAE,CAAC;AACxB,eAAO,WAAW,KAAK,MAAM,QAAQ;AACjC,cAAI,MAAM,KAAK,MAAM,UAAU;AAC/B,cAAI,UAAU,KAAK,cAAc,KAAK,KAAK,UAAU,OAAO;AAC5D,cAAI,SAAS;AACT,mBAAO,KAAK,CAAC,KAAK,GAAG,CAAC;AACtB,gBAAI,YAAY,QAAQ,GAAG;AAC3B,kBAAM,QAAQ,cAAc,QAAQ;AAAA,UACxC;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,YAAY,KAAK,mBAAmB,MAAM,OAAO,CAAC;AAAA,IAC1D,CAAC;AACD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,mBAAmB,MAAM,SAAS;AAC9B,QAAI,EAAE,KAAK,WAAW,IAAI,WAAW,IAAI,OAAO,GAAG,KAAK,MAAM,KAAK,KAAK,IAAI,EAAE,IAAI,GAAG,MAAM,KAAK,KAAK;AACrG,QAAI,YAAY;AACZ,UAAI,KAAK;AACL,cAAM,IAAI,WAAW,8CAA8C;AACvE,WAAK,kBAAkB,KAAK,SAAS,SAAS,UAAU;AAAA,IAC5D;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,MAAM,UAAU,CAAC,GAAG;AAC9B,QAAI,MAAM,KAAK,mBAAmB,MAAM,OAAO;AAC/C,aAAS,IAAI,KAAK,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK;AAC7C,UAAI,OAAO,KAAK,cAAc,KAAK,MAAM,CAAC,GAAG,KAAK,UAAU,OAAO;AACnE,UAAI,MAAM;AACN,SAAC,KAAK,cAAc,KAAK,KAAK,YAAY,GAAG;AAC7C,cAAM,KAAK;AAAA,MACf;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,MAAM,QAAQ,UAAU,CAAC,GAAG;AACtC,QAAI,QAAQ,KAAK,MAAM,KAAK,KAAK,IAAI;AACrC,WAAO,SAAS,WAAW,IAAI,OAAO,GAAG,MAAM,MAAM,MAAM,GAAG,MAAM,KAAK,KAAK;AAAA,EAClF;AAAA,EACA,OAAO,WAAWE,MAAK,WAAW,QAAQ,MAAM,eAAe;AAC3D,WAAO,WAAWA,MAAK,WAAW,OAAO,aAAa;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,WAAW,QAAQ;AACtB,WAAO,OAAO,OAAO,kBAChB,OAAO,OAAO,gBAAgB,IAAI,eAAc,KAAK,gBAAgB,MAAM,GAAG,KAAK,gBAAgB,MAAM,CAAC;AAAA,EACnH;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,gBAAgB,QAAQ;AAC3B,QAAI,SAAS,YAAY,OAAO,KAAK;AACrC,QAAI,CAAC,OAAO;AACR,aAAO,OAAO,UAAQ,KAAK;AAC/B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,gBAAgB,QAAQ;AAC3B,WAAO,YAAY,OAAO,KAAK;AAAA,EACnC;AACJ;AACA,SAAS,YAAY,KAAK;AACtB,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ,KAAK;AAClB,QAAI,QAAQ,IAAI,IAAI,EAAE,KAAK;AAC3B,QAAI;AACA,aAAO,IAAI,IAAI;AAAA,EACvB;AACA,SAAO;AACX;AACA,SAAS,IAAI,SAAS;AAClB,SAAO,QAAQ,YAAY,OAAO;AACtC;AACA,IAAM,2BAA2B,oBAAI,QAAQ;AAC7C,SAAS,qBAAqB,OAAO;AACjC,MAAI,QAAQ,yBAAyB,IAAI,KAAK;AAC9C,MAAI,UAAU;AACV,6BAAyB,IAAI,OAAO,QAAQ,0BAA0B,KAAK,CAAC;AAChF,SAAO;AACX;AACA,SAAS,0BAA0B,OAAO;AACtC,MAAI,SAAS;AACb,WAAS,KAAK,OAAO;AACjB,QAAI,SAAS,OAAO,SAAS,UAAU;AACnC,UAAI,MAAM,QAAQ,KAAK,GAAG;AACtB,YAAI,OAAO,MAAM,CAAC,KAAK,UAAU;AAC7B,cAAI,CAAC;AACD,qBAAS,CAAC;AACd,iBAAO,KAAK,KAAK;AAAA,QACrB,OACK;AACD,mBAAS,IAAI,GAAG,IAAI,MAAM,QAAQ;AAC9B,iBAAK,MAAM,CAAC,CAAC;AAAA,QACrB;AAAA,MACJ,OACK;AACD,iBAAS,QAAQ;AACb,eAAK,MAAM,IAAI,CAAC;AAAA,MACxB;AAAA,IACJ;AAAA,EACJ;AACA,OAAK,KAAK;AACV,SAAO;AACX;AACA,SAAS,WAAWA,MAAK,WAAW,OAAO,eAAe;AACtD,MAAI,OAAO,aAAa;AACpB,WAAO,EAAE,KAAKA,KAAI,eAAe,SAAS,EAAE;AAChD,MAAI,UAAU,YAAY;AACtB,WAAO,EAAE,KAAK,UAAU;AAC5B,MAAI,UAAU,OAAO,UAAU,IAAI,YAAY;AAC3C,WAAO;AACX,MAAI,UAAU,UAAU,CAAC,GAAG;AAC5B,MAAI,OAAO,WAAW;AAClB,UAAM,IAAI,WAAW,oCAAoC;AAC7D,MAAI,kBAAkB,aAAa,qBAAqB,aAAa,MACjE,WAAW,QAAQ,SAAS,IAAI;AAChC,UAAM,IAAI,WAAW,8GAA8G;AACvI,MAAI,QAAQ,QAAQ,QAAQ,GAAG;AAC/B,MAAI,QAAQ,GAAG;AACX,YAAQ,QAAQ,MAAM,GAAG,KAAK;AAC9B,cAAU,QAAQ,MAAM,QAAQ,CAAC;AAAA,EACrC;AACA,MAAI;AACJ,MAAI,MAAO,QAAQA,KAAI,gBAAgB,OAAO,OAAO,IAAIA,KAAI,cAAc,OAAO;AAClF,MAAI,QAAQ,UAAU,CAAC,GAAG,QAAQ;AAClC,MAAI,SAAS,OAAO,SAAS,YAAY,MAAM,YAAY,QAAQ,CAAC,MAAM,QAAQ,KAAK,GAAG;AACtF,YAAQ;AACR,aAAS,QAAQ;AACb,UAAI,MAAM,IAAI,KAAK,MAAM;AACrB,YAAIU,SAAQ,KAAK,QAAQ,GAAG;AAC5B,YAAIA,SAAQ;AACR,cAAI,eAAe,KAAK,MAAM,GAAGA,MAAK,GAAG,KAAK,MAAMA,SAAQ,CAAC,GAAG,MAAM,IAAI,CAAC;AAAA;AAE3E,cAAI,aAAa,MAAM,MAAM,IAAI,CAAC;AAAA,MAC1C;AAAA,EACR;AACA,WAAS,IAAI,OAAO,IAAI,UAAU,QAAQ,KAAK;AAC3C,QAAI,QAAQ,UAAU,CAAC;AACvB,QAAI,UAAU,GAAG;AACb,UAAI,IAAI,UAAU,SAAS,KAAK,IAAI;AAChC,cAAM,IAAI,WAAW,wDAAwD;AACjF,aAAO,EAAE,KAAK,YAAY,IAAI;AAAA,IAClC,OACK;AACD,UAAI,EAAE,KAAK,OAAO,YAAY,aAAa,IAAI,WAAWV,MAAK,OAAO,OAAO,aAAa;AAC1F,UAAI,YAAY,KAAK;AACrB,UAAI,cAAc;AACd,YAAI;AACA,gBAAM,IAAI,WAAW,wBAAwB;AACjD,qBAAa;AAAA,MACjB;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,EAAE,KAAK,WAAW;AAC7B;", "names": ["import_dist", "import_dist", "found", "copy", "found", "doc", "i", "type", "nfa", "edge", "expr", "node", "states", "name", "mark", "space"]}