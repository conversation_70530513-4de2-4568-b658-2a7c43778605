{"version": 3, "sources": ["../../prosemirror-transform/dist/index.js", "../../prosemirror-state/dist/index.js"], "sourcesContent": ["import { Replace<PERSON><PERSON>r, <PERSON>lice, <PERSON>ag<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'prosemirror-model';\n\n// Recovery values encode a range index and an offset. They are\n// represented as numbers, because tons of them will be created when\n// mapping, for example, a large number of decorations. The number's\n// lower 16 bits provide the index, the remaining bits the offset.\n//\n// Note: We intentionally don't use bit shift operators to en- and\n// decode these, since those clip to 32 bits, which we might in rare\n// cases want to overflow. A 64-bit float can represent 48-bit\n// integers precisely.\nconst lower16 = 0xffff;\nconst factor16 = Math.pow(2, 16);\nfunction makeRecover(index, offset) { return index + offset * factor16; }\nfunction recoverIndex(value) { return value & lower16; }\nfunction recoverOffset(value) { return (value - (value & lower16)) / factor16; }\nconst DEL_BEFORE = 1, DEL_AFTER = 2, DEL_ACROSS = 4, DEL_SIDE = 8;\n/**\nAn object representing a mapped position with extra\ninformation.\n*/\nclass MapResult {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The mapped version of the position.\n    */\n    pos, \n    /**\n    @internal\n    */\n    delInfo, \n    /**\n    @internal\n    */\n    recover) {\n        this.pos = pos;\n        this.delInfo = delInfo;\n        this.recover = recover;\n    }\n    /**\n    Tells you whether the position was deleted, that is, whether the\n    step removed the token on the side queried (via the `assoc`)\n    argument from the document.\n    */\n    get deleted() { return (this.delInfo & DEL_SIDE) > 0; }\n    /**\n    Tells you whether the token before the mapped position was deleted.\n    */\n    get deletedBefore() { return (this.delInfo & (DEL_BEFORE | DEL_ACROSS)) > 0; }\n    /**\n    True when the token after the mapped position was deleted.\n    */\n    get deletedAfter() { return (this.delInfo & (DEL_AFTER | DEL_ACROSS)) > 0; }\n    /**\n    Tells whether any of the steps mapped through deletes across the\n    position (including both the token before and after the\n    position).\n    */\n    get deletedAcross() { return (this.delInfo & DEL_ACROSS) > 0; }\n}\n/**\nA map describing the deletions and insertions made by a step, which\ncan be used to find the correspondence between positions in the\npre-step version of a document and the same position in the\npost-step version.\n*/\nclass StepMap {\n    /**\n    Create a position map. The modifications to the document are\n    represented as an array of numbers, in which each group of three\n    represents a modified chunk as `[start, oldSize, newSize]`.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    ranges, \n    /**\n    @internal\n    */\n    inverted = false) {\n        this.ranges = ranges;\n        this.inverted = inverted;\n        if (!ranges.length && StepMap.empty)\n            return StepMap.empty;\n    }\n    /**\n    @internal\n    */\n    recover(value) {\n        let diff = 0, index = recoverIndex(value);\n        if (!this.inverted)\n            for (let i = 0; i < index; i++)\n                diff += this.ranges[i * 3 + 2] - this.ranges[i * 3 + 1];\n        return this.ranges[index * 3] + diff + recoverOffset(value);\n    }\n    mapResult(pos, assoc = 1) { return this._map(pos, assoc, false); }\n    map(pos, assoc = 1) { return this._map(pos, assoc, true); }\n    /**\n    @internal\n    */\n    _map(pos, assoc, simple) {\n        let diff = 0, oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i] - (this.inverted ? diff : 0);\n            if (start > pos)\n                break;\n            let oldSize = this.ranges[i + oldIndex], newSize = this.ranges[i + newIndex], end = start + oldSize;\n            if (pos <= end) {\n                let side = !oldSize ? assoc : pos == start ? -1 : pos == end ? 1 : assoc;\n                let result = start + diff + (side < 0 ? 0 : newSize);\n                if (simple)\n                    return result;\n                let recover = pos == (assoc < 0 ? start : end) ? null : makeRecover(i / 3, pos - start);\n                let del = pos == start ? DEL_AFTER : pos == end ? DEL_BEFORE : DEL_ACROSS;\n                if (assoc < 0 ? pos != start : pos != end)\n                    del |= DEL_SIDE;\n                return new MapResult(result, del, recover);\n            }\n            diff += newSize - oldSize;\n        }\n        return simple ? pos + diff : new MapResult(pos + diff, 0, null);\n    }\n    /**\n    @internal\n    */\n    touches(pos, recover) {\n        let diff = 0, index = recoverIndex(recover);\n        let oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i] - (this.inverted ? diff : 0);\n            if (start > pos)\n                break;\n            let oldSize = this.ranges[i + oldIndex], end = start + oldSize;\n            if (pos <= end && i == index * 3)\n                return true;\n            diff += this.ranges[i + newIndex] - oldSize;\n        }\n        return false;\n    }\n    /**\n    Calls the given function on each of the changed ranges included in\n    this map.\n    */\n    forEach(f) {\n        let oldIndex = this.inverted ? 2 : 1, newIndex = this.inverted ? 1 : 2;\n        for (let i = 0, diff = 0; i < this.ranges.length; i += 3) {\n            let start = this.ranges[i], oldStart = start - (this.inverted ? diff : 0), newStart = start + (this.inverted ? 0 : diff);\n            let oldSize = this.ranges[i + oldIndex], newSize = this.ranges[i + newIndex];\n            f(oldStart, oldStart + oldSize, newStart, newStart + newSize);\n            diff += newSize - oldSize;\n        }\n    }\n    /**\n    Create an inverted version of this map. The result can be used to\n    map positions in the post-step document to the pre-step document.\n    */\n    invert() {\n        return new StepMap(this.ranges, !this.inverted);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return (this.inverted ? \"-\" : \"\") + JSON.stringify(this.ranges);\n    }\n    /**\n    Create a map that moves all positions by offset `n` (which may be\n    negative). This can be useful when applying steps meant for a\n    sub-document to a larger document, or vice-versa.\n    */\n    static offset(n) {\n        return n == 0 ? StepMap.empty : new StepMap(n < 0 ? [0, -n, 0] : [0, 0, n]);\n    }\n}\n/**\nA StepMap that contains no changed ranges.\n*/\nStepMap.empty = new StepMap([]);\n/**\nA mapping represents a pipeline of zero or more [step\nmaps](https://prosemirror.net/docs/ref/#transform.StepMap). It has special provisions for losslessly\nhandling mapping positions through a series of steps in which some\nsteps are inverted versions of earlier steps. (This comes up when\n‘[rebasing](https://prosemirror.net/docs/guide/#transform.rebasing)’ steps for\ncollaboration or history management.)\n*/\nclass Mapping {\n    /**\n    Create a new mapping with the given position maps.\n    */\n    constructor(maps, \n    /**\n    @internal\n    */\n    mirror, \n    /**\n    The starting position in the `maps` array, used when `map` or\n    `mapResult` is called.\n    */\n    from = 0, \n    /**\n    The end position in the `maps` array.\n    */\n    to = maps ? maps.length : 0) {\n        this.mirror = mirror;\n        this.from = from;\n        this.to = to;\n        this._maps = maps || [];\n        this.ownData = !(maps || mirror);\n    }\n    /**\n    The step maps in this mapping.\n    */\n    get maps() { return this._maps; }\n    /**\n    Create a mapping that maps only through a part of this one.\n    */\n    slice(from = 0, to = this.maps.length) {\n        return new Mapping(this._maps, this.mirror, from, to);\n    }\n    /**\n    Add a step map to the end of this mapping. If `mirrors` is\n    given, it should be the index of the step map that is the mirror\n    image of this one.\n    */\n    appendMap(map, mirrors) {\n        if (!this.ownData) {\n            this._maps = this._maps.slice();\n            this.mirror = this.mirror && this.mirror.slice();\n            this.ownData = true;\n        }\n        this.to = this._maps.push(map);\n        if (mirrors != null)\n            this.setMirror(this._maps.length - 1, mirrors);\n    }\n    /**\n    Add all the step maps in a given mapping to this one (preserving\n    mirroring information).\n    */\n    appendMapping(mapping) {\n        for (let i = 0, startSize = this._maps.length; i < mapping._maps.length; i++) {\n            let mirr = mapping.getMirror(i);\n            this.appendMap(mapping._maps[i], mirr != null && mirr < i ? startSize + mirr : undefined);\n        }\n    }\n    /**\n    Finds the offset of the step map that mirrors the map at the\n    given offset, in this mapping (as per the second argument to\n    `appendMap`).\n    */\n    getMirror(n) {\n        if (this.mirror)\n            for (let i = 0; i < this.mirror.length; i++)\n                if (this.mirror[i] == n)\n                    return this.mirror[i + (i % 2 ? -1 : 1)];\n    }\n    /**\n    @internal\n    */\n    setMirror(n, m) {\n        if (!this.mirror)\n            this.mirror = [];\n        this.mirror.push(n, m);\n    }\n    /**\n    Append the inverse of the given mapping to this one.\n    */\n    appendMappingInverted(mapping) {\n        for (let i = mapping.maps.length - 1, totalSize = this._maps.length + mapping._maps.length; i >= 0; i--) {\n            let mirr = mapping.getMirror(i);\n            this.appendMap(mapping._maps[i].invert(), mirr != null && mirr > i ? totalSize - mirr - 1 : undefined);\n        }\n    }\n    /**\n    Create an inverted version of this mapping.\n    */\n    invert() {\n        let inverse = new Mapping;\n        inverse.appendMappingInverted(this);\n        return inverse;\n    }\n    /**\n    Map a position through this mapping.\n    */\n    map(pos, assoc = 1) {\n        if (this.mirror)\n            return this._map(pos, assoc, true);\n        for (let i = this.from; i < this.to; i++)\n            pos = this._maps[i].map(pos, assoc);\n        return pos;\n    }\n    /**\n    Map a position through this mapping, returning a mapping\n    result.\n    */\n    mapResult(pos, assoc = 1) { return this._map(pos, assoc, false); }\n    /**\n    @internal\n    */\n    _map(pos, assoc, simple) {\n        let delInfo = 0;\n        for (let i = this.from; i < this.to; i++) {\n            let map = this._maps[i], result = map.mapResult(pos, assoc);\n            if (result.recover != null) {\n                let corr = this.getMirror(i);\n                if (corr != null && corr > i && corr < this.to) {\n                    i = corr;\n                    pos = this._maps[corr].recover(result.recover);\n                    continue;\n                }\n            }\n            delInfo |= result.delInfo;\n            pos = result.pos;\n        }\n        return simple ? pos : new MapResult(pos, delInfo, null);\n    }\n}\n\nconst stepsByID = Object.create(null);\n/**\nA step object represents an atomic change. It generally applies\nonly to the document it was created for, since the positions\nstored in it will only make sense for that document.\n\nNew steps are defined by creating classes that extend `Step`,\noverriding the `apply`, `invert`, `map`, `getMap` and `fromJSON`\nmethods, and registering your class with a unique\nJSON-serialization identifier using\n[`Step.jsonID`](https://prosemirror.net/docs/ref/#transform.Step^jsonID).\n*/\nclass Step {\n    /**\n    Get the step map that represents the changes made by this step,\n    and which can be used to transform between positions in the old\n    and the new document.\n    */\n    getMap() { return StepMap.empty; }\n    /**\n    Try to merge this step with another one, to be applied directly\n    after it. Returns the merged step when possible, null if the\n    steps can't be merged.\n    */\n    merge(other) { return null; }\n    /**\n    Deserialize a step from its JSON representation. Will call\n    through to the step class' own implementation of this method.\n    */\n    static fromJSON(schema, json) {\n        if (!json || !json.stepType)\n            throw new RangeError(\"Invalid input for Step.fromJSON\");\n        let type = stepsByID[json.stepType];\n        if (!type)\n            throw new RangeError(`No step type ${json.stepType} defined`);\n        return type.fromJSON(schema, json);\n    }\n    /**\n    To be able to serialize steps to JSON, each step needs a string\n    ID to attach to its JSON representation. Use this method to\n    register an ID for your step classes. Try to pick something\n    that's unlikely to clash with steps from other modules.\n    */\n    static jsonID(id, stepClass) {\n        if (id in stepsByID)\n            throw new RangeError(\"Duplicate use of step JSON ID \" + id);\n        stepsByID[id] = stepClass;\n        stepClass.prototype.jsonID = id;\n        return stepClass;\n    }\n}\n/**\nThe result of [applying](https://prosemirror.net/docs/ref/#transform.Step.apply) a step. Contains either a\nnew document or a failure value.\n*/\nclass StepResult {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The transformed document, if successful.\n    */\n    doc, \n    /**\n    The failure message, if unsuccessful.\n    */\n    failed) {\n        this.doc = doc;\n        this.failed = failed;\n    }\n    /**\n    Create a successful step result.\n    */\n    static ok(doc) { return new StepResult(doc, null); }\n    /**\n    Create a failed step result.\n    */\n    static fail(message) { return new StepResult(null, message); }\n    /**\n    Call [`Node.replace`](https://prosemirror.net/docs/ref/#model.Node.replace) with the given\n    arguments. Create a successful result if it succeeds, and a\n    failed one if it throws a `ReplaceError`.\n    */\n    static fromReplace(doc, from, to, slice) {\n        try {\n            return StepResult.ok(doc.replace(from, to, slice));\n        }\n        catch (e) {\n            if (e instanceof ReplaceError)\n                return StepResult.fail(e.message);\n            throw e;\n        }\n    }\n}\n\nfunction mapFragment(fragment, f, parent) {\n    let mapped = [];\n    for (let i = 0; i < fragment.childCount; i++) {\n        let child = fragment.child(i);\n        if (child.content.size)\n            child = child.copy(mapFragment(child.content, f, child));\n        if (child.isInline)\n            child = f(child, parent, i);\n        mapped.push(child);\n    }\n    return Fragment.fromArray(mapped);\n}\n/**\nAdd a mark to all inline content between two positions.\n*/\nclass AddMarkStep extends Step {\n    /**\n    Create a mark step.\n    */\n    constructor(\n    /**\n    The start of the marked range.\n    */\n    from, \n    /**\n    The end of the marked range.\n    */\n    to, \n    /**\n    The mark to add.\n    */\n    mark) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let oldSlice = doc.slice(this.from, this.to), $from = doc.resolve(this.from);\n        let parent = $from.node($from.sharedDepth(this.to));\n        let slice = new Slice(mapFragment(oldSlice.content, (node, parent) => {\n            if (!node.isAtom || !parent.type.allowsMarkType(this.mark.type))\n                return node;\n            return node.mark(this.mark.addToSet(node.marks));\n        }, parent), oldSlice.openStart, oldSlice.openEnd);\n        return StepResult.fromReplace(doc, this.from, this.to, slice);\n    }\n    invert() {\n        return new RemoveMarkStep(this.from, this.to, this.mark);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deleted && to.deleted || from.pos >= to.pos)\n            return null;\n        return new AddMarkStep(from.pos, to.pos, this.mark);\n    }\n    merge(other) {\n        if (other instanceof AddMarkStep &&\n            other.mark.eq(this.mark) &&\n            this.from <= other.to && this.to >= other.from)\n            return new AddMarkStep(Math.min(this.from, other.from), Math.max(this.to, other.to), this.mark);\n        return null;\n    }\n    toJSON() {\n        return { stepType: \"addMark\", mark: this.mark.toJSON(),\n            from: this.from, to: this.to };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for AddMarkStep.fromJSON\");\n        return new AddMarkStep(json.from, json.to, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"addMark\", AddMarkStep);\n/**\nRemove a mark from all inline content between two positions.\n*/\nclass RemoveMarkStep extends Step {\n    /**\n    Create a mark-removing step.\n    */\n    constructor(\n    /**\n    The start of the unmarked range.\n    */\n    from, \n    /**\n    The end of the unmarked range.\n    */\n    to, \n    /**\n    The mark to remove.\n    */\n    mark) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let oldSlice = doc.slice(this.from, this.to);\n        let slice = new Slice(mapFragment(oldSlice.content, node => {\n            return node.mark(this.mark.removeFromSet(node.marks));\n        }, doc), oldSlice.openStart, oldSlice.openEnd);\n        return StepResult.fromReplace(doc, this.from, this.to, slice);\n    }\n    invert() {\n        return new AddMarkStep(this.from, this.to, this.mark);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deleted && to.deleted || from.pos >= to.pos)\n            return null;\n        return new RemoveMarkStep(from.pos, to.pos, this.mark);\n    }\n    merge(other) {\n        if (other instanceof RemoveMarkStep &&\n            other.mark.eq(this.mark) &&\n            this.from <= other.to && this.to >= other.from)\n            return new RemoveMarkStep(Math.min(this.from, other.from), Math.max(this.to, other.to), this.mark);\n        return null;\n    }\n    toJSON() {\n        return { stepType: \"removeMark\", mark: this.mark.toJSON(),\n            from: this.from, to: this.to };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for RemoveMarkStep.fromJSON\");\n        return new RemoveMarkStep(json.from, json.to, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"removeMark\", RemoveMarkStep);\n/**\nAdd a mark to a specific node.\n*/\nclass AddNodeMarkStep extends Step {\n    /**\n    Create a node mark step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The mark to add.\n    */\n    mark) {\n        super();\n        this.pos = pos;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at mark step's position\");\n        let updated = node.type.create(node.attrs, null, this.mark.addToSet(node.marks));\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new Slice(Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    invert(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (node) {\n            let newSet = this.mark.addToSet(node.marks);\n            if (newSet.length == node.marks.length) {\n                for (let i = 0; i < node.marks.length; i++)\n                    if (!node.marks[i].isInSet(newSet))\n                        return new AddNodeMarkStep(this.pos, node.marks[i]);\n                return new AddNodeMarkStep(this.pos, this.mark);\n            }\n        }\n        return new RemoveNodeMarkStep(this.pos, this.mark);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new AddNodeMarkStep(pos.pos, this.mark);\n    }\n    toJSON() {\n        return { stepType: \"addNodeMark\", pos: this.pos, mark: this.mark.toJSON() };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for AddNodeMarkStep.fromJSON\");\n        return new AddNodeMarkStep(json.pos, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"addNodeMark\", AddNodeMarkStep);\n/**\nRemove a mark from a specific node.\n*/\nclass RemoveNodeMarkStep extends Step {\n    /**\n    Create a mark-removing step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The mark to remove.\n    */\n    mark) {\n        super();\n        this.pos = pos;\n        this.mark = mark;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at mark step's position\");\n        let updated = node.type.create(node.attrs, null, this.mark.removeFromSet(node.marks));\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new Slice(Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    invert(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node || !this.mark.isInSet(node.marks))\n            return this;\n        return new AddNodeMarkStep(this.pos, this.mark);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new RemoveNodeMarkStep(pos.pos, this.mark);\n    }\n    toJSON() {\n        return { stepType: \"removeNodeMark\", pos: this.pos, mark: this.mark.toJSON() };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\")\n            throw new RangeError(\"Invalid input for RemoveNodeMarkStep.fromJSON\");\n        return new RemoveNodeMarkStep(json.pos, schema.markFromJSON(json.mark));\n    }\n}\nStep.jsonID(\"removeNodeMark\", RemoveNodeMarkStep);\n\n/**\nReplace a part of the document with a slice of new content.\n*/\nclass ReplaceStep extends Step {\n    /**\n    The given `slice` should fit the 'gap' between `from` and\n    `to`—the depths must line up, and the surrounding nodes must be\n    able to be joined with the open sides of the slice. When\n    `structure` is true, the step will fail if the content between\n    from and to is not just a sequence of closing and then opening\n    tokens (this is to guard against rebased replace steps\n    overwriting something they weren't supposed to).\n    */\n    constructor(\n    /**\n    The start position of the replaced range.\n    */\n    from, \n    /**\n    The end position of the replaced range.\n    */\n    to, \n    /**\n    The slice to insert.\n    */\n    slice, \n    /**\n    @internal\n    */\n    structure = false) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.slice = slice;\n        this.structure = structure;\n    }\n    apply(doc) {\n        if (this.structure && contentBetween(doc, this.from, this.to))\n            return StepResult.fail(\"Structure replace would overwrite content\");\n        return StepResult.fromReplace(doc, this.from, this.to, this.slice);\n    }\n    getMap() {\n        return new StepMap([this.from, this.to - this.from, this.slice.size]);\n    }\n    invert(doc) {\n        return new ReplaceStep(this.from, this.from + this.slice.size, doc.slice(this.from, this.to));\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        if (from.deletedAcross && to.deletedAcross)\n            return null;\n        return new ReplaceStep(from.pos, Math.max(from.pos, to.pos), this.slice, this.structure);\n    }\n    merge(other) {\n        if (!(other instanceof ReplaceStep) || other.structure || this.structure)\n            return null;\n        if (this.from + this.slice.size == other.from && !this.slice.openEnd && !other.slice.openStart) {\n            let slice = this.slice.size + other.slice.size == 0 ? Slice.empty\n                : new Slice(this.slice.content.append(other.slice.content), this.slice.openStart, other.slice.openEnd);\n            return new ReplaceStep(this.from, this.to + (other.to - other.from), slice, this.structure);\n        }\n        else if (other.to == this.from && !this.slice.openStart && !other.slice.openEnd) {\n            let slice = this.slice.size + other.slice.size == 0 ? Slice.empty\n                : new Slice(other.slice.content.append(this.slice.content), other.slice.openStart, this.slice.openEnd);\n            return new ReplaceStep(other.from, this.to, slice, this.structure);\n        }\n        else {\n            return null;\n        }\n    }\n    toJSON() {\n        let json = { stepType: \"replace\", from: this.from, to: this.to };\n        if (this.slice.size)\n            json.slice = this.slice.toJSON();\n        if (this.structure)\n            json.structure = true;\n        return json;\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\")\n            throw new RangeError(\"Invalid input for ReplaceStep.fromJSON\");\n        return new ReplaceStep(json.from, json.to, Slice.fromJSON(schema, json.slice), !!json.structure);\n    }\n}\nStep.jsonID(\"replace\", ReplaceStep);\n/**\nReplace a part of the document with a slice of content, but\npreserve a range of the replaced content by moving it into the\nslice.\n*/\nclass ReplaceAroundStep extends Step {\n    /**\n    Create a replace-around step with the given range and gap.\n    `insert` should be the point in the slice into which the content\n    of the gap should be moved. `structure` has the same meaning as\n    it has in the [`ReplaceStep`](https://prosemirror.net/docs/ref/#transform.ReplaceStep) class.\n    */\n    constructor(\n    /**\n    The start position of the replaced range.\n    */\n    from, \n    /**\n    The end position of the replaced range.\n    */\n    to, \n    /**\n    The start of preserved range.\n    */\n    gapFrom, \n    /**\n    The end of preserved range.\n    */\n    gapTo, \n    /**\n    The slice to insert.\n    */\n    slice, \n    /**\n    The position in the slice where the preserved range should be\n    inserted.\n    */\n    insert, \n    /**\n    @internal\n    */\n    structure = false) {\n        super();\n        this.from = from;\n        this.to = to;\n        this.gapFrom = gapFrom;\n        this.gapTo = gapTo;\n        this.slice = slice;\n        this.insert = insert;\n        this.structure = structure;\n    }\n    apply(doc) {\n        if (this.structure && (contentBetween(doc, this.from, this.gapFrom) ||\n            contentBetween(doc, this.gapTo, this.to)))\n            return StepResult.fail(\"Structure gap-replace would overwrite content\");\n        let gap = doc.slice(this.gapFrom, this.gapTo);\n        if (gap.openStart || gap.openEnd)\n            return StepResult.fail(\"Gap is not a flat range\");\n        let inserted = this.slice.insertAt(this.insert, gap.content);\n        if (!inserted)\n            return StepResult.fail(\"Content does not fit in gap\");\n        return StepResult.fromReplace(doc, this.from, this.to, inserted);\n    }\n    getMap() {\n        return new StepMap([this.from, this.gapFrom - this.from, this.insert,\n            this.gapTo, this.to - this.gapTo, this.slice.size - this.insert]);\n    }\n    invert(doc) {\n        let gap = this.gapTo - this.gapFrom;\n        return new ReplaceAroundStep(this.from, this.from + this.slice.size + gap, this.from + this.insert, this.from + this.insert + gap, doc.slice(this.from, this.to).removeBetween(this.gapFrom - this.from, this.gapTo - this.from), this.gapFrom - this.from, this.structure);\n    }\n    map(mapping) {\n        let from = mapping.mapResult(this.from, 1), to = mapping.mapResult(this.to, -1);\n        let gapFrom = this.from == this.gapFrom ? from.pos : mapping.map(this.gapFrom, -1);\n        let gapTo = this.to == this.gapTo ? to.pos : mapping.map(this.gapTo, 1);\n        if ((from.deletedAcross && to.deletedAcross) || gapFrom < from.pos || gapTo > to.pos)\n            return null;\n        return new ReplaceAroundStep(from.pos, to.pos, gapFrom, gapTo, this.slice, this.insert, this.structure);\n    }\n    toJSON() {\n        let json = { stepType: \"replaceAround\", from: this.from, to: this.to,\n            gapFrom: this.gapFrom, gapTo: this.gapTo, insert: this.insert };\n        if (this.slice.size)\n            json.slice = this.slice.toJSON();\n        if (this.structure)\n            json.structure = true;\n        return json;\n    }\n    /**\n    @internal\n    */\n    static fromJSON(schema, json) {\n        if (typeof json.from != \"number\" || typeof json.to != \"number\" ||\n            typeof json.gapFrom != \"number\" || typeof json.gapTo != \"number\" || typeof json.insert != \"number\")\n            throw new RangeError(\"Invalid input for ReplaceAroundStep.fromJSON\");\n        return new ReplaceAroundStep(json.from, json.to, json.gapFrom, json.gapTo, Slice.fromJSON(schema, json.slice), json.insert, !!json.structure);\n    }\n}\nStep.jsonID(\"replaceAround\", ReplaceAroundStep);\nfunction contentBetween(doc, from, to) {\n    let $from = doc.resolve(from), dist = to - from, depth = $from.depth;\n    while (dist > 0 && depth > 0 && $from.indexAfter(depth) == $from.node(depth).childCount) {\n        depth--;\n        dist--;\n    }\n    if (dist > 0) {\n        let next = $from.node(depth).maybeChild($from.indexAfter(depth));\n        while (dist > 0) {\n            if (!next || next.isLeaf)\n                return true;\n            next = next.firstChild;\n            dist--;\n        }\n    }\n    return false;\n}\n\nfunction addMark(tr, from, to, mark) {\n    let removed = [], added = [];\n    let removing, adding;\n    tr.doc.nodesBetween(from, to, (node, pos, parent) => {\n        if (!node.isInline)\n            return;\n        let marks = node.marks;\n        if (!mark.isInSet(marks) && parent.type.allowsMarkType(mark.type)) {\n            let start = Math.max(pos, from), end = Math.min(pos + node.nodeSize, to);\n            let newSet = mark.addToSet(marks);\n            for (let i = 0; i < marks.length; i++) {\n                if (!marks[i].isInSet(newSet)) {\n                    if (removing && removing.to == start && removing.mark.eq(marks[i]))\n                        removing.to = end;\n                    else\n                        removed.push(removing = new RemoveMarkStep(start, end, marks[i]));\n                }\n            }\n            if (adding && adding.to == start)\n                adding.to = end;\n            else\n                added.push(adding = new AddMarkStep(start, end, mark));\n        }\n    });\n    removed.forEach(s => tr.step(s));\n    added.forEach(s => tr.step(s));\n}\nfunction removeMark(tr, from, to, mark) {\n    let matched = [], step = 0;\n    tr.doc.nodesBetween(from, to, (node, pos) => {\n        if (!node.isInline)\n            return;\n        step++;\n        let toRemove = null;\n        if (mark instanceof MarkType) {\n            let set = node.marks, found;\n            while (found = mark.isInSet(set)) {\n                (toRemove || (toRemove = [])).push(found);\n                set = found.removeFromSet(set);\n            }\n        }\n        else if (mark) {\n            if (mark.isInSet(node.marks))\n                toRemove = [mark];\n        }\n        else {\n            toRemove = node.marks;\n        }\n        if (toRemove && toRemove.length) {\n            let end = Math.min(pos + node.nodeSize, to);\n            for (let i = 0; i < toRemove.length; i++) {\n                let style = toRemove[i], found;\n                for (let j = 0; j < matched.length; j++) {\n                    let m = matched[j];\n                    if (m.step == step - 1 && style.eq(matched[j].style))\n                        found = m;\n                }\n                if (found) {\n                    found.to = end;\n                    found.step = step;\n                }\n                else {\n                    matched.push({ style, from: Math.max(pos, from), to: end, step });\n                }\n            }\n        }\n    });\n    matched.forEach(m => tr.step(new RemoveMarkStep(m.from, m.to, m.style)));\n}\nfunction clearIncompatible(tr, pos, parentType, match = parentType.contentMatch, clearNewlines = true) {\n    let node = tr.doc.nodeAt(pos);\n    let replSteps = [], cur = pos + 1;\n    for (let i = 0; i < node.childCount; i++) {\n        let child = node.child(i), end = cur + child.nodeSize;\n        let allowed = match.matchType(child.type);\n        if (!allowed) {\n            replSteps.push(new ReplaceStep(cur, end, Slice.empty));\n        }\n        else {\n            match = allowed;\n            for (let j = 0; j < child.marks.length; j++)\n                if (!parentType.allowsMarkType(child.marks[j].type))\n                    tr.step(new RemoveMarkStep(cur, end, child.marks[j]));\n            if (clearNewlines && child.isText && parentType.whitespace != \"pre\") {\n                let m, newline = /\\r?\\n|\\r/g, slice;\n                while (m = newline.exec(child.text)) {\n                    if (!slice)\n                        slice = new Slice(Fragment.from(parentType.schema.text(\" \", parentType.allowedMarks(child.marks))), 0, 0);\n                    replSteps.push(new ReplaceStep(cur + m.index, cur + m.index + m[0].length, slice));\n                }\n            }\n        }\n        cur = end;\n    }\n    if (!match.validEnd) {\n        let fill = match.fillBefore(Fragment.empty, true);\n        tr.replace(cur, cur, new Slice(fill, 0, 0));\n    }\n    for (let i = replSteps.length - 1; i >= 0; i--)\n        tr.step(replSteps[i]);\n}\n\nfunction canCut(node, start, end) {\n    return (start == 0 || node.canReplace(start, node.childCount)) &&\n        (end == node.childCount || node.canReplace(0, end));\n}\n/**\nTry to find a target depth to which the content in the given range\ncan be lifted. Will not go across\n[isolating](https://prosemirror.net/docs/ref/#model.NodeSpec.isolating) parent nodes.\n*/\nfunction liftTarget(range) {\n    let parent = range.parent;\n    let content = parent.content.cutByIndex(range.startIndex, range.endIndex);\n    for (let depth = range.depth;; --depth) {\n        let node = range.$from.node(depth);\n        let index = range.$from.index(depth), endIndex = range.$to.indexAfter(depth);\n        if (depth < range.depth && node.canReplace(index, endIndex, content))\n            return depth;\n        if (depth == 0 || node.type.spec.isolating || !canCut(node, index, endIndex))\n            break;\n    }\n    return null;\n}\nfunction lift(tr, range, target) {\n    let { $from, $to, depth } = range;\n    let gapStart = $from.before(depth + 1), gapEnd = $to.after(depth + 1);\n    let start = gapStart, end = gapEnd;\n    let before = Fragment.empty, openStart = 0;\n    for (let d = depth, splitting = false; d > target; d--)\n        if (splitting || $from.index(d) > 0) {\n            splitting = true;\n            before = Fragment.from($from.node(d).copy(before));\n            openStart++;\n        }\n        else {\n            start--;\n        }\n    let after = Fragment.empty, openEnd = 0;\n    for (let d = depth, splitting = false; d > target; d--)\n        if (splitting || $to.after(d + 1) < $to.end(d)) {\n            splitting = true;\n            after = Fragment.from($to.node(d).copy(after));\n            openEnd++;\n        }\n        else {\n            end++;\n        }\n    tr.step(new ReplaceAroundStep(start, end, gapStart, gapEnd, new Slice(before.append(after), openStart, openEnd), before.size - openStart, true));\n}\n/**\nTry to find a valid way to wrap the content in the given range in a\nnode of the given type. May introduce extra nodes around and inside\nthe wrapper node, if necessary. Returns null if no valid wrapping\ncould be found. When `innerRange` is given, that range's content is\nused as the content to fit into the wrapping, instead of the\ncontent of `range`.\n*/\nfunction findWrapping(range, nodeType, attrs = null, innerRange = range) {\n    let around = findWrappingOutside(range, nodeType);\n    let inner = around && findWrappingInside(innerRange, nodeType);\n    if (!inner)\n        return null;\n    return around.map(withAttrs)\n        .concat({ type: nodeType, attrs }).concat(inner.map(withAttrs));\n}\nfunction withAttrs(type) { return { type, attrs: null }; }\nfunction findWrappingOutside(range, type) {\n    let { parent, startIndex, endIndex } = range;\n    let around = parent.contentMatchAt(startIndex).findWrapping(type);\n    if (!around)\n        return null;\n    let outer = around.length ? around[0] : type;\n    return parent.canReplaceWith(startIndex, endIndex, outer) ? around : null;\n}\nfunction findWrappingInside(range, type) {\n    let { parent, startIndex, endIndex } = range;\n    let inner = parent.child(startIndex);\n    let inside = type.contentMatch.findWrapping(inner.type);\n    if (!inside)\n        return null;\n    let lastType = inside.length ? inside[inside.length - 1] : type;\n    let innerMatch = lastType.contentMatch;\n    for (let i = startIndex; innerMatch && i < endIndex; i++)\n        innerMatch = innerMatch.matchType(parent.child(i).type);\n    if (!innerMatch || !innerMatch.validEnd)\n        return null;\n    return inside;\n}\nfunction wrap(tr, range, wrappers) {\n    let content = Fragment.empty;\n    for (let i = wrappers.length - 1; i >= 0; i--) {\n        if (content.size) {\n            let match = wrappers[i].type.contentMatch.matchFragment(content);\n            if (!match || !match.validEnd)\n                throw new RangeError(\"Wrapper type given to Transform.wrap does not form valid content of its parent wrapper\");\n        }\n        content = Fragment.from(wrappers[i].type.create(wrappers[i].attrs, content));\n    }\n    let start = range.start, end = range.end;\n    tr.step(new ReplaceAroundStep(start, end, start, end, new Slice(content, 0, 0), wrappers.length, true));\n}\nfunction setBlockType(tr, from, to, type, attrs) {\n    if (!type.isTextblock)\n        throw new RangeError(\"Type given to setBlockType should be a textblock\");\n    let mapFrom = tr.steps.length;\n    tr.doc.nodesBetween(from, to, (node, pos) => {\n        let attrsHere = typeof attrs == \"function\" ? attrs(node) : attrs;\n        if (node.isTextblock && !node.hasMarkup(type, attrsHere) &&\n            canChangeType(tr.doc, tr.mapping.slice(mapFrom).map(pos), type)) {\n            let convertNewlines = null;\n            if (type.schema.linebreakReplacement) {\n                let pre = type.whitespace == \"pre\", supportLinebreak = !!type.contentMatch.matchType(type.schema.linebreakReplacement);\n                if (pre && !supportLinebreak)\n                    convertNewlines = false;\n                else if (!pre && supportLinebreak)\n                    convertNewlines = true;\n            }\n            // Ensure all markup that isn't allowed in the new node type is cleared\n            if (convertNewlines === false)\n                replaceLinebreaks(tr, node, pos, mapFrom);\n            clearIncompatible(tr, tr.mapping.slice(mapFrom).map(pos, 1), type, undefined, convertNewlines === null);\n            let mapping = tr.mapping.slice(mapFrom);\n            let startM = mapping.map(pos, 1), endM = mapping.map(pos + node.nodeSize, 1);\n            tr.step(new ReplaceAroundStep(startM, endM, startM + 1, endM - 1, new Slice(Fragment.from(type.create(attrsHere, null, node.marks)), 0, 0), 1, true));\n            if (convertNewlines === true)\n                replaceNewlines(tr, node, pos, mapFrom);\n            return false;\n        }\n    });\n}\nfunction replaceNewlines(tr, node, pos, mapFrom) {\n    node.forEach((child, offset) => {\n        if (child.isText) {\n            let m, newline = /\\r?\\n|\\r/g;\n            while (m = newline.exec(child.text)) {\n                let start = tr.mapping.slice(mapFrom).map(pos + 1 + offset + m.index);\n                tr.replaceWith(start, start + 1, node.type.schema.linebreakReplacement.create());\n            }\n        }\n    });\n}\nfunction replaceLinebreaks(tr, node, pos, mapFrom) {\n    node.forEach((child, offset) => {\n        if (child.type == child.type.schema.linebreakReplacement) {\n            let start = tr.mapping.slice(mapFrom).map(pos + 1 + offset);\n            tr.replaceWith(start, start + 1, node.type.schema.text(\"\\n\"));\n        }\n    });\n}\nfunction canChangeType(doc, pos, type) {\n    let $pos = doc.resolve(pos), index = $pos.index();\n    return $pos.parent.canReplaceWith(index, index + 1, type);\n}\n/**\nChange the type, attributes, and/or marks of the node at `pos`.\nWhen `type` isn't given, the existing node type is preserved,\n*/\nfunction setNodeMarkup(tr, pos, type, attrs, marks) {\n    let node = tr.doc.nodeAt(pos);\n    if (!node)\n        throw new RangeError(\"No node at given position\");\n    if (!type)\n        type = node.type;\n    let newNode = type.create(attrs, null, marks || node.marks);\n    if (node.isLeaf)\n        return tr.replaceWith(pos, pos + node.nodeSize, newNode);\n    if (!type.validContent(node.content))\n        throw new RangeError(\"Invalid content for node type \" + type.name);\n    tr.step(new ReplaceAroundStep(pos, pos + node.nodeSize, pos + 1, pos + node.nodeSize - 1, new Slice(Fragment.from(newNode), 0, 0), 1, true));\n}\n/**\nCheck whether splitting at the given position is allowed.\n*/\nfunction canSplit(doc, pos, depth = 1, typesAfter) {\n    let $pos = doc.resolve(pos), base = $pos.depth - depth;\n    let innerType = (typesAfter && typesAfter[typesAfter.length - 1]) || $pos.parent;\n    if (base < 0 || $pos.parent.type.spec.isolating ||\n        !$pos.parent.canReplace($pos.index(), $pos.parent.childCount) ||\n        !innerType.type.validContent($pos.parent.content.cutByIndex($pos.index(), $pos.parent.childCount)))\n        return false;\n    for (let d = $pos.depth - 1, i = depth - 2; d > base; d--, i--) {\n        let node = $pos.node(d), index = $pos.index(d);\n        if (node.type.spec.isolating)\n            return false;\n        let rest = node.content.cutByIndex(index, node.childCount);\n        let overrideChild = typesAfter && typesAfter[i + 1];\n        if (overrideChild)\n            rest = rest.replaceChild(0, overrideChild.type.create(overrideChild.attrs));\n        let after = (typesAfter && typesAfter[i]) || node;\n        if (!node.canReplace(index + 1, node.childCount) || !after.type.validContent(rest))\n            return false;\n    }\n    let index = $pos.indexAfter(base);\n    let baseType = typesAfter && typesAfter[0];\n    return $pos.node(base).canReplaceWith(index, index, baseType ? baseType.type : $pos.node(base + 1).type);\n}\nfunction split(tr, pos, depth = 1, typesAfter) {\n    let $pos = tr.doc.resolve(pos), before = Fragment.empty, after = Fragment.empty;\n    for (let d = $pos.depth, e = $pos.depth - depth, i = depth - 1; d > e; d--, i--) {\n        before = Fragment.from($pos.node(d).copy(before));\n        let typeAfter = typesAfter && typesAfter[i];\n        after = Fragment.from(typeAfter ? typeAfter.type.create(typeAfter.attrs, after) : $pos.node(d).copy(after));\n    }\n    tr.step(new ReplaceStep(pos, pos, new Slice(before.append(after), depth, depth), true));\n}\n/**\nTest whether the blocks before and after a given position can be\njoined.\n*/\nfunction canJoin(doc, pos) {\n    let $pos = doc.resolve(pos), index = $pos.index();\n    return joinable($pos.nodeBefore, $pos.nodeAfter) &&\n        $pos.parent.canReplace(index, index + 1);\n}\nfunction canAppendWithSubstitutedLinebreaks(a, b) {\n    if (!b.content.size)\n        a.type.compatibleContent(b.type);\n    let match = a.contentMatchAt(a.childCount);\n    let { linebreakReplacement } = a.type.schema;\n    for (let i = 0; i < b.childCount; i++) {\n        let child = b.child(i);\n        let type = child.type == linebreakReplacement ? a.type.schema.nodes.text : child.type;\n        match = match.matchType(type);\n        if (!match)\n            return false;\n        if (!a.type.allowsMarks(child.marks))\n            return false;\n    }\n    return match.validEnd;\n}\nfunction joinable(a, b) {\n    return !!(a && b && !a.isLeaf && canAppendWithSubstitutedLinebreaks(a, b));\n}\n/**\nFind an ancestor of the given position that can be joined to the\nblock before (or after if `dir` is positive). Returns the joinable\npoint, if any.\n*/\nfunction joinPoint(doc, pos, dir = -1) {\n    let $pos = doc.resolve(pos);\n    for (let d = $pos.depth;; d--) {\n        let before, after, index = $pos.index(d);\n        if (d == $pos.depth) {\n            before = $pos.nodeBefore;\n            after = $pos.nodeAfter;\n        }\n        else if (dir > 0) {\n            before = $pos.node(d + 1);\n            index++;\n            after = $pos.node(d).maybeChild(index);\n        }\n        else {\n            before = $pos.node(d).maybeChild(index - 1);\n            after = $pos.node(d + 1);\n        }\n        if (before && !before.isTextblock && joinable(before, after) &&\n            $pos.node(d).canReplace(index, index + 1))\n            return pos;\n        if (d == 0)\n            break;\n        pos = dir < 0 ? $pos.before(d) : $pos.after(d);\n    }\n}\nfunction join(tr, pos, depth) {\n    let convertNewlines = null;\n    let { linebreakReplacement } = tr.doc.type.schema;\n    let $before = tr.doc.resolve(pos - depth), beforeType = $before.node().type;\n    if (linebreakReplacement && beforeType.inlineContent) {\n        let pre = beforeType.whitespace == \"pre\";\n        let supportLinebreak = !!beforeType.contentMatch.matchType(linebreakReplacement);\n        if (pre && !supportLinebreak)\n            convertNewlines = false;\n        else if (!pre && supportLinebreak)\n            convertNewlines = true;\n    }\n    let mapFrom = tr.steps.length;\n    if (convertNewlines === false) {\n        let $after = tr.doc.resolve(pos + depth);\n        replaceLinebreaks(tr, $after.node(), $after.before(), mapFrom);\n    }\n    if (beforeType.inlineContent)\n        clearIncompatible(tr, pos + depth - 1, beforeType, $before.node().contentMatchAt($before.index()), convertNewlines == null);\n    let mapping = tr.mapping.slice(mapFrom), start = mapping.map(pos - depth);\n    tr.step(new ReplaceStep(start, mapping.map(pos + depth, -1), Slice.empty, true));\n    if (convertNewlines === true) {\n        let $full = tr.doc.resolve(start);\n        replaceNewlines(tr, $full.node(), $full.before(), tr.steps.length);\n    }\n    return tr;\n}\n/**\nTry to find a point where a node of the given type can be inserted\nnear `pos`, by searching up the node hierarchy when `pos` itself\nisn't a valid place but is at the start or end of a node. Return\nnull if no position was found.\n*/\nfunction insertPoint(doc, pos, nodeType) {\n    let $pos = doc.resolve(pos);\n    if ($pos.parent.canReplaceWith($pos.index(), $pos.index(), nodeType))\n        return pos;\n    if ($pos.parentOffset == 0)\n        for (let d = $pos.depth - 1; d >= 0; d--) {\n            let index = $pos.index(d);\n            if ($pos.node(d).canReplaceWith(index, index, nodeType))\n                return $pos.before(d + 1);\n            if (index > 0)\n                return null;\n        }\n    if ($pos.parentOffset == $pos.parent.content.size)\n        for (let d = $pos.depth - 1; d >= 0; d--) {\n            let index = $pos.indexAfter(d);\n            if ($pos.node(d).canReplaceWith(index, index, nodeType))\n                return $pos.after(d + 1);\n            if (index < $pos.node(d).childCount)\n                return null;\n        }\n    return null;\n}\n/**\nFinds a position at or around the given position where the given\nslice can be inserted. Will look at parent nodes' nearest boundary\nand try there, even if the original position wasn't directly at the\nstart or end of that node. Returns null when no position was found.\n*/\nfunction dropPoint(doc, pos, slice) {\n    let $pos = doc.resolve(pos);\n    if (!slice.content.size)\n        return pos;\n    let content = slice.content;\n    for (let i = 0; i < slice.openStart; i++)\n        content = content.firstChild.content;\n    for (let pass = 1; pass <= (slice.openStart == 0 && slice.size ? 2 : 1); pass++) {\n        for (let d = $pos.depth; d >= 0; d--) {\n            let bias = d == $pos.depth ? 0 : $pos.pos <= ($pos.start(d + 1) + $pos.end(d + 1)) / 2 ? -1 : 1;\n            let insertPos = $pos.index(d) + (bias > 0 ? 1 : 0);\n            let parent = $pos.node(d), fits = false;\n            if (pass == 1) {\n                fits = parent.canReplace(insertPos, insertPos, content);\n            }\n            else {\n                let wrapping = parent.contentMatchAt(insertPos).findWrapping(content.firstChild.type);\n                fits = wrapping && parent.canReplaceWith(insertPos, insertPos, wrapping[0]);\n            }\n            if (fits)\n                return bias == 0 ? $pos.pos : bias < 0 ? $pos.before(d + 1) : $pos.after(d + 1);\n        }\n    }\n    return null;\n}\n\n/**\n‘Fit’ a slice into a given position in the document, producing a\n[step](https://prosemirror.net/docs/ref/#transform.Step) that inserts it. Will return null if\nthere's no meaningful way to insert the slice here, or inserting it\nwould be a no-op (an empty slice over an empty range).\n*/\nfunction replaceStep(doc, from, to = from, slice = Slice.empty) {\n    if (from == to && !slice.size)\n        return null;\n    let $from = doc.resolve(from), $to = doc.resolve(to);\n    // Optimization -- avoid work if it's obvious that it's not needed.\n    if (fitsTrivially($from, $to, slice))\n        return new ReplaceStep(from, to, slice);\n    return new Fitter($from, $to, slice).fit();\n}\nfunction fitsTrivially($from, $to, slice) {\n    return !slice.openStart && !slice.openEnd && $from.start() == $to.start() &&\n        $from.parent.canReplace($from.index(), $to.index(), slice.content);\n}\n// Algorithm for 'placing' the elements of a slice into a gap:\n//\n// We consider the content of each node that is open to the left to be\n// independently placeable. I.e. in <p(\"foo\"), p(\"bar\")>, when the\n// paragraph on the left is open, \"foo\" can be placed (somewhere on\n// the left side of the replacement gap) independently from p(\"bar\").\n//\n// This class tracks the state of the placement progress in the\n// following properties:\n//\n//  - `frontier` holds a stack of `{type, match}` objects that\n//    represent the open side of the replacement. It starts at\n//    `$from`, then moves forward as content is placed, and is finally\n//    reconciled with `$to`.\n//\n//  - `unplaced` is a slice that represents the content that hasn't\n//    been placed yet.\n//\n//  - `placed` is a fragment of placed content. Its open-start value\n//    is implicit in `$from`, and its open-end value in `frontier`.\nclass Fitter {\n    constructor($from, $to, unplaced) {\n        this.$from = $from;\n        this.$to = $to;\n        this.unplaced = unplaced;\n        this.frontier = [];\n        this.placed = Fragment.empty;\n        for (let i = 0; i <= $from.depth; i++) {\n            let node = $from.node(i);\n            this.frontier.push({\n                type: node.type,\n                match: node.contentMatchAt($from.indexAfter(i))\n            });\n        }\n        for (let i = $from.depth; i > 0; i--)\n            this.placed = Fragment.from($from.node(i).copy(this.placed));\n    }\n    get depth() { return this.frontier.length - 1; }\n    fit() {\n        // As long as there's unplaced content, try to place some of it.\n        // If that fails, either increase the open score of the unplaced\n        // slice, or drop nodes from it, and then try again.\n        while (this.unplaced.size) {\n            let fit = this.findFittable();\n            if (fit)\n                this.placeNodes(fit);\n            else\n                this.openMore() || this.dropNode();\n        }\n        // When there's inline content directly after the frontier _and_\n        // directly after `this.$to`, we must generate a `ReplaceAround`\n        // step that pulls that content into the node after the frontier.\n        // That means the fitting must be done to the end of the textblock\n        // node after `this.$to`, not `this.$to` itself.\n        let moveInline = this.mustMoveInline(), placedSize = this.placed.size - this.depth - this.$from.depth;\n        let $from = this.$from, $to = this.close(moveInline < 0 ? this.$to : $from.doc.resolve(moveInline));\n        if (!$to)\n            return null;\n        // If closing to `$to` succeeded, create a step\n        let content = this.placed, openStart = $from.depth, openEnd = $to.depth;\n        while (openStart && openEnd && content.childCount == 1) { // Normalize by dropping open parent nodes\n            content = content.firstChild.content;\n            openStart--;\n            openEnd--;\n        }\n        let slice = new Slice(content, openStart, openEnd);\n        if (moveInline > -1)\n            return new ReplaceAroundStep($from.pos, moveInline, this.$to.pos, this.$to.end(), slice, placedSize);\n        if (slice.size || $from.pos != this.$to.pos) // Don't generate no-op steps\n            return new ReplaceStep($from.pos, $to.pos, slice);\n        return null;\n    }\n    // Find a position on the start spine of `this.unplaced` that has\n    // content that can be moved somewhere on the frontier. Returns two\n    // depths, one for the slice and one for the frontier.\n    findFittable() {\n        let startDepth = this.unplaced.openStart;\n        for (let cur = this.unplaced.content, d = 0, openEnd = this.unplaced.openEnd; d < startDepth; d++) {\n            let node = cur.firstChild;\n            if (cur.childCount > 1)\n                openEnd = 0;\n            if (node.type.spec.isolating && openEnd <= d) {\n                startDepth = d;\n                break;\n            }\n            cur = node.content;\n        }\n        // Only try wrapping nodes (pass 2) after finding a place without\n        // wrapping failed.\n        for (let pass = 1; pass <= 2; pass++) {\n            for (let sliceDepth = pass == 1 ? startDepth : this.unplaced.openStart; sliceDepth >= 0; sliceDepth--) {\n                let fragment, parent = null;\n                if (sliceDepth) {\n                    parent = contentAt(this.unplaced.content, sliceDepth - 1).firstChild;\n                    fragment = parent.content;\n                }\n                else {\n                    fragment = this.unplaced.content;\n                }\n                let first = fragment.firstChild;\n                for (let frontierDepth = this.depth; frontierDepth >= 0; frontierDepth--) {\n                    let { type, match } = this.frontier[frontierDepth], wrap, inject = null;\n                    // In pass 1, if the next node matches, or there is no next\n                    // node but the parents look compatible, we've found a\n                    // place.\n                    if (pass == 1 && (first ? match.matchType(first.type) || (inject = match.fillBefore(Fragment.from(first), false))\n                        : parent && type.compatibleContent(parent.type)))\n                        return { sliceDepth, frontierDepth, parent, inject };\n                    // In pass 2, look for a set of wrapping nodes that make\n                    // `first` fit here.\n                    else if (pass == 2 && first && (wrap = match.findWrapping(first.type)))\n                        return { sliceDepth, frontierDepth, parent, wrap };\n                    // Don't continue looking further up if the parent node\n                    // would fit here.\n                    if (parent && match.matchType(parent.type))\n                        break;\n                }\n            }\n        }\n    }\n    openMore() {\n        let { content, openStart, openEnd } = this.unplaced;\n        let inner = contentAt(content, openStart);\n        if (!inner.childCount || inner.firstChild.isLeaf)\n            return false;\n        this.unplaced = new Slice(content, openStart + 1, Math.max(openEnd, inner.size + openStart >= content.size - openEnd ? openStart + 1 : 0));\n        return true;\n    }\n    dropNode() {\n        let { content, openStart, openEnd } = this.unplaced;\n        let inner = contentAt(content, openStart);\n        if (inner.childCount <= 1 && openStart > 0) {\n            let openAtEnd = content.size - openStart <= openStart + inner.size;\n            this.unplaced = new Slice(dropFromFragment(content, openStart - 1, 1), openStart - 1, openAtEnd ? openStart - 1 : openEnd);\n        }\n        else {\n            this.unplaced = new Slice(dropFromFragment(content, openStart, 1), openStart, openEnd);\n        }\n    }\n    // Move content from the unplaced slice at `sliceDepth` to the\n    // frontier node at `frontierDepth`. Close that frontier node when\n    // applicable.\n    placeNodes({ sliceDepth, frontierDepth, parent, inject, wrap }) {\n        while (this.depth > frontierDepth)\n            this.closeFrontierNode();\n        if (wrap)\n            for (let i = 0; i < wrap.length; i++)\n                this.openFrontierNode(wrap[i]);\n        let slice = this.unplaced, fragment = parent ? parent.content : slice.content;\n        let openStart = slice.openStart - sliceDepth;\n        let taken = 0, add = [];\n        let { match, type } = this.frontier[frontierDepth];\n        if (inject) {\n            for (let i = 0; i < inject.childCount; i++)\n                add.push(inject.child(i));\n            match = match.matchFragment(inject);\n        }\n        // Computes the amount of (end) open nodes at the end of the\n        // fragment. When 0, the parent is open, but no more. When\n        // negative, nothing is open.\n        let openEndCount = (fragment.size + sliceDepth) - (slice.content.size - slice.openEnd);\n        // Scan over the fragment, fitting as many child nodes as\n        // possible.\n        while (taken < fragment.childCount) {\n            let next = fragment.child(taken), matches = match.matchType(next.type);\n            if (!matches)\n                break;\n            taken++;\n            if (taken > 1 || openStart == 0 || next.content.size) { // Drop empty open nodes\n                match = matches;\n                add.push(closeNodeStart(next.mark(type.allowedMarks(next.marks)), taken == 1 ? openStart : 0, taken == fragment.childCount ? openEndCount : -1));\n            }\n        }\n        let toEnd = taken == fragment.childCount;\n        if (!toEnd)\n            openEndCount = -1;\n        this.placed = addToFragment(this.placed, frontierDepth, Fragment.from(add));\n        this.frontier[frontierDepth].match = match;\n        // If the parent types match, and the entire node was moved, and\n        // it's not open, close this frontier node right away.\n        if (toEnd && openEndCount < 0 && parent && parent.type == this.frontier[this.depth].type && this.frontier.length > 1)\n            this.closeFrontierNode();\n        // Add new frontier nodes for any open nodes at the end.\n        for (let i = 0, cur = fragment; i < openEndCount; i++) {\n            let node = cur.lastChild;\n            this.frontier.push({ type: node.type, match: node.contentMatchAt(node.childCount) });\n            cur = node.content;\n        }\n        // Update `this.unplaced`. Drop the entire node from which we\n        // placed it we got to its end, otherwise just drop the placed\n        // nodes.\n        this.unplaced = !toEnd ? new Slice(dropFromFragment(slice.content, sliceDepth, taken), slice.openStart, slice.openEnd)\n            : sliceDepth == 0 ? Slice.empty\n                : new Slice(dropFromFragment(slice.content, sliceDepth - 1, 1), sliceDepth - 1, openEndCount < 0 ? slice.openEnd : sliceDepth - 1);\n    }\n    mustMoveInline() {\n        if (!this.$to.parent.isTextblock)\n            return -1;\n        let top = this.frontier[this.depth], level;\n        if (!top.type.isTextblock || !contentAfterFits(this.$to, this.$to.depth, top.type, top.match, false) ||\n            (this.$to.depth == this.depth && (level = this.findCloseLevel(this.$to)) && level.depth == this.depth))\n            return -1;\n        let { depth } = this.$to, after = this.$to.after(depth);\n        while (depth > 1 && after == this.$to.end(--depth))\n            ++after;\n        return after;\n    }\n    findCloseLevel($to) {\n        scan: for (let i = Math.min(this.depth, $to.depth); i >= 0; i--) {\n            let { match, type } = this.frontier[i];\n            let dropInner = i < $to.depth && $to.end(i + 1) == $to.pos + ($to.depth - (i + 1));\n            let fit = contentAfterFits($to, i, type, match, dropInner);\n            if (!fit)\n                continue;\n            for (let d = i - 1; d >= 0; d--) {\n                let { match, type } = this.frontier[d];\n                let matches = contentAfterFits($to, d, type, match, true);\n                if (!matches || matches.childCount)\n                    continue scan;\n            }\n            return { depth: i, fit, move: dropInner ? $to.doc.resolve($to.after(i + 1)) : $to };\n        }\n    }\n    close($to) {\n        let close = this.findCloseLevel($to);\n        if (!close)\n            return null;\n        while (this.depth > close.depth)\n            this.closeFrontierNode();\n        if (close.fit.childCount)\n            this.placed = addToFragment(this.placed, close.depth, close.fit);\n        $to = close.move;\n        for (let d = close.depth + 1; d <= $to.depth; d++) {\n            let node = $to.node(d), add = node.type.contentMatch.fillBefore(node.content, true, $to.index(d));\n            this.openFrontierNode(node.type, node.attrs, add);\n        }\n        return $to;\n    }\n    openFrontierNode(type, attrs = null, content) {\n        let top = this.frontier[this.depth];\n        top.match = top.match.matchType(type);\n        this.placed = addToFragment(this.placed, this.depth, Fragment.from(type.create(attrs, content)));\n        this.frontier.push({ type, match: type.contentMatch });\n    }\n    closeFrontierNode() {\n        let open = this.frontier.pop();\n        let add = open.match.fillBefore(Fragment.empty, true);\n        if (add.childCount)\n            this.placed = addToFragment(this.placed, this.frontier.length, add);\n    }\n}\nfunction dropFromFragment(fragment, depth, count) {\n    if (depth == 0)\n        return fragment.cutByIndex(count, fragment.childCount);\n    return fragment.replaceChild(0, fragment.firstChild.copy(dropFromFragment(fragment.firstChild.content, depth - 1, count)));\n}\nfunction addToFragment(fragment, depth, content) {\n    if (depth == 0)\n        return fragment.append(content);\n    return fragment.replaceChild(fragment.childCount - 1, fragment.lastChild.copy(addToFragment(fragment.lastChild.content, depth - 1, content)));\n}\nfunction contentAt(fragment, depth) {\n    for (let i = 0; i < depth; i++)\n        fragment = fragment.firstChild.content;\n    return fragment;\n}\nfunction closeNodeStart(node, openStart, openEnd) {\n    if (openStart <= 0)\n        return node;\n    let frag = node.content;\n    if (openStart > 1)\n        frag = frag.replaceChild(0, closeNodeStart(frag.firstChild, openStart - 1, frag.childCount == 1 ? openEnd - 1 : 0));\n    if (openStart > 0) {\n        frag = node.type.contentMatch.fillBefore(frag).append(frag);\n        if (openEnd <= 0)\n            frag = frag.append(node.type.contentMatch.matchFragment(frag).fillBefore(Fragment.empty, true));\n    }\n    return node.copy(frag);\n}\nfunction contentAfterFits($to, depth, type, match, open) {\n    let node = $to.node(depth), index = open ? $to.indexAfter(depth) : $to.index(depth);\n    if (index == node.childCount && !type.compatibleContent(node.type))\n        return null;\n    let fit = match.fillBefore(node.content, true, index);\n    return fit && !invalidMarks(type, node.content, index) ? fit : null;\n}\nfunction invalidMarks(type, fragment, start) {\n    for (let i = start; i < fragment.childCount; i++)\n        if (!type.allowsMarks(fragment.child(i).marks))\n            return true;\n    return false;\n}\nfunction definesContent(type) {\n    return type.spec.defining || type.spec.definingForContent;\n}\nfunction replaceRange(tr, from, to, slice) {\n    if (!slice.size)\n        return tr.deleteRange(from, to);\n    let $from = tr.doc.resolve(from), $to = tr.doc.resolve(to);\n    if (fitsTrivially($from, $to, slice))\n        return tr.step(new ReplaceStep(from, to, slice));\n    let targetDepths = coveredDepths($from, tr.doc.resolve(to));\n    // Can't replace the whole document, so remove 0 if it's present\n    if (targetDepths[targetDepths.length - 1] == 0)\n        targetDepths.pop();\n    // Negative numbers represent not expansion over the whole node at\n    // that depth, but replacing from $from.before(-D) to $to.pos.\n    let preferredTarget = -($from.depth + 1);\n    targetDepths.unshift(preferredTarget);\n    // This loop picks a preferred target depth, if one of the covering\n    // depths is not outside of a defining node, and adds negative\n    // depths for any depth that has $from at its start and does not\n    // cross a defining node.\n    for (let d = $from.depth, pos = $from.pos - 1; d > 0; d--, pos--) {\n        let spec = $from.node(d).type.spec;\n        if (spec.defining || spec.definingAsContext || spec.isolating)\n            break;\n        if (targetDepths.indexOf(d) > -1)\n            preferredTarget = d;\n        else if ($from.before(d) == pos)\n            targetDepths.splice(1, 0, -d);\n    }\n    // Try to fit each possible depth of the slice into each possible\n    // target depth, starting with the preferred depths.\n    let preferredTargetIndex = targetDepths.indexOf(preferredTarget);\n    let leftNodes = [], preferredDepth = slice.openStart;\n    for (let content = slice.content, i = 0;; i++) {\n        let node = content.firstChild;\n        leftNodes.push(node);\n        if (i == slice.openStart)\n            break;\n        content = node.content;\n    }\n    // Back up preferredDepth to cover defining textblocks directly\n    // above it, possibly skipping a non-defining textblock.\n    for (let d = preferredDepth - 1; d >= 0; d--) {\n        let leftNode = leftNodes[d], def = definesContent(leftNode.type);\n        if (def && !leftNode.sameMarkup($from.node(Math.abs(preferredTarget) - 1)))\n            preferredDepth = d;\n        else if (def || !leftNode.type.isTextblock)\n            break;\n    }\n    for (let j = slice.openStart; j >= 0; j--) {\n        let openDepth = (j + preferredDepth + 1) % (slice.openStart + 1);\n        let insert = leftNodes[openDepth];\n        if (!insert)\n            continue;\n        for (let i = 0; i < targetDepths.length; i++) {\n            // Loop over possible expansion levels, starting with the\n            // preferred one\n            let targetDepth = targetDepths[(i + preferredTargetIndex) % targetDepths.length], expand = true;\n            if (targetDepth < 0) {\n                expand = false;\n                targetDepth = -targetDepth;\n            }\n            let parent = $from.node(targetDepth - 1), index = $from.index(targetDepth - 1);\n            if (parent.canReplaceWith(index, index, insert.type, insert.marks))\n                return tr.replace($from.before(targetDepth), expand ? $to.after(targetDepth) : to, new Slice(closeFragment(slice.content, 0, slice.openStart, openDepth), openDepth, slice.openEnd));\n        }\n    }\n    let startSteps = tr.steps.length;\n    for (let i = targetDepths.length - 1; i >= 0; i--) {\n        tr.replace(from, to, slice);\n        if (tr.steps.length > startSteps)\n            break;\n        let depth = targetDepths[i];\n        if (depth < 0)\n            continue;\n        from = $from.before(depth);\n        to = $to.after(depth);\n    }\n}\nfunction closeFragment(fragment, depth, oldOpen, newOpen, parent) {\n    if (depth < oldOpen) {\n        let first = fragment.firstChild;\n        fragment = fragment.replaceChild(0, first.copy(closeFragment(first.content, depth + 1, oldOpen, newOpen, first)));\n    }\n    if (depth > newOpen) {\n        let match = parent.contentMatchAt(0);\n        let start = match.fillBefore(fragment).append(fragment);\n        fragment = start.append(match.matchFragment(start).fillBefore(Fragment.empty, true));\n    }\n    return fragment;\n}\nfunction replaceRangeWith(tr, from, to, node) {\n    if (!node.isInline && from == to && tr.doc.resolve(from).parent.content.size) {\n        let point = insertPoint(tr.doc, from, node.type);\n        if (point != null)\n            from = to = point;\n    }\n    tr.replaceRange(from, to, new Slice(Fragment.from(node), 0, 0));\n}\nfunction deleteRange(tr, from, to) {\n    let $from = tr.doc.resolve(from), $to = tr.doc.resolve(to);\n    let covered = coveredDepths($from, $to);\n    for (let i = 0; i < covered.length; i++) {\n        let depth = covered[i], last = i == covered.length - 1;\n        if ((last && depth == 0) || $from.node(depth).type.contentMatch.validEnd)\n            return tr.delete($from.start(depth), $to.end(depth));\n        if (depth > 0 && (last || $from.node(depth - 1).canReplace($from.index(depth - 1), $to.indexAfter(depth - 1))))\n            return tr.delete($from.before(depth), $to.after(depth));\n    }\n    for (let d = 1; d <= $from.depth && d <= $to.depth; d++) {\n        if (from - $from.start(d) == $from.depth - d && to > $from.end(d) && $to.end(d) - to != $to.depth - d &&\n            $from.start(d - 1) == $to.start(d - 1) && $from.node(d - 1).canReplace($from.index(d - 1), $to.index(d - 1)))\n            return tr.delete($from.before(d), to);\n    }\n    tr.delete(from, to);\n}\n// Returns an array of all depths for which $from - $to spans the\n// whole content of the nodes at that depth.\nfunction coveredDepths($from, $to) {\n    let result = [], minDepth = Math.min($from.depth, $to.depth);\n    for (let d = minDepth; d >= 0; d--) {\n        let start = $from.start(d);\n        if (start < $from.pos - ($from.depth - d) ||\n            $to.end(d) > $to.pos + ($to.depth - d) ||\n            $from.node(d).type.spec.isolating ||\n            $to.node(d).type.spec.isolating)\n            break;\n        if (start == $to.start(d) ||\n            (d == $from.depth && d == $to.depth && $from.parent.inlineContent && $to.parent.inlineContent &&\n                d && $to.start(d - 1) == start - 1))\n            result.push(d);\n    }\n    return result;\n}\n\n/**\nUpdate an attribute in a specific node.\n*/\nclass AttrStep extends Step {\n    /**\n    Construct an attribute step.\n    */\n    constructor(\n    /**\n    The position of the target node.\n    */\n    pos, \n    /**\n    The attribute to set.\n    */\n    attr, \n    // The attribute's new value.\n    value) {\n        super();\n        this.pos = pos;\n        this.attr = attr;\n        this.value = value;\n    }\n    apply(doc) {\n        let node = doc.nodeAt(this.pos);\n        if (!node)\n            return StepResult.fail(\"No node at attribute step's position\");\n        let attrs = Object.create(null);\n        for (let name in node.attrs)\n            attrs[name] = node.attrs[name];\n        attrs[this.attr] = this.value;\n        let updated = node.type.create(attrs, null, node.marks);\n        return StepResult.fromReplace(doc, this.pos, this.pos + 1, new Slice(Fragment.from(updated), 0, node.isLeaf ? 0 : 1));\n    }\n    getMap() {\n        return StepMap.empty;\n    }\n    invert(doc) {\n        return new AttrStep(this.pos, this.attr, doc.nodeAt(this.pos).attrs[this.attr]);\n    }\n    map(mapping) {\n        let pos = mapping.mapResult(this.pos, 1);\n        return pos.deletedAfter ? null : new AttrStep(pos.pos, this.attr, this.value);\n    }\n    toJSON() {\n        return { stepType: \"attr\", pos: this.pos, attr: this.attr, value: this.value };\n    }\n    static fromJSON(schema, json) {\n        if (typeof json.pos != \"number\" || typeof json.attr != \"string\")\n            throw new RangeError(\"Invalid input for AttrStep.fromJSON\");\n        return new AttrStep(json.pos, json.attr, json.value);\n    }\n}\nStep.jsonID(\"attr\", AttrStep);\n/**\nUpdate an attribute in the doc node.\n*/\nclass DocAttrStep extends Step {\n    /**\n    Construct an attribute step.\n    */\n    constructor(\n    /**\n    The attribute to set.\n    */\n    attr, \n    // The attribute's new value.\n    value) {\n        super();\n        this.attr = attr;\n        this.value = value;\n    }\n    apply(doc) {\n        let attrs = Object.create(null);\n        for (let name in doc.attrs)\n            attrs[name] = doc.attrs[name];\n        attrs[this.attr] = this.value;\n        let updated = doc.type.create(attrs, doc.content, doc.marks);\n        return StepResult.ok(updated);\n    }\n    getMap() {\n        return StepMap.empty;\n    }\n    invert(doc) {\n        return new DocAttrStep(this.attr, doc.attrs[this.attr]);\n    }\n    map(mapping) {\n        return this;\n    }\n    toJSON() {\n        return { stepType: \"docAttr\", attr: this.attr, value: this.value };\n    }\n    static fromJSON(schema, json) {\n        if (typeof json.attr != \"string\")\n            throw new RangeError(\"Invalid input for DocAttrStep.fromJSON\");\n        return new DocAttrStep(json.attr, json.value);\n    }\n}\nStep.jsonID(\"docAttr\", DocAttrStep);\n\n/**\n@internal\n*/\nlet TransformError = class extends Error {\n};\nTransformError = function TransformError(message) {\n    let err = Error.call(this, message);\n    err.__proto__ = TransformError.prototype;\n    return err;\n};\nTransformError.prototype = Object.create(Error.prototype);\nTransformError.prototype.constructor = TransformError;\nTransformError.prototype.name = \"TransformError\";\n/**\nAbstraction to build up and track an array of\n[steps](https://prosemirror.net/docs/ref/#transform.Step) representing a document transformation.\n\nMost transforming methods return the `Transform` object itself, so\nthat they can be chained.\n*/\nclass Transform {\n    /**\n    Create a transform that starts with the given document.\n    */\n    constructor(\n    /**\n    The current document (the result of applying the steps in the\n    transform).\n    */\n    doc) {\n        this.doc = doc;\n        /**\n        The steps in this transform.\n        */\n        this.steps = [];\n        /**\n        The documents before each of the steps.\n        */\n        this.docs = [];\n        /**\n        A mapping with the maps for each of the steps in this transform.\n        */\n        this.mapping = new Mapping;\n    }\n    /**\n    The starting document.\n    */\n    get before() { return this.docs.length ? this.docs[0] : this.doc; }\n    /**\n    Apply a new step in this transform, saving the result. Throws an\n    error when the step fails.\n    */\n    step(step) {\n        let result = this.maybeStep(step);\n        if (result.failed)\n            throw new TransformError(result.failed);\n        return this;\n    }\n    /**\n    Try to apply a step in this transformation, ignoring it if it\n    fails. Returns the step result.\n    */\n    maybeStep(step) {\n        let result = step.apply(this.doc);\n        if (!result.failed)\n            this.addStep(step, result.doc);\n        return result;\n    }\n    /**\n    True when the document has been changed (when there are any\n    steps).\n    */\n    get docChanged() {\n        return this.steps.length > 0;\n    }\n    /**\n    @internal\n    */\n    addStep(step, doc) {\n        this.docs.push(this.doc);\n        this.steps.push(step);\n        this.mapping.appendMap(step.getMap());\n        this.doc = doc;\n    }\n    /**\n    Replace the part of the document between `from` and `to` with the\n    given `slice`.\n    */\n    replace(from, to = from, slice = Slice.empty) {\n        let step = replaceStep(this.doc, from, to, slice);\n        if (step)\n            this.step(step);\n        return this;\n    }\n    /**\n    Replace the given range with the given content, which may be a\n    fragment, node, or array of nodes.\n    */\n    replaceWith(from, to, content) {\n        return this.replace(from, to, new Slice(Fragment.from(content), 0, 0));\n    }\n    /**\n    Delete the content between the given positions.\n    */\n    delete(from, to) {\n        return this.replace(from, to, Slice.empty);\n    }\n    /**\n    Insert the given content at the given position.\n    */\n    insert(pos, content) {\n        return this.replaceWith(pos, pos, content);\n    }\n    /**\n    Replace a range of the document with a given slice, using\n    `from`, `to`, and the slice's\n    [`openStart`](https://prosemirror.net/docs/ref/#model.Slice.openStart) property as hints, rather\n    than fixed start and end points. This method may grow the\n    replaced area or close open nodes in the slice in order to get a\n    fit that is more in line with WYSIWYG expectations, by dropping\n    fully covered parent nodes of the replaced region when they are\n    marked [non-defining as\n    context](https://prosemirror.net/docs/ref/#model.NodeSpec.definingAsContext), or including an\n    open parent node from the slice that _is_ marked as [defining\n    its content](https://prosemirror.net/docs/ref/#model.NodeSpec.definingForContent).\n    \n    This is the method, for example, to handle paste. The similar\n    [`replace`](https://prosemirror.net/docs/ref/#transform.Transform.replace) method is a more\n    primitive tool which will _not_ move the start and end of its given\n    range, and is useful in situations where you need more precise\n    control over what happens.\n    */\n    replaceRange(from, to, slice) {\n        replaceRange(this, from, to, slice);\n        return this;\n    }\n    /**\n    Replace the given range with a node, but use `from` and `to` as\n    hints, rather than precise positions. When from and to are the same\n    and are at the start or end of a parent node in which the given\n    node doesn't fit, this method may _move_ them out towards a parent\n    that does allow the given node to be placed. When the given range\n    completely covers a parent node, this method may completely replace\n    that parent node.\n    */\n    replaceRangeWith(from, to, node) {\n        replaceRangeWith(this, from, to, node);\n        return this;\n    }\n    /**\n    Delete the given range, expanding it to cover fully covered\n    parent nodes until a valid replace is found.\n    */\n    deleteRange(from, to) {\n        deleteRange(this, from, to);\n        return this;\n    }\n    /**\n    Split the content in the given range off from its parent, if there\n    is sibling content before or after it, and move it up the tree to\n    the depth specified by `target`. You'll probably want to use\n    [`liftTarget`](https://prosemirror.net/docs/ref/#transform.liftTarget) to compute `target`, to make\n    sure the lift is valid.\n    */\n    lift(range, target) {\n        lift(this, range, target);\n        return this;\n    }\n    /**\n    Join the blocks around the given position. If depth is 2, their\n    last and first siblings are also joined, and so on.\n    */\n    join(pos, depth = 1) {\n        join(this, pos, depth);\n        return this;\n    }\n    /**\n    Wrap the given [range](https://prosemirror.net/docs/ref/#model.NodeRange) in the given set of wrappers.\n    The wrappers are assumed to be valid in this position, and should\n    probably be computed with [`findWrapping`](https://prosemirror.net/docs/ref/#transform.findWrapping).\n    */\n    wrap(range, wrappers) {\n        wrap(this, range, wrappers);\n        return this;\n    }\n    /**\n    Set the type of all textblocks (partly) between `from` and `to` to\n    the given node type with the given attributes.\n    */\n    setBlockType(from, to = from, type, attrs = null) {\n        setBlockType(this, from, to, type, attrs);\n        return this;\n    }\n    /**\n    Change the type, attributes, and/or marks of the node at `pos`.\n    When `type` isn't given, the existing node type is preserved,\n    */\n    setNodeMarkup(pos, type, attrs = null, marks) {\n        setNodeMarkup(this, pos, type, attrs, marks);\n        return this;\n    }\n    /**\n    Set a single attribute on a given node to a new value.\n    The `pos` addresses the document content. Use `setDocAttribute`\n    to set attributes on the document itself.\n    */\n    setNodeAttribute(pos, attr, value) {\n        this.step(new AttrStep(pos, attr, value));\n        return this;\n    }\n    /**\n    Set a single attribute on the document to a new value.\n    */\n    setDocAttribute(attr, value) {\n        this.step(new DocAttrStep(attr, value));\n        return this;\n    }\n    /**\n    Add a mark to the node at position `pos`.\n    */\n    addNodeMark(pos, mark) {\n        this.step(new AddNodeMarkStep(pos, mark));\n        return this;\n    }\n    /**\n    Remove a mark (or all marks of the given type) from the node at\n    position `pos`.\n    */\n    removeNodeMark(pos, mark) {\n        let node = this.doc.nodeAt(pos);\n        if (!node)\n            throw new RangeError(\"No node at position \" + pos);\n        if (mark instanceof Mark) {\n            if (mark.isInSet(node.marks))\n                this.step(new RemoveNodeMarkStep(pos, mark));\n        }\n        else {\n            let set = node.marks, found, steps = [];\n            while (found = mark.isInSet(set)) {\n                steps.push(new RemoveNodeMarkStep(pos, found));\n                set = found.removeFromSet(set);\n            }\n            for (let i = steps.length - 1; i >= 0; i--)\n                this.step(steps[i]);\n        }\n        return this;\n    }\n    /**\n    Split the node at the given position, and optionally, if `depth` is\n    greater than one, any number of nodes above that. By default, the\n    parts split off will inherit the node type of the original node.\n    This can be changed by passing an array of types and attributes to\n    use after the split (with the outermost nodes coming first).\n    */\n    split(pos, depth = 1, typesAfter) {\n        split(this, pos, depth, typesAfter);\n        return this;\n    }\n    /**\n    Add the given mark to the inline content between `from` and `to`.\n    */\n    addMark(from, to, mark) {\n        addMark(this, from, to, mark);\n        return this;\n    }\n    /**\n    Remove marks from inline nodes between `from` and `to`. When\n    `mark` is a single mark, remove precisely that mark. When it is\n    a mark type, remove all marks of that type. When it is null,\n    remove all marks of any type.\n    */\n    removeMark(from, to, mark) {\n        removeMark(this, from, to, mark);\n        return this;\n    }\n    /**\n    Removes all marks and nodes from the content of the node at\n    `pos` that don't match the given new parent node type. Accepts\n    an optional starting [content match](https://prosemirror.net/docs/ref/#model.ContentMatch) as\n    third argument.\n    */\n    clearIncompatible(pos, parentType, match) {\n        clearIncompatible(this, pos, parentType, match);\n        return this;\n    }\n}\n\nexport { AddMarkStep, AddNodeMarkStep, AttrStep, DocAttrStep, MapResult, Mapping, RemoveMarkStep, RemoveNodeMarkStep, ReplaceAroundStep, ReplaceStep, Step, StepMap, StepResult, Transform, TransformError, canJoin, canSplit, dropPoint, findWrapping, insertPoint, joinPoint, liftTarget, replaceStep };\n", "import { Slice, Fragment, <PERSON>, Node } from 'prosemirror-model';\nimport { ReplaceStep, ReplaceAroundStep, Transform } from 'prosemirror-transform';\n\nconst classesById = Object.create(null);\n/**\nSuperclass for editor selections. Every selection type should\nextend this. Should not be instantiated directly.\n*/\nclass Selection {\n    /**\n    Initialize a selection with the head and anchor and ranges. If no\n    ranges are given, constructs a single range across `$anchor` and\n    `$head`.\n    */\n    constructor(\n    /**\n    The resolved anchor of the selection (the side that stays in\n    place when the selection is modified).\n    */\n    $anchor, \n    /**\n    The resolved head of the selection (the side that moves when\n    the selection is modified).\n    */\n    $head, ranges) {\n        this.$anchor = $anchor;\n        this.$head = $head;\n        this.ranges = ranges || [new SelectionRange($anchor.min($head), $anchor.max($head))];\n    }\n    /**\n    The selection's anchor, as an unresolved position.\n    */\n    get anchor() { return this.$anchor.pos; }\n    /**\n    The selection's head.\n    */\n    get head() { return this.$head.pos; }\n    /**\n    The lower bound of the selection's main range.\n    */\n    get from() { return this.$from.pos; }\n    /**\n    The upper bound of the selection's main range.\n    */\n    get to() { return this.$to.pos; }\n    /**\n    The resolved lower  bound of the selection's main range.\n    */\n    get $from() {\n        return this.ranges[0].$from;\n    }\n    /**\n    The resolved upper bound of the selection's main range.\n    */\n    get $to() {\n        return this.ranges[0].$to;\n    }\n    /**\n    Indicates whether the selection contains any content.\n    */\n    get empty() {\n        let ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++)\n            if (ranges[i].$from.pos != ranges[i].$to.pos)\n                return false;\n        return true;\n    }\n    /**\n    Get the content of this selection as a slice.\n    */\n    content() {\n        return this.$from.doc.slice(this.from, this.to, true);\n    }\n    /**\n    Replace the selection with a slice or, if no slice is given,\n    delete the selection. Will append to the given transaction.\n    */\n    replace(tr, content = Slice.empty) {\n        // Put the new selection at the position after the inserted\n        // content. When that ended in an inline node, search backwards,\n        // to get the position after that node. If not, search forward.\n        let lastNode = content.content.lastChild, lastParent = null;\n        for (let i = 0; i < content.openEnd; i++) {\n            lastParent = lastNode;\n            lastNode = lastNode.lastChild;\n        }\n        let mapFrom = tr.steps.length, ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++) {\n            let { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n            tr.replaceRange(mapping.map($from.pos), mapping.map($to.pos), i ? Slice.empty : content);\n            if (i == 0)\n                selectionToInsertionEnd(tr, mapFrom, (lastNode ? lastNode.isInline : lastParent && lastParent.isTextblock) ? -1 : 1);\n        }\n    }\n    /**\n    Replace the selection with the given node, appending the changes\n    to the given transaction.\n    */\n    replaceWith(tr, node) {\n        let mapFrom = tr.steps.length, ranges = this.ranges;\n        for (let i = 0; i < ranges.length; i++) {\n            let { $from, $to } = ranges[i], mapping = tr.mapping.slice(mapFrom);\n            let from = mapping.map($from.pos), to = mapping.map($to.pos);\n            if (i) {\n                tr.deleteRange(from, to);\n            }\n            else {\n                tr.replaceRangeWith(from, to, node);\n                selectionToInsertionEnd(tr, mapFrom, node.isInline ? -1 : 1);\n            }\n        }\n    }\n    /**\n    Find a valid cursor or leaf node selection starting at the given\n    position and searching back if `dir` is negative, and forward if\n    positive. When `textOnly` is true, only consider cursor\n    selections. Will return null when no valid selection position is\n    found.\n    */\n    static findFrom($pos, dir, textOnly = false) {\n        let inner = $pos.parent.inlineContent ? new TextSelection($pos)\n            : findSelectionIn($pos.node(0), $pos.parent, $pos.pos, $pos.index(), dir, textOnly);\n        if (inner)\n            return inner;\n        for (let depth = $pos.depth - 1; depth >= 0; depth--) {\n            let found = dir < 0\n                ? findSelectionIn($pos.node(0), $pos.node(depth), $pos.before(depth + 1), $pos.index(depth), dir, textOnly)\n                : findSelectionIn($pos.node(0), $pos.node(depth), $pos.after(depth + 1), $pos.index(depth) + 1, dir, textOnly);\n            if (found)\n                return found;\n        }\n        return null;\n    }\n    /**\n    Find a valid cursor or leaf node selection near the given\n    position. Searches forward first by default, but if `bias` is\n    negative, it will search backwards first.\n    */\n    static near($pos, bias = 1) {\n        return this.findFrom($pos, bias) || this.findFrom($pos, -bias) || new AllSelection($pos.node(0));\n    }\n    /**\n    Find the cursor or leaf node selection closest to the start of\n    the given document. Will return an\n    [`AllSelection`](https://prosemirror.net/docs/ref/#state.AllSelection) if no valid position\n    exists.\n    */\n    static atStart(doc) {\n        return findSelectionIn(doc, doc, 0, 0, 1) || new AllSelection(doc);\n    }\n    /**\n    Find the cursor or leaf node selection closest to the end of the\n    given document.\n    */\n    static atEnd(doc) {\n        return findSelectionIn(doc, doc, doc.content.size, doc.childCount, -1) || new AllSelection(doc);\n    }\n    /**\n    Deserialize the JSON representation of a selection. Must be\n    implemented for custom classes (as a static class method).\n    */\n    static fromJSON(doc, json) {\n        if (!json || !json.type)\n            throw new RangeError(\"Invalid input for Selection.fromJSON\");\n        let cls = classesById[json.type];\n        if (!cls)\n            throw new RangeError(`No selection type ${json.type} defined`);\n        return cls.fromJSON(doc, json);\n    }\n    /**\n    To be able to deserialize selections from JSON, custom selection\n    classes must register themselves with an ID string, so that they\n    can be disambiguated. Try to pick something that's unlikely to\n    clash with classes from other modules.\n    */\n    static jsonID(id, selectionClass) {\n        if (id in classesById)\n            throw new RangeError(\"Duplicate use of selection JSON ID \" + id);\n        classesById[id] = selectionClass;\n        selectionClass.prototype.jsonID = id;\n        return selectionClass;\n    }\n    /**\n    Get a [bookmark](https://prosemirror.net/docs/ref/#state.SelectionBookmark) for this selection,\n    which is a value that can be mapped without having access to a\n    current document, and later resolved to a real selection for a\n    given document again. (This is used mostly by the history to\n    track and restore old selections.) The default implementation of\n    this method just converts the selection to a text selection and\n    returns the bookmark for that.\n    */\n    getBookmark() {\n        return TextSelection.between(this.$anchor, this.$head).getBookmark();\n    }\n}\nSelection.prototype.visible = true;\n/**\nRepresents a selected range in a document.\n*/\nclass SelectionRange {\n    /**\n    Create a range.\n    */\n    constructor(\n    /**\n    The lower bound of the range.\n    */\n    $from, \n    /**\n    The upper bound of the range.\n    */\n    $to) {\n        this.$from = $from;\n        this.$to = $to;\n    }\n}\nlet warnedAboutTextSelection = false;\nfunction checkTextSelection($pos) {\n    if (!warnedAboutTextSelection && !$pos.parent.inlineContent) {\n        warnedAboutTextSelection = true;\n        console[\"warn\"](\"TextSelection endpoint not pointing into a node with inline content (\" + $pos.parent.type.name + \")\");\n    }\n}\n/**\nA text selection represents a classical editor selection, with a\nhead (the moving side) and anchor (immobile side), both of which\npoint into textblock nodes. It can be empty (a regular cursor\nposition).\n*/\nclass TextSelection extends Selection {\n    /**\n    Construct a text selection between the given points.\n    */\n    constructor($anchor, $head = $anchor) {\n        checkTextSelection($anchor);\n        checkTextSelection($head);\n        super($anchor, $head);\n    }\n    /**\n    Returns a resolved position if this is a cursor selection (an\n    empty text selection), and null otherwise.\n    */\n    get $cursor() { return this.$anchor.pos == this.$head.pos ? this.$head : null; }\n    map(doc, mapping) {\n        let $head = doc.resolve(mapping.map(this.head));\n        if (!$head.parent.inlineContent)\n            return Selection.near($head);\n        let $anchor = doc.resolve(mapping.map(this.anchor));\n        return new TextSelection($anchor.parent.inlineContent ? $anchor : $head, $head);\n    }\n    replace(tr, content = Slice.empty) {\n        super.replace(tr, content);\n        if (content == Slice.empty) {\n            let marks = this.$from.marksAcross(this.$to);\n            if (marks)\n                tr.ensureMarks(marks);\n        }\n    }\n    eq(other) {\n        return other instanceof TextSelection && other.anchor == this.anchor && other.head == this.head;\n    }\n    getBookmark() {\n        return new TextBookmark(this.anchor, this.head);\n    }\n    toJSON() {\n        return { type: \"text\", anchor: this.anchor, head: this.head };\n    }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.anchor != \"number\" || typeof json.head != \"number\")\n            throw new RangeError(\"Invalid input for TextSelection.fromJSON\");\n        return new TextSelection(doc.resolve(json.anchor), doc.resolve(json.head));\n    }\n    /**\n    Create a text selection from non-resolved positions.\n    */\n    static create(doc, anchor, head = anchor) {\n        let $anchor = doc.resolve(anchor);\n        return new this($anchor, head == anchor ? $anchor : doc.resolve(head));\n    }\n    /**\n    Return a text selection that spans the given positions or, if\n    they aren't text positions, find a text selection near them.\n    `bias` determines whether the method searches forward (default)\n    or backwards (negative number) first. Will fall back to calling\n    [`Selection.near`](https://prosemirror.net/docs/ref/#state.Selection^near) when the document\n    doesn't contain a valid text position.\n    */\n    static between($anchor, $head, bias) {\n        let dPos = $anchor.pos - $head.pos;\n        if (!bias || dPos)\n            bias = dPos >= 0 ? 1 : -1;\n        if (!$head.parent.inlineContent) {\n            let found = Selection.findFrom($head, bias, true) || Selection.findFrom($head, -bias, true);\n            if (found)\n                $head = found.$head;\n            else\n                return Selection.near($head, bias);\n        }\n        if (!$anchor.parent.inlineContent) {\n            if (dPos == 0) {\n                $anchor = $head;\n            }\n            else {\n                $anchor = (Selection.findFrom($anchor, -bias, true) || Selection.findFrom($anchor, bias, true)).$anchor;\n                if (($anchor.pos < $head.pos) != (dPos < 0))\n                    $anchor = $head;\n            }\n        }\n        return new TextSelection($anchor, $head);\n    }\n}\nSelection.jsonID(\"text\", TextSelection);\nclass TextBookmark {\n    constructor(anchor, head) {\n        this.anchor = anchor;\n        this.head = head;\n    }\n    map(mapping) {\n        return new TextBookmark(mapping.map(this.anchor), mapping.map(this.head));\n    }\n    resolve(doc) {\n        return TextSelection.between(doc.resolve(this.anchor), doc.resolve(this.head));\n    }\n}\n/**\nA node selection is a selection that points at a single node. All\nnodes marked [selectable](https://prosemirror.net/docs/ref/#model.NodeSpec.selectable) can be the\ntarget of a node selection. In such a selection, `from` and `to`\npoint directly before and after the selected node, `anchor` equals\n`from`, and `head` equals `to`..\n*/\nclass NodeSelection extends Selection {\n    /**\n    Create a node selection. Does not verify the validity of its\n    argument.\n    */\n    constructor($pos) {\n        let node = $pos.nodeAfter;\n        let $end = $pos.node(0).resolve($pos.pos + node.nodeSize);\n        super($pos, $end);\n        this.node = node;\n    }\n    map(doc, mapping) {\n        let { deleted, pos } = mapping.mapResult(this.anchor);\n        let $pos = doc.resolve(pos);\n        if (deleted)\n            return Selection.near($pos);\n        return new NodeSelection($pos);\n    }\n    content() {\n        return new Slice(Fragment.from(this.node), 0, 0);\n    }\n    eq(other) {\n        return other instanceof NodeSelection && other.anchor == this.anchor;\n    }\n    toJSON() {\n        return { type: \"node\", anchor: this.anchor };\n    }\n    getBookmark() { return new NodeBookmark(this.anchor); }\n    /**\n    @internal\n    */\n    static fromJSON(doc, json) {\n        if (typeof json.anchor != \"number\")\n            throw new RangeError(\"Invalid input for NodeSelection.fromJSON\");\n        return new NodeSelection(doc.resolve(json.anchor));\n    }\n    /**\n    Create a node selection from non-resolved positions.\n    */\n    static create(doc, from) {\n        return new NodeSelection(doc.resolve(from));\n    }\n    /**\n    Determines whether the given node may be selected as a node\n    selection.\n    */\n    static isSelectable(node) {\n        return !node.isText && node.type.spec.selectable !== false;\n    }\n}\nNodeSelection.prototype.visible = false;\nSelection.jsonID(\"node\", NodeSelection);\nclass NodeBookmark {\n    constructor(anchor) {\n        this.anchor = anchor;\n    }\n    map(mapping) {\n        let { deleted, pos } = mapping.mapResult(this.anchor);\n        return deleted ? new TextBookmark(pos, pos) : new NodeBookmark(pos);\n    }\n    resolve(doc) {\n        let $pos = doc.resolve(this.anchor), node = $pos.nodeAfter;\n        if (node && NodeSelection.isSelectable(node))\n            return new NodeSelection($pos);\n        return Selection.near($pos);\n    }\n}\n/**\nA selection type that represents selecting the whole document\n(which can not necessarily be expressed with a text selection, when\nthere are for example leaf block nodes at the start or end of the\ndocument).\n*/\nclass AllSelection extends Selection {\n    /**\n    Create an all-selection over the given document.\n    */\n    constructor(doc) {\n        super(doc.resolve(0), doc.resolve(doc.content.size));\n    }\n    replace(tr, content = Slice.empty) {\n        if (content == Slice.empty) {\n            tr.delete(0, tr.doc.content.size);\n            let sel = Selection.atStart(tr.doc);\n            if (!sel.eq(tr.selection))\n                tr.setSelection(sel);\n        }\n        else {\n            super.replace(tr, content);\n        }\n    }\n    toJSON() { return { type: \"all\" }; }\n    /**\n    @internal\n    */\n    static fromJSON(doc) { return new AllSelection(doc); }\n    map(doc) { return new AllSelection(doc); }\n    eq(other) { return other instanceof AllSelection; }\n    getBookmark() { return AllBookmark; }\n}\nSelection.jsonID(\"all\", AllSelection);\nconst AllBookmark = {\n    map() { return this; },\n    resolve(doc) { return new AllSelection(doc); }\n};\n// FIXME we'll need some awareness of text direction when scanning for selections\n// Try to find a selection inside the given node. `pos` points at the\n// position where the search starts. When `text` is true, only return\n// text selections.\nfunction findSelectionIn(doc, node, pos, index, dir, text = false) {\n    if (node.inlineContent)\n        return TextSelection.create(doc, pos);\n    for (let i = index - (dir > 0 ? 0 : 1); dir > 0 ? i < node.childCount : i >= 0; i += dir) {\n        let child = node.child(i);\n        if (!child.isAtom) {\n            let inner = findSelectionIn(doc, child, pos + dir, dir < 0 ? child.childCount : 0, dir, text);\n            if (inner)\n                return inner;\n        }\n        else if (!text && NodeSelection.isSelectable(child)) {\n            return NodeSelection.create(doc, pos - (dir < 0 ? child.nodeSize : 0));\n        }\n        pos += child.nodeSize * dir;\n    }\n    return null;\n}\nfunction selectionToInsertionEnd(tr, startLen, bias) {\n    let last = tr.steps.length - 1;\n    if (last < startLen)\n        return;\n    let step = tr.steps[last];\n    if (!(step instanceof ReplaceStep || step instanceof ReplaceAroundStep))\n        return;\n    let map = tr.mapping.maps[last], end;\n    map.forEach((_from, _to, _newFrom, newTo) => { if (end == null)\n        end = newTo; });\n    tr.setSelection(Selection.near(tr.doc.resolve(end), bias));\n}\n\nconst UPDATED_SEL = 1, UPDATED_MARKS = 2, UPDATED_SCROLL = 4;\n/**\nAn editor state transaction, which can be applied to a state to\ncreate an updated state. Use\n[`EditorState.tr`](https://prosemirror.net/docs/ref/#state.EditorState.tr) to create an instance.\n\nTransactions track changes to the document (they are a subclass of\n[`Transform`](https://prosemirror.net/docs/ref/#transform.Transform)), but also other state changes,\nlike selection updates and adjustments of the set of [stored\nmarks](https://prosemirror.net/docs/ref/#state.EditorState.storedMarks). In addition, you can store\nmetadata properties in a transaction, which are extra pieces of\ninformation that client code or plugins can use to describe what a\ntransaction represents, so that they can update their [own\nstate](https://prosemirror.net/docs/ref/#state.StateField) accordingly.\n\nThe [editor view](https://prosemirror.net/docs/ref/#view.EditorView) uses a few metadata\nproperties: it will attach a property `\"pointer\"` with the value\n`true` to selection transactions directly caused by mouse or touch\ninput, a `\"composition\"` property holding an ID identifying the\ncomposition that caused it to transactions caused by composed DOM\ninput, and a `\"uiEvent\"` property of that may be `\"paste\"`,\n`\"cut\"`, or `\"drop\"`.\n*/\nclass Transaction extends Transform {\n    /**\n    @internal\n    */\n    constructor(state) {\n        super(state.doc);\n        // The step count for which the current selection is valid.\n        this.curSelectionFor = 0;\n        // Bitfield to track which aspects of the state were updated by\n        // this transaction.\n        this.updated = 0;\n        // Object used to store metadata properties for the transaction.\n        this.meta = Object.create(null);\n        this.time = Date.now();\n        this.curSelection = state.selection;\n        this.storedMarks = state.storedMarks;\n    }\n    /**\n    The transaction's current selection. This defaults to the editor\n    selection [mapped](https://prosemirror.net/docs/ref/#state.Selection.map) through the steps in the\n    transaction, but can be overwritten with\n    [`setSelection`](https://prosemirror.net/docs/ref/#state.Transaction.setSelection).\n    */\n    get selection() {\n        if (this.curSelectionFor < this.steps.length) {\n            this.curSelection = this.curSelection.map(this.doc, this.mapping.slice(this.curSelectionFor));\n            this.curSelectionFor = this.steps.length;\n        }\n        return this.curSelection;\n    }\n    /**\n    Update the transaction's current selection. Will determine the\n    selection that the editor gets when the transaction is applied.\n    */\n    setSelection(selection) {\n        if (selection.$from.doc != this.doc)\n            throw new RangeError(\"Selection passed to setSelection must point at the current document\");\n        this.curSelection = selection;\n        this.curSelectionFor = this.steps.length;\n        this.updated = (this.updated | UPDATED_SEL) & ~UPDATED_MARKS;\n        this.storedMarks = null;\n        return this;\n    }\n    /**\n    Whether the selection was explicitly updated by this transaction.\n    */\n    get selectionSet() {\n        return (this.updated & UPDATED_SEL) > 0;\n    }\n    /**\n    Set the current stored marks.\n    */\n    setStoredMarks(marks) {\n        this.storedMarks = marks;\n        this.updated |= UPDATED_MARKS;\n        return this;\n    }\n    /**\n    Make sure the current stored marks or, if that is null, the marks\n    at the selection, match the given set of marks. Does nothing if\n    this is already the case.\n    */\n    ensureMarks(marks) {\n        if (!Mark.sameSet(this.storedMarks || this.selection.$from.marks(), marks))\n            this.setStoredMarks(marks);\n        return this;\n    }\n    /**\n    Add a mark to the set of stored marks.\n    */\n    addStoredMark(mark) {\n        return this.ensureMarks(mark.addToSet(this.storedMarks || this.selection.$head.marks()));\n    }\n    /**\n    Remove a mark or mark type from the set of stored marks.\n    */\n    removeStoredMark(mark) {\n        return this.ensureMarks(mark.removeFromSet(this.storedMarks || this.selection.$head.marks()));\n    }\n    /**\n    Whether the stored marks were explicitly set for this transaction.\n    */\n    get storedMarksSet() {\n        return (this.updated & UPDATED_MARKS) > 0;\n    }\n    /**\n    @internal\n    */\n    addStep(step, doc) {\n        super.addStep(step, doc);\n        this.updated = this.updated & ~UPDATED_MARKS;\n        this.storedMarks = null;\n    }\n    /**\n    Update the timestamp for the transaction.\n    */\n    setTime(time) {\n        this.time = time;\n        return this;\n    }\n    /**\n    Replace the current selection with the given slice.\n    */\n    replaceSelection(slice) {\n        this.selection.replace(this, slice);\n        return this;\n    }\n    /**\n    Replace the selection with the given node. When `inheritMarks` is\n    true and the content is inline, it inherits the marks from the\n    place where it is inserted.\n    */\n    replaceSelectionWith(node, inheritMarks = true) {\n        let selection = this.selection;\n        if (inheritMarks)\n            node = node.mark(this.storedMarks || (selection.empty ? selection.$from.marks() : (selection.$from.marksAcross(selection.$to) || Mark.none)));\n        selection.replaceWith(this, node);\n        return this;\n    }\n    /**\n    Delete the selection.\n    */\n    deleteSelection() {\n        this.selection.replace(this);\n        return this;\n    }\n    /**\n    Replace the given range, or the selection if no range is given,\n    with a text node containing the given string.\n    */\n    insertText(text, from, to) {\n        let schema = this.doc.type.schema;\n        if (from == null) {\n            if (!text)\n                return this.deleteSelection();\n            return this.replaceSelectionWith(schema.text(text), true);\n        }\n        else {\n            if (to == null)\n                to = from;\n            to = to == null ? from : to;\n            if (!text)\n                return this.deleteRange(from, to);\n            let marks = this.storedMarks;\n            if (!marks) {\n                let $from = this.doc.resolve(from);\n                marks = to == from ? $from.marks() : $from.marksAcross(this.doc.resolve(to));\n            }\n            this.replaceRangeWith(from, to, schema.text(text, marks));\n            if (!this.selection.empty)\n                this.setSelection(Selection.near(this.selection.$to));\n            return this;\n        }\n    }\n    /**\n    Store a metadata property in this transaction, keyed either by\n    name or by plugin.\n    */\n    setMeta(key, value) {\n        this.meta[typeof key == \"string\" ? key : key.key] = value;\n        return this;\n    }\n    /**\n    Retrieve a metadata property for a given name or plugin.\n    */\n    getMeta(key) {\n        return this.meta[typeof key == \"string\" ? key : key.key];\n    }\n    /**\n    Returns true if this transaction doesn't contain any metadata,\n    and can thus safely be extended.\n    */\n    get isGeneric() {\n        for (let _ in this.meta)\n            return false;\n        return true;\n    }\n    /**\n    Indicate that the editor should scroll the selection into view\n    when updated to the state produced by this transaction.\n    */\n    scrollIntoView() {\n        this.updated |= UPDATED_SCROLL;\n        return this;\n    }\n    /**\n    True when this transaction has had `scrollIntoView` called on it.\n    */\n    get scrolledIntoView() {\n        return (this.updated & UPDATED_SCROLL) > 0;\n    }\n}\n\nfunction bind(f, self) {\n    return !self || !f ? f : f.bind(self);\n}\nclass FieldDesc {\n    constructor(name, desc, self) {\n        this.name = name;\n        this.init = bind(desc.init, self);\n        this.apply = bind(desc.apply, self);\n    }\n}\nconst baseFields = [\n    new FieldDesc(\"doc\", {\n        init(config) { return config.doc || config.schema.topNodeType.createAndFill(); },\n        apply(tr) { return tr.doc; }\n    }),\n    new FieldDesc(\"selection\", {\n        init(config, instance) { return config.selection || Selection.atStart(instance.doc); },\n        apply(tr) { return tr.selection; }\n    }),\n    new FieldDesc(\"storedMarks\", {\n        init(config) { return config.storedMarks || null; },\n        apply(tr, _marks, _old, state) { return state.selection.$cursor ? tr.storedMarks : null; }\n    }),\n    new FieldDesc(\"scrollToSelection\", {\n        init() { return 0; },\n        apply(tr, prev) { return tr.scrolledIntoView ? prev + 1 : prev; }\n    })\n];\n// Object wrapping the part of a state object that stays the same\n// across transactions. Stored in the state's `config` property.\nclass Configuration {\n    constructor(schema, plugins) {\n        this.schema = schema;\n        this.plugins = [];\n        this.pluginsByKey = Object.create(null);\n        this.fields = baseFields.slice();\n        if (plugins)\n            plugins.forEach(plugin => {\n                if (this.pluginsByKey[plugin.key])\n                    throw new RangeError(\"Adding different instances of a keyed plugin (\" + plugin.key + \")\");\n                this.plugins.push(plugin);\n                this.pluginsByKey[plugin.key] = plugin;\n                if (plugin.spec.state)\n                    this.fields.push(new FieldDesc(plugin.key, plugin.spec.state, plugin));\n            });\n    }\n}\n/**\nThe state of a ProseMirror editor is represented by an object of\nthis type. A state is a persistent data structure—it isn't\nupdated, but rather a new state value is computed from an old one\nusing the [`apply`](https://prosemirror.net/docs/ref/#state.EditorState.apply) method.\n\nA state holds a number of built-in fields, and plugins can\n[define](https://prosemirror.net/docs/ref/#state.PluginSpec.state) additional fields.\n*/\nclass EditorState {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    config) {\n        this.config = config;\n    }\n    /**\n    The schema of the state's document.\n    */\n    get schema() {\n        return this.config.schema;\n    }\n    /**\n    The plugins that are active in this state.\n    */\n    get plugins() {\n        return this.config.plugins;\n    }\n    /**\n    Apply the given transaction to produce a new state.\n    */\n    apply(tr) {\n        return this.applyTransaction(tr).state;\n    }\n    /**\n    @internal\n    */\n    filterTransaction(tr, ignore = -1) {\n        for (let i = 0; i < this.config.plugins.length; i++)\n            if (i != ignore) {\n                let plugin = this.config.plugins[i];\n                if (plugin.spec.filterTransaction && !plugin.spec.filterTransaction.call(plugin, tr, this))\n                    return false;\n            }\n        return true;\n    }\n    /**\n    Verbose variant of [`apply`](https://prosemirror.net/docs/ref/#state.EditorState.apply) that\n    returns the precise transactions that were applied (which might\n    be influenced by the [transaction\n    hooks](https://prosemirror.net/docs/ref/#state.PluginSpec.filterTransaction) of\n    plugins) along with the new state.\n    */\n    applyTransaction(rootTr) {\n        if (!this.filterTransaction(rootTr))\n            return { state: this, transactions: [] };\n        let trs = [rootTr], newState = this.applyInner(rootTr), seen = null;\n        // This loop repeatedly gives plugins a chance to respond to\n        // transactions as new transactions are added, making sure to only\n        // pass the transactions the plugin did not see before.\n        for (;;) {\n            let haveNew = false;\n            for (let i = 0; i < this.config.plugins.length; i++) {\n                let plugin = this.config.plugins[i];\n                if (plugin.spec.appendTransaction) {\n                    let n = seen ? seen[i].n : 0, oldState = seen ? seen[i].state : this;\n                    let tr = n < trs.length &&\n                        plugin.spec.appendTransaction.call(plugin, n ? trs.slice(n) : trs, oldState, newState);\n                    if (tr && newState.filterTransaction(tr, i)) {\n                        tr.setMeta(\"appendedTransaction\", rootTr);\n                        if (!seen) {\n                            seen = [];\n                            for (let j = 0; j < this.config.plugins.length; j++)\n                                seen.push(j < i ? { state: newState, n: trs.length } : { state: this, n: 0 });\n                        }\n                        trs.push(tr);\n                        newState = newState.applyInner(tr);\n                        haveNew = true;\n                    }\n                    if (seen)\n                        seen[i] = { state: newState, n: trs.length };\n                }\n            }\n            if (!haveNew)\n                return { state: newState, transactions: trs };\n        }\n    }\n    /**\n    @internal\n    */\n    applyInner(tr) {\n        if (!tr.before.eq(this.doc))\n            throw new RangeError(\"Applying a mismatched transaction\");\n        let newInstance = new EditorState(this.config), fields = this.config.fields;\n        for (let i = 0; i < fields.length; i++) {\n            let field = fields[i];\n            newInstance[field.name] = field.apply(tr, this[field.name], this, newInstance);\n        }\n        return newInstance;\n    }\n    /**\n    Start a [transaction](https://prosemirror.net/docs/ref/#state.Transaction) from this state.\n    */\n    get tr() { return new Transaction(this); }\n    /**\n    Create a new state.\n    */\n    static create(config) {\n        let $config = new Configuration(config.doc ? config.doc.type.schema : config.schema, config.plugins);\n        let instance = new EditorState($config);\n        for (let i = 0; i < $config.fields.length; i++)\n            instance[$config.fields[i].name] = $config.fields[i].init(config, instance);\n        return instance;\n    }\n    /**\n    Create a new state based on this one, but with an adjusted set\n    of active plugins. State fields that exist in both sets of\n    plugins are kept unchanged. Those that no longer exist are\n    dropped, and those that are new are initialized using their\n    [`init`](https://prosemirror.net/docs/ref/#state.StateField.init) method, passing in the new\n    configuration object..\n    */\n    reconfigure(config) {\n        let $config = new Configuration(this.schema, config.plugins);\n        let fields = $config.fields, instance = new EditorState($config);\n        for (let i = 0; i < fields.length; i++) {\n            let name = fields[i].name;\n            instance[name] = this.hasOwnProperty(name) ? this[name] : fields[i].init(config, instance);\n        }\n        return instance;\n    }\n    /**\n    Serialize this state to JSON. If you want to serialize the state\n    of plugins, pass an object mapping property names to use in the\n    resulting JSON object to plugin objects. The argument may also be\n    a string or number, in which case it is ignored, to support the\n    way `JSON.stringify` calls `toString` methods.\n    */\n    toJSON(pluginFields) {\n        let result = { doc: this.doc.toJSON(), selection: this.selection.toJSON() };\n        if (this.storedMarks)\n            result.storedMarks = this.storedMarks.map(m => m.toJSON());\n        if (pluginFields && typeof pluginFields == 'object')\n            for (let prop in pluginFields) {\n                if (prop == \"doc\" || prop == \"selection\")\n                    throw new RangeError(\"The JSON fields `doc` and `selection` are reserved\");\n                let plugin = pluginFields[prop], state = plugin.spec.state;\n                if (state && state.toJSON)\n                    result[prop] = state.toJSON.call(plugin, this[plugin.key]);\n            }\n        return result;\n    }\n    /**\n    Deserialize a JSON representation of a state. `config` should\n    have at least a `schema` field, and should contain array of\n    plugins to initialize the state with. `pluginFields` can be used\n    to deserialize the state of plugins, by associating plugin\n    instances with the property names they use in the JSON object.\n    */\n    static fromJSON(config, json, pluginFields) {\n        if (!json)\n            throw new RangeError(\"Invalid input for EditorState.fromJSON\");\n        if (!config.schema)\n            throw new RangeError(\"Required config field 'schema' missing\");\n        let $config = new Configuration(config.schema, config.plugins);\n        let instance = new EditorState($config);\n        $config.fields.forEach(field => {\n            if (field.name == \"doc\") {\n                instance.doc = Node.fromJSON(config.schema, json.doc);\n            }\n            else if (field.name == \"selection\") {\n                instance.selection = Selection.fromJSON(instance.doc, json.selection);\n            }\n            else if (field.name == \"storedMarks\") {\n                if (json.storedMarks)\n                    instance.storedMarks = json.storedMarks.map(config.schema.markFromJSON);\n            }\n            else {\n                if (pluginFields)\n                    for (let prop in pluginFields) {\n                        let plugin = pluginFields[prop], state = plugin.spec.state;\n                        if (plugin.key == field.name && state && state.fromJSON &&\n                            Object.prototype.hasOwnProperty.call(json, prop)) {\n                            instance[field.name] = state.fromJSON.call(plugin, config, json[prop], instance);\n                            return;\n                        }\n                    }\n                instance[field.name] = field.init(config, instance);\n            }\n        });\n        return instance;\n    }\n}\n\nfunction bindProps(obj, self, target) {\n    for (let prop in obj) {\n        let val = obj[prop];\n        if (val instanceof Function)\n            val = val.bind(self);\n        else if (prop == \"handleDOMEvents\")\n            val = bindProps(val, self, {});\n        target[prop] = val;\n    }\n    return target;\n}\n/**\nPlugins bundle functionality that can be added to an editor.\nThey are part of the [editor state](https://prosemirror.net/docs/ref/#state.EditorState) and\nmay influence that state and the view that contains it.\n*/\nclass Plugin {\n    /**\n    Create a plugin.\n    */\n    constructor(\n    /**\n    The plugin's [spec object](https://prosemirror.net/docs/ref/#state.PluginSpec).\n    */\n    spec) {\n        this.spec = spec;\n        /**\n        The [props](https://prosemirror.net/docs/ref/#view.EditorProps) exported by this plugin.\n        */\n        this.props = {};\n        if (spec.props)\n            bindProps(spec.props, this, this.props);\n        this.key = spec.key ? spec.key.key : createKey(\"plugin\");\n    }\n    /**\n    Extract the plugin's state field from an editor state.\n    */\n    getState(state) { return state[this.key]; }\n}\nconst keys = Object.create(null);\nfunction createKey(name) {\n    if (name in keys)\n        return name + \"$\" + ++keys[name];\n    keys[name] = 0;\n    return name + \"$\";\n}\n/**\nA key is used to [tag](https://prosemirror.net/docs/ref/#state.PluginSpec.key) plugins in a way\nthat makes it possible to find them, given an editor state.\nAssigning a key does mean only one plugin of that type can be\nactive in a state.\n*/\nclass PluginKey {\n    /**\n    Create a plugin key.\n    */\n    constructor(name = \"key\") { this.key = createKey(name); }\n    /**\n    Get the active plugin with this key, if any, from an editor\n    state.\n    */\n    get(state) { return state.config.pluginsByKey[this.key]; }\n    /**\n    Get the plugin's state from an editor state.\n    */\n    getState(state) { return state[this.key]; }\n}\n\nexport { AllSelection, EditorState, NodeSelection, Plugin, PluginKey, Selection, SelectionRange, TextSelection, Transaction };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAWA,IAAM,UAAU;AAChB,IAAM,WAAW,KAAK,IAAI,GAAG,EAAE;AAC/B,SAAS,YAAY,OAAO,QAAQ;AAAE,SAAO,QAAQ,SAAS;AAAU;AACxE,SAAS,aAAa,OAAO;AAAE,SAAO,QAAQ;AAAS;AACvD,SAAS,cAAc,OAAO;AAAE,UAAQ,SAAS,QAAQ,YAAY;AAAU;AAC/E,IAAM,aAAa;AAAnB,IAAsB,YAAY;AAAlC,IAAqC,aAAa;AAAlD,IAAqD,WAAW;AAKhE,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA,EAIZ,YAIA,KAIA,SAIA,SAAS;AACL,SAAK,MAAM;AACX,SAAK,UAAU;AACf,SAAK,UAAU;AAAA,EACnB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,IAAI,UAAU;AAAE,YAAQ,KAAK,UAAU,YAAY;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAItD,IAAI,gBAAgB;AAAE,YAAQ,KAAK,WAAW,aAAa,eAAe;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAI7E,IAAI,eAAe;AAAE,YAAQ,KAAK,WAAW,YAAY,eAAe;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM3E,IAAI,gBAAgB;AAAE,YAAQ,KAAK,UAAU,cAAc;AAAA,EAAG;AAClE;AAOA,IAAM,UAAN,MAAM,SAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMV,YAIA,QAIA,WAAW,OAAO;AACd,SAAK,SAAS;AACd,SAAK,WAAW;AAChB,QAAI,CAAC,OAAO,UAAU,SAAQ;AAC1B,aAAO,SAAQ;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,OAAO;AACX,QAAI,OAAO,GAAG,QAAQ,aAAa,KAAK;AACxC,QAAI,CAAC,KAAK;AACN,eAAS,IAAI,GAAG,IAAI,OAAO;AACvB,gBAAQ,KAAK,OAAO,IAAI,IAAI,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC;AAC9D,WAAO,KAAK,OAAO,QAAQ,CAAC,IAAI,OAAO,cAAc,KAAK;AAAA,EAC9D;AAAA,EACA,UAAU,KAAK,QAAQ,GAAG;AAAE,WAAO,KAAK,KAAK,KAAK,OAAO,KAAK;AAAA,EAAG;AAAA,EACjE,IAAI,KAAK,QAAQ,GAAG;AAAE,WAAO,KAAK,KAAK,KAAK,OAAO,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAI1D,KAAK,KAAK,OAAO,QAAQ;AACrB,QAAI,OAAO,GAAG,WAAW,KAAK,WAAW,IAAI,GAAG,WAAW,KAAK,WAAW,IAAI;AAC/E,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC5C,UAAI,QAAQ,KAAK,OAAO,CAAC,KAAK,KAAK,WAAW,OAAO;AACrD,UAAI,QAAQ;AACR;AACJ,UAAI,UAAU,KAAK,OAAO,IAAI,QAAQ,GAAG,UAAU,KAAK,OAAO,IAAI,QAAQ,GAAG,MAAM,QAAQ;AAC5F,UAAI,OAAO,KAAK;AACZ,YAAI,OAAO,CAAC,UAAU,QAAQ,OAAO,QAAQ,KAAK,OAAO,MAAM,IAAI;AACnE,YAAI,SAAS,QAAQ,QAAQ,OAAO,IAAI,IAAI;AAC5C,YAAI;AACA,iBAAO;AACX,YAAI,UAAU,QAAQ,QAAQ,IAAI,QAAQ,OAAO,OAAO,YAAY,IAAI,GAAG,MAAM,KAAK;AACtF,YAAI,MAAM,OAAO,QAAQ,YAAY,OAAO,MAAM,aAAa;AAC/D,YAAI,QAAQ,IAAI,OAAO,QAAQ,OAAO;AAClC,iBAAO;AACX,eAAO,IAAI,UAAU,QAAQ,KAAK,OAAO;AAAA,MAC7C;AACA,cAAQ,UAAU;AAAA,IACtB;AACA,WAAO,SAAS,MAAM,OAAO,IAAI,UAAU,MAAM,MAAM,GAAG,IAAI;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,KAAK,SAAS;AAClB,QAAI,OAAO,GAAG,QAAQ,aAAa,OAAO;AAC1C,QAAI,WAAW,KAAK,WAAW,IAAI,GAAG,WAAW,KAAK,WAAW,IAAI;AACrE,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK,GAAG;AAC5C,UAAI,QAAQ,KAAK,OAAO,CAAC,KAAK,KAAK,WAAW,OAAO;AACrD,UAAI,QAAQ;AACR;AACJ,UAAI,UAAU,KAAK,OAAO,IAAI,QAAQ,GAAG,MAAM,QAAQ;AACvD,UAAI,OAAO,OAAO,KAAK,QAAQ;AAC3B,eAAO;AACX,cAAQ,KAAK,OAAO,IAAI,QAAQ,IAAI;AAAA,IACxC;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,GAAG;AACP,QAAI,WAAW,KAAK,WAAW,IAAI,GAAG,WAAW,KAAK,WAAW,IAAI;AACrE,aAAS,IAAI,GAAG,OAAO,GAAG,IAAI,KAAK,OAAO,QAAQ,KAAK,GAAG;AACtD,UAAI,QAAQ,KAAK,OAAO,CAAC,GAAG,WAAW,SAAS,KAAK,WAAW,OAAO,IAAI,WAAW,SAAS,KAAK,WAAW,IAAI;AACnH,UAAI,UAAU,KAAK,OAAO,IAAI,QAAQ,GAAG,UAAU,KAAK,OAAO,IAAI,QAAQ;AAC3E,QAAE,UAAU,WAAW,SAAS,UAAU,WAAW,OAAO;AAC5D,cAAQ,UAAU;AAAA,IACtB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,SAAS;AACL,WAAO,IAAI,SAAQ,KAAK,QAAQ,CAAC,KAAK,QAAQ;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW;AACP,YAAQ,KAAK,WAAW,MAAM,MAAM,KAAK,UAAU,KAAK,MAAM;AAAA,EAClE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,OAAO,GAAG;AACb,WAAO,KAAK,IAAI,SAAQ,QAAQ,IAAI,SAAQ,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC;AAAA,EAC9E;AACJ;AAIA,QAAQ,QAAQ,IAAI,QAAQ,CAAC,CAAC;AAS9B,IAAM,UAAN,MAAM,SAAQ;AAAA;AAAA;AAAA;AAAA,EAIV,YAAY,MAIZ,QAKA,OAAO,GAIP,KAAK,OAAO,KAAK,SAAS,GAAG;AACzB,SAAK,SAAS;AACd,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,QAAQ,QAAQ,CAAC;AACtB,SAAK,UAAU,EAAE,QAAQ;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,OAAO;AAAE,WAAO,KAAK;AAAA,EAAO;AAAA;AAAA;AAAA;AAAA,EAIhC,MAAM,OAAO,GAAG,KAAK,KAAK,KAAK,QAAQ;AACnC,WAAO,IAAI,SAAQ,KAAK,OAAO,KAAK,QAAQ,MAAM,EAAE;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,KAAK,SAAS;AACpB,QAAI,CAAC,KAAK,SAAS;AACf,WAAK,QAAQ,KAAK,MAAM,MAAM;AAC9B,WAAK,SAAS,KAAK,UAAU,KAAK,OAAO,MAAM;AAC/C,WAAK,UAAU;AAAA,IACnB;AACA,SAAK,KAAK,KAAK,MAAM,KAAK,GAAG;AAC7B,QAAI,WAAW;AACX,WAAK,UAAU,KAAK,MAAM,SAAS,GAAG,OAAO;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,SAAS;AACnB,aAAS,IAAI,GAAG,YAAY,KAAK,MAAM,QAAQ,IAAI,QAAQ,MAAM,QAAQ,KAAK;AAC1E,UAAI,OAAO,QAAQ,UAAU,CAAC;AAC9B,WAAK,UAAU,QAAQ,MAAM,CAAC,GAAG,QAAQ,QAAQ,OAAO,IAAI,YAAY,OAAO,MAAS;AAAA,IAC5F;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAAU,GAAG;AACT,QAAI,KAAK;AACL,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ;AACpC,YAAI,KAAK,OAAO,CAAC,KAAK;AAClB,iBAAO,KAAK,OAAO,KAAK,IAAI,IAAI,KAAK,EAAE;AAAA;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU,GAAG,GAAG;AACZ,QAAI,CAAC,KAAK;AACN,WAAK,SAAS,CAAC;AACnB,SAAK,OAAO,KAAK,GAAG,CAAC;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAIA,sBAAsB,SAAS;AAC3B,aAAS,IAAI,QAAQ,KAAK,SAAS,GAAG,YAAY,KAAK,MAAM,SAAS,QAAQ,MAAM,QAAQ,KAAK,GAAG,KAAK;AACrG,UAAI,OAAO,QAAQ,UAAU,CAAC;AAC9B,WAAK,UAAU,QAAQ,MAAM,CAAC,EAAE,OAAO,GAAG,QAAQ,QAAQ,OAAO,IAAI,YAAY,OAAO,IAAI,MAAS;AAAA,IACzG;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS;AACL,QAAI,UAAU,IAAI;AAClB,YAAQ,sBAAsB,IAAI;AAClC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,KAAK,QAAQ,GAAG;AAChB,QAAI,KAAK;AACL,aAAO,KAAK,KAAK,KAAK,OAAO,IAAI;AACrC,aAAS,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI;AACjC,YAAM,KAAK,MAAM,CAAC,EAAE,IAAI,KAAK,KAAK;AACtC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,KAAK,QAAQ,GAAG;AAAE,WAAO,KAAK,KAAK,KAAK,OAAO,KAAK;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIjE,KAAK,KAAK,OAAO,QAAQ;AACrB,QAAI,UAAU;AACd,aAAS,IAAI,KAAK,MAAM,IAAI,KAAK,IAAI,KAAK;AACtC,UAAI,MAAM,KAAK,MAAM,CAAC,GAAG,SAAS,IAAI,UAAU,KAAK,KAAK;AAC1D,UAAI,OAAO,WAAW,MAAM;AACxB,YAAI,OAAO,KAAK,UAAU,CAAC;AAC3B,YAAI,QAAQ,QAAQ,OAAO,KAAK,OAAO,KAAK,IAAI;AAC5C,cAAI;AACJ,gBAAM,KAAK,MAAM,IAAI,EAAE,QAAQ,OAAO,OAAO;AAC7C;AAAA,QACJ;AAAA,MACJ;AACA,iBAAW,OAAO;AAClB,YAAM,OAAO;AAAA,IACjB;AACA,WAAO,SAAS,MAAM,IAAI,UAAU,KAAK,SAAS,IAAI;AAAA,EAC1D;AACJ;AAEA,IAAM,YAAY,uBAAO,OAAO,IAAI;AAYpC,IAAM,OAAN,MAAW;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMP,SAAS;AAAE,WAAO,QAAQ;AAAA,EAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMjC,MAAM,OAAO;AAAE,WAAO;AAAA,EAAM;AAAA;AAAA;AAAA;AAAA;AAAA,EAK5B,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,CAAC,QAAQ,CAAC,KAAK;AACf,YAAM,IAAI,WAAW,iCAAiC;AAC1D,QAAI,OAAO,UAAU,KAAK,QAAQ;AAClC,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,gBAAgB,KAAK,QAAQ,UAAU;AAChE,WAAO,KAAK,SAAS,QAAQ,IAAI;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO,IAAI,WAAW;AACzB,QAAI,MAAM;AACN,YAAM,IAAI,WAAW,mCAAmC,EAAE;AAC9D,cAAU,EAAE,IAAI;AAChB,cAAU,UAAU,SAAS;AAC7B,WAAO;AAAA,EACX;AACJ;AAKA,IAAM,aAAN,MAAM,YAAW;AAAA;AAAA;AAAA;AAAA,EAIb,YAIA,KAIA,QAAQ;AACJ,SAAK,MAAM;AACX,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,GAAG,KAAK;AAAE,WAAO,IAAI,YAAW,KAAK,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAInD,OAAO,KAAK,SAAS;AAAE,WAAO,IAAI,YAAW,MAAM,OAAO;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM7D,OAAO,YAAY,KAAK,MAAM,IAAI,OAAO;AACrC,QAAI;AACA,aAAO,YAAW,GAAG,IAAI,QAAQ,MAAM,IAAI,KAAK,CAAC;AAAA,IACrD,SACO,GAAG;AACN,UAAI,aAAa;AACb,eAAO,YAAW,KAAK,EAAE,OAAO;AACpC,YAAM;AAAA,IACV;AAAA,EACJ;AACJ;AAEA,SAAS,YAAY,UAAU,GAAG,QAAQ;AACtC,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,GAAG,IAAI,SAAS,YAAY,KAAK;AAC1C,QAAI,QAAQ,SAAS,MAAM,CAAC;AAC5B,QAAI,MAAM,QAAQ;AACd,cAAQ,MAAM,KAAK,YAAY,MAAM,SAAS,GAAG,KAAK,CAAC;AAC3D,QAAI,MAAM;AACN,cAAQ,EAAE,OAAO,QAAQ,CAAC;AAC9B,WAAO,KAAK,KAAK;AAAA,EACrB;AACA,SAAO,SAAS,UAAU,MAAM;AACpC;AAIA,IAAM,cAAN,MAAM,qBAAoB,KAAK;AAAA;AAAA;AAAA;AAAA,EAI3B,YAIA,MAIA,IAIA,MAAM;AACF,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,KAAK;AACP,QAAI,WAAW,IAAI,MAAM,KAAK,MAAM,KAAK,EAAE,GAAG,QAAQ,IAAI,QAAQ,KAAK,IAAI;AAC3E,QAAI,SAAS,MAAM,KAAK,MAAM,YAAY,KAAK,EAAE,CAAC;AAClD,QAAI,QAAQ,IAAI,MAAM,YAAY,SAAS,SAAS,CAAC,MAAMC,YAAW;AAClE,UAAI,CAAC,KAAK,UAAU,CAACA,QAAO,KAAK,eAAe,KAAK,KAAK,IAAI;AAC1D,eAAO;AACX,aAAO,KAAK,KAAK,KAAK,KAAK,SAAS,KAAK,KAAK,CAAC;AAAA,IACnD,GAAG,MAAM,GAAG,SAAS,WAAW,SAAS,OAAO;AAChD,WAAO,WAAW,YAAY,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK;AAAA,EAChE;AAAA,EACA,SAAS;AACL,WAAO,IAAI,eAAe,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AAAA,EAC3D;AAAA,EACA,IAAI,SAAS;AACT,QAAI,OAAO,QAAQ,UAAU,KAAK,MAAM,CAAC,GAAG,KAAK,QAAQ,UAAU,KAAK,IAAI,EAAE;AAC9E,QAAI,KAAK,WAAW,GAAG,WAAW,KAAK,OAAO,GAAG;AAC7C,aAAO;AACX,WAAO,IAAI,aAAY,KAAK,KAAK,GAAG,KAAK,KAAK,IAAI;AAAA,EACtD;AAAA,EACA,MAAM,OAAO;AACT,QAAI,iBAAiB,gBACjB,MAAM,KAAK,GAAG,KAAK,IAAI,KACvB,KAAK,QAAQ,MAAM,MAAM,KAAK,MAAM,MAAM;AAC1C,aAAO,IAAI,aAAY,KAAK,IAAI,KAAK,MAAM,MAAM,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE,GAAG,KAAK,IAAI;AAClG,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO;AAAA,MAAE,UAAU;AAAA,MAAW,MAAM,KAAK,KAAK,OAAO;AAAA,MACjD,MAAM,KAAK;AAAA,MAAM,IAAI,KAAK;AAAA,IAAG;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,QAAQ,YAAY,OAAO,KAAK,MAAM;AAClD,YAAM,IAAI,WAAW,wCAAwC;AACjE,WAAO,IAAI,aAAY,KAAK,MAAM,KAAK,IAAI,OAAO,aAAa,KAAK,IAAI,CAAC;AAAA,EAC7E;AACJ;AACA,KAAK,OAAO,WAAW,WAAW;AAIlC,IAAM,iBAAN,MAAM,wBAAuB,KAAK;AAAA;AAAA;AAAA;AAAA,EAI9B,YAIA,MAIA,IAIA,MAAM;AACF,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,KAAK;AACP,QAAI,WAAW,IAAI,MAAM,KAAK,MAAM,KAAK,EAAE;AAC3C,QAAI,QAAQ,IAAI,MAAM,YAAY,SAAS,SAAS,UAAQ;AACxD,aAAO,KAAK,KAAK,KAAK,KAAK,cAAc,KAAK,KAAK,CAAC;AAAA,IACxD,GAAG,GAAG,GAAG,SAAS,WAAW,SAAS,OAAO;AAC7C,WAAO,WAAW,YAAY,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK;AAAA,EAChE;AAAA,EACA,SAAS;AACL,WAAO,IAAI,YAAY,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI;AAAA,EACxD;AAAA,EACA,IAAI,SAAS;AACT,QAAI,OAAO,QAAQ,UAAU,KAAK,MAAM,CAAC,GAAG,KAAK,QAAQ,UAAU,KAAK,IAAI,EAAE;AAC9E,QAAI,KAAK,WAAW,GAAG,WAAW,KAAK,OAAO,GAAG;AAC7C,aAAO;AACX,WAAO,IAAI,gBAAe,KAAK,KAAK,GAAG,KAAK,KAAK,IAAI;AAAA,EACzD;AAAA,EACA,MAAM,OAAO;AACT,QAAI,iBAAiB,mBACjB,MAAM,KAAK,GAAG,KAAK,IAAI,KACvB,KAAK,QAAQ,MAAM,MAAM,KAAK,MAAM,MAAM;AAC1C,aAAO,IAAI,gBAAe,KAAK,IAAI,KAAK,MAAM,MAAM,IAAI,GAAG,KAAK,IAAI,KAAK,IAAI,MAAM,EAAE,GAAG,KAAK,IAAI;AACrG,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO;AAAA,MAAE,UAAU;AAAA,MAAc,MAAM,KAAK,KAAK,OAAO;AAAA,MACpD,MAAM,KAAK;AAAA,MAAM,IAAI,KAAK;AAAA,IAAG;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,QAAQ,YAAY,OAAO,KAAK,MAAM;AAClD,YAAM,IAAI,WAAW,2CAA2C;AACpE,WAAO,IAAI,gBAAe,KAAK,MAAM,KAAK,IAAI,OAAO,aAAa,KAAK,IAAI,CAAC;AAAA,EAChF;AACJ;AACA,KAAK,OAAO,cAAc,cAAc;AAIxC,IAAM,kBAAN,MAAM,yBAAwB,KAAK;AAAA;AAAA;AAAA;AAAA,EAI/B,YAIA,KAIA,MAAM;AACF,UAAM;AACN,SAAK,MAAM;AACX,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,KAAK;AACP,QAAI,OAAO,IAAI,OAAO,KAAK,GAAG;AAC9B,QAAI,CAAC;AACD,aAAO,WAAW,KAAK,iCAAiC;AAC5D,QAAI,UAAU,KAAK,KAAK,OAAO,KAAK,OAAO,MAAM,KAAK,KAAK,SAAS,KAAK,KAAK,CAAC;AAC/E,WAAO,WAAW,YAAY,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,IAAI,MAAM,SAAS,KAAK,OAAO,GAAG,GAAG,KAAK,SAAS,IAAI,CAAC,CAAC;AAAA,EACxH;AAAA,EACA,OAAO,KAAK;AACR,QAAI,OAAO,IAAI,OAAO,KAAK,GAAG;AAC9B,QAAI,MAAM;AACN,UAAI,SAAS,KAAK,KAAK,SAAS,KAAK,KAAK;AAC1C,UAAI,OAAO,UAAU,KAAK,MAAM,QAAQ;AACpC,iBAAS,IAAI,GAAG,IAAI,KAAK,MAAM,QAAQ;AACnC,cAAI,CAAC,KAAK,MAAM,CAAC,EAAE,QAAQ,MAAM;AAC7B,mBAAO,IAAI,iBAAgB,KAAK,KAAK,KAAK,MAAM,CAAC,CAAC;AAC1D,eAAO,IAAI,iBAAgB,KAAK,KAAK,KAAK,IAAI;AAAA,MAClD;AAAA,IACJ;AACA,WAAO,IAAI,mBAAmB,KAAK,KAAK,KAAK,IAAI;AAAA,EACrD;AAAA,EACA,IAAI,SAAS;AACT,QAAI,MAAM,QAAQ,UAAU,KAAK,KAAK,CAAC;AACvC,WAAO,IAAI,eAAe,OAAO,IAAI,iBAAgB,IAAI,KAAK,KAAK,IAAI;AAAA,EAC3E;AAAA,EACA,SAAS;AACL,WAAO,EAAE,UAAU,eAAe,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,OAAO,EAAE;AAAA,EAC9E;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,OAAO;AACnB,YAAM,IAAI,WAAW,4CAA4C;AACrE,WAAO,IAAI,iBAAgB,KAAK,KAAK,OAAO,aAAa,KAAK,IAAI,CAAC;AAAA,EACvE;AACJ;AACA,KAAK,OAAO,eAAe,eAAe;AAI1C,IAAM,qBAAN,MAAM,4BAA2B,KAAK;AAAA;AAAA;AAAA;AAAA,EAIlC,YAIA,KAIA,MAAM;AACF,UAAM;AACN,SAAK,MAAM;AACX,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,KAAK;AACP,QAAI,OAAO,IAAI,OAAO,KAAK,GAAG;AAC9B,QAAI,CAAC;AACD,aAAO,WAAW,KAAK,iCAAiC;AAC5D,QAAI,UAAU,KAAK,KAAK,OAAO,KAAK,OAAO,MAAM,KAAK,KAAK,cAAc,KAAK,KAAK,CAAC;AACpF,WAAO,WAAW,YAAY,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,IAAI,MAAM,SAAS,KAAK,OAAO,GAAG,GAAG,KAAK,SAAS,IAAI,CAAC,CAAC;AAAA,EACxH;AAAA,EACA,OAAO,KAAK;AACR,QAAI,OAAO,IAAI,OAAO,KAAK,GAAG;AAC9B,QAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,QAAQ,KAAK,KAAK;AACtC,aAAO;AACX,WAAO,IAAI,gBAAgB,KAAK,KAAK,KAAK,IAAI;AAAA,EAClD;AAAA,EACA,IAAI,SAAS;AACT,QAAI,MAAM,QAAQ,UAAU,KAAK,KAAK,CAAC;AACvC,WAAO,IAAI,eAAe,OAAO,IAAI,oBAAmB,IAAI,KAAK,KAAK,IAAI;AAAA,EAC9E;AAAA,EACA,SAAS;AACL,WAAO,EAAE,UAAU,kBAAkB,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK,OAAO,EAAE;AAAA,EACjF;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,OAAO;AACnB,YAAM,IAAI,WAAW,+CAA+C;AACxE,WAAO,IAAI,oBAAmB,KAAK,KAAK,OAAO,aAAa,KAAK,IAAI,CAAC;AAAA,EAC1E;AACJ;AACA,KAAK,OAAO,kBAAkB,kBAAkB;AAKhD,IAAM,cAAN,MAAM,qBAAoB,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAU3B,YAIA,MAIA,IAIA,OAIA,YAAY,OAAO;AACf,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,QAAQ;AACb,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,MAAM,KAAK;AACP,QAAI,KAAK,aAAa,eAAe,KAAK,KAAK,MAAM,KAAK,EAAE;AACxD,aAAO,WAAW,KAAK,2CAA2C;AACtE,WAAO,WAAW,YAAY,KAAK,KAAK,MAAM,KAAK,IAAI,KAAK,KAAK;AAAA,EACrE;AAAA,EACA,SAAS;AACL,WAAO,IAAI,QAAQ,CAAC,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM,IAAI,CAAC;AAAA,EACxE;AAAA,EACA,OAAO,KAAK;AACR,WAAO,IAAI,aAAY,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,MAAM,IAAI,MAAM,KAAK,MAAM,KAAK,EAAE,CAAC;AAAA,EAChG;AAAA,EACA,IAAI,SAAS;AACT,QAAI,OAAO,QAAQ,UAAU,KAAK,MAAM,CAAC,GAAG,KAAK,QAAQ,UAAU,KAAK,IAAI,EAAE;AAC9E,QAAI,KAAK,iBAAiB,GAAG;AACzB,aAAO;AACX,WAAO,IAAI,aAAY,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,GAAG,GAAG,KAAK,OAAO,KAAK,SAAS;AAAA,EAC3F;AAAA,EACA,MAAM,OAAO;AACT,QAAI,EAAE,iBAAiB,iBAAgB,MAAM,aAAa,KAAK;AAC3D,aAAO;AACX,QAAI,KAAK,OAAO,KAAK,MAAM,QAAQ,MAAM,QAAQ,CAAC,KAAK,MAAM,WAAW,CAAC,MAAM,MAAM,WAAW;AAC5F,UAAI,QAAQ,KAAK,MAAM,OAAO,MAAM,MAAM,QAAQ,IAAI,MAAM,QACtD,IAAI,MAAM,KAAK,MAAM,QAAQ,OAAO,MAAM,MAAM,OAAO,GAAG,KAAK,MAAM,WAAW,MAAM,MAAM,OAAO;AACzG,aAAO,IAAI,aAAY,KAAK,MAAM,KAAK,MAAM,MAAM,KAAK,MAAM,OAAO,OAAO,KAAK,SAAS;AAAA,IAC9F,WACS,MAAM,MAAM,KAAK,QAAQ,CAAC,KAAK,MAAM,aAAa,CAAC,MAAM,MAAM,SAAS;AAC7E,UAAI,QAAQ,KAAK,MAAM,OAAO,MAAM,MAAM,QAAQ,IAAI,MAAM,QACtD,IAAI,MAAM,MAAM,MAAM,QAAQ,OAAO,KAAK,MAAM,OAAO,GAAG,MAAM,MAAM,WAAW,KAAK,MAAM,OAAO;AACzG,aAAO,IAAI,aAAY,MAAM,MAAM,KAAK,IAAI,OAAO,KAAK,SAAS;AAAA,IACrE,OACK;AACD,aAAO;AAAA,IACX;AAAA,EACJ;AAAA,EACA,SAAS;AACL,QAAI,OAAO,EAAE,UAAU,WAAW,MAAM,KAAK,MAAM,IAAI,KAAK,GAAG;AAC/D,QAAI,KAAK,MAAM;AACX,WAAK,QAAQ,KAAK,MAAM,OAAO;AACnC,QAAI,KAAK;AACL,WAAK,YAAY;AACrB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,QAAQ,YAAY,OAAO,KAAK,MAAM;AAClD,YAAM,IAAI,WAAW,wCAAwC;AACjE,WAAO,IAAI,aAAY,KAAK,MAAM,KAAK,IAAI,MAAM,SAAS,QAAQ,KAAK,KAAK,GAAG,CAAC,CAAC,KAAK,SAAS;AAAA,EACnG;AACJ;AACA,KAAK,OAAO,WAAW,WAAW;AAMlC,IAAM,oBAAN,MAAM,2BAA0B,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOjC,YAIA,MAIA,IAIA,SAIA,OAIA,OAKA,QAIA,YAAY,OAAO;AACf,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,KAAK;AACV,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,QAAQ;AACb,SAAK,SAAS;AACd,SAAK,YAAY;AAAA,EACrB;AAAA,EACA,MAAM,KAAK;AACP,QAAI,KAAK,cAAc,eAAe,KAAK,KAAK,MAAM,KAAK,OAAO,KAC9D,eAAe,KAAK,KAAK,OAAO,KAAK,EAAE;AACvC,aAAO,WAAW,KAAK,+CAA+C;AAC1E,QAAI,MAAM,IAAI,MAAM,KAAK,SAAS,KAAK,KAAK;AAC5C,QAAI,IAAI,aAAa,IAAI;AACrB,aAAO,WAAW,KAAK,yBAAyB;AACpD,QAAI,WAAW,KAAK,MAAM,SAAS,KAAK,QAAQ,IAAI,OAAO;AAC3D,QAAI,CAAC;AACD,aAAO,WAAW,KAAK,6BAA6B;AACxD,WAAO,WAAW,YAAY,KAAK,KAAK,MAAM,KAAK,IAAI,QAAQ;AAAA,EACnE;AAAA,EACA,SAAS;AACL,WAAO,IAAI,QAAQ;AAAA,MAAC,KAAK;AAAA,MAAM,KAAK,UAAU,KAAK;AAAA,MAAM,KAAK;AAAA,MAC1D,KAAK;AAAA,MAAO,KAAK,KAAK,KAAK;AAAA,MAAO,KAAK,MAAM,OAAO,KAAK;AAAA,IAAM,CAAC;AAAA,EACxE;AAAA,EACA,OAAO,KAAK;AACR,QAAI,MAAM,KAAK,QAAQ,KAAK;AAC5B,WAAO,IAAI,mBAAkB,KAAK,MAAM,KAAK,OAAO,KAAK,MAAM,OAAO,KAAK,KAAK,OAAO,KAAK,QAAQ,KAAK,OAAO,KAAK,SAAS,KAAK,IAAI,MAAM,KAAK,MAAM,KAAK,EAAE,EAAE,cAAc,KAAK,UAAU,KAAK,MAAM,KAAK,QAAQ,KAAK,IAAI,GAAG,KAAK,UAAU,KAAK,MAAM,KAAK,SAAS;AAAA,EAC9Q;AAAA,EACA,IAAI,SAAS;AACT,QAAI,OAAO,QAAQ,UAAU,KAAK,MAAM,CAAC,GAAG,KAAK,QAAQ,UAAU,KAAK,IAAI,EAAE;AAC9E,QAAI,UAAU,KAAK,QAAQ,KAAK,UAAU,KAAK,MAAM,QAAQ,IAAI,KAAK,SAAS,EAAE;AACjF,QAAI,QAAQ,KAAK,MAAM,KAAK,QAAQ,GAAG,MAAM,QAAQ,IAAI,KAAK,OAAO,CAAC;AACtE,QAAK,KAAK,iBAAiB,GAAG,iBAAkB,UAAU,KAAK,OAAO,QAAQ,GAAG;AAC7E,aAAO;AACX,WAAO,IAAI,mBAAkB,KAAK,KAAK,GAAG,KAAK,SAAS,OAAO,KAAK,OAAO,KAAK,QAAQ,KAAK,SAAS;AAAA,EAC1G;AAAA,EACA,SAAS;AACL,QAAI,OAAO;AAAA,MAAE,UAAU;AAAA,MAAiB,MAAM,KAAK;AAAA,MAAM,IAAI,KAAK;AAAA,MAC9D,SAAS,KAAK;AAAA,MAAS,OAAO,KAAK;AAAA,MAAO,QAAQ,KAAK;AAAA,IAAO;AAClE,QAAI,KAAK,MAAM;AACX,WAAK,QAAQ,KAAK,MAAM,OAAO;AACnC,QAAI,KAAK;AACL,WAAK,YAAY;AACrB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,QAAQ,YAAY,OAAO,KAAK,MAAM,YAClD,OAAO,KAAK,WAAW,YAAY,OAAO,KAAK,SAAS,YAAY,OAAO,KAAK,UAAU;AAC1F,YAAM,IAAI,WAAW,8CAA8C;AACvE,WAAO,IAAI,mBAAkB,KAAK,MAAM,KAAK,IAAI,KAAK,SAAS,KAAK,OAAO,MAAM,SAAS,QAAQ,KAAK,KAAK,GAAG,KAAK,QAAQ,CAAC,CAAC,KAAK,SAAS;AAAA,EAChJ;AACJ;AACA,KAAK,OAAO,iBAAiB,iBAAiB;AAC9C,SAAS,eAAe,KAAK,MAAM,IAAI;AACnC,MAAI,QAAQ,IAAI,QAAQ,IAAI,GAAG,OAAO,KAAK,MAAM,QAAQ,MAAM;AAC/D,SAAO,OAAO,KAAK,QAAQ,KAAK,MAAM,WAAW,KAAK,KAAK,MAAM,KAAK,KAAK,EAAE,YAAY;AACrF;AACA;AAAA,EACJ;AACA,MAAI,OAAO,GAAG;AACV,QAAI,OAAO,MAAM,KAAK,KAAK,EAAE,WAAW,MAAM,WAAW,KAAK,CAAC;AAC/D,WAAO,OAAO,GAAG;AACb,UAAI,CAAC,QAAQ,KAAK;AACd,eAAO;AACX,aAAO,KAAK;AACZ;AAAA,IACJ;AAAA,EACJ;AACA,SAAO;AACX;AAEA,SAAS,QAAQ,IAAI,MAAM,IAAI,MAAM;AACjC,MAAI,UAAU,CAAC,GAAG,QAAQ,CAAC;AAC3B,MAAI,UAAU;AACd,KAAG,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,KAAK,WAAW;AACjD,QAAI,CAAC,KAAK;AACN;AACJ,QAAI,QAAQ,KAAK;AACjB,QAAI,CAAC,KAAK,QAAQ,KAAK,KAAK,OAAO,KAAK,eAAe,KAAK,IAAI,GAAG;AAC/D,UAAI,QAAQ,KAAK,IAAI,KAAK,IAAI,GAAG,MAAM,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AACvE,UAAI,SAAS,KAAK,SAAS,KAAK;AAChC,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACnC,YAAI,CAAC,MAAM,CAAC,EAAE,QAAQ,MAAM,GAAG;AAC3B,cAAI,YAAY,SAAS,MAAM,SAAS,SAAS,KAAK,GAAG,MAAM,CAAC,CAAC;AAC7D,qBAAS,KAAK;AAAA;AAEd,oBAAQ,KAAK,WAAW,IAAI,eAAe,OAAO,KAAK,MAAM,CAAC,CAAC,CAAC;AAAA,QACxE;AAAA,MACJ;AACA,UAAI,UAAU,OAAO,MAAM;AACvB,eAAO,KAAK;AAAA;AAEZ,cAAM,KAAK,SAAS,IAAI,YAAY,OAAO,KAAK,IAAI,CAAC;AAAA,IAC7D;AAAA,EACJ,CAAC;AACD,UAAQ,QAAQ,OAAK,GAAG,KAAK,CAAC,CAAC;AAC/B,QAAM,QAAQ,OAAK,GAAG,KAAK,CAAC,CAAC;AACjC;AACA,SAAS,WAAW,IAAI,MAAM,IAAI,MAAM;AACpC,MAAI,UAAU,CAAC,GAAG,OAAO;AACzB,KAAG,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAQ;AACzC,QAAI,CAAC,KAAK;AACN;AACJ;AACA,QAAI,WAAW;AACf,QAAI,gBAAgB,UAAU;AAC1B,UAAI,MAAM,KAAK,OAAO;AACtB,aAAO,QAAQ,KAAK,QAAQ,GAAG,GAAG;AAC9B,SAAC,aAAa,WAAW,CAAC,IAAI,KAAK,KAAK;AACxC,cAAM,MAAM,cAAc,GAAG;AAAA,MACjC;AAAA,IACJ,WACS,MAAM;AACX,UAAI,KAAK,QAAQ,KAAK,KAAK;AACvB,mBAAW,CAAC,IAAI;AAAA,IACxB,OACK;AACD,iBAAW,KAAK;AAAA,IACpB;AACA,QAAI,YAAY,SAAS,QAAQ;AAC7B,UAAI,MAAM,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AAC1C,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACtC,YAAI,QAAQ,SAAS,CAAC,GAAG;AACzB,iBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,cAAI,IAAI,QAAQ,CAAC;AACjB,cAAI,EAAE,QAAQ,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,EAAE,KAAK;AAC/C,oBAAQ;AAAA,QAChB;AACA,YAAI,OAAO;AACP,gBAAM,KAAK;AACX,gBAAM,OAAO;AAAA,QACjB,OACK;AACD,kBAAQ,KAAK,EAAE,OAAO,MAAM,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC;AAAA,QACpE;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ,CAAC;AACD,UAAQ,QAAQ,OAAK,GAAG,KAAK,IAAI,eAAe,EAAE,MAAM,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;AAC3E;AACA,SAAS,kBAAkB,IAAI,KAAK,YAAY,QAAQ,WAAW,cAAc,gBAAgB,MAAM;AACnG,MAAI,OAAO,GAAG,IAAI,OAAO,GAAG;AAC5B,MAAI,YAAY,CAAC,GAAG,MAAM,MAAM;AAChC,WAAS,IAAI,GAAG,IAAI,KAAK,YAAY,KAAK;AACtC,QAAI,QAAQ,KAAK,MAAM,CAAC,GAAG,MAAM,MAAM,MAAM;AAC7C,QAAI,UAAU,MAAM,UAAU,MAAM,IAAI;AACxC,QAAI,CAAC,SAAS;AACV,gBAAU,KAAK,IAAI,YAAY,KAAK,KAAK,MAAM,KAAK,CAAC;AAAA,IACzD,OACK;AACD,cAAQ;AACR,eAAS,IAAI,GAAG,IAAI,MAAM,MAAM,QAAQ;AACpC,YAAI,CAAC,WAAW,eAAe,MAAM,MAAM,CAAC,EAAE,IAAI;AAC9C,aAAG,KAAK,IAAI,eAAe,KAAK,KAAK,MAAM,MAAM,CAAC,CAAC,CAAC;AAC5D,UAAI,iBAAiB,MAAM,UAAU,WAAW,cAAc,OAAO;AACjE,YAAI,GAAG,UAAU,aAAa;AAC9B,eAAO,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG;AACjC,cAAI,CAAC;AACD,oBAAQ,IAAI,MAAM,SAAS,KAAK,WAAW,OAAO,KAAK,KAAK,WAAW,aAAa,MAAM,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC;AAC5G,oBAAU,KAAK,IAAI,YAAY,MAAM,EAAE,OAAO,MAAM,EAAE,QAAQ,EAAE,CAAC,EAAE,QAAQ,KAAK,CAAC;AAAA,QACrF;AAAA,MACJ;AAAA,IACJ;AACA,UAAM;AAAA,EACV;AACA,MAAI,CAAC,MAAM,UAAU;AACjB,QAAI,OAAO,MAAM,WAAW,SAAS,OAAO,IAAI;AAChD,OAAG,QAAQ,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG,CAAC,CAAC;AAAA,EAC9C;AACA,WAAS,IAAI,UAAU,SAAS,GAAG,KAAK,GAAG;AACvC,OAAG,KAAK,UAAU,CAAC,CAAC;AAC5B;AAEA,SAAS,OAAO,MAAM,OAAO,KAAK;AAC9B,UAAQ,SAAS,KAAK,KAAK,WAAW,OAAO,KAAK,UAAU,OACvD,OAAO,KAAK,cAAc,KAAK,WAAW,GAAG,GAAG;AACzD;AAMA,SAAS,WAAW,OAAO;AACvB,MAAI,SAAS,MAAM;AACnB,MAAI,UAAU,OAAO,QAAQ,WAAW,MAAM,YAAY,MAAM,QAAQ;AACxE,WAAS,QAAQ,MAAM,SAAQ,EAAE,OAAO;AACpC,QAAI,OAAO,MAAM,MAAM,KAAK,KAAK;AACjC,QAAI,QAAQ,MAAM,MAAM,MAAM,KAAK,GAAG,WAAW,MAAM,IAAI,WAAW,KAAK;AAC3E,QAAI,QAAQ,MAAM,SAAS,KAAK,WAAW,OAAO,UAAU,OAAO;AAC/D,aAAO;AACX,QAAI,SAAS,KAAK,KAAK,KAAK,KAAK,aAAa,CAAC,OAAO,MAAM,OAAO,QAAQ;AACvE;AAAA,EACR;AACA,SAAO;AACX;AACA,SAAS,KAAK,IAAI,OAAO,QAAQ;AAC7B,MAAI,EAAE,OAAO,KAAK,MAAM,IAAI;AAC5B,MAAI,WAAW,MAAM,OAAO,QAAQ,CAAC,GAAG,SAAS,IAAI,MAAM,QAAQ,CAAC;AACpE,MAAI,QAAQ,UAAU,MAAM;AAC5B,MAAI,SAAS,SAAS,OAAO,YAAY;AACzC,WAAS,IAAI,OAAO,YAAY,OAAO,IAAI,QAAQ;AAC/C,QAAI,aAAa,MAAM,MAAM,CAAC,IAAI,GAAG;AACjC,kBAAY;AACZ,eAAS,SAAS,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,MAAM,CAAC;AACjD;AAAA,IACJ,OACK;AACD;AAAA,IACJ;AACJ,MAAI,QAAQ,SAAS,OAAO,UAAU;AACtC,WAAS,IAAI,OAAO,YAAY,OAAO,IAAI,QAAQ;AAC/C,QAAI,aAAa,IAAI,MAAM,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG;AAC5C,kBAAY;AACZ,cAAQ,SAAS,KAAK,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC;AAC7C;AAAA,IACJ,OACK;AACD;AAAA,IACJ;AACJ,KAAG,KAAK,IAAI,kBAAkB,OAAO,KAAK,UAAU,QAAQ,IAAI,MAAM,OAAO,OAAO,KAAK,GAAG,WAAW,OAAO,GAAG,OAAO,OAAO,WAAW,IAAI,CAAC;AACnJ;AASA,SAAS,aAAa,OAAO,UAAU,QAAQ,MAAM,aAAa,OAAO;AACrE,MAAI,SAAS,oBAAoB,OAAO,QAAQ;AAChD,MAAI,QAAQ,UAAU,mBAAmB,YAAY,QAAQ;AAC7D,MAAI,CAAC;AACD,WAAO;AACX,SAAO,OAAO,IAAI,SAAS,EACtB,OAAO,EAAE,MAAM,UAAU,MAAM,CAAC,EAAE,OAAO,MAAM,IAAI,SAAS,CAAC;AACtE;AACA,SAAS,UAAU,MAAM;AAAE,SAAO,EAAE,MAAM,OAAO,KAAK;AAAG;AACzD,SAAS,oBAAoB,OAAO,MAAM;AACtC,MAAI,EAAE,QAAQ,YAAY,SAAS,IAAI;AACvC,MAAI,SAAS,OAAO,eAAe,UAAU,EAAE,aAAa,IAAI;AAChE,MAAI,CAAC;AACD,WAAO;AACX,MAAI,QAAQ,OAAO,SAAS,OAAO,CAAC,IAAI;AACxC,SAAO,OAAO,eAAe,YAAY,UAAU,KAAK,IAAI,SAAS;AACzE;AACA,SAAS,mBAAmB,OAAO,MAAM;AACrC,MAAI,EAAE,QAAQ,YAAY,SAAS,IAAI;AACvC,MAAI,QAAQ,OAAO,MAAM,UAAU;AACnC,MAAI,SAAS,KAAK,aAAa,aAAa,MAAM,IAAI;AACtD,MAAI,CAAC;AACD,WAAO;AACX,MAAI,WAAW,OAAO,SAAS,OAAO,OAAO,SAAS,CAAC,IAAI;AAC3D,MAAI,aAAa,SAAS;AAC1B,WAAS,IAAI,YAAY,cAAc,IAAI,UAAU;AACjD,iBAAa,WAAW,UAAU,OAAO,MAAM,CAAC,EAAE,IAAI;AAC1D,MAAI,CAAC,cAAc,CAAC,WAAW;AAC3B,WAAO;AACX,SAAO;AACX;AACA,SAAS,KAAK,IAAI,OAAO,UAAU;AAC/B,MAAI,UAAU,SAAS;AACvB,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK;AAC3C,QAAI,QAAQ,MAAM;AACd,UAAI,QAAQ,SAAS,CAAC,EAAE,KAAK,aAAa,cAAc,OAAO;AAC/D,UAAI,CAAC,SAAS,CAAC,MAAM;AACjB,cAAM,IAAI,WAAW,wFAAwF;AAAA,IACrH;AACA,cAAU,SAAS,KAAK,SAAS,CAAC,EAAE,KAAK,OAAO,SAAS,CAAC,EAAE,OAAO,OAAO,CAAC;AAAA,EAC/E;AACA,MAAI,QAAQ,MAAM,OAAO,MAAM,MAAM;AACrC,KAAG,KAAK,IAAI,kBAAkB,OAAO,KAAK,OAAO,KAAK,IAAI,MAAM,SAAS,GAAG,CAAC,GAAG,SAAS,QAAQ,IAAI,CAAC;AAC1G;AACA,SAAS,aAAa,IAAI,MAAM,IAAI,MAAM,OAAO;AAC7C,MAAI,CAAC,KAAK;AACN,UAAM,IAAI,WAAW,kDAAkD;AAC3E,MAAI,UAAU,GAAG,MAAM;AACvB,KAAG,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAQ;AACzC,QAAI,YAAY,OAAO,SAAS,aAAa,MAAM,IAAI,IAAI;AAC3D,QAAI,KAAK,eAAe,CAAC,KAAK,UAAU,MAAM,SAAS,KACnD,cAAc,GAAG,KAAK,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,GAAG,GAAG,IAAI,GAAG;AACjE,UAAI,kBAAkB;AACtB,UAAI,KAAK,OAAO,sBAAsB;AAClC,YAAI,MAAM,KAAK,cAAc,OAAO,mBAAmB,CAAC,CAAC,KAAK,aAAa,UAAU,KAAK,OAAO,oBAAoB;AACrH,YAAI,OAAO,CAAC;AACR,4BAAkB;AAAA,iBACb,CAAC,OAAO;AACb,4BAAkB;AAAA,MAC1B;AAEA,UAAI,oBAAoB;AACpB,0BAAkB,IAAI,MAAM,KAAK,OAAO;AAC5C,wBAAkB,IAAI,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,KAAK,CAAC,GAAG,MAAM,QAAW,oBAAoB,IAAI;AACtG,UAAI,UAAU,GAAG,QAAQ,MAAM,OAAO;AACtC,UAAI,SAAS,QAAQ,IAAI,KAAK,CAAC,GAAG,OAAO,QAAQ,IAAI,MAAM,KAAK,UAAU,CAAC;AAC3E,SAAG,KAAK,IAAI,kBAAkB,QAAQ,MAAM,SAAS,GAAG,OAAO,GAAG,IAAI,MAAM,SAAS,KAAK,KAAK,OAAO,WAAW,MAAM,KAAK,KAAK,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC;AACpJ,UAAI,oBAAoB;AACpB,wBAAgB,IAAI,MAAM,KAAK,OAAO;AAC1C,aAAO;AAAA,IACX;AAAA,EACJ,CAAC;AACL;AACA,SAAS,gBAAgB,IAAI,MAAM,KAAK,SAAS;AAC7C,OAAK,QAAQ,CAAC,OAAO,WAAW;AAC5B,QAAI,MAAM,QAAQ;AACd,UAAI,GAAG,UAAU;AACjB,aAAO,IAAI,QAAQ,KAAK,MAAM,IAAI,GAAG;AACjC,YAAI,QAAQ,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,MAAM,IAAI,SAAS,EAAE,KAAK;AACpE,WAAG,YAAY,OAAO,QAAQ,GAAG,KAAK,KAAK,OAAO,qBAAqB,OAAO,CAAC;AAAA,MACnF;AAAA,IACJ;AAAA,EACJ,CAAC;AACL;AACA,SAAS,kBAAkB,IAAI,MAAM,KAAK,SAAS;AAC/C,OAAK,QAAQ,CAAC,OAAO,WAAW;AAC5B,QAAI,MAAM,QAAQ,MAAM,KAAK,OAAO,sBAAsB;AACtD,UAAI,QAAQ,GAAG,QAAQ,MAAM,OAAO,EAAE,IAAI,MAAM,IAAI,MAAM;AAC1D,SAAG,YAAY,OAAO,QAAQ,GAAG,KAAK,KAAK,OAAO,KAAK,IAAI,CAAC;AAAA,IAChE;AAAA,EACJ,CAAC;AACL;AACA,SAAS,cAAc,KAAK,KAAK,MAAM;AACnC,MAAI,OAAO,IAAI,QAAQ,GAAG,GAAG,QAAQ,KAAK,MAAM;AAChD,SAAO,KAAK,OAAO,eAAe,OAAO,QAAQ,GAAG,IAAI;AAC5D;AAKA,SAAS,cAAc,IAAI,KAAK,MAAM,OAAO,OAAO;AAChD,MAAI,OAAO,GAAG,IAAI,OAAO,GAAG;AAC5B,MAAI,CAAC;AACD,UAAM,IAAI,WAAW,2BAA2B;AACpD,MAAI,CAAC;AACD,WAAO,KAAK;AAChB,MAAI,UAAU,KAAK,OAAO,OAAO,MAAM,SAAS,KAAK,KAAK;AAC1D,MAAI,KAAK;AACL,WAAO,GAAG,YAAY,KAAK,MAAM,KAAK,UAAU,OAAO;AAC3D,MAAI,CAAC,KAAK,aAAa,KAAK,OAAO;AAC/B,UAAM,IAAI,WAAW,mCAAmC,KAAK,IAAI;AACrE,KAAG,KAAK,IAAI,kBAAkB,KAAK,MAAM,KAAK,UAAU,MAAM,GAAG,MAAM,KAAK,WAAW,GAAG,IAAI,MAAM,SAAS,KAAK,OAAO,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC;AAC/I;AAIA,SAAS,SAAS,KAAK,KAAK,QAAQ,GAAG,YAAY;AAC/C,MAAI,OAAO,IAAI,QAAQ,GAAG,GAAG,OAAO,KAAK,QAAQ;AACjD,MAAI,YAAa,cAAc,WAAW,WAAW,SAAS,CAAC,KAAM,KAAK;AAC1E,MAAI,OAAO,KAAK,KAAK,OAAO,KAAK,KAAK,aAClC,CAAC,KAAK,OAAO,WAAW,KAAK,MAAM,GAAG,KAAK,OAAO,UAAU,KAC5D,CAAC,UAAU,KAAK,aAAa,KAAK,OAAO,QAAQ,WAAW,KAAK,MAAM,GAAG,KAAK,OAAO,UAAU,CAAC;AACjG,WAAO;AACX,WAAS,IAAI,KAAK,QAAQ,GAAG,IAAI,QAAQ,GAAG,IAAI,MAAM,KAAK,KAAK;AAC5D,QAAI,OAAO,KAAK,KAAK,CAAC,GAAGC,SAAQ,KAAK,MAAM,CAAC;AAC7C,QAAI,KAAK,KAAK,KAAK;AACf,aAAO;AACX,QAAI,OAAO,KAAK,QAAQ,WAAWA,QAAO,KAAK,UAAU;AACzD,QAAI,gBAAgB,cAAc,WAAW,IAAI,CAAC;AAClD,QAAI;AACA,aAAO,KAAK,aAAa,GAAG,cAAc,KAAK,OAAO,cAAc,KAAK,CAAC;AAC9E,QAAI,QAAS,cAAc,WAAW,CAAC,KAAM;AAC7C,QAAI,CAAC,KAAK,WAAWA,SAAQ,GAAG,KAAK,UAAU,KAAK,CAAC,MAAM,KAAK,aAAa,IAAI;AAC7E,aAAO;AAAA,EACf;AACA,MAAI,QAAQ,KAAK,WAAW,IAAI;AAChC,MAAI,WAAW,cAAc,WAAW,CAAC;AACzC,SAAO,KAAK,KAAK,IAAI,EAAE,eAAe,OAAO,OAAO,WAAW,SAAS,OAAO,KAAK,KAAK,OAAO,CAAC,EAAE,IAAI;AAC3G;AACA,SAAS,MAAM,IAAI,KAAK,QAAQ,GAAG,YAAY;AAC3C,MAAI,OAAO,GAAG,IAAI,QAAQ,GAAG,GAAG,SAAS,SAAS,OAAO,QAAQ,SAAS;AAC1E,WAAS,IAAI,KAAK,OAAO,IAAI,KAAK,QAAQ,OAAO,IAAI,QAAQ,GAAG,IAAI,GAAG,KAAK,KAAK;AAC7E,aAAS,SAAS,KAAK,KAAK,KAAK,CAAC,EAAE,KAAK,MAAM,CAAC;AAChD,QAAI,YAAY,cAAc,WAAW,CAAC;AAC1C,YAAQ,SAAS,KAAK,YAAY,UAAU,KAAK,OAAO,UAAU,OAAO,KAAK,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC;AAAA,EAC9G;AACA,KAAG,KAAK,IAAI,YAAY,KAAK,KAAK,IAAI,MAAM,OAAO,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,IAAI,CAAC;AAC1F;AAKA,SAAS,QAAQ,KAAK,KAAK;AACvB,MAAI,OAAO,IAAI,QAAQ,GAAG,GAAG,QAAQ,KAAK,MAAM;AAChD,SAAO,SAAS,KAAK,YAAY,KAAK,SAAS,KAC3C,KAAK,OAAO,WAAW,OAAO,QAAQ,CAAC;AAC/C;AACA,SAAS,mCAAmC,GAAG,GAAG;AAC9C,MAAI,CAAC,EAAE,QAAQ;AACX,MAAE,KAAK,kBAAkB,EAAE,IAAI;AACnC,MAAI,QAAQ,EAAE,eAAe,EAAE,UAAU;AACzC,MAAI,EAAE,qBAAqB,IAAI,EAAE,KAAK;AACtC,WAAS,IAAI,GAAG,IAAI,EAAE,YAAY,KAAK;AACnC,QAAI,QAAQ,EAAE,MAAM,CAAC;AACrB,QAAI,OAAO,MAAM,QAAQ,uBAAuB,EAAE,KAAK,OAAO,MAAM,OAAO,MAAM;AACjF,YAAQ,MAAM,UAAU,IAAI;AAC5B,QAAI,CAAC;AACD,aAAO;AACX,QAAI,CAAC,EAAE,KAAK,YAAY,MAAM,KAAK;AAC/B,aAAO;AAAA,EACf;AACA,SAAO,MAAM;AACjB;AACA,SAAS,SAAS,GAAG,GAAG;AACpB,SAAO,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,UAAU,mCAAmC,GAAG,CAAC;AAC5E;AAMA,SAAS,UAAU,KAAK,KAAK,MAAM,IAAI;AACnC,MAAI,OAAO,IAAI,QAAQ,GAAG;AAC1B,WAAS,IAAI,KAAK,SAAQ,KAAK;AAC3B,QAAI,QAAQ,OAAO,QAAQ,KAAK,MAAM,CAAC;AACvC,QAAI,KAAK,KAAK,OAAO;AACjB,eAAS,KAAK;AACd,cAAQ,KAAK;AAAA,IACjB,WACS,MAAM,GAAG;AACd,eAAS,KAAK,KAAK,IAAI,CAAC;AACxB;AACA,cAAQ,KAAK,KAAK,CAAC,EAAE,WAAW,KAAK;AAAA,IACzC,OACK;AACD,eAAS,KAAK,KAAK,CAAC,EAAE,WAAW,QAAQ,CAAC;AAC1C,cAAQ,KAAK,KAAK,IAAI,CAAC;AAAA,IAC3B;AACA,QAAI,UAAU,CAAC,OAAO,eAAe,SAAS,QAAQ,KAAK,KACvD,KAAK,KAAK,CAAC,EAAE,WAAW,OAAO,QAAQ,CAAC;AACxC,aAAO;AACX,QAAI,KAAK;AACL;AACJ,UAAM,MAAM,IAAI,KAAK,OAAO,CAAC,IAAI,KAAK,MAAM,CAAC;AAAA,EACjD;AACJ;AACA,SAAS,KAAK,IAAI,KAAK,OAAO;AAC1B,MAAI,kBAAkB;AACtB,MAAI,EAAE,qBAAqB,IAAI,GAAG,IAAI,KAAK;AAC3C,MAAI,UAAU,GAAG,IAAI,QAAQ,MAAM,KAAK,GAAG,aAAa,QAAQ,KAAK,EAAE;AACvE,MAAI,wBAAwB,WAAW,eAAe;AAClD,QAAI,MAAM,WAAW,cAAc;AACnC,QAAI,mBAAmB,CAAC,CAAC,WAAW,aAAa,UAAU,oBAAoB;AAC/E,QAAI,OAAO,CAAC;AACR,wBAAkB;AAAA,aACb,CAAC,OAAO;AACb,wBAAkB;AAAA,EAC1B;AACA,MAAI,UAAU,GAAG,MAAM;AACvB,MAAI,oBAAoB,OAAO;AAC3B,QAAI,SAAS,GAAG,IAAI,QAAQ,MAAM,KAAK;AACvC,sBAAkB,IAAI,OAAO,KAAK,GAAG,OAAO,OAAO,GAAG,OAAO;AAAA,EACjE;AACA,MAAI,WAAW;AACX,sBAAkB,IAAI,MAAM,QAAQ,GAAG,YAAY,QAAQ,KAAK,EAAE,eAAe,QAAQ,MAAM,CAAC,GAAG,mBAAmB,IAAI;AAC9H,MAAI,UAAU,GAAG,QAAQ,MAAM,OAAO,GAAG,QAAQ,QAAQ,IAAI,MAAM,KAAK;AACxE,KAAG,KAAK,IAAI,YAAY,OAAO,QAAQ,IAAI,MAAM,OAAO,EAAE,GAAG,MAAM,OAAO,IAAI,CAAC;AAC/E,MAAI,oBAAoB,MAAM;AAC1B,QAAI,QAAQ,GAAG,IAAI,QAAQ,KAAK;AAChC,oBAAgB,IAAI,MAAM,KAAK,GAAG,MAAM,OAAO,GAAG,GAAG,MAAM,MAAM;AAAA,EACrE;AACA,SAAO;AACX;AAOA,SAAS,YAAY,KAAK,KAAK,UAAU;AACrC,MAAI,OAAO,IAAI,QAAQ,GAAG;AAC1B,MAAI,KAAK,OAAO,eAAe,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG,QAAQ;AAC/D,WAAO;AACX,MAAI,KAAK,gBAAgB;AACrB,aAAS,IAAI,KAAK,QAAQ,GAAG,KAAK,GAAG,KAAK;AACtC,UAAI,QAAQ,KAAK,MAAM,CAAC;AACxB,UAAI,KAAK,KAAK,CAAC,EAAE,eAAe,OAAO,OAAO,QAAQ;AAClD,eAAO,KAAK,OAAO,IAAI,CAAC;AAC5B,UAAI,QAAQ;AACR,eAAO;AAAA,IACf;AACJ,MAAI,KAAK,gBAAgB,KAAK,OAAO,QAAQ;AACzC,aAAS,IAAI,KAAK,QAAQ,GAAG,KAAK,GAAG,KAAK;AACtC,UAAI,QAAQ,KAAK,WAAW,CAAC;AAC7B,UAAI,KAAK,KAAK,CAAC,EAAE,eAAe,OAAO,OAAO,QAAQ;AAClD,eAAO,KAAK,MAAM,IAAI,CAAC;AAC3B,UAAI,QAAQ,KAAK,KAAK,CAAC,EAAE;AACrB,eAAO;AAAA,IACf;AACJ,SAAO;AACX;AAOA,SAAS,UAAU,KAAK,KAAK,OAAO;AAChC,MAAI,OAAO,IAAI,QAAQ,GAAG;AAC1B,MAAI,CAAC,MAAM,QAAQ;AACf,WAAO;AACX,MAAI,UAAU,MAAM;AACpB,WAAS,IAAI,GAAG,IAAI,MAAM,WAAW;AACjC,cAAU,QAAQ,WAAW;AACjC,WAAS,OAAO,GAAG,SAAS,MAAM,aAAa,KAAK,MAAM,OAAO,IAAI,IAAI,QAAQ;AAC7E,aAAS,IAAI,KAAK,OAAO,KAAK,GAAG,KAAK;AAClC,UAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,QAAQ,KAAK,MAAM,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,IAAI,KAAK;AAC9F,UAAI,YAAY,KAAK,MAAM,CAAC,KAAK,OAAO,IAAI,IAAI;AAChD,UAAI,SAAS,KAAK,KAAK,CAAC,GAAG,OAAO;AAClC,UAAI,QAAQ,GAAG;AACX,eAAO,OAAO,WAAW,WAAW,WAAW,OAAO;AAAA,MAC1D,OACK;AACD,YAAI,WAAW,OAAO,eAAe,SAAS,EAAE,aAAa,QAAQ,WAAW,IAAI;AACpF,eAAO,YAAY,OAAO,eAAe,WAAW,WAAW,SAAS,CAAC,CAAC;AAAA,MAC9E;AACA,UAAI;AACA,eAAO,QAAQ,IAAI,KAAK,MAAM,OAAO,IAAI,KAAK,OAAO,IAAI,CAAC,IAAI,KAAK,MAAM,IAAI,CAAC;AAAA,IACtF;AAAA,EACJ;AACA,SAAO;AACX;AAQA,SAAS,YAAY,KAAK,MAAM,KAAK,MAAM,QAAQ,MAAM,OAAO;AAC5D,MAAI,QAAQ,MAAM,CAAC,MAAM;AACrB,WAAO;AACX,MAAI,QAAQ,IAAI,QAAQ,IAAI,GAAG,MAAM,IAAI,QAAQ,EAAE;AAEnD,MAAI,cAAc,OAAO,KAAK,KAAK;AAC/B,WAAO,IAAI,YAAY,MAAM,IAAI,KAAK;AAC1C,SAAO,IAAI,OAAO,OAAO,KAAK,KAAK,EAAE,IAAI;AAC7C;AACA,SAAS,cAAc,OAAO,KAAK,OAAO;AACtC,SAAO,CAAC,MAAM,aAAa,CAAC,MAAM,WAAW,MAAM,MAAM,KAAK,IAAI,MAAM,KACpE,MAAM,OAAO,WAAW,MAAM,MAAM,GAAG,IAAI,MAAM,GAAG,MAAM,OAAO;AACzE;AAqBA,IAAM,SAAN,MAAa;AAAA,EACT,YAAY,OAAO,KAAK,UAAU;AAC9B,SAAK,QAAQ;AACb,SAAK,MAAM;AACX,SAAK,WAAW;AAChB,SAAK,WAAW,CAAC;AACjB,SAAK,SAAS,SAAS;AACvB,aAAS,IAAI,GAAG,KAAK,MAAM,OAAO,KAAK;AACnC,UAAI,OAAO,MAAM,KAAK,CAAC;AACvB,WAAK,SAAS,KAAK;AAAA,QACf,MAAM,KAAK;AAAA,QACX,OAAO,KAAK,eAAe,MAAM,WAAW,CAAC,CAAC;AAAA,MAClD,CAAC;AAAA,IACL;AACA,aAAS,IAAI,MAAM,OAAO,IAAI,GAAG;AAC7B,WAAK,SAAS,SAAS,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,KAAK,MAAM,CAAC;AAAA,EACnE;AAAA,EACA,IAAI,QAAQ;AAAE,WAAO,KAAK,SAAS,SAAS;AAAA,EAAG;AAAA,EAC/C,MAAM;AAIF,WAAO,KAAK,SAAS,MAAM;AACvB,UAAI,MAAM,KAAK,aAAa;AAC5B,UAAI;AACA,aAAK,WAAW,GAAG;AAAA;AAEnB,aAAK,SAAS,KAAK,KAAK,SAAS;AAAA,IACzC;AAMA,QAAI,aAAa,KAAK,eAAe,GAAG,aAAa,KAAK,OAAO,OAAO,KAAK,QAAQ,KAAK,MAAM;AAChG,QAAI,QAAQ,KAAK,OAAO,MAAM,KAAK,MAAM,aAAa,IAAI,KAAK,MAAM,MAAM,IAAI,QAAQ,UAAU,CAAC;AAClG,QAAI,CAAC;AACD,aAAO;AAEX,QAAI,UAAU,KAAK,QAAQ,YAAY,MAAM,OAAO,UAAU,IAAI;AAClE,WAAO,aAAa,WAAW,QAAQ,cAAc,GAAG;AACpD,gBAAU,QAAQ,WAAW;AAC7B;AACA;AAAA,IACJ;AACA,QAAI,QAAQ,IAAI,MAAM,SAAS,WAAW,OAAO;AACjD,QAAI,aAAa;AACb,aAAO,IAAI,kBAAkB,MAAM,KAAK,YAAY,KAAK,IAAI,KAAK,KAAK,IAAI,IAAI,GAAG,OAAO,UAAU;AACvG,QAAI,MAAM,QAAQ,MAAM,OAAO,KAAK,IAAI;AACpC,aAAO,IAAI,YAAY,MAAM,KAAK,IAAI,KAAK,KAAK;AACpD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe;AACX,QAAI,aAAa,KAAK,SAAS;AAC/B,aAAS,MAAM,KAAK,SAAS,SAAS,IAAI,GAAG,UAAU,KAAK,SAAS,SAAS,IAAI,YAAY,KAAK;AAC/F,UAAI,OAAO,IAAI;AACf,UAAI,IAAI,aAAa;AACjB,kBAAU;AACd,UAAI,KAAK,KAAK,KAAK,aAAa,WAAW,GAAG;AAC1C,qBAAa;AACb;AAAA,MACJ;AACA,YAAM,KAAK;AAAA,IACf;AAGA,aAAS,OAAO,GAAG,QAAQ,GAAG,QAAQ;AAClC,eAAS,aAAa,QAAQ,IAAI,aAAa,KAAK,SAAS,WAAW,cAAc,GAAG,cAAc;AACnG,YAAI,UAAU,SAAS;AACvB,YAAI,YAAY;AACZ,mBAAS,UAAU,KAAK,SAAS,SAAS,aAAa,CAAC,EAAE;AAC1D,qBAAW,OAAO;AAAA,QACtB,OACK;AACD,qBAAW,KAAK,SAAS;AAAA,QAC7B;AACA,YAAI,QAAQ,SAAS;AACrB,iBAAS,gBAAgB,KAAK,OAAO,iBAAiB,GAAG,iBAAiB;AACtE,cAAI,EAAE,MAAM,MAAM,IAAI,KAAK,SAAS,aAAa,GAAGC,OAAM,SAAS;AAInE,cAAI,QAAQ,MAAM,QAAQ,MAAM,UAAU,MAAM,IAAI,MAAM,SAAS,MAAM,WAAW,SAAS,KAAK,KAAK,GAAG,KAAK,KACzG,UAAU,KAAK,kBAAkB,OAAO,IAAI;AAC9C,mBAAO,EAAE,YAAY,eAAe,QAAQ,OAAO;AAAA,mBAG9C,QAAQ,KAAK,UAAUA,QAAO,MAAM,aAAa,MAAM,IAAI;AAChE,mBAAO,EAAE,YAAY,eAAe,QAAQ,MAAAA,MAAK;AAGrD,cAAI,UAAU,MAAM,UAAU,OAAO,IAAI;AACrC;AAAA,QACR;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AAAA,EACA,WAAW;AACP,QAAI,EAAE,SAAS,WAAW,QAAQ,IAAI,KAAK;AAC3C,QAAI,QAAQ,UAAU,SAAS,SAAS;AACxC,QAAI,CAAC,MAAM,cAAc,MAAM,WAAW;AACtC,aAAO;AACX,SAAK,WAAW,IAAI,MAAM,SAAS,YAAY,GAAG,KAAK,IAAI,SAAS,MAAM,OAAO,aAAa,QAAQ,OAAO,UAAU,YAAY,IAAI,CAAC,CAAC;AACzI,WAAO;AAAA,EACX;AAAA,EACA,WAAW;AACP,QAAI,EAAE,SAAS,WAAW,QAAQ,IAAI,KAAK;AAC3C,QAAI,QAAQ,UAAU,SAAS,SAAS;AACxC,QAAI,MAAM,cAAc,KAAK,YAAY,GAAG;AACxC,UAAI,YAAY,QAAQ,OAAO,aAAa,YAAY,MAAM;AAC9D,WAAK,WAAW,IAAI,MAAM,iBAAiB,SAAS,YAAY,GAAG,CAAC,GAAG,YAAY,GAAG,YAAY,YAAY,IAAI,OAAO;AAAA,IAC7H,OACK;AACD,WAAK,WAAW,IAAI,MAAM,iBAAiB,SAAS,WAAW,CAAC,GAAG,WAAW,OAAO;AAAA,IACzF;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,EAAE,YAAY,eAAe,QAAQ,QAAQ,MAAAA,MAAK,GAAG;AAC5D,WAAO,KAAK,QAAQ;AAChB,WAAK,kBAAkB;AAC3B,QAAIA;AACA,eAAS,IAAI,GAAG,IAAIA,MAAK,QAAQ;AAC7B,aAAK,iBAAiBA,MAAK,CAAC,CAAC;AACrC,QAAI,QAAQ,KAAK,UAAU,WAAW,SAAS,OAAO,UAAU,MAAM;AACtE,QAAI,YAAY,MAAM,YAAY;AAClC,QAAI,QAAQ,GAAG,MAAM,CAAC;AACtB,QAAI,EAAE,OAAO,KAAK,IAAI,KAAK,SAAS,aAAa;AACjD,QAAI,QAAQ;AACR,eAAS,IAAI,GAAG,IAAI,OAAO,YAAY;AACnC,YAAI,KAAK,OAAO,MAAM,CAAC,CAAC;AAC5B,cAAQ,MAAM,cAAc,MAAM;AAAA,IACtC;AAIA,QAAI,eAAgB,SAAS,OAAO,cAAe,MAAM,QAAQ,OAAO,MAAM;AAG9E,WAAO,QAAQ,SAAS,YAAY;AAChC,UAAI,OAAO,SAAS,MAAM,KAAK,GAAG,UAAU,MAAM,UAAU,KAAK,IAAI;AACrE,UAAI,CAAC;AACD;AACJ;AACA,UAAI,QAAQ,KAAK,aAAa,KAAK,KAAK,QAAQ,MAAM;AAClD,gBAAQ;AACR,YAAI,KAAK,eAAe,KAAK,KAAK,KAAK,aAAa,KAAK,KAAK,CAAC,GAAG,SAAS,IAAI,YAAY,GAAG,SAAS,SAAS,aAAa,eAAe,EAAE,CAAC;AAAA,MACnJ;AAAA,IACJ;AACA,QAAI,QAAQ,SAAS,SAAS;AAC9B,QAAI,CAAC;AACD,qBAAe;AACnB,SAAK,SAAS,cAAc,KAAK,QAAQ,eAAe,SAAS,KAAK,GAAG,CAAC;AAC1E,SAAK,SAAS,aAAa,EAAE,QAAQ;AAGrC,QAAI,SAAS,eAAe,KAAK,UAAU,OAAO,QAAQ,KAAK,SAAS,KAAK,KAAK,EAAE,QAAQ,KAAK,SAAS,SAAS;AAC/G,WAAK,kBAAkB;AAE3B,aAAS,IAAI,GAAG,MAAM,UAAU,IAAI,cAAc,KAAK;AACnD,UAAI,OAAO,IAAI;AACf,WAAK,SAAS,KAAK,EAAE,MAAM,KAAK,MAAM,OAAO,KAAK,eAAe,KAAK,UAAU,EAAE,CAAC;AACnF,YAAM,KAAK;AAAA,IACf;AAIA,SAAK,WAAW,CAAC,QAAQ,IAAI,MAAM,iBAAiB,MAAM,SAAS,YAAY,KAAK,GAAG,MAAM,WAAW,MAAM,OAAO,IAC/G,cAAc,IAAI,MAAM,QACpB,IAAI,MAAM,iBAAiB,MAAM,SAAS,aAAa,GAAG,CAAC,GAAG,aAAa,GAAG,eAAe,IAAI,MAAM,UAAU,aAAa,CAAC;AAAA,EAC7I;AAAA,EACA,iBAAiB;AACb,QAAI,CAAC,KAAK,IAAI,OAAO;AACjB,aAAO;AACX,QAAI,MAAM,KAAK,SAAS,KAAK,KAAK,GAAG;AACrC,QAAI,CAAC,IAAI,KAAK,eAAe,CAAC,iBAAiB,KAAK,KAAK,KAAK,IAAI,OAAO,IAAI,MAAM,IAAI,OAAO,KAAK,KAC9F,KAAK,IAAI,SAAS,KAAK,UAAU,QAAQ,KAAK,eAAe,KAAK,GAAG,MAAM,MAAM,SAAS,KAAK;AAChG,aAAO;AACX,QAAI,EAAE,MAAM,IAAI,KAAK,KAAK,QAAQ,KAAK,IAAI,MAAM,KAAK;AACtD,WAAO,QAAQ,KAAK,SAAS,KAAK,IAAI,IAAI,EAAE,KAAK;AAC7C,QAAE;AACN,WAAO;AAAA,EACX;AAAA,EACA,eAAe,KAAK;AAChB,SAAM,UAAS,IAAI,KAAK,IAAI,KAAK,OAAO,IAAI,KAAK,GAAG,KAAK,GAAG,KAAK;AAC7D,UAAI,EAAE,OAAO,KAAK,IAAI,KAAK,SAAS,CAAC;AACrC,UAAI,YAAY,IAAI,IAAI,SAAS,IAAI,IAAI,IAAI,CAAC,KAAK,IAAI,OAAO,IAAI,SAAS,IAAI;AAC/E,UAAI,MAAM,iBAAiB,KAAK,GAAG,MAAM,OAAO,SAAS;AACzD,UAAI,CAAC;AACD;AACJ,eAAS,IAAI,IAAI,GAAG,KAAK,GAAG,KAAK;AAC7B,YAAI,EAAE,OAAAC,QAAO,MAAAC,MAAK,IAAI,KAAK,SAAS,CAAC;AACrC,YAAI,UAAU,iBAAiB,KAAK,GAAGA,OAAMD,QAAO,IAAI;AACxD,YAAI,CAAC,WAAW,QAAQ;AACpB,mBAAS;AAAA,MACjB;AACA,aAAO,EAAE,OAAO,GAAG,KAAK,MAAM,YAAY,IAAI,IAAI,QAAQ,IAAI,MAAM,IAAI,CAAC,CAAC,IAAI,IAAI;AAAA,IACtF;AAAA,EACJ;AAAA,EACA,MAAM,KAAK;AACP,QAAI,QAAQ,KAAK,eAAe,GAAG;AACnC,QAAI,CAAC;AACD,aAAO;AACX,WAAO,KAAK,QAAQ,MAAM;AACtB,WAAK,kBAAkB;AAC3B,QAAI,MAAM,IAAI;AACV,WAAK,SAAS,cAAc,KAAK,QAAQ,MAAM,OAAO,MAAM,GAAG;AACnE,UAAM,MAAM;AACZ,aAAS,IAAI,MAAM,QAAQ,GAAG,KAAK,IAAI,OAAO,KAAK;AAC/C,UAAI,OAAO,IAAI,KAAK,CAAC,GAAG,MAAM,KAAK,KAAK,aAAa,WAAW,KAAK,SAAS,MAAM,IAAI,MAAM,CAAC,CAAC;AAChG,WAAK,iBAAiB,KAAK,MAAM,KAAK,OAAO,GAAG;AAAA,IACpD;AACA,WAAO;AAAA,EACX;AAAA,EACA,iBAAiB,MAAM,QAAQ,MAAM,SAAS;AAC1C,QAAI,MAAM,KAAK,SAAS,KAAK,KAAK;AAClC,QAAI,QAAQ,IAAI,MAAM,UAAU,IAAI;AACpC,SAAK,SAAS,cAAc,KAAK,QAAQ,KAAK,OAAO,SAAS,KAAK,KAAK,OAAO,OAAO,OAAO,CAAC,CAAC;AAC/F,SAAK,SAAS,KAAK,EAAE,MAAM,OAAO,KAAK,aAAa,CAAC;AAAA,EACzD;AAAA,EACA,oBAAoB;AAChB,QAAI,OAAO,KAAK,SAAS,IAAI;AAC7B,QAAI,MAAM,KAAK,MAAM,WAAW,SAAS,OAAO,IAAI;AACpD,QAAI,IAAI;AACJ,WAAK,SAAS,cAAc,KAAK,QAAQ,KAAK,SAAS,QAAQ,GAAG;AAAA,EAC1E;AACJ;AACA,SAAS,iBAAiB,UAAU,OAAO,OAAO;AAC9C,MAAI,SAAS;AACT,WAAO,SAAS,WAAW,OAAO,SAAS,UAAU;AACzD,SAAO,SAAS,aAAa,GAAG,SAAS,WAAW,KAAK,iBAAiB,SAAS,WAAW,SAAS,QAAQ,GAAG,KAAK,CAAC,CAAC;AAC7H;AACA,SAAS,cAAc,UAAU,OAAO,SAAS;AAC7C,MAAI,SAAS;AACT,WAAO,SAAS,OAAO,OAAO;AAClC,SAAO,SAAS,aAAa,SAAS,aAAa,GAAG,SAAS,UAAU,KAAK,cAAc,SAAS,UAAU,SAAS,QAAQ,GAAG,OAAO,CAAC,CAAC;AAChJ;AACA,SAAS,UAAU,UAAU,OAAO;AAChC,WAAS,IAAI,GAAG,IAAI,OAAO;AACvB,eAAW,SAAS,WAAW;AACnC,SAAO;AACX;AACA,SAAS,eAAe,MAAM,WAAW,SAAS;AAC9C,MAAI,aAAa;AACb,WAAO;AACX,MAAI,OAAO,KAAK;AAChB,MAAI,YAAY;AACZ,WAAO,KAAK,aAAa,GAAG,eAAe,KAAK,YAAY,YAAY,GAAG,KAAK,cAAc,IAAI,UAAU,IAAI,CAAC,CAAC;AACtH,MAAI,YAAY,GAAG;AACf,WAAO,KAAK,KAAK,aAAa,WAAW,IAAI,EAAE,OAAO,IAAI;AAC1D,QAAI,WAAW;AACX,aAAO,KAAK,OAAO,KAAK,KAAK,aAAa,cAAc,IAAI,EAAE,WAAW,SAAS,OAAO,IAAI,CAAC;AAAA,EACtG;AACA,SAAO,KAAK,KAAK,IAAI;AACzB;AACA,SAAS,iBAAiB,KAAK,OAAO,MAAM,OAAO,MAAM;AACrD,MAAI,OAAO,IAAI,KAAK,KAAK,GAAG,QAAQ,OAAO,IAAI,WAAW,KAAK,IAAI,IAAI,MAAM,KAAK;AAClF,MAAI,SAAS,KAAK,cAAc,CAAC,KAAK,kBAAkB,KAAK,IAAI;AAC7D,WAAO;AACX,MAAI,MAAM,MAAM,WAAW,KAAK,SAAS,MAAM,KAAK;AACpD,SAAO,OAAO,CAAC,aAAa,MAAM,KAAK,SAAS,KAAK,IAAI,MAAM;AACnE;AACA,SAAS,aAAa,MAAM,UAAU,OAAO;AACzC,WAAS,IAAI,OAAO,IAAI,SAAS,YAAY;AACzC,QAAI,CAAC,KAAK,YAAY,SAAS,MAAM,CAAC,EAAE,KAAK;AACzC,aAAO;AACf,SAAO;AACX;AACA,SAAS,eAAe,MAAM;AAC1B,SAAO,KAAK,KAAK,YAAY,KAAK,KAAK;AAC3C;AACA,SAAS,aAAa,IAAI,MAAM,IAAI,OAAO;AACvC,MAAI,CAAC,MAAM;AACP,WAAO,GAAG,YAAY,MAAM,EAAE;AAClC,MAAI,QAAQ,GAAG,IAAI,QAAQ,IAAI,GAAG,MAAM,GAAG,IAAI,QAAQ,EAAE;AACzD,MAAI,cAAc,OAAO,KAAK,KAAK;AAC/B,WAAO,GAAG,KAAK,IAAI,YAAY,MAAM,IAAI,KAAK,CAAC;AACnD,MAAI,eAAe,cAAc,OAAO,GAAG,IAAI,QAAQ,EAAE,CAAC;AAE1D,MAAI,aAAa,aAAa,SAAS,CAAC,KAAK;AACzC,iBAAa,IAAI;AAGrB,MAAI,kBAAkB,EAAE,MAAM,QAAQ;AACtC,eAAa,QAAQ,eAAe;AAKpC,WAAS,IAAI,MAAM,OAAO,MAAM,MAAM,MAAM,GAAG,IAAI,GAAG,KAAK,OAAO;AAC9D,QAAI,OAAO,MAAM,KAAK,CAAC,EAAE,KAAK;AAC9B,QAAI,KAAK,YAAY,KAAK,qBAAqB,KAAK;AAChD;AACJ,QAAI,aAAa,QAAQ,CAAC,IAAI;AAC1B,wBAAkB;AAAA,aACb,MAAM,OAAO,CAAC,KAAK;AACxB,mBAAa,OAAO,GAAG,GAAG,CAAC,CAAC;AAAA,EACpC;AAGA,MAAI,uBAAuB,aAAa,QAAQ,eAAe;AAC/D,MAAI,YAAY,CAAC,GAAG,iBAAiB,MAAM;AAC3C,WAAS,UAAU,MAAM,SAAS,IAAI,KAAI,KAAK;AAC3C,QAAI,OAAO,QAAQ;AACnB,cAAU,KAAK,IAAI;AACnB,QAAI,KAAK,MAAM;AACX;AACJ,cAAU,KAAK;AAAA,EACnB;AAGA,WAAS,IAAI,iBAAiB,GAAG,KAAK,GAAG,KAAK;AAC1C,QAAI,WAAW,UAAU,CAAC,GAAG,MAAM,eAAe,SAAS,IAAI;AAC/D,QAAI,OAAO,CAAC,SAAS,WAAW,MAAM,KAAK,KAAK,IAAI,eAAe,IAAI,CAAC,CAAC;AACrE,uBAAiB;AAAA,aACZ,OAAO,CAAC,SAAS,KAAK;AAC3B;AAAA,EACR;AACA,WAAS,IAAI,MAAM,WAAW,KAAK,GAAG,KAAK;AACvC,QAAI,aAAa,IAAI,iBAAiB,MAAM,MAAM,YAAY;AAC9D,QAAI,SAAS,UAAU,SAAS;AAChC,QAAI,CAAC;AACD;AACJ,aAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAG1C,UAAI,cAAc,cAAc,IAAI,wBAAwB,aAAa,MAAM,GAAG,SAAS;AAC3F,UAAI,cAAc,GAAG;AACjB,iBAAS;AACT,sBAAc,CAAC;AAAA,MACnB;AACA,UAAI,SAAS,MAAM,KAAK,cAAc,CAAC,GAAG,QAAQ,MAAM,MAAM,cAAc,CAAC;AAC7E,UAAI,OAAO,eAAe,OAAO,OAAO,OAAO,MAAM,OAAO,KAAK;AAC7D,eAAO,GAAG,QAAQ,MAAM,OAAO,WAAW,GAAG,SAAS,IAAI,MAAM,WAAW,IAAI,IAAI,IAAI,MAAM,cAAc,MAAM,SAAS,GAAG,MAAM,WAAW,SAAS,GAAG,WAAW,MAAM,OAAO,CAAC;AAAA,IAC3L;AAAA,EACJ;AACA,MAAI,aAAa,GAAG,MAAM;AAC1B,WAAS,IAAI,aAAa,SAAS,GAAG,KAAK,GAAG,KAAK;AAC/C,OAAG,QAAQ,MAAM,IAAI,KAAK;AAC1B,QAAI,GAAG,MAAM,SAAS;AAClB;AACJ,QAAI,QAAQ,aAAa,CAAC;AAC1B,QAAI,QAAQ;AACR;AACJ,WAAO,MAAM,OAAO,KAAK;AACzB,SAAK,IAAI,MAAM,KAAK;AAAA,EACxB;AACJ;AACA,SAAS,cAAc,UAAU,OAAO,SAAS,SAAS,QAAQ;AAC9D,MAAI,QAAQ,SAAS;AACjB,QAAI,QAAQ,SAAS;AACrB,eAAW,SAAS,aAAa,GAAG,MAAM,KAAK,cAAc,MAAM,SAAS,QAAQ,GAAG,SAAS,SAAS,KAAK,CAAC,CAAC;AAAA,EACpH;AACA,MAAI,QAAQ,SAAS;AACjB,QAAI,QAAQ,OAAO,eAAe,CAAC;AACnC,QAAI,QAAQ,MAAM,WAAW,QAAQ,EAAE,OAAO,QAAQ;AACtD,eAAW,MAAM,OAAO,MAAM,cAAc,KAAK,EAAE,WAAW,SAAS,OAAO,IAAI,CAAC;AAAA,EACvF;AACA,SAAO;AACX;AACA,SAAS,iBAAiB,IAAI,MAAM,IAAI,MAAM;AAC1C,MAAI,CAAC,KAAK,YAAY,QAAQ,MAAM,GAAG,IAAI,QAAQ,IAAI,EAAE,OAAO,QAAQ,MAAM;AAC1E,QAAI,QAAQ,YAAY,GAAG,KAAK,MAAM,KAAK,IAAI;AAC/C,QAAI,SAAS;AACT,aAAO,KAAK;AAAA,EACpB;AACA,KAAG,aAAa,MAAM,IAAI,IAAI,MAAM,SAAS,KAAK,IAAI,GAAG,GAAG,CAAC,CAAC;AAClE;AACA,SAAS,YAAY,IAAI,MAAM,IAAI;AAC/B,MAAI,QAAQ,GAAG,IAAI,QAAQ,IAAI,GAAG,MAAM,GAAG,IAAI,QAAQ,EAAE;AACzD,MAAI,UAAU,cAAc,OAAO,GAAG;AACtC,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACrC,QAAI,QAAQ,QAAQ,CAAC,GAAG,OAAO,KAAK,QAAQ,SAAS;AACrD,QAAK,QAAQ,SAAS,KAAM,MAAM,KAAK,KAAK,EAAE,KAAK,aAAa;AAC5D,aAAO,GAAG,OAAO,MAAM,MAAM,KAAK,GAAG,IAAI,IAAI,KAAK,CAAC;AACvD,QAAI,QAAQ,MAAM,QAAQ,MAAM,KAAK,QAAQ,CAAC,EAAE,WAAW,MAAM,MAAM,QAAQ,CAAC,GAAG,IAAI,WAAW,QAAQ,CAAC,CAAC;AACxG,aAAO,GAAG,OAAO,MAAM,OAAO,KAAK,GAAG,IAAI,MAAM,KAAK,CAAC;AAAA,EAC9D;AACA,WAAS,IAAI,GAAG,KAAK,MAAM,SAAS,KAAK,IAAI,OAAO,KAAK;AACrD,QAAI,OAAO,MAAM,MAAM,CAAC,KAAK,MAAM,QAAQ,KAAK,KAAK,MAAM,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI,QAAQ,KAChG,MAAM,MAAM,IAAI,CAAC,KAAK,IAAI,MAAM,IAAI,CAAC,KAAK,MAAM,KAAK,IAAI,CAAC,EAAE,WAAW,MAAM,MAAM,IAAI,CAAC,GAAG,IAAI,MAAM,IAAI,CAAC,CAAC;AAC3G,aAAO,GAAG,OAAO,MAAM,OAAO,CAAC,GAAG,EAAE;AAAA,EAC5C;AACA,KAAG,OAAO,MAAM,EAAE;AACtB;AAGA,SAAS,cAAc,OAAO,KAAK;AAC/B,MAAI,SAAS,CAAC,GAAG,WAAW,KAAK,IAAI,MAAM,OAAO,IAAI,KAAK;AAC3D,WAAS,IAAI,UAAU,KAAK,GAAG,KAAK;AAChC,QAAI,QAAQ,MAAM,MAAM,CAAC;AACzB,QAAI,QAAQ,MAAM,OAAO,MAAM,QAAQ,MACnC,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,IAAI,QAAQ,MACpC,MAAM,KAAK,CAAC,EAAE,KAAK,KAAK,aACxB,IAAI,KAAK,CAAC,EAAE,KAAK,KAAK;AACtB;AACJ,QAAI,SAAS,IAAI,MAAM,CAAC,KACnB,KAAK,MAAM,SAAS,KAAK,IAAI,SAAS,MAAM,OAAO,iBAAiB,IAAI,OAAO,iBAC5E,KAAK,IAAI,MAAM,IAAI,CAAC,KAAK,QAAQ;AACrC,aAAO,KAAK,CAAC;AAAA,EACrB;AACA,SAAO;AACX;AAKA,IAAM,WAAN,MAAM,kBAAiB,KAAK;AAAA;AAAA;AAAA;AAAA,EAIxB,YAIA,KAIA,MAEA,OAAO;AACH,UAAM;AACN,SAAK,MAAM;AACX,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,MAAM,KAAK;AACP,QAAI,OAAO,IAAI,OAAO,KAAK,GAAG;AAC9B,QAAI,CAAC;AACD,aAAO,WAAW,KAAK,sCAAsC;AACjE,QAAI,QAAQ,uBAAO,OAAO,IAAI;AAC9B,aAAS,QAAQ,KAAK;AAClB,YAAM,IAAI,IAAI,KAAK,MAAM,IAAI;AACjC,UAAM,KAAK,IAAI,IAAI,KAAK;AACxB,QAAI,UAAU,KAAK,KAAK,OAAO,OAAO,MAAM,KAAK,KAAK;AACtD,WAAO,WAAW,YAAY,KAAK,KAAK,KAAK,KAAK,MAAM,GAAG,IAAI,MAAM,SAAS,KAAK,OAAO,GAAG,GAAG,KAAK,SAAS,IAAI,CAAC,CAAC;AAAA,EACxH;AAAA,EACA,SAAS;AACL,WAAO,QAAQ;AAAA,EACnB;AAAA,EACA,OAAO,KAAK;AACR,WAAO,IAAI,UAAS,KAAK,KAAK,KAAK,MAAM,IAAI,OAAO,KAAK,GAAG,EAAE,MAAM,KAAK,IAAI,CAAC;AAAA,EAClF;AAAA,EACA,IAAI,SAAS;AACT,QAAI,MAAM,QAAQ,UAAU,KAAK,KAAK,CAAC;AACvC,WAAO,IAAI,eAAe,OAAO,IAAI,UAAS,IAAI,KAAK,KAAK,MAAM,KAAK,KAAK;AAAA,EAChF;AAAA,EACA,SAAS;AACL,WAAO,EAAE,UAAU,QAAQ,KAAK,KAAK,KAAK,MAAM,KAAK,MAAM,OAAO,KAAK,MAAM;AAAA,EACjF;AAAA,EACA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,OAAO,YAAY,OAAO,KAAK,QAAQ;AACnD,YAAM,IAAI,WAAW,qCAAqC;AAC9D,WAAO,IAAI,UAAS,KAAK,KAAK,KAAK,MAAM,KAAK,KAAK;AAAA,EACvD;AACJ;AACA,KAAK,OAAO,QAAQ,QAAQ;AAI5B,IAAM,cAAN,MAAM,qBAAoB,KAAK;AAAA;AAAA;AAAA;AAAA,EAI3B,YAIA,MAEA,OAAO;AACH,UAAM;AACN,SAAK,OAAO;AACZ,SAAK,QAAQ;AAAA,EACjB;AAAA,EACA,MAAM,KAAK;AACP,QAAI,QAAQ,uBAAO,OAAO,IAAI;AAC9B,aAAS,QAAQ,IAAI;AACjB,YAAM,IAAI,IAAI,IAAI,MAAM,IAAI;AAChC,UAAM,KAAK,IAAI,IAAI,KAAK;AACxB,QAAI,UAAU,IAAI,KAAK,OAAO,OAAO,IAAI,SAAS,IAAI,KAAK;AAC3D,WAAO,WAAW,GAAG,OAAO;AAAA,EAChC;AAAA,EACA,SAAS;AACL,WAAO,QAAQ;AAAA,EACnB;AAAA,EACA,OAAO,KAAK;AACR,WAAO,IAAI,aAAY,KAAK,MAAM,IAAI,MAAM,KAAK,IAAI,CAAC;AAAA,EAC1D;AAAA,EACA,IAAI,SAAS;AACT,WAAO;AAAA,EACX;AAAA,EACA,SAAS;AACL,WAAO,EAAE,UAAU,WAAW,MAAM,KAAK,MAAM,OAAO,KAAK,MAAM;AAAA,EACrE;AAAA,EACA,OAAO,SAAS,QAAQ,MAAM;AAC1B,QAAI,OAAO,KAAK,QAAQ;AACpB,YAAM,IAAI,WAAW,wCAAwC;AACjE,WAAO,IAAI,aAAY,KAAK,MAAM,KAAK,KAAK;AAAA,EAChD;AACJ;AACA,KAAK,OAAO,WAAW,WAAW;AAKlC,IAAI,iBAAiB,cAAc,MAAM;AACzC;AACA,iBAAiB,SAASE,gBAAe,SAAS;AAC9C,MAAI,MAAM,MAAM,KAAK,MAAM,OAAO;AAClC,MAAI,YAAYA,gBAAe;AAC/B,SAAO;AACX;AACA,eAAe,YAAY,OAAO,OAAO,MAAM,SAAS;AACxD,eAAe,UAAU,cAAc;AACvC,eAAe,UAAU,OAAO;AAQhC,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA,EAIZ,YAKA,KAAK;AACD,SAAK,MAAM;AAIX,SAAK,QAAQ,CAAC;AAId,SAAK,OAAO,CAAC;AAIb,SAAK,UAAU,IAAI;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AAAE,WAAO,KAAK,KAAK,SAAS,KAAK,KAAK,CAAC,IAAI,KAAK;AAAA,EAAK;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlE,KAAK,MAAM;AACP,QAAI,SAAS,KAAK,UAAU,IAAI;AAChC,QAAI,OAAO;AACP,YAAM,IAAI,eAAe,OAAO,MAAM;AAC1C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,UAAU,MAAM;AACZ,QAAI,SAAS,KAAK,MAAM,KAAK,GAAG;AAChC,QAAI,CAAC,OAAO;AACR,WAAK,QAAQ,MAAM,OAAO,GAAG;AACjC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,aAAa;AACb,WAAO,KAAK,MAAM,SAAS;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAM,KAAK;AACf,SAAK,KAAK,KAAK,KAAK,GAAG;AACvB,SAAK,MAAM,KAAK,IAAI;AACpB,SAAK,QAAQ,UAAU,KAAK,OAAO,CAAC;AACpC,SAAK,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,MAAM,KAAK,MAAM,QAAQ,MAAM,OAAO;AAC1C,QAAI,OAAO,YAAY,KAAK,KAAK,MAAM,IAAI,KAAK;AAChD,QAAI;AACA,WAAK,KAAK,IAAI;AAClB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAM,IAAI,SAAS;AAC3B,WAAO,KAAK,QAAQ,MAAM,IAAI,IAAI,MAAM,SAAS,KAAK,OAAO,GAAG,GAAG,CAAC,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,MAAM,IAAI;AACb,WAAO,KAAK,QAAQ,MAAM,IAAI,MAAM,KAAK;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,KAAK,SAAS;AACjB,WAAO,KAAK,YAAY,KAAK,KAAK,OAAO;AAAA,EAC7C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAoBA,aAAa,MAAM,IAAI,OAAO;AAC1B,iBAAa,MAAM,MAAM,IAAI,KAAK;AAClC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,iBAAiB,MAAM,IAAI,MAAM;AAC7B,qBAAiB,MAAM,MAAM,IAAI,IAAI;AACrC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,MAAM,IAAI;AAClB,gBAAY,MAAM,MAAM,EAAE;AAC1B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,KAAK,OAAO,QAAQ;AAChB,SAAK,MAAM,OAAO,MAAM;AACxB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,KAAK,KAAK,QAAQ,GAAG;AACjB,SAAK,MAAM,KAAK,KAAK;AACrB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,KAAK,OAAO,UAAU;AAClB,SAAK,MAAM,OAAO,QAAQ;AAC1B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,MAAM,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC9C,iBAAa,MAAM,MAAM,IAAI,MAAM,KAAK;AACxC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc,KAAK,MAAM,QAAQ,MAAM,OAAO;AAC1C,kBAAc,MAAM,KAAK,MAAM,OAAO,KAAK;AAC3C,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,iBAAiB,KAAK,MAAM,OAAO;AAC/B,SAAK,KAAK,IAAI,SAAS,KAAK,MAAM,KAAK,CAAC;AACxC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,gBAAgB,MAAM,OAAO;AACzB,SAAK,KAAK,IAAI,YAAY,MAAM,KAAK,CAAC;AACtC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,YAAY,KAAK,MAAM;AACnB,SAAK,KAAK,IAAI,gBAAgB,KAAK,IAAI,CAAC;AACxC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,eAAe,KAAK,MAAM;AACtB,QAAI,OAAO,KAAK,IAAI,OAAO,GAAG;AAC9B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,yBAAyB,GAAG;AACrD,QAAI,gBAAgB,MAAM;AACtB,UAAI,KAAK,QAAQ,KAAK,KAAK;AACvB,aAAK,KAAK,IAAI,mBAAmB,KAAK,IAAI,CAAC;AAAA,IACnD,OACK;AACD,UAAI,MAAM,KAAK,OAAO,OAAO,QAAQ,CAAC;AACtC,aAAO,QAAQ,KAAK,QAAQ,GAAG,GAAG;AAC9B,cAAM,KAAK,IAAI,mBAAmB,KAAK,KAAK,CAAC;AAC7C,cAAM,MAAM,cAAc,GAAG;AAAA,MACjC;AACA,eAAS,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG;AACnC,aAAK,KAAK,MAAM,CAAC,CAAC;AAAA,IAC1B;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,MAAM,KAAK,QAAQ,GAAG,YAAY;AAC9B,UAAM,MAAM,KAAK,OAAO,UAAU;AAClC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAM,IAAI,MAAM;AACpB,YAAQ,MAAM,MAAM,IAAI,IAAI;AAC5B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,WAAW,MAAM,IAAI,MAAM;AACvB,eAAW,MAAM,MAAM,IAAI,IAAI;AAC/B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,kBAAkB,KAAK,YAAY,OAAO;AACtC,sBAAkB,MAAM,KAAK,YAAY,KAAK;AAC9C,WAAO;AAAA,EACX;AACJ;;;ACzmEA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAGA,IAAM,cAAc,uBAAO,OAAO,IAAI;AAKtC,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMZ,YAKA,SAKA,OAAO,QAAQ;AACX,SAAK,UAAU;AACf,SAAK,QAAQ;AACb,SAAK,SAAS,UAAU,CAAC,IAAI,eAAe,QAAQ,IAAI,KAAK,GAAG,QAAQ,IAAI,KAAK,CAAC,CAAC;AAAA,EACvF;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AAAE,WAAO,KAAK,QAAQ;AAAA,EAAK;AAAA;AAAA;AAAA;AAAA,EAIxC,IAAI,OAAO;AAAE,WAAO,KAAK,MAAM;AAAA,EAAK;AAAA;AAAA;AAAA;AAAA,EAIpC,IAAI,OAAO;AAAE,WAAO,KAAK,MAAM;AAAA,EAAK;AAAA;AAAA;AAAA;AAAA,EAIpC,IAAI,KAAK;AAAE,WAAO,KAAK,IAAI;AAAA,EAAK;AAAA;AAAA;AAAA;AAAA,EAIhC,IAAI,QAAQ;AACR,WAAO,KAAK,OAAO,CAAC,EAAE;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,MAAM;AACN,WAAO,KAAK,OAAO,CAAC,EAAE;AAAA,EAC1B;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,QAAQ;AACR,QAAI,SAAS,KAAK;AAClB,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ;AAC/B,UAAI,OAAO,CAAC,EAAE,MAAM,OAAO,OAAO,CAAC,EAAE,IAAI;AACrC,eAAO;AACf,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,UAAU;AACN,WAAO,KAAK,MAAM,IAAI,MAAM,KAAK,MAAM,KAAK,IAAI,IAAI;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,IAAI,UAAU,MAAM,OAAO;AAI/B,QAAI,WAAW,QAAQ,QAAQ,WAAW,aAAa;AACvD,aAAS,IAAI,GAAG,IAAI,QAAQ,SAAS,KAAK;AACtC,mBAAa;AACb,iBAAW,SAAS;AAAA,IACxB;AACA,QAAI,UAAU,GAAG,MAAM,QAAQ,SAAS,KAAK;AAC7C,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,EAAE,OAAO,IAAI,IAAI,OAAO,CAAC,GAAG,UAAU,GAAG,QAAQ,MAAM,OAAO;AAClE,SAAG,aAAa,QAAQ,IAAI,MAAM,GAAG,GAAG,QAAQ,IAAI,IAAI,GAAG,GAAG,IAAI,MAAM,QAAQ,OAAO;AACvF,UAAI,KAAK;AACL,gCAAwB,IAAI,UAAU,WAAW,SAAS,WAAW,cAAc,WAAW,eAAe,KAAK,CAAC;AAAA,IAC3H;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,YAAY,IAAI,MAAM;AAClB,QAAI,UAAU,GAAG,MAAM,QAAQ,SAAS,KAAK;AAC7C,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,EAAE,OAAO,IAAI,IAAI,OAAO,CAAC,GAAG,UAAU,GAAG,QAAQ,MAAM,OAAO;AAClE,UAAI,OAAO,QAAQ,IAAI,MAAM,GAAG,GAAG,KAAK,QAAQ,IAAI,IAAI,GAAG;AAC3D,UAAI,GAAG;AACH,WAAG,YAAY,MAAM,EAAE;AAAA,MAC3B,OACK;AACD,WAAG,iBAAiB,MAAM,IAAI,IAAI;AAClC,gCAAwB,IAAI,SAAS,KAAK,WAAW,KAAK,CAAC;AAAA,MAC/D;AAAA,IACJ;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,SAAS,MAAM,KAAK,WAAW,OAAO;AACzC,QAAI,QAAQ,KAAK,OAAO,gBAAgB,IAAI,cAAc,IAAI,IACxD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,QAAQ,KAAK,KAAK,KAAK,MAAM,GAAG,KAAK,QAAQ;AACtF,QAAI;AACA,aAAO;AACX,aAAS,QAAQ,KAAK,QAAQ,GAAG,SAAS,GAAG,SAAS;AAClD,UAAI,QAAQ,MAAM,IACZ,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,OAAO,QAAQ,CAAC,GAAG,KAAK,MAAM,KAAK,GAAG,KAAK,QAAQ,IACxG,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,KAAK,KAAK,GAAG,KAAK,MAAM,QAAQ,CAAC,GAAG,KAAK,MAAM,KAAK,IAAI,GAAG,KAAK,QAAQ;AACjH,UAAI;AACA,eAAO;AAAA,IACf;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,OAAO,KAAK,MAAM,OAAO,GAAG;AACxB,WAAO,KAAK,SAAS,MAAM,IAAI,KAAK,KAAK,SAAS,MAAM,CAAC,IAAI,KAAK,IAAI,aAAa,KAAK,KAAK,CAAC,CAAC;AAAA,EACnG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,QAAQ,KAAK;AAChB,WAAO,gBAAgB,KAAK,KAAK,GAAG,GAAG,CAAC,KAAK,IAAI,aAAa,GAAG;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,MAAM,KAAK;AACd,WAAO,gBAAgB,KAAK,KAAK,IAAI,QAAQ,MAAM,IAAI,YAAY,EAAE,KAAK,IAAI,aAAa,GAAG;AAAA,EAClG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,SAAS,KAAK,MAAM;AACvB,QAAI,CAAC,QAAQ,CAAC,KAAK;AACf,YAAM,IAAI,WAAW,sCAAsC;AAC/D,QAAI,MAAM,YAAY,KAAK,IAAI;AAC/B,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,qBAAqB,KAAK,IAAI,UAAU;AACjE,WAAO,IAAI,SAAS,KAAK,IAAI;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,OAAO,IAAI,gBAAgB;AAC9B,QAAI,MAAM;AACN,YAAM,IAAI,WAAW,wCAAwC,EAAE;AACnE,gBAAY,EAAE,IAAI;AAClB,mBAAe,UAAU,SAAS;AAClC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,cAAc;AACV,WAAO,cAAc,QAAQ,KAAK,SAAS,KAAK,KAAK,EAAE,YAAY;AAAA,EACvE;AACJ;AACA,UAAU,UAAU,UAAU;AAI9B,IAAM,iBAAN,MAAqB;AAAA;AAAA;AAAA;AAAA,EAIjB,YAIA,OAIA,KAAK;AACD,SAAK,QAAQ;AACb,SAAK,MAAM;AAAA,EACf;AACJ;AACA,IAAI,2BAA2B;AAC/B,SAAS,mBAAmB,MAAM;AAC9B,MAAI,CAAC,4BAA4B,CAAC,KAAK,OAAO,eAAe;AACzD,+BAA2B;AAC3B,YAAQ,MAAM,EAAE,0EAA0E,KAAK,OAAO,KAAK,OAAO,GAAG;AAAA,EACzH;AACJ;AAOA,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA;AAAA;AAAA;AAAA,EAIlC,YAAY,SAAS,QAAQ,SAAS;AAClC,uBAAmB,OAAO;AAC1B,uBAAmB,KAAK;AACxB,UAAM,SAAS,KAAK;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,UAAU;AAAE,WAAO,KAAK,QAAQ,OAAO,KAAK,MAAM,MAAM,KAAK,QAAQ;AAAA,EAAM;AAAA,EAC/E,IAAI,KAAK,SAAS;AACd,QAAI,QAAQ,IAAI,QAAQ,QAAQ,IAAI,KAAK,IAAI,CAAC;AAC9C,QAAI,CAAC,MAAM,OAAO;AACd,aAAO,UAAU,KAAK,KAAK;AAC/B,QAAI,UAAU,IAAI,QAAQ,QAAQ,IAAI,KAAK,MAAM,CAAC;AAClD,WAAO,IAAI,eAAc,QAAQ,OAAO,gBAAgB,UAAU,OAAO,KAAK;AAAA,EAClF;AAAA,EACA,QAAQ,IAAI,UAAU,MAAM,OAAO;AAC/B,UAAM,QAAQ,IAAI,OAAO;AACzB,QAAI,WAAW,MAAM,OAAO;AACxB,UAAI,QAAQ,KAAK,MAAM,YAAY,KAAK,GAAG;AAC3C,UAAI;AACA,WAAG,YAAY,KAAK;AAAA,IAC5B;AAAA,EACJ;AAAA,EACA,GAAG,OAAO;AACN,WAAO,iBAAiB,kBAAiB,MAAM,UAAU,KAAK,UAAU,MAAM,QAAQ,KAAK;AAAA,EAC/F;AAAA,EACA,cAAc;AACV,WAAO,IAAI,aAAa,KAAK,QAAQ,KAAK,IAAI;AAAA,EAClD;AAAA,EACA,SAAS;AACL,WAAO,EAAE,MAAM,QAAQ,QAAQ,KAAK,QAAQ,MAAM,KAAK,KAAK;AAAA,EAChE;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,SAAS,KAAK,MAAM;AACvB,QAAI,OAAO,KAAK,UAAU,YAAY,OAAO,KAAK,QAAQ;AACtD,YAAM,IAAI,WAAW,0CAA0C;AACnE,WAAO,IAAI,eAAc,IAAI,QAAQ,KAAK,MAAM,GAAG,IAAI,QAAQ,KAAK,IAAI,CAAC;AAAA,EAC7E;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,KAAK,QAAQ,OAAO,QAAQ;AACtC,QAAI,UAAU,IAAI,QAAQ,MAAM;AAChC,WAAO,IAAI,KAAK,SAAS,QAAQ,SAAS,UAAU,IAAI,QAAQ,IAAI,CAAC;AAAA,EACzE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,OAAO,QAAQ,SAAS,OAAO,MAAM;AACjC,QAAI,OAAO,QAAQ,MAAM,MAAM;AAC/B,QAAI,CAAC,QAAQ;AACT,aAAO,QAAQ,IAAI,IAAI;AAC3B,QAAI,CAAC,MAAM,OAAO,eAAe;AAC7B,UAAI,QAAQ,UAAU,SAAS,OAAO,MAAM,IAAI,KAAK,UAAU,SAAS,OAAO,CAAC,MAAM,IAAI;AAC1F,UAAI;AACA,gBAAQ,MAAM;AAAA;AAEd,eAAO,UAAU,KAAK,OAAO,IAAI;AAAA,IACzC;AACA,QAAI,CAAC,QAAQ,OAAO,eAAe;AAC/B,UAAI,QAAQ,GAAG;AACX,kBAAU;AAAA,MACd,OACK;AACD,mBAAW,UAAU,SAAS,SAAS,CAAC,MAAM,IAAI,KAAK,UAAU,SAAS,SAAS,MAAM,IAAI,GAAG;AAChG,YAAK,QAAQ,MAAM,MAAM,OAAS,OAAO;AACrC,oBAAU;AAAA,MAClB;AAAA,IACJ;AACA,WAAO,IAAI,eAAc,SAAS,KAAK;AAAA,EAC3C;AACJ;AACA,UAAU,OAAO,QAAQ,aAAa;AACtC,IAAM,eAAN,MAAM,cAAa;AAAA,EACf,YAAY,QAAQ,MAAM;AACtB,SAAK,SAAS;AACd,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,SAAS;AACT,WAAO,IAAI,cAAa,QAAQ,IAAI,KAAK,MAAM,GAAG,QAAQ,IAAI,KAAK,IAAI,CAAC;AAAA,EAC5E;AAAA,EACA,QAAQ,KAAK;AACT,WAAO,cAAc,QAAQ,IAAI,QAAQ,KAAK,MAAM,GAAG,IAAI,QAAQ,KAAK,IAAI,CAAC;AAAA,EACjF;AACJ;AAQA,IAAM,gBAAN,MAAM,uBAAsB,UAAU;AAAA;AAAA;AAAA;AAAA;AAAA,EAKlC,YAAY,MAAM;AACd,QAAI,OAAO,KAAK;AAChB,QAAI,OAAO,KAAK,KAAK,CAAC,EAAE,QAAQ,KAAK,MAAM,KAAK,QAAQ;AACxD,UAAM,MAAM,IAAI;AAChB,SAAK,OAAO;AAAA,EAChB;AAAA,EACA,IAAI,KAAK,SAAS;AACd,QAAI,EAAE,SAAS,IAAI,IAAI,QAAQ,UAAU,KAAK,MAAM;AACpD,QAAI,OAAO,IAAI,QAAQ,GAAG;AAC1B,QAAI;AACA,aAAO,UAAU,KAAK,IAAI;AAC9B,WAAO,IAAI,eAAc,IAAI;AAAA,EACjC;AAAA,EACA,UAAU;AACN,WAAO,IAAI,MAAM,SAAS,KAAK,KAAK,IAAI,GAAG,GAAG,CAAC;AAAA,EACnD;AAAA,EACA,GAAG,OAAO;AACN,WAAO,iBAAiB,kBAAiB,MAAM,UAAU,KAAK;AAAA,EAClE;AAAA,EACA,SAAS;AACL,WAAO,EAAE,MAAM,QAAQ,QAAQ,KAAK,OAAO;AAAA,EAC/C;AAAA,EACA,cAAc;AAAE,WAAO,IAAI,aAAa,KAAK,MAAM;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAItD,OAAO,SAAS,KAAK,MAAM;AACvB,QAAI,OAAO,KAAK,UAAU;AACtB,YAAM,IAAI,WAAW,0CAA0C;AACnE,WAAO,IAAI,eAAc,IAAI,QAAQ,KAAK,MAAM,CAAC;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA,EAIA,OAAO,OAAO,KAAK,MAAM;AACrB,WAAO,IAAI,eAAc,IAAI,QAAQ,IAAI,CAAC;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAO,aAAa,MAAM;AACtB,WAAO,CAAC,KAAK,UAAU,KAAK,KAAK,KAAK,eAAe;AAAA,EACzD;AACJ;AACA,cAAc,UAAU,UAAU;AAClC,UAAU,OAAO,QAAQ,aAAa;AACtC,IAAM,eAAN,MAAM,cAAa;AAAA,EACf,YAAY,QAAQ;AAChB,SAAK,SAAS;AAAA,EAClB;AAAA,EACA,IAAI,SAAS;AACT,QAAI,EAAE,SAAS,IAAI,IAAI,QAAQ,UAAU,KAAK,MAAM;AACpD,WAAO,UAAU,IAAI,aAAa,KAAK,GAAG,IAAI,IAAI,cAAa,GAAG;AAAA,EACtE;AAAA,EACA,QAAQ,KAAK;AACT,QAAI,OAAO,IAAI,QAAQ,KAAK,MAAM,GAAG,OAAO,KAAK;AACjD,QAAI,QAAQ,cAAc,aAAa,IAAI;AACvC,aAAO,IAAI,cAAc,IAAI;AACjC,WAAO,UAAU,KAAK,IAAI;AAAA,EAC9B;AACJ;AAOA,IAAM,eAAN,MAAM,sBAAqB,UAAU;AAAA;AAAA;AAAA;AAAA,EAIjC,YAAY,KAAK;AACb,UAAM,IAAI,QAAQ,CAAC,GAAG,IAAI,QAAQ,IAAI,QAAQ,IAAI,CAAC;AAAA,EACvD;AAAA,EACA,QAAQ,IAAI,UAAU,MAAM,OAAO;AAC/B,QAAI,WAAW,MAAM,OAAO;AACxB,SAAG,OAAO,GAAG,GAAG,IAAI,QAAQ,IAAI;AAChC,UAAI,MAAM,UAAU,QAAQ,GAAG,GAAG;AAClC,UAAI,CAAC,IAAI,GAAG,GAAG,SAAS;AACpB,WAAG,aAAa,GAAG;AAAA,IAC3B,OACK;AACD,YAAM,QAAQ,IAAI,OAAO;AAAA,IAC7B;AAAA,EACJ;AAAA,EACA,SAAS;AAAE,WAAO,EAAE,MAAM,MAAM;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAInC,OAAO,SAAS,KAAK;AAAE,WAAO,IAAI,cAAa,GAAG;AAAA,EAAG;AAAA,EACrD,IAAI,KAAK;AAAE,WAAO,IAAI,cAAa,GAAG;AAAA,EAAG;AAAA,EACzC,GAAG,OAAO;AAAE,WAAO,iBAAiB;AAAA,EAAc;AAAA,EAClD,cAAc;AAAE,WAAO;AAAA,EAAa;AACxC;AACA,UAAU,OAAO,OAAO,YAAY;AACpC,IAAM,cAAc;AAAA,EAChB,MAAM;AAAE,WAAO;AAAA,EAAM;AAAA,EACrB,QAAQ,KAAK;AAAE,WAAO,IAAI,aAAa,GAAG;AAAA,EAAG;AACjD;AAKA,SAAS,gBAAgB,KAAK,MAAM,KAAK,OAAO,KAAK,OAAO,OAAO;AAC/D,MAAI,KAAK;AACL,WAAO,cAAc,OAAO,KAAK,GAAG;AACxC,WAAS,IAAI,SAAS,MAAM,IAAI,IAAI,IAAI,MAAM,IAAI,IAAI,KAAK,aAAa,KAAK,GAAG,KAAK,KAAK;AACtF,QAAI,QAAQ,KAAK,MAAM,CAAC;AACxB,QAAI,CAAC,MAAM,QAAQ;AACf,UAAI,QAAQ,gBAAgB,KAAK,OAAO,MAAM,KAAK,MAAM,IAAI,MAAM,aAAa,GAAG,KAAK,IAAI;AAC5F,UAAI;AACA,eAAO;AAAA,IACf,WACS,CAAC,QAAQ,cAAc,aAAa,KAAK,GAAG;AACjD,aAAO,cAAc,OAAO,KAAK,OAAO,MAAM,IAAI,MAAM,WAAW,EAAE;AAAA,IACzE;AACA,WAAO,MAAM,WAAW;AAAA,EAC5B;AACA,SAAO;AACX;AACA,SAAS,wBAAwB,IAAI,UAAU,MAAM;AACjD,MAAI,OAAO,GAAG,MAAM,SAAS;AAC7B,MAAI,OAAO;AACP;AACJ,MAAI,OAAO,GAAG,MAAM,IAAI;AACxB,MAAI,EAAE,gBAAgB,eAAe,gBAAgB;AACjD;AACJ,MAAI,MAAM,GAAG,QAAQ,KAAK,IAAI,GAAG;AACjC,MAAI,QAAQ,CAAC,OAAO,KAAK,UAAU,UAAU;AAAE,QAAI,OAAO;AACtD,YAAM;AAAA,EAAO,CAAC;AAClB,KAAG,aAAa,UAAU,KAAK,GAAG,IAAI,QAAQ,GAAG,GAAG,IAAI,CAAC;AAC7D;AAEA,IAAM,cAAc;AAApB,IAAuB,gBAAgB;AAAvC,IAA0C,iBAAiB;AAuB3D,IAAM,cAAN,cAA0B,UAAU;AAAA;AAAA;AAAA;AAAA,EAIhC,YAAY,OAAO;AACf,UAAM,MAAM,GAAG;AAEf,SAAK,kBAAkB;AAGvB,SAAK,UAAU;AAEf,SAAK,OAAO,uBAAO,OAAO,IAAI;AAC9B,SAAK,OAAO,KAAK,IAAI;AACrB,SAAK,eAAe,MAAM;AAC1B,SAAK,cAAc,MAAM;AAAA,EAC7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,IAAI,YAAY;AACZ,QAAI,KAAK,kBAAkB,KAAK,MAAM,QAAQ;AAC1C,WAAK,eAAe,KAAK,aAAa,IAAI,KAAK,KAAK,KAAK,QAAQ,MAAM,KAAK,eAAe,CAAC;AAC5F,WAAK,kBAAkB,KAAK,MAAM;AAAA,IACtC;AACA,WAAO,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa,WAAW;AACpB,QAAI,UAAU,MAAM,OAAO,KAAK;AAC5B,YAAM,IAAI,WAAW,qEAAqE;AAC9F,SAAK,eAAe;AACpB,SAAK,kBAAkB,KAAK,MAAM;AAClC,SAAK,WAAW,KAAK,UAAU,eAAe,CAAC;AAC/C,SAAK,cAAc;AACnB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,eAAe;AACf,YAAQ,KAAK,UAAU,eAAe;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAIA,eAAe,OAAO;AAClB,SAAK,cAAc;AACnB,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,OAAO;AACf,QAAI,CAAC,KAAK,QAAQ,KAAK,eAAe,KAAK,UAAU,MAAM,MAAM,GAAG,KAAK;AACrE,WAAK,eAAe,KAAK;AAC7B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,cAAc,MAAM;AAChB,WAAO,KAAK,YAAY,KAAK,SAAS,KAAK,eAAe,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC;AAAA,EAC3F;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,MAAM;AACnB,WAAO,KAAK,YAAY,KAAK,cAAc,KAAK,eAAe,KAAK,UAAU,MAAM,MAAM,CAAC,CAAC;AAAA,EAChG;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,iBAAiB;AACjB,YAAQ,KAAK,UAAU,iBAAiB;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAM,KAAK;AACf,UAAM,QAAQ,MAAM,GAAG;AACvB,SAAK,UAAU,KAAK,UAAU,CAAC;AAC/B,SAAK,cAAc;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,MAAM;AACV,SAAK,OAAO;AACZ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,iBAAiB,OAAO;AACpB,SAAK,UAAU,QAAQ,MAAM,KAAK;AAClC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,qBAAqB,MAAM,eAAe,MAAM;AAC5C,QAAI,YAAY,KAAK;AACrB,QAAI;AACA,aAAO,KAAK,KAAK,KAAK,gBAAgB,UAAU,QAAQ,UAAU,MAAM,MAAM,IAAK,UAAU,MAAM,YAAY,UAAU,GAAG,KAAK,KAAK,KAAM;AAChJ,cAAU,YAAY,MAAM,IAAI;AAChC,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB;AACd,SAAK,UAAU,QAAQ,IAAI;AAC3B,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,MAAM,MAAM,IAAI;AACvB,QAAI,SAAS,KAAK,IAAI,KAAK;AAC3B,QAAI,QAAQ,MAAM;AACd,UAAI,CAAC;AACD,eAAO,KAAK,gBAAgB;AAChC,aAAO,KAAK,qBAAqB,OAAO,KAAK,IAAI,GAAG,IAAI;AAAA,IAC5D,OACK;AACD,UAAI,MAAM;AACN,aAAK;AACT,WAAK,MAAM,OAAO,OAAO;AACzB,UAAI,CAAC;AACD,eAAO,KAAK,YAAY,MAAM,EAAE;AACpC,UAAI,QAAQ,KAAK;AACjB,UAAI,CAAC,OAAO;AACR,YAAI,QAAQ,KAAK,IAAI,QAAQ,IAAI;AACjC,gBAAQ,MAAM,OAAO,MAAM,MAAM,IAAI,MAAM,YAAY,KAAK,IAAI,QAAQ,EAAE,CAAC;AAAA,MAC/E;AACA,WAAK,iBAAiB,MAAM,IAAI,OAAO,KAAK,MAAM,KAAK,CAAC;AACxD,UAAI,CAAC,KAAK,UAAU;AAChB,aAAK,aAAa,UAAU,KAAK,KAAK,UAAU,GAAG,CAAC;AACxD,aAAO;AAAA,IACX;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,QAAQ,KAAK,OAAO;AAChB,SAAK,KAAK,OAAO,OAAO,WAAW,MAAM,IAAI,GAAG,IAAI;AACpD,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,QAAQ,KAAK;AACT,WAAO,KAAK,KAAK,OAAO,OAAO,WAAW,MAAM,IAAI,GAAG;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,YAAY;AACZ,aAAS,KAAK,KAAK;AACf,aAAO;AACX,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,iBAAiB;AACb,SAAK,WAAW;AAChB,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,mBAAmB;AACnB,YAAQ,KAAK,UAAU,kBAAkB;AAAA,EAC7C;AACJ;AAEA,SAAS,KAAK,GAAG,MAAM;AACnB,SAAO,CAAC,QAAQ,CAAC,IAAI,IAAI,EAAE,KAAK,IAAI;AACxC;AACA,IAAM,YAAN,MAAgB;AAAA,EACZ,YAAY,MAAM,MAAM,MAAM;AAC1B,SAAK,OAAO;AACZ,SAAK,OAAO,KAAK,KAAK,MAAM,IAAI;AAChC,SAAK,QAAQ,KAAK,KAAK,OAAO,IAAI;AAAA,EACtC;AACJ;AACA,IAAM,aAAa;AAAA,EACf,IAAI,UAAU,OAAO;AAAA,IACjB,KAAK,QAAQ;AAAE,aAAO,OAAO,OAAO,OAAO,OAAO,YAAY,cAAc;AAAA,IAAG;AAAA,IAC/E,MAAM,IAAI;AAAE,aAAO,GAAG;AAAA,IAAK;AAAA,EAC/B,CAAC;AAAA,EACD,IAAI,UAAU,aAAa;AAAA,IACvB,KAAK,QAAQ,UAAU;AAAE,aAAO,OAAO,aAAa,UAAU,QAAQ,SAAS,GAAG;AAAA,IAAG;AAAA,IACrF,MAAM,IAAI;AAAE,aAAO,GAAG;AAAA,IAAW;AAAA,EACrC,CAAC;AAAA,EACD,IAAI,UAAU,eAAe;AAAA,IACzB,KAAK,QAAQ;AAAE,aAAO,OAAO,eAAe;AAAA,IAAM;AAAA,IAClD,MAAM,IAAI,QAAQ,MAAM,OAAO;AAAE,aAAO,MAAM,UAAU,UAAU,GAAG,cAAc;AAAA,IAAM;AAAA,EAC7F,CAAC;AAAA,EACD,IAAI,UAAU,qBAAqB;AAAA,IAC/B,OAAO;AAAE,aAAO;AAAA,IAAG;AAAA,IACnB,MAAM,IAAI,MAAM;AAAE,aAAO,GAAG,mBAAmB,OAAO,IAAI;AAAA,IAAM;AAAA,EACpE,CAAC;AACL;AAGA,IAAM,gBAAN,MAAoB;AAAA,EAChB,YAAY,QAAQ,SAAS;AACzB,SAAK,SAAS;AACd,SAAK,UAAU,CAAC;AAChB,SAAK,eAAe,uBAAO,OAAO,IAAI;AACtC,SAAK,SAAS,WAAW,MAAM;AAC/B,QAAI;AACA,cAAQ,QAAQ,YAAU;AACtB,YAAI,KAAK,aAAa,OAAO,GAAG;AAC5B,gBAAM,IAAI,WAAW,mDAAmD,OAAO,MAAM,GAAG;AAC5F,aAAK,QAAQ,KAAK,MAAM;AACxB,aAAK,aAAa,OAAO,GAAG,IAAI;AAChC,YAAI,OAAO,KAAK;AACZ,eAAK,OAAO,KAAK,IAAI,UAAU,OAAO,KAAK,OAAO,KAAK,OAAO,MAAM,CAAC;AAAA,MAC7E,CAAC;AAAA,EACT;AACJ;AAUA,IAAM,cAAN,MAAM,aAAY;AAAA;AAAA;AAAA;AAAA,EAId,YAIA,QAAQ;AACJ,SAAK,SAAS;AAAA,EAClB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,SAAS;AACT,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,UAAU;AACV,WAAO,KAAK,OAAO;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA,EAIA,MAAM,IAAI;AACN,WAAO,KAAK,iBAAiB,EAAE,EAAE;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA,EAIA,kBAAkB,IAAI,SAAS,IAAI;AAC/B,aAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,QAAQ;AAC5C,UAAI,KAAK,QAAQ;AACb,YAAI,SAAS,KAAK,OAAO,QAAQ,CAAC;AAClC,YAAI,OAAO,KAAK,qBAAqB,CAAC,OAAO,KAAK,kBAAkB,KAAK,QAAQ,IAAI,IAAI;AACrF,iBAAO;AAAA,MACf;AACJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,iBAAiB,QAAQ;AACrB,QAAI,CAAC,KAAK,kBAAkB,MAAM;AAC9B,aAAO,EAAE,OAAO,MAAM,cAAc,CAAC,EAAE;AAC3C,QAAI,MAAM,CAAC,MAAM,GAAG,WAAW,KAAK,WAAW,MAAM,GAAG,OAAO;AAI/D,eAAS;AACL,UAAI,UAAU;AACd,eAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,QAAQ,KAAK;AACjD,YAAI,SAAS,KAAK,OAAO,QAAQ,CAAC;AAClC,YAAI,OAAO,KAAK,mBAAmB;AAC/B,cAAI,IAAI,OAAO,KAAK,CAAC,EAAE,IAAI,GAAG,WAAW,OAAO,KAAK,CAAC,EAAE,QAAQ;AAChE,cAAI,KAAK,IAAI,IAAI,UACb,OAAO,KAAK,kBAAkB,KAAK,QAAQ,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,UAAU,QAAQ;AACzF,cAAI,MAAM,SAAS,kBAAkB,IAAI,CAAC,GAAG;AACzC,eAAG,QAAQ,uBAAuB,MAAM;AACxC,gBAAI,CAAC,MAAM;AACP,qBAAO,CAAC;AACR,uBAAS,IAAI,GAAG,IAAI,KAAK,OAAO,QAAQ,QAAQ;AAC5C,qBAAK,KAAK,IAAI,IAAI,EAAE,OAAO,UAAU,GAAG,IAAI,OAAO,IAAI,EAAE,OAAO,MAAM,GAAG,EAAE,CAAC;AAAA,YACpF;AACA,gBAAI,KAAK,EAAE;AACX,uBAAW,SAAS,WAAW,EAAE;AACjC,sBAAU;AAAA,UACd;AACA,cAAI;AACA,iBAAK,CAAC,IAAI,EAAE,OAAO,UAAU,GAAG,IAAI,OAAO;AAAA,QACnD;AAAA,MACJ;AACA,UAAI,CAAC;AACD,eAAO,EAAE,OAAO,UAAU,cAAc,IAAI;AAAA,IACpD;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAIA,WAAW,IAAI;AACX,QAAI,CAAC,GAAG,OAAO,GAAG,KAAK,GAAG;AACtB,YAAM,IAAI,WAAW,mCAAmC;AAC5D,QAAI,cAAc,IAAI,aAAY,KAAK,MAAM,GAAG,SAAS,KAAK,OAAO;AACrE,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,QAAQ,OAAO,CAAC;AACpB,kBAAY,MAAM,IAAI,IAAI,MAAM,MAAM,IAAI,KAAK,MAAM,IAAI,GAAG,MAAM,WAAW;AAAA,IACjF;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA,EAIA,IAAI,KAAK;AAAE,WAAO,IAAI,YAAY,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIzC,OAAO,OAAO,QAAQ;AAClB,QAAI,UAAU,IAAI,cAAc,OAAO,MAAM,OAAO,IAAI,KAAK,SAAS,OAAO,QAAQ,OAAO,OAAO;AACnG,QAAI,WAAW,IAAI,aAAY,OAAO;AACtC,aAAS,IAAI,GAAG,IAAI,QAAQ,OAAO,QAAQ;AACvC,eAAS,QAAQ,OAAO,CAAC,EAAE,IAAI,IAAI,QAAQ,OAAO,CAAC,EAAE,KAAK,QAAQ,QAAQ;AAC9E,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,QAAQ;AAChB,QAAI,UAAU,IAAI,cAAc,KAAK,QAAQ,OAAO,OAAO;AAC3D,QAAI,SAAS,QAAQ,QAAQ,WAAW,IAAI,aAAY,OAAO;AAC/D,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,UAAI,OAAO,OAAO,CAAC,EAAE;AACrB,eAAS,IAAI,IAAI,KAAK,eAAe,IAAI,IAAI,KAAK,IAAI,IAAI,OAAO,CAAC,EAAE,KAAK,QAAQ,QAAQ;AAAA,IAC7F;AACA,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,cAAc;AACjB,QAAI,SAAS,EAAE,KAAK,KAAK,IAAI,OAAO,GAAG,WAAW,KAAK,UAAU,OAAO,EAAE;AAC1E,QAAI,KAAK;AACL,aAAO,cAAc,KAAK,YAAY,IAAI,OAAK,EAAE,OAAO,CAAC;AAC7D,QAAI,gBAAgB,OAAO,gBAAgB;AACvC,eAAS,QAAQ,cAAc;AAC3B,YAAI,QAAQ,SAAS,QAAQ;AACzB,gBAAM,IAAI,WAAW,oDAAoD;AAC7E,YAAI,SAAS,aAAa,IAAI,GAAG,QAAQ,OAAO,KAAK;AACrD,YAAI,SAAS,MAAM;AACf,iBAAO,IAAI,IAAI,MAAM,OAAO,KAAK,QAAQ,KAAK,OAAO,GAAG,CAAC;AAAA,MACjE;AACJ,WAAO;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,SAAS,QAAQ,MAAM,cAAc;AACxC,QAAI,CAAC;AACD,YAAM,IAAI,WAAW,wCAAwC;AACjE,QAAI,CAAC,OAAO;AACR,YAAM,IAAI,WAAW,wCAAwC;AACjE,QAAI,UAAU,IAAI,cAAc,OAAO,QAAQ,OAAO,OAAO;AAC7D,QAAI,WAAW,IAAI,aAAY,OAAO;AACtC,YAAQ,OAAO,QAAQ,WAAS;AAC5B,UAAI,MAAM,QAAQ,OAAO;AACrB,iBAAS,MAAM,KAAK,SAAS,OAAO,QAAQ,KAAK,GAAG;AAAA,MACxD,WACS,MAAM,QAAQ,aAAa;AAChC,iBAAS,YAAY,UAAU,SAAS,SAAS,KAAK,KAAK,SAAS;AAAA,MACxE,WACS,MAAM,QAAQ,eAAe;AAClC,YAAI,KAAK;AACL,mBAAS,cAAc,KAAK,YAAY,IAAI,OAAO,OAAO,YAAY;AAAA,MAC9E,OACK;AACD,YAAI;AACA,mBAAS,QAAQ,cAAc;AAC3B,gBAAI,SAAS,aAAa,IAAI,GAAG,QAAQ,OAAO,KAAK;AACrD,gBAAI,OAAO,OAAO,MAAM,QAAQ,SAAS,MAAM,YAC3C,OAAO,UAAU,eAAe,KAAK,MAAM,IAAI,GAAG;AAClD,uBAAS,MAAM,IAAI,IAAI,MAAM,SAAS,KAAK,QAAQ,QAAQ,KAAK,IAAI,GAAG,QAAQ;AAC/E;AAAA,YACJ;AAAA,UACJ;AACJ,iBAAS,MAAM,IAAI,IAAI,MAAM,KAAK,QAAQ,QAAQ;AAAA,MACtD;AAAA,IACJ,CAAC;AACD,WAAO;AAAA,EACX;AACJ;AAEA,SAAS,UAAU,KAAK,MAAM,QAAQ;AAClC,WAAS,QAAQ,KAAK;AAClB,QAAI,MAAM,IAAI,IAAI;AAClB,QAAI,eAAe;AACf,YAAM,IAAI,KAAK,IAAI;AAAA,aACd,QAAQ;AACb,YAAM,UAAU,KAAK,MAAM,CAAC,CAAC;AACjC,WAAO,IAAI,IAAI;AAAA,EACnB;AACA,SAAO;AACX;AAMA,IAAM,SAAN,MAAa;AAAA;AAAA;AAAA;AAAA,EAIT,YAIA,MAAM;AACF,SAAK,OAAO;AAIZ,SAAK,QAAQ,CAAC;AACd,QAAI,KAAK;AACL,gBAAU,KAAK,OAAO,MAAM,KAAK,KAAK;AAC1C,SAAK,MAAM,KAAK,MAAM,KAAK,IAAI,MAAM,UAAU,QAAQ;AAAA,EAC3D;AAAA;AAAA;AAAA;AAAA,EAIA,SAAS,OAAO;AAAE,WAAO,MAAM,KAAK,GAAG;AAAA,EAAG;AAC9C;AACA,IAAM,OAAO,uBAAO,OAAO,IAAI;AAC/B,SAAS,UAAU,MAAM;AACrB,MAAI,QAAQ;AACR,WAAO,OAAO,MAAM,EAAE,KAAK,IAAI;AACnC,OAAK,IAAI,IAAI;AACb,SAAO,OAAO;AAClB;AAOA,IAAM,YAAN,MAAgB;AAAA;AAAA;AAAA;AAAA,EAIZ,YAAY,OAAO,OAAO;AAAE,SAAK,MAAM,UAAU,IAAI;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA;AAAA,EAKxD,IAAI,OAAO;AAAE,WAAO,MAAM,OAAO,aAAa,KAAK,GAAG;AAAA,EAAG;AAAA;AAAA;AAAA;AAAA,EAIzD,SAAS,OAAO;AAAE,WAAO,MAAM,KAAK,GAAG;AAAA,EAAG;AAC9C;", "names": ["import_dist", "parent", "index", "wrap", "match", "type", "TransformError", "import_dist"]}