<template>
  <div class="simple-click-menu-test">
    <h1>简化版点击菜单测试</h1>
    
    <div class="instructions">
      <p><strong>测试说明：</strong></p>
      <ul>
        <li>将鼠标悬停在段落左侧，应该看到红色背景的菜单容器</li>
        <li>悬停在段落上应该显示 "+" 和 "⋮⋮" 按钮</li>
        <li>点击 "+" 按钮会弹出提示</li>
        <li>打开浏览器控制台查看调试信息</li>
      </ul>
    </div>

    <div class="editor-container">
      <div ref="editorRef" class="editor"></div>
    </div>

    <div class="debug">
      <h3>调试信息</h3>
      <p>编辑模式: {{ isEditable ? '是' : '否' }}</p>
      <button @click="toggleEditable">切换编辑模式</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import { onMounted, onUnmounted, ref } from 'vue'

import { SimpleClickMenuExtension } from '@/components/tiptap/extensions/click-menu/SimpleClickMenu'

const editorRef = ref<HTMLElement>()
const isEditable = ref(true)
let editor: Editor | null = null

const testContent = `
  <h1>测试标题</h1>
  <p>这是第一个段落。悬停在左侧查看菜单。</p>
  <p>这是第二个段落。</p>
  <h2>二级标题</h2>
  <p>标题下的段落。</p>
  <ul>
    <li>列表项 1</li>
    <li>列表项 2</li>
  </ul>
  <p>最后一个段落。</p>
`

onMounted(() => {
  if (!editorRef.value) return

  console.log('Creating editor with SimpleClickMenuExtension')

  editor = new Editor({
    element: editorRef.value,
    extensions: [
      StarterKit,
      SimpleClickMenuExtension.configure({
        showDragHandle: true,
        showAddButton: true,
        onAddClick: (editor, pos) => {
          console.log('onAddClick called with pos:', pos)
        },
      }),
    ],
    content: testContent,
    editable: isEditable.value,
  })

  console.log('Editor created:', editor)
})

onUnmounted(() => {
  if (editor) {
    editor.destroy()
  }
})

const toggleEditable = () => {
  isEditable.value = !isEditable.value
  if (editor) {
    editor.setEditable(isEditable.value)
  }
}
</script>

<style scoped lang="scss">
.simple-click-menu-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 2rem;

  h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 2rem;
  }

  .instructions {
    background-color: #f0f8ff;
    border: 1px solid #007bff;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;

    p {
      margin-top: 0;
      font-weight: 600;
      color: #007bff;
    }

    ul {
      margin-bottom: 0;
    }

    li {
      margin-bottom: 0.5rem;
    }
  }

  .editor-container {
    border: 2px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem;
    margin-bottom: 2rem;
    background-color: white;
    min-height: 400px;
    position: relative;

    .editor {
      min-height: 350px;

      :deep(.ProseMirror) {
        outline: none;
        padding: 1rem;
        line-height: 1.6;

        h1, h2 {
          margin: 1.5rem 0 1rem 0;
          color: #2c3e50;
        }

        p {
          margin: 1rem 0;
          min-height: 1.5rem;
        }

        ul {
          padding-left: 2rem;
          margin: 1rem 0;
        }

        li {
          margin: 0.5rem 0;
        }
      }
    }
  }

  .debug {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1.5rem;

    h3 {
      margin-top: 0;
      color: #495057;
    }

    button {
      padding: 0.5rem 1rem;
      border: 1px solid #007bff;
      border-radius: 0.25rem;
      background-color: #007bff;
      color: white;
      cursor: pointer;

      &:hover {
        background-color: #0056b3;
      }
    }
  }
}
</style>
