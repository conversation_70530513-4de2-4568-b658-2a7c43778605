{"version": 3, "sources": ["../src/bold.tsx", "../src/index.ts"], "sourcesContent": ["/** @jsxImportSource @tiptap/core */\nimport { Mark, markInputRule, markPasteRule, mergeAttributes } from '@tiptap/core'\n\nexport interface BoldOptions {\n  /**\n   * HTML attributes to add to the bold element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bold: {\n      /**\n       * Set a bold mark\n       */\n      setBold: () => ReturnType\n      /**\n       * Toggle a bold mark\n       */\n      toggleBold: () => ReturnType\n      /**\n       * Unset a bold mark\n       */\n      unsetBold: () => ReturnType\n    }\n  }\n}\n\n/**\n * Matches bold text via `**` as input.\n */\nexport const starInputRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))$/\n\n/**\n * Matches bold text via `**` while pasting.\n */\nexport const starPasteRegex = /(?:^|\\s)(\\*\\*(?!\\s+\\*\\*)((?:[^*]+))\\*\\*(?!\\s+\\*\\*))/g\n\n/**\n * Matches bold text via `__` as input.\n */\nexport const underscoreInputRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))$/\n\n/**\n * Matches bold text via `__` while pasting.\n */\nexport const underscorePasteRegex = /(?:^|\\s)(__(?!\\s+__)((?:[^_]+))__(?!\\s+__))/g\n\n/**\n * This extension allows you to mark text as bold.\n * @see https://tiptap.dev/api/marks/bold\n */\nexport const Bold = Mark.create<BoldOptions>({\n  name: 'bold',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'strong',\n      },\n      {\n        tag: 'b',\n        getAttrs: node => (node as HTMLElement).style.fontWeight !== 'normal' && null,\n      },\n      {\n        style: 'font-weight=400',\n        clearMark: mark => mark.type.name === this.name,\n      },\n      {\n        style: 'font-weight',\n        getAttrs: value => /^(bold(er)?|[5-9]\\d{2,})$/.test(value as string) && null,\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return (\n      <strong {...mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)}>\n        <slot />\n      </strong>\n    )\n  },\n\n  addCommands() {\n    return {\n      setBold:\n        () =>\n        ({ commands }) => {\n          return commands.setMark(this.name)\n        },\n      toggleBold:\n        () =>\n        ({ commands }) => {\n          return commands.toggleMark(this.name)\n        },\n      unsetBold:\n        () =>\n        ({ commands }) => {\n          return commands.unsetMark(this.name)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-b': () => this.editor.commands.toggleBold(),\n      'Mod-B': () => this.editor.commands.toggleBold(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: starInputRegex,\n        type: this.type,\n      }),\n      markInputRule({\n        find: underscoreInputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: starPasteRegex,\n        type: this.type,\n      }),\n      markPasteRule({\n        find: underscorePasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n", "import { Bold } from './bold.jsx'\n\nexport * from './bold.jsx'\n\nexport default Bold\n"], "mappings": ";AACA,SAAS,MAAM,eAAe,eAAe,uBAAuB;AAsF5D;AArDD,IAAM,iBAAiB;AAKvB,IAAM,iBAAiB;AAKvB,IAAM,uBAAuB;AAK7B,IAAM,uBAAuB;AAM7B,IAAM,OAAO,KAAK,OAAoB;AAAA,EAC3C,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,YAAY;AACV,WAAO;AAAA,MACL;AAAA,QACE,KAAK;AAAA,MACP;AAAA,MACA;AAAA,QACE,KAAK;AAAA,QACL,UAAU,UAAS,KAAqB,MAAM,eAAe,YAAY;AAAA,MAC3E;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,WAAW,UAAQ,KAAK,KAAK,SAAS,KAAK;AAAA,MAC7C;AAAA,MACA;AAAA,QACE,OAAO;AAAA,QACP,UAAU,WAAS,4BAA4B,KAAK,KAAe,KAAK;AAAA,MAC1E;AAAA,IACF;AAAA,EACF;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WACE,oBAAC,YAAQ,GAAG,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GACrE,8BAAC,UAAK,GACR;AAAA,EAEJ;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,SACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,QAAQ,KAAK,IAAI;AAAA,MACnC;AAAA,MACF,YACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,WAAW,KAAK,IAAI;AAAA,MACtC;AAAA,MACF,WACE,MACA,CAAC,EAAE,SAAS,MAAM;AAChB,eAAO,SAAS,UAAU,KAAK,IAAI;AAAA,MACrC;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,SAAS,MAAM,KAAK,OAAO,SAAS,WAAW;AAAA,MAC/C,SAAS,MAAM,KAAK,OAAO,SAAS,WAAW;AAAA,IACjD;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,WAAO;AAAA,MACL,cAAc;AAAA,QACZ,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,MACD,cAAc;AAAA,QACZ,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,WAAO;AAAA,MACL,cAAc;AAAA,QACZ,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,MACD,cAAc;AAAA,QACZ,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AC5ID,IAAO,gBAAQ;", "names": []}