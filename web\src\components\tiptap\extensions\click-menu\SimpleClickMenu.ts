import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from '@tiptap/pm/state'

import type { ClickMenuOptions } from './types'

export const SimpleClickMenuExtension = Extension.create<ClickMenuOptions>({
  name: 'simpleClickMenu',

  addOptions() {
    return {
      showDragHandle: true,
      showAddButton: true,
      onAddClick: undefined,
      onDragStart: undefined,
      onDragEnd: undefined,
    }
  },

  addProseMirrorPlugins() {
    const extension = this

    return [
      new Plugin({
        key: new PluginKey('simple-click-menu'),
        view: () => ({
          mount: (view) => {
            console.log('SimpleClickMenu mounted')
            
            // 创建菜单容器
            const container = document.createElement('div')
            container.id = 'simple-click-menu-container'
            container.style.cssText = `
              position: absolute;
              left: -50px;
              top: 0;
              width: 40px;
              height: 100%;
              pointer-events: none;
              z-index: 1000;
              background: rgba(255, 0, 0, 0.1);
            `

            // 添加到编辑器父容器
            const editorParent = view.dom.parentElement
            if (editorParent) {
              editorParent.style.position = 'relative'
              editorParent.appendChild(container)
              console.log('Menu container added to:', editorParent)
            }

            // 添加鼠标事件监听
            const handleMouseOver = (e: MouseEvent) => {
              if (!extension.editor.isEditable) return

              const target = e.target as Element
              const blockElement = target.closest('p, h1, h2, h3, h4, h5, h6, ul, ol, li, blockquote, pre')

              console.log('Mouse over:', {
                target: target.tagName,
                blockElement: blockElement?.tagName,
                isInEditor: blockElement ? view.dom.contains(blockElement) : false
              })

              if (blockElement && view.dom.contains(blockElement)) {
                showMenuForElement(blockElement, container)
              }
            }

            const handleMouseOut = (e: MouseEvent) => {
              const relatedTarget = e.relatedTarget as Element
              if (!relatedTarget || !relatedTarget.closest('#simple-click-menu-container')) {
                hideMenu(container)
              }
            }

            view.dom.addEventListener('mouseover', handleMouseOver)
            view.dom.addEventListener('mouseout', handleMouseOut)

            // 存储清理函数
            ;(view.dom as any)._clickMenuCleanup = () => {
              view.dom.removeEventListener('mouseover', handleMouseOver)
              view.dom.removeEventListener('mouseout', handleMouseOut)
              container.remove()
            }
          },
          
          destroy: (view) => {
            console.log('SimpleClickMenu destroyed')
            if ((view.dom as any)._clickMenuCleanup) {
              ;(view.dom as any)._clickMenuCleanup()
            }
          }
        })
      })
    ]
  },
})

function showMenuForElement(element: Element, container: HTMLElement) {
  // 清除现有菜单
  const existingMenu = container.querySelector('.simple-click-menu')
  if (existingMenu) {
    existingMenu.remove()
  }

  // 创建新菜单
  const menu = document.createElement('div')
  menu.className = 'simple-click-menu'
  menu.style.cssText = `
    position: absolute;
    left: 8px;
    display: flex;
    gap: 4px;
    pointer-events: all;
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  `

  // 计算位置
  const elementRect = element.getBoundingClientRect()
  const containerRect = container.getBoundingClientRect()
  const top = elementRect.top - containerRect.top

  menu.style.top = `${top}px`

  // 添加按钮
  const addButton = document.createElement('button')
  addButton.innerHTML = '+'
  addButton.style.cssText = `
    width: 20px;
    height: 20px;
    border: 1px solid #007bff;
    background: white;
    color: #007bff;
    border-radius: 3px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
  `
  addButton.addEventListener('click', () => {
    console.log('Add button clicked for element:', element.tagName)
    alert('添加按钮被点击了！')
  })

  const dragButton = document.createElement('button')
  dragButton.innerHTML = '⋮⋮'
  dragButton.style.cssText = `
    width: 20px;
    height: 20px;
    border: 1px solid #6c757d;
    background: white;
    color: #6c757d;
    border-radius: 3px;
    cursor: grab;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
  `
  dragButton.addEventListener('mousedown', () => {
    console.log('Drag button clicked for element:', element.tagName)
  })

  menu.appendChild(addButton)
  menu.appendChild(dragButton)
  container.appendChild(menu)

  console.log('Menu created for element:', element.tagName, 'at position:', top)
}

function hideMenu(container: HTMLElement) {
  const menu = container.querySelector('.simple-click-menu')
  if (menu) {
    menu.remove()
    console.log('Menu hidden')
  }
}
