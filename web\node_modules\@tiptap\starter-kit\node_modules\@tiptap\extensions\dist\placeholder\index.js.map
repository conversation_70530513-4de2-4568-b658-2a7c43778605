{"version": 3, "sources": ["../../src/placeholder/placeholder.ts"], "sourcesContent": ["import type { Editor } from '@tiptap/core'\nimport { Extension, isNodeEmpty } from '@tiptap/core'\nimport type { Node as ProsemirrorNode } from '@tiptap/pm/model'\nimport { Plugin, PluginKey } from '@tiptap/pm/state'\nimport { Decoration, DecorationSet } from '@tiptap/pm/view'\n\nexport interface PlaceholderOptions {\n  /**\n   * **The class name for the empty editor**\n   * @default 'is-editor-empty'\n   */\n  emptyEditorClass: string\n\n  /**\n   * **The class name for empty nodes**\n   * @default 'is-empty'\n   */\n  emptyNodeClass: string\n\n  /**\n   * **The placeholder content**\n   *\n   * You can use a function to return a dynamic placeholder or a string.\n   * @default 'Write something …'\n   */\n  placeholder:\n    | ((PlaceholderProps: { editor: Editor; node: ProsemirrorNode; pos: number; hasAnchor: boolean }) => string)\n    | string\n\n  /**\n   * **Checks if the placeholder should be only shown when the editor is editable.**\n   *\n   * If true, the placeholder will only be shown when the editor is editable.\n   * If false, the placeholder will always be shown.\n   * @default true\n   */\n  showOnlyWhenEditable: boolean\n\n  /**\n   * **Checks if the placeholder should be only shown when the current node is empty.**\n   *\n   * If true, the placeholder will only be shown when the current node is empty.\n   * If false, the placeholder will be shown when any node is empty.\n   * @default true\n   */\n  showOnlyCurrent: boolean\n\n  /**\n   * **Controls if the placeholder should be shown for all descendents.**\n   *\n   * If true, the placeholder will be shown for all descendents.\n   * If false, the placeholder will only be shown for the current node.\n   * @default false\n   */\n  includeChildren: boolean\n}\n\n/**\n * This extension allows you to add a placeholder to your editor.\n * A placeholder is a text that appears when the editor or a node is empty.\n * @see https://www.tiptap.dev/api/extensions/placeholder\n */\nexport const Placeholder = Extension.create<PlaceholderOptions>({\n  name: 'placeholder',\n\n  addOptions() {\n    return {\n      emptyEditorClass: 'is-editor-empty',\n      emptyNodeClass: 'is-empty',\n      placeholder: 'Write something …',\n      showOnlyWhenEditable: true,\n      showOnlyCurrent: true,\n      includeChildren: false,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('placeholder'),\n        props: {\n          decorations: ({ doc, selection }) => {\n            const active = this.editor.isEditable || !this.options.showOnlyWhenEditable\n            const { anchor } = selection\n            const decorations: Decoration[] = []\n\n            if (!active) {\n              return null\n            }\n\n            const isEmptyDoc = this.editor.isEmpty\n\n            doc.descendants((node, pos) => {\n              const hasAnchor = anchor >= pos && anchor <= pos + node.nodeSize\n              const isEmpty = !node.isLeaf && isNodeEmpty(node)\n\n              if ((hasAnchor || !this.options.showOnlyCurrent) && isEmpty) {\n                const classes = [this.options.emptyNodeClass]\n\n                if (isEmptyDoc) {\n                  classes.push(this.options.emptyEditorClass)\n                }\n\n                const decoration = Decoration.node(pos, pos + node.nodeSize, {\n                  class: classes.join(' '),\n                  'data-placeholder':\n                    typeof this.options.placeholder === 'function'\n                      ? this.options.placeholder({\n                          editor: this.editor,\n                          node,\n                          pos,\n                          hasAnchor,\n                        })\n                      : this.options.placeholder,\n                })\n\n                decorations.push(decoration)\n              }\n\n              return this.options.includeChildren\n            })\n\n            return DecorationSet.create(doc, decorations)\n          },\n        },\n      }),\n    ]\n  },\n})\n"], "mappings": ";AACA,SAAS,WAAW,mBAAmB;AAEvC,SAAS,QAAQ,iBAAiB;AAClC,SAAS,YAAY,qBAAqB;AA0DnC,IAAM,cAAc,UAAU,OAA2B;AAAA,EAC9D,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,kBAAkB;AAAA,MAClB,gBAAgB;AAAA,MAChB,aAAa;AAAA,MACb,sBAAsB;AAAA,MACtB,iBAAiB;AAAA,MACjB,iBAAiB;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,wBAAwB;AACtB,WAAO;AAAA,MACL,IAAI,OAAO;AAAA,QACT,KAAK,IAAI,UAAU,aAAa;AAAA,QAChC,OAAO;AAAA,UACL,aAAa,CAAC,EAAE,KAAK,UAAU,MAAM;AACnC,kBAAM,SAAS,KAAK,OAAO,cAAc,CAAC,KAAK,QAAQ;AACvD,kBAAM,EAAE,OAAO,IAAI;AACnB,kBAAM,cAA4B,CAAC;AAEnC,gBAAI,CAAC,QAAQ;AACX,qBAAO;AAAA,YACT;AAEA,kBAAM,aAAa,KAAK,OAAO;AAE/B,gBAAI,YAAY,CAAC,MAAM,QAAQ;AAC7B,oBAAM,YAAY,UAAU,OAAO,UAAU,MAAM,KAAK;AACxD,oBAAM,UAAU,CAAC,KAAK,UAAU,YAAY,IAAI;AAEhD,mBAAK,aAAa,CAAC,KAAK,QAAQ,oBAAoB,SAAS;AAC3D,sBAAM,UAAU,CAAC,KAAK,QAAQ,cAAc;AAE5C,oBAAI,YAAY;AACd,0BAAQ,KAAK,KAAK,QAAQ,gBAAgB;AAAA,gBAC5C;AAEA,sBAAM,aAAa,WAAW,KAAK,KAAK,MAAM,KAAK,UAAU;AAAA,kBAC3D,OAAO,QAAQ,KAAK,GAAG;AAAA,kBACvB,oBACE,OAAO,KAAK,QAAQ,gBAAgB,aAChC,KAAK,QAAQ,YAAY;AAAA,oBACvB,QAAQ,KAAK;AAAA,oBACb;AAAA,oBACA;AAAA,oBACA;AAAA,kBACF,CAAC,IACD,KAAK,QAAQ;AAAA,gBACrB,CAAC;AAED,4BAAY,KAAK,UAAU;AAAA,cAC7B;AAEA,qBAAO,KAAK,QAAQ;AAAA,YACtB,CAAC;AAED,mBAAO,cAAc,OAAO,KAAK,WAAW;AAAA,UAC9C;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;", "names": []}