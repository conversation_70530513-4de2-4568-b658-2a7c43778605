{"version": 3, "sources": ["../../@tiptap/extension-text/src/text.ts"], "sourcesContent": ["import { Node } from '@tiptap/core'\n\n/**\n * This extension allows you to create text nodes.\n * @see https://www.tiptap.dev/api/nodes/text\n */\nexport const Text = Node.create({\n  name: 'text',\n  group: 'inline',\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMa,IAAA,OAAO,KAAK,OAAO;EAC9B,MAAM;EACN,OAAO;AACR,CAAA;", "names": []}