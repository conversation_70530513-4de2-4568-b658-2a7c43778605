{"version": 3, "sources": ["../../src/focus/focus.ts"], "sourcesContent": ["import { Extension } from '@tiptap/core'\nimport { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport { Decoration, DecorationSet } from '@tiptap/pm/view'\n\nexport interface FocusOptions {\n  /**\n   * The class name that should be added to the focused node.\n   * @default 'has-focus'\n   * @example 'is-focused'\n   */\n  className: string\n\n  /**\n   * The mode by which the focused node is determined.\n   * - All: All nodes are marked as focused.\n   * - Deepest: Only the deepest node is marked as focused.\n   * - Shallowest: Only the shallowest node is marked as focused.\n   *\n   * @default 'all'\n   * @example 'deepest'\n   * @example 'shallowest'\n   */\n  mode: 'all' | 'deepest' | 'shallowest'\n}\n\n/**\n * This extension allows you to add a class to the focused node.\n * @see https://www.tiptap.dev/api/extensions/focus\n */\nexport const Focus = Extension.create<FocusOptions>({\n  name: 'focus',\n\n  addOptions() {\n    return {\n      className: 'has-focus',\n      mode: 'all',\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('focus'),\n        props: {\n          decorations: ({ doc, selection }) => {\n            const { isEditable, isFocused } = this.editor\n            const { anchor } = selection\n            const decorations: Decoration[] = []\n\n            if (!isEditable || !isFocused) {\n              return DecorationSet.create(doc, [])\n            }\n\n            // Maximum Levels\n            let maxLevels = 0\n\n            if (this.options.mode === 'deepest') {\n              doc.descendants((node, pos) => {\n                if (node.isText) {\n                  return\n                }\n\n                const isCurrent = anchor >= pos && anchor <= pos + node.nodeSize - 1\n\n                if (!isCurrent) {\n                  return false\n                }\n\n                maxLevels += 1\n              })\n            }\n\n            // Loop through current\n            let currentLevel = 0\n\n            doc.descendants((node, pos) => {\n              if (node.isText) {\n                return false\n              }\n\n              const isCurrent = anchor >= pos && anchor <= pos + node.nodeSize - 1\n\n              if (!isCurrent) {\n                return false\n              }\n\n              currentLevel += 1\n\n              const outOfScope =\n                (this.options.mode === 'deepest' && maxLevels - currentLevel > 0) ||\n                (this.options.mode === 'shallowest' && currentLevel > 1)\n\n              if (outOfScope) {\n                return this.options.mode === 'deepest'\n              }\n\n              decorations.push(\n                Decoration.node(pos, pos + node.nodeSize, {\n                  class: this.options.className,\n                }),\n              )\n            })\n\n            return DecorationSet.create(doc, decorations)\n          },\n        },\n      }),\n    ]\n  },\n})\n"], "mappings": ";AAAA,SAAS,iBAAiB;AAC1B,SAAS,QAAQ,iBAAiB;AAClC,SAAS,YAAY,qBAAqB;AA2BnC,IAAM,QAAQ,UAAU,OAAqB;AAAA,EAClD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,WAAW;AAAA,MACX,MAAM;AAAA,IACR;AAAA,EACF;AAAA,EAEA,wBAAwB;AACtB,WAAO;AAAA,MACL,IAAI,OAAO;AAAA,QACT,KAAK,IAAI,UAAU,OAAO;AAAA,QAC1B,OAAO;AAAA,UACL,aAAa,CAAC,EAAE,KAAK,UAAU,MAAM;AACnC,kBAAM,EAAE,YAAY,UAAU,IAAI,KAAK;AACvC,kBAAM,EAAE,OAAO,IAAI;AACnB,kBAAM,cAA4B,CAAC;AAEnC,gBAAI,CAAC,cAAc,CAAC,WAAW;AAC7B,qBAAO,cAAc,OAAO,KAAK,CAAC,CAAC;AAAA,YACrC;AAGA,gBAAI,YAAY;AAEhB,gBAAI,KAAK,QAAQ,SAAS,WAAW;AACnC,kBAAI,YAAY,CAAC,MAAM,QAAQ;AAC7B,oBAAI,KAAK,QAAQ;AACf;AAAA,gBACF;AAEA,sBAAM,YAAY,UAAU,OAAO,UAAU,MAAM,KAAK,WAAW;AAEnE,oBAAI,CAAC,WAAW;AACd,yBAAO;AAAA,gBACT;AAEA,6BAAa;AAAA,cACf,CAAC;AAAA,YACH;AAGA,gBAAI,eAAe;AAEnB,gBAAI,YAAY,CAAC,MAAM,QAAQ;AAC7B,kBAAI,KAAK,QAAQ;AACf,uBAAO;AAAA,cACT;AAEA,oBAAM,YAAY,UAAU,OAAO,UAAU,MAAM,KAAK,WAAW;AAEnE,kBAAI,CAAC,WAAW;AACd,uBAAO;AAAA,cACT;AAEA,8BAAgB;AAEhB,oBAAM,aACH,KAAK,QAAQ,SAAS,aAAa,YAAY,eAAe,KAC9D,KAAK,QAAQ,SAAS,gBAAgB,eAAe;AAExD,kBAAI,YAAY;AACd,uBAAO,KAAK,QAAQ,SAAS;AAAA,cAC/B;AAEA,0BAAY;AAAA,gBACV,WAAW,KAAK,KAAK,MAAM,KAAK,UAAU;AAAA,kBACxC,OAAO,KAAK,QAAQ;AAAA,gBACtB,CAAC;AAAA,cACH;AAAA,YACF,CAAC;AAED,mBAAO,cAAc,OAAO,KAAK,WAAW;AAAA,UAC9C;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;", "names": []}