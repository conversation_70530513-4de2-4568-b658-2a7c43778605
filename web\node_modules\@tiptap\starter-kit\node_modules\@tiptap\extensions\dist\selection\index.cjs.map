{"version": 3, "sources": ["../../src/selection/index.ts", "../../src/selection/selection.ts"], "sourcesContent": ["export * from './selection.js'\n", "import { Extension, isNodeSelection } from '@tiptap/core'\nimport { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\nimport { Decoration, DecorationSet } from '@tiptap/pm/view'\n\nexport type SelectionOptions = {\n  /**\n   * The class name that should be added to the selected text.\n   * @default 'selection'\n   * @example 'is-selected'\n   */\n  className: string\n}\n\n/**\n * This extension allows you to add a class to the selected text.\n * @see https://www.tiptap.dev/api/extensions/selection\n */\nexport const Selection = Extension.create({\n  name: 'selection',\n\n  addOptions() {\n    return {\n      className: 'selection',\n    }\n  },\n\n  addProseMirrorPlugins() {\n    const { editor, options } = this\n\n    return [\n      new Plugin({\n        key: new PluginKey('selection'),\n        props: {\n          decorations(state) {\n            if (\n              state.selection.empty ||\n              editor.isFocused ||\n              !editor.isEditable ||\n              isNodeSelection(state.selection) ||\n              editor.view.dragging\n            ) {\n              return null\n            }\n\n            return DecorationSet.create(state.doc, [\n              Decoration.inline(state.selection.from, state.selection.to, {\n                class: options.className,\n              }),\n            ])\n          },\n        },\n      }),\n    ]\n  },\n})\n\nexport default Selection\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,kBAA2C;AAC3C,mBAAkC;AAClC,kBAA0C;AAenC,IAAM,YAAY,sBAAU,OAAO;AAAA,EACxC,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,WAAW;AAAA,IACb;AAAA,EACF;AAAA,EAEA,wBAAwB;AACtB,UAAM,EAAE,QAAQ,QAAQ,IAAI;AAE5B,WAAO;AAAA,MACL,IAAI,oBAAO;AAAA,QACT,KAAK,IAAI,uBAAU,WAAW;AAAA,QAC9B,OAAO;AAAA,UACL,YAAY,OAAO;AACjB,gBACE,MAAM,UAAU,SAChB,OAAO,aACP,CAAC,OAAO,kBACR,6BAAgB,MAAM,SAAS,KAC/B,OAAO,KAAK,UACZ;AACA,qBAAO;AAAA,YACT;AAEA,mBAAO,0BAAc,OAAO,MAAM,KAAK;AAAA,cACrC,uBAAW,OAAO,MAAM,UAAU,MAAM,MAAM,UAAU,IAAI;AAAA,gBAC1D,OAAO,QAAQ;AAAA,cACjB,CAAC;AAAA,YACH,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;", "names": []}