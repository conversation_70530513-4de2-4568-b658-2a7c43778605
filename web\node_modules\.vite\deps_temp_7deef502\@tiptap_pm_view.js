import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import "./chunk-YZBTLTZP.js";
import {
  Decoration,
  DecorationSet,
  EditorView,
  __endComposition,
  __parseFromClipboard
} from "./chunk-CE2CDXOX.js";
import "./chunk-QHVAV34Y.js";
import "./chunk-O7RF7KNN.js";
import "./chunk-ZMSOBIYE.js";
export {
  Decoration,
  DecorationSet,
  EditorView,
  __endComposition,
  __parseFromClipboard
};
//# sourceMappingURL=@tiptap_pm_view.js.map
