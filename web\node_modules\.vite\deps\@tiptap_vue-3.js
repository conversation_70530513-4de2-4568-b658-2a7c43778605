import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  customRef,
  defineComponent,
  getCurrentInstance,
  h,
  markRaw,
  nextTick,
  onBeforeUnmount,
  onMounted,
  provide,
  reactive,
  ref,
  render,
  shallowRef,
  unref,
  watchEffect
} from "./chunk-ZPQY4RR2.js";
import {
  FloatingMenuPlugin
} from "./chunk-KEOW3RE4.js";
import {
  BubbleMenuPlugin
} from "./chunk-NSCZZQB6.js";
import "./chunk-A266GPMG.js";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Editor,
  Extension,
  InputRule,
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>de<PERSON><PERSON>,
  <PERSON>de<PERSON><PERSON><PERSON>,
  <PERSON>e<PERSON><PERSON>,
  <PERSON><PERSON>,
  callOrReturn,
  combineTransactionSteps,
  createChainableState,
  createDocument,
  createNodeFromContent,
  createStyleTag,
  defaultBlockAt,
  deleteProps,
  elementFromString,
  escapeForRegEx,
  findChildren,
  findChildrenInRange,
  findDuplicates,
  findParentNode,
  findParentNodeClosestToPos,
  fromString,
  generateHTML,
  generateJSON,
  generateText,
  getAttributes,
  getAttributesFromExtensions,
  getChangedRanges,
  getDebugJSON,
  getExtensionField,
  getHTMLFromFragment,
  getMarkAttributes,
  getMarkRange,
  getMarkType,
  getMarksBetween,
  getNodeAtPosition,
  getNodeAttributes,
  getNodeType,
  getRenderedAttributes,
  getSchema,
  getSchemaByResolvedExtensions,
  getSchemaTypeByName,
  getSchemaTypeNameByName,
  getSplittedAttributes,
  getText,
  getTextBetween,
  getTextContentFromNodes,
  getTextSerializersFromSchema,
  index,
  injectExtensionAttributesToParseRule,
  inputRulesPlugin,
  isActive,
  isAtEndOfNode,
  isAtStartOfNode,
  isEmptyObject,
  isExtensionRulesEnabled,
  isFunction,
  isList,
  isMacOS,
  isMarkActive,
  isNodeActive,
  isNodeEmpty,
  isNodeSelection,
  isNumber,
  isPlainObject,
  isRegExp,
  isString,
  isTextSelection,
  isiOS,
  markInputRule,
  markPasteRule,
  mergeAttributes,
  mergeDeep,
  minMax,
  nodeInputRule,
  nodePasteRule,
  objectIncludes,
  pasteRulesPlugin,
  posToDOMRect,
  removeDuplicates,
  resolveFocusPosition,
  rewriteUnknownContent,
  selectionToInsertionEnd,
  splitExtensions,
  textInputRule,
  textPasteRule,
  textblockTypeInputRule,
  wrappingInputRule
} from "./chunk-ZDW4XOTA.js";
import "./chunk-AL46U7Q7.js";
import "./chunk-REYSTJ5T.js";
import "./chunk-YZBTLTZP.js";
import "./chunk-XLOADKRT.js";
import "./chunk-CE2CDXOX.js";
import "./chunk-QHVAV34Y.js";
import "./chunk-O7RF7KNN.js";
import {
  __toESM,
  require_dist,
  require_dist2,
  require_dist3
} from "./chunk-ZMSOBIYE.js";

// node_modules/@tiptap/vue-3/dist/index.js
var import_dist = __toESM(require_dist());
var import_dist2 = __toESM(require_dist2());
var import_dist3 = __toESM(require_dist3());
var BubbleMenu = defineComponent({
  name: "BubbleMenu",
  props: {
    pluginKey: {
      type: [String, Object],
      default: "bubbleMenu"
    },
    editor: {
      type: Object,
      required: true
    },
    updateDelay: {
      type: Number,
      default: void 0
    },
    tippyOptions: {
      type: Object,
      default: () => ({})
    },
    shouldShow: {
      type: Function,
      default: null
    }
  },
  setup(props, { slots }) {
    const root = ref(null);
    onMounted(() => {
      const { updateDelay, editor, pluginKey, shouldShow, tippyOptions } = props;
      editor.registerPlugin(BubbleMenuPlugin({
        updateDelay,
        editor,
        element: root.value,
        pluginKey,
        shouldShow,
        tippyOptions
      }));
    });
    onBeforeUnmount(() => {
      const { pluginKey, editor } = props;
      editor.unregisterPlugin(pluginKey);
    });
    return () => {
      var _a;
      return h("div", { ref: root }, (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots));
    };
  }
});
function useDebouncedRef(value) {
  return customRef((track, trigger) => {
    return {
      get() {
        track();
        return value;
      },
      set(newValue) {
        value = newValue;
        requestAnimationFrame(() => {
          requestAnimationFrame(() => {
            trigger();
          });
        });
      }
    };
  });
}
var Editor2 = class extends Editor {
  constructor(options = {}) {
    super(options);
    this.contentComponent = null;
    this.appContext = null;
    this.reactiveState = useDebouncedRef(this.view.state);
    this.reactiveExtensionStorage = useDebouncedRef(this.extensionStorage);
    this.on("beforeTransaction", ({ nextState }) => {
      this.reactiveState.value = nextState;
      this.reactiveExtensionStorage.value = this.extensionStorage;
    });
    return markRaw(this);
  }
  get state() {
    return this.reactiveState ? this.reactiveState.value : this.view.state;
  }
  get storage() {
    return this.reactiveExtensionStorage ? this.reactiveExtensionStorage.value : super.storage;
  }
  /**
   * Register a ProseMirror plugin.
   */
  registerPlugin(plugin, handlePlugins) {
    const nextState = super.registerPlugin(plugin, handlePlugins);
    if (this.reactiveState) {
      this.reactiveState.value = nextState;
    }
    return nextState;
  }
  /**
   * Unregister a ProseMirror plugin.
   */
  unregisterPlugin(nameOrPluginKey) {
    const nextState = super.unregisterPlugin(nameOrPluginKey);
    if (this.reactiveState && nextState) {
      this.reactiveState.value = nextState;
    }
    return nextState;
  }
};
var EditorContent = defineComponent({
  name: "EditorContent",
  props: {
    editor: {
      default: null,
      type: Object
    }
  },
  setup(props) {
    const rootEl = ref();
    const instance = getCurrentInstance();
    watchEffect(() => {
      const editor = props.editor;
      if (editor && editor.options.element && rootEl.value) {
        nextTick(() => {
          if (!rootEl.value || !editor.options.element.firstChild) {
            return;
          }
          const element = unref(rootEl.value);
          rootEl.value.append(...editor.options.element.childNodes);
          editor.contentComponent = instance.ctx._;
          if (instance) {
            editor.appContext = {
              ...instance.appContext,
              // Vue internally uses prototype chain to forward/shadow injects across the entire component chain
              // so don't use object spread operator or 'Object.assign' and just set `provides` as is on editor's appContext
              // @ts-expect-error forward instance's 'provides' into appContext
              provides: instance.provides
            };
          }
          editor.setOptions({
            element
          });
          editor.createNodeViews();
        });
      }
    });
    onBeforeUnmount(() => {
      const editor = props.editor;
      if (!editor) {
        return;
      }
      editor.contentComponent = null;
      editor.appContext = null;
    });
    return { rootEl };
  },
  render() {
    return h("div", {
      ref: (el) => {
        this.rootEl = el;
      }
    });
  }
});
var FloatingMenu = defineComponent({
  name: "FloatingMenu",
  props: {
    pluginKey: {
      // TODO: TypeScript breaks :(
      // type: [String, Object as PropType<Exclude<FloatingMenuPluginProps['pluginKey'], string>>],
      type: null,
      default: "floatingMenu"
    },
    editor: {
      type: Object,
      required: true
    },
    tippyOptions: {
      type: Object,
      default: () => ({})
    },
    shouldShow: {
      type: Function,
      default: null
    }
  },
  setup(props, { slots }) {
    const root = ref(null);
    onMounted(() => {
      const { pluginKey, editor, tippyOptions, shouldShow } = props;
      editor.registerPlugin(FloatingMenuPlugin({
        pluginKey,
        editor,
        element: root.value,
        tippyOptions,
        shouldShow
      }));
    });
    onBeforeUnmount(() => {
      const { pluginKey, editor } = props;
      editor.unregisterPlugin(pluginKey);
    });
    return () => {
      var _a;
      return h("div", { ref: root }, (_a = slots.default) === null || _a === void 0 ? void 0 : _a.call(slots));
    };
  }
});
var NodeViewContent = defineComponent({
  name: "NodeViewContent",
  props: {
    as: {
      type: String,
      default: "div"
    }
  },
  render() {
    return h(this.as, {
      style: {
        whiteSpace: "pre-wrap"
      },
      "data-node-view-content": ""
    });
  }
});
var NodeViewWrapper = defineComponent({
  name: "NodeViewWrapper",
  props: {
    as: {
      type: String,
      default: "div"
    }
  },
  inject: ["onDragStart", "decorationClasses"],
  render() {
    var _a, _b;
    return h(this.as, {
      // @ts-ignore
      class: this.decorationClasses,
      style: {
        whiteSpace: "normal"
      },
      "data-node-view-wrapper": "",
      // @ts-ignore (https://github.com/vuejs/vue-next/issues/3031)
      onDragstart: this.onDragStart
    }, (_b = (_a = this.$slots).default) === null || _b === void 0 ? void 0 : _b.call(_a));
  }
});
var useEditor = (options = {}) => {
  const editor = shallowRef();
  onMounted(() => {
    editor.value = new Editor2(options);
  });
  onBeforeUnmount(() => {
    var _a, _b, _c;
    const nodes = (_a = editor.value) === null || _a === void 0 ? void 0 : _a.options.element;
    const newEl = nodes === null || nodes === void 0 ? void 0 : nodes.cloneNode(true);
    (_b = nodes === null || nodes === void 0 ? void 0 : nodes.parentNode) === null || _b === void 0 ? void 0 : _b.replaceChild(newEl, nodes);
    (_c = editor.value) === null || _c === void 0 ? void 0 : _c.destroy();
  });
  return editor;
};
var VueRenderer = class {
  constructor(component, { props = {}, editor }) {
    this.editor = editor;
    this.component = markRaw(component);
    this.el = document.createElement("div");
    this.props = reactive(props);
    this.renderedComponent = this.renderComponent();
  }
  get element() {
    return this.renderedComponent.el;
  }
  get ref() {
    var _a, _b, _c, _d;
    if ((_b = (_a = this.renderedComponent.vNode) === null || _a === void 0 ? void 0 : _a.component) === null || _b === void 0 ? void 0 : _b.exposed) {
      return this.renderedComponent.vNode.component.exposed;
    }
    return (_d = (_c = this.renderedComponent.vNode) === null || _c === void 0 ? void 0 : _c.component) === null || _d === void 0 ? void 0 : _d.proxy;
  }
  renderComponent() {
    let vNode = h(this.component, this.props);
    if (this.editor.appContext) {
      vNode.appContext = this.editor.appContext;
    }
    if (typeof document !== "undefined" && this.el) {
      render(vNode, this.el);
    }
    const destroy = () => {
      if (this.el) {
        render(null, this.el);
      }
      this.el = null;
      vNode = null;
    };
    return { vNode, destroy, el: this.el ? this.el.firstElementChild : null };
  }
  updateProps(props = {}) {
    Object.entries(props).forEach(([key, value]) => {
      this.props[key] = value;
    });
    this.renderComponent();
  }
  destroy() {
    this.renderedComponent.destroy();
  }
};
var nodeViewProps = {
  editor: {
    type: Object,
    required: true
  },
  node: {
    type: Object,
    required: true
  },
  decorations: {
    type: Object,
    required: true
  },
  selected: {
    type: Boolean,
    required: true
  },
  extension: {
    type: Object,
    required: true
  },
  getPos: {
    type: Function,
    required: true
  },
  updateAttributes: {
    type: Function,
    required: true
  },
  deleteNode: {
    type: Function,
    required: true
  },
  view: {
    type: Object,
    required: true
  },
  innerDecorations: {
    type: Object,
    required: true
  },
  HTMLAttributes: {
    type: Object,
    required: true
  }
};
var VueNodeView = class extends NodeView {
  mount() {
    const props = {
      editor: this.editor,
      node: this.node,
      decorations: this.decorations,
      innerDecorations: this.innerDecorations,
      view: this.view,
      selected: false,
      extension: this.extension,
      HTMLAttributes: this.HTMLAttributes,
      getPos: () => this.getPos(),
      updateAttributes: (attributes = {}) => this.updateAttributes(attributes),
      deleteNode: () => this.deleteNode()
    };
    const onDragStart = this.onDragStart.bind(this);
    this.decorationClasses = ref(this.getDecorationClasses());
    const extendedComponent = defineComponent({
      extends: { ...this.component },
      props: Object.keys(props),
      template: this.component.template,
      setup: (reactiveProps) => {
        var _a, _b;
        provide("onDragStart", onDragStart);
        provide("decorationClasses", this.decorationClasses);
        return (_b = (_a = this.component).setup) === null || _b === void 0 ? void 0 : _b.call(_a, reactiveProps, {
          expose: () => void 0
        });
      },
      // add support for scoped styles
      // @ts-ignore
      // eslint-disable-next-line
      __scopeId: this.component.__scopeId,
      // add support for CSS Modules
      // @ts-ignore
      // eslint-disable-next-line
      __cssModules: this.component.__cssModules,
      // add support for vue devtools
      // @ts-ignore
      // eslint-disable-next-line
      __name: this.component.__name,
      // @ts-ignore
      // eslint-disable-next-line
      __file: this.component.__file
    });
    this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this);
    this.editor.on("selectionUpdate", this.handleSelectionUpdate);
    this.renderer = new VueRenderer(extendedComponent, {
      editor: this.editor,
      props
    });
  }
  /**
   * Return the DOM element.
   * This is the element that will be used to display the node view.
   */
  get dom() {
    if (!this.renderer.element || !this.renderer.element.hasAttribute("data-node-view-wrapper")) {
      throw Error("Please use the NodeViewWrapper component for your node view.");
    }
    return this.renderer.element;
  }
  /**
   * Return the content DOM element.
   * This is the element that will be used to display the rich-text content of the node.
   */
  get contentDOM() {
    if (this.node.isLeaf) {
      return null;
    }
    return this.dom.querySelector("[data-node-view-content]");
  }
  /**
   * On editor selection update, check if the node is selected.
   * If it is, call `selectNode`, otherwise call `deselectNode`.
   */
  handleSelectionUpdate() {
    const { from, to } = this.editor.state.selection;
    const pos = this.getPos();
    if (typeof pos !== "number") {
      return;
    }
    if (from <= pos && to >= pos + this.node.nodeSize) {
      if (this.renderer.props.selected) {
        return;
      }
      this.selectNode();
    } else {
      if (!this.renderer.props.selected) {
        return;
      }
      this.deselectNode();
    }
  }
  /**
   * On update, update the React component.
   * To prevent unnecessary updates, the `update` option can be used.
   */
  update(node, decorations, innerDecorations) {
    const rerenderComponent = (props) => {
      this.decorationClasses.value = this.getDecorationClasses();
      this.renderer.updateProps(props);
    };
    if (typeof this.options.update === "function") {
      const oldNode = this.node;
      const oldDecorations = this.decorations;
      const oldInnerDecorations = this.innerDecorations;
      this.node = node;
      this.decorations = decorations;
      this.innerDecorations = innerDecorations;
      return this.options.update({
        oldNode,
        oldDecorations,
        newNode: node,
        newDecorations: decorations,
        oldInnerDecorations,
        innerDecorations,
        updateProps: () => rerenderComponent({ node, decorations, innerDecorations })
      });
    }
    if (node.type !== this.node.type) {
      return false;
    }
    if (node === this.node && this.decorations === decorations && this.innerDecorations === innerDecorations) {
      return true;
    }
    this.node = node;
    this.decorations = decorations;
    this.innerDecorations = innerDecorations;
    rerenderComponent({ node, decorations, innerDecorations });
    return true;
  }
  /**
   * Select the node.
   * Add the `selected` prop and the `ProseMirror-selectednode` class.
   */
  selectNode() {
    this.renderer.updateProps({
      selected: true
    });
    if (this.renderer.element) {
      this.renderer.element.classList.add("ProseMirror-selectednode");
    }
  }
  /**
   * Deselect the node.
   * Remove the `selected` prop and the `ProseMirror-selectednode` class.
   */
  deselectNode() {
    this.renderer.updateProps({
      selected: false
    });
    if (this.renderer.element) {
      this.renderer.element.classList.remove("ProseMirror-selectednode");
    }
  }
  getDecorationClasses() {
    return this.decorations.map((item) => item.type.attrs.class).flat().join(" ");
  }
  destroy() {
    this.renderer.destroy();
    this.editor.off("selectionUpdate", this.handleSelectionUpdate);
  }
};
function VueNodeViewRenderer(component, options) {
  return (props) => {
    if (!props.editor.contentComponent) {
      return {};
    }
    const normalizedComponent = typeof component === "function" && "__vccOpts" in component ? component.__vccOpts : component;
    return new VueNodeView(normalizedComponent, props, options);
  };
}
export {
  BubbleMenu,
  CommandManager,
  Editor2 as Editor,
  EditorContent,
  Extension,
  FloatingMenu,
  InputRule,
  Mark,
  Node,
  NodePos,
  NodeView,
  NodeViewContent,
  NodeViewWrapper,
  PasteRule,
  Tracker,
  VueNodeViewRenderer,
  VueRenderer,
  callOrReturn,
  combineTransactionSteps,
  createChainableState,
  createDocument,
  createNodeFromContent,
  createStyleTag,
  defaultBlockAt,
  deleteProps,
  elementFromString,
  escapeForRegEx,
  index as extensions,
  findChildren,
  findChildrenInRange,
  findDuplicates,
  findParentNode,
  findParentNodeClosestToPos,
  fromString,
  generateHTML,
  generateJSON,
  generateText,
  getAttributes,
  getAttributesFromExtensions,
  getChangedRanges,
  getDebugJSON,
  getExtensionField,
  getHTMLFromFragment,
  getMarkAttributes,
  getMarkRange,
  getMarkType,
  getMarksBetween,
  getNodeAtPosition,
  getNodeAttributes,
  getNodeType,
  getRenderedAttributes,
  getSchema,
  getSchemaByResolvedExtensions,
  getSchemaTypeByName,
  getSchemaTypeNameByName,
  getSplittedAttributes,
  getText,
  getTextBetween,
  getTextContentFromNodes,
  getTextSerializersFromSchema,
  injectExtensionAttributesToParseRule,
  inputRulesPlugin,
  isActive,
  isAtEndOfNode,
  isAtStartOfNode,
  isEmptyObject,
  isExtensionRulesEnabled,
  isFunction,
  isList,
  isMacOS,
  isMarkActive,
  isNodeActive,
  isNodeEmpty,
  isNodeSelection,
  isNumber,
  isPlainObject,
  isRegExp,
  isString,
  isTextSelection,
  isiOS,
  markInputRule,
  markPasteRule,
  mergeAttributes,
  mergeDeep,
  minMax,
  nodeInputRule,
  nodePasteRule,
  nodeViewProps,
  objectIncludes,
  pasteRulesPlugin,
  posToDOMRect,
  removeDuplicates,
  resolveFocusPosition,
  rewriteUnknownContent,
  selectionToInsertionEnd,
  splitExtensions,
  textInputRule,
  textPasteRule,
  textblockTypeInputRule,
  useEditor,
  wrappingInputRule
};
//# sourceMappingURL=@tiptap_vue-3.js.map
