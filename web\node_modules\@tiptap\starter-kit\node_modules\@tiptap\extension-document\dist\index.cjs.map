{"version": 3, "sources": ["../src/index.ts", "../src/document.ts"], "sourcesContent": ["import { Document } from './document.js'\n\nexport * from './document.js'\n\nexport default Document\n", "import { Node } from '@tiptap/core'\n\n/**\n * The default document node which represents the top level node of the editor.\n * @see https://tiptap.dev/api/nodes/document\n */\nexport const Document = Node.create({\n  name: 'doc',\n  topNode: true,\n  content: 'block+',\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,kBAAqB;AAMd,IAAM,WAAW,iBAAK,OAAO;AAAA,EAClC,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AACX,CAAC;;;ADND,IAAO,gBAAQ;", "names": []}