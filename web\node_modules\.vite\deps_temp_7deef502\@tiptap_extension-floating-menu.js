import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  FloatingMenu,
  FloatingMenuPlugin,
  FloatingMenuView
} from "./chunk-KEOW3RE4.js";
import "./chunk-A266GPMG.js";
import "./chunk-ZDW4XOTA.js";
import "./chunk-AL46U7Q7.js";
import "./chunk-YZBTLTZP.js";
import "./chunk-REYSTJ5T.js";
import "./chunk-XLOADKRT.js";
import "./chunk-CE2CDXOX.js";
import "./chunk-QHVAV34Y.js";
import "./chunk-O7RF7KNN.js";
import "./chunk-ZMSOBIYE.js";
export {
  FloatingMenu,
  FloatingMenuPlugin,
  FloatingMenuView,
  FloatingMenu as default
};
//# sourceMappingURL=@tiptap_extension-floating-menu.js.map
