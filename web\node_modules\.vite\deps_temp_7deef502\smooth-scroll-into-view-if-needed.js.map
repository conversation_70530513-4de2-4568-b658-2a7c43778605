{"version": 3, "sources": ["../../compute-scroll-into-view/src/index.ts", "../../scroll-into-view-if-needed/src/index.ts", "../../smooth-scroll-into-view-if-needed/src/index.ts"], "sourcesContent": ["// Compute what scrolling needs to be done on required scrolling boxes for target to be in view\n\n// The type names here are named after the spec to make it easier to find more information around what they mean:\n// To reduce churn and reduce things that need be maintained things from the official TS DOM library is used here\n// https://drafts.csswg.org/cssom-view/\n\n// For a definition on what is \"block flow direction\" exactly, check this: https://drafts.csswg.org/css-writing-modes-4/#block-flow-direction\n\n/**\n * This new option is tracked in this PR, which is the most likely candidate at the time: https://github.com/w3c/csswg-drafts/pull/1805\n * @public\n */\nexport type ScrollMode = 'always' | 'if-needed'\n\n/** @public */\nexport interface Options {\n  /**\n   * Control the logical scroll position on the y-axis. The spec states that the `block` direction is related to the [writing-mode](https://developer.mozilla.org/en-US/docs/Web/CSS/writing-mode), but this is not implemented yet in this library.\n   * This means that `block: 'start'` aligns to the top edge and `block: 'end'` to the bottom.\n   * @defaultValue 'center'\n   */\n  block?: ScrollLogicalPosition\n  /**\n   * Like `block` this is affected by the [writing-mode](https://developer.mozilla.org/en-US/docs/Web/CSS/writing-mode). In left-to-right pages `inline: 'start'` will align to the left edge. In right-to-left it should be flipped. This will be supported in a future release.\n   * @defaultValue 'nearest'\n   */\n  inline?: ScrollLogicalPosition\n  /**\n   * This is a proposed addition to the spec that you can track here: https://github.com/w3c/csswg-drafts/pull/5677\n   *\n   * This library will be updated to reflect any changes to the spec and will provide a migration path.\n   * To be backwards compatible with `Element.scrollIntoViewIfNeeded` if something is not 100% visible it will count as \"needs scrolling\". If you need a different visibility ratio your best option would be to implement an [Intersection Observer](https://developer.mozilla.org/en-US/docs/Web/API/Intersection_Observer_API).\n   * @defaultValue 'always'\n   */\n  scrollMode?: ScrollMode\n  /**\n   * By default there is no boundary. All the parent elements of your target is checked until it reaches the viewport ([`document.scrollingElement`](https://developer.mozilla.org/en-US/docs/Web/API/document/scrollingElement)) when calculating layout and what to scroll.\n   * By passing a boundary you can short-circuit this loop depending on your needs:\n   * \n   * - Prevent the browser window from scrolling.\n   * - Scroll elements into view in a list, without scrolling container elements.\n   * \n   * You can also pass a function to do more dynamic checks to override the scroll scoping:\n   * \n   * ```js\n   * let actions = compute(target, {\n   *   boundary: (parent) => {\n   *     // By default `overflow: hidden` elements are allowed, only `overflow: visible | clip` is skipped as\n   *     // this is required by the CSSOM spec\n   *     if (getComputedStyle(parent)['overflow'] === 'hidden') {\n   *       return false\n   *     }\n\n   *     return true\n   *   },\n   * })\n   * ```\n   * @defaultValue null\n   */\n  boundary?: Element | ((parent: Element) => boolean) | null\n  /**\n   * New option that skips auto-scrolling all nodes with overflow: hidden set\n   * See FF implementation: https://hg.mozilla.org/integration/fx-team/rev/c48c3ec05012#l7.18\n   * @defaultValue false\n   * @public\n   */\n  skipOverflowHiddenElements?: boolean\n}\n\n/** @public */\nexport interface ScrollAction {\n  el: Element\n  top: number\n  left: number\n}\n\n// @TODO better shadowdom test, 11 = document fragment\nconst isElement = (el: any): el is Element =>\n  typeof el === 'object' && el != null && el.nodeType === 1\n\nconst canOverflow = (\n  overflow: string | null,\n  skipOverflowHiddenElements?: boolean\n) => {\n  if (skipOverflowHiddenElements && overflow === 'hidden') {\n    return false\n  }\n\n  return overflow !== 'visible' && overflow !== 'clip'\n}\n\nconst getFrameElement = (el: Element) => {\n  if (!el.ownerDocument || !el.ownerDocument.defaultView) {\n    return null\n  }\n\n  try {\n    return el.ownerDocument.defaultView.frameElement\n  } catch (e) {\n    return null\n  }\n}\n\nconst isHiddenByFrame = (el: Element): boolean => {\n  const frame = getFrameElement(el)\n  if (!frame) {\n    return false\n  }\n\n  return (\n    frame.clientHeight < el.scrollHeight || frame.clientWidth < el.scrollWidth\n  )\n}\n\nconst isScrollable = (el: Element, skipOverflowHiddenElements?: boolean) => {\n  if (el.clientHeight < el.scrollHeight || el.clientWidth < el.scrollWidth) {\n    const style = getComputedStyle(el, null)\n    return (\n      canOverflow(style.overflowY, skipOverflowHiddenElements) ||\n      canOverflow(style.overflowX, skipOverflowHiddenElements) ||\n      isHiddenByFrame(el)\n    )\n  }\n\n  return false\n}\n/**\n * Find out which edge to align against when logical scroll position is \"nearest\"\n * Interesting fact: \"nearest\" works similarily to \"if-needed\", if the element is fully visible it will not scroll it\n *\n * Legends:\n * ┌────────┐ ┏ ━ ━ ━ ┓\n * │ target │   frame\n * └────────┘ ┗ ━ ━ ━ ┛\n */\nconst alignNearest = (\n  scrollingEdgeStart: number,\n  scrollingEdgeEnd: number,\n  scrollingSize: number,\n  scrollingBorderStart: number,\n  scrollingBorderEnd: number,\n  elementEdgeStart: number,\n  elementEdgeEnd: number,\n  elementSize: number\n) => {\n  /**\n   * If element edge A and element edge B are both outside scrolling box edge A and scrolling box edge B\n   *\n   *          ┌──┐\n   *        ┏━│━━│━┓\n   *          │  │\n   *        ┃ │  │ ┃        do nothing\n   *          │  │\n   *        ┗━│━━│━┛\n   *          └──┘\n   *\n   *  If element edge C and element edge D are both outside scrolling box edge C and scrolling box edge D\n   *\n   *    ┏ ━ ━ ━ ━ ┓\n   *   ┌───────────┐\n   *   │┃         ┃│        do nothing\n   *   └───────────┘\n   *    ┗ ━ ━ ━ ━ ┛\n   */\n  if (\n    (elementEdgeStart < scrollingEdgeStart &&\n      elementEdgeEnd > scrollingEdgeEnd) ||\n    (elementEdgeStart > scrollingEdgeStart && elementEdgeEnd < scrollingEdgeEnd)\n  ) {\n    return 0\n  }\n\n  /**\n   * If element edge A is outside scrolling box edge A and element height is less than scrolling box height\n   *\n   *          ┌──┐\n   *        ┏━│━━│━┓         ┏━┌━━┐━┓\n   *          └──┘             │  │\n   *  from  ┃      ┃     to  ┃ └──┘ ┃\n   *\n   *        ┗━ ━━ ━┛         ┗━ ━━ ━┛\n   *\n   * If element edge B is outside scrolling box edge B and element height is greater than scrolling box height\n   *\n   *        ┏━ ━━ ━┓         ┏━┌━━┐━┓\n   *                           │  │\n   *  from  ┃ ┌──┐ ┃     to  ┃ │  │ ┃\n   *          │  │             │  │\n   *        ┗━│━━│━┛         ┗━│━━│━┛\n   *          │  │             └──┘\n   *          │  │\n   *          └──┘\n   *\n   * If element edge C is outside scrolling box edge C and element width is less than scrolling box width\n   *\n   *       from                 to\n   *    ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *  ┌───┐                 ┌───┐\n   *  │ ┃ │       ┃         ┃   │     ┃\n   *  └───┘                 └───┘\n   *    ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   * If element edge D is outside scrolling box edge D and element width is greater than scrolling box width\n   *\n   *       from                 to\n   *    ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *        ┌───────────┐   ┌───────────┐\n   *    ┃   │     ┃     │   ┃         ┃ │\n   *        └───────────┘   └───────────┘\n   *    ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   */\n  if (\n    (elementEdgeStart <= scrollingEdgeStart && elementSize <= scrollingSize) ||\n    (elementEdgeEnd >= scrollingEdgeEnd && elementSize >= scrollingSize)\n  ) {\n    return elementEdgeStart - scrollingEdgeStart - scrollingBorderStart\n  }\n\n  /**\n   * If element edge B is outside scrolling box edge B and element height is less than scrolling box height\n   *\n   *        ┏━ ━━ ━┓         ┏━ ━━ ━┓\n   *\n   *  from  ┃      ┃     to  ┃ ┌──┐ ┃\n   *          ┌──┐             │  │\n   *        ┗━│━━│━┛         ┗━└━━┘━┛\n   *          └──┘\n   *\n   * If element edge A is outside scrolling box edge A and element height is greater than scrolling box height\n   *\n   *          ┌──┐\n   *          │  │\n   *          │  │             ┌──┐\n   *        ┏━│━━│━┓         ┏━│━━│━┓\n   *          │  │             │  │\n   *  from  ┃ └──┘ ┃     to  ┃ │  │ ┃\n   *                           │  │\n   *        ┗━ ━━ ━┛         ┗━└━━┘━┛\n   *\n   * If element edge C is outside scrolling box edge C and element width is greater than scrolling box width\n   *\n   *           from                 to\n   *        ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *  ┌───────────┐           ┌───────────┐\n   *  │     ┃     │   ┃       │ ┃         ┃\n   *  └───────────┘           └───────────┘\n   *        ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   * If element edge D is outside scrolling box edge D and element width is less than scrolling box width\n   *\n   *           from                 to\n   *        ┏ ━ ━ ━ ━ ┓         ┏ ━ ━ ━ ━ ┓\n   *                ┌───┐             ┌───┐\n   *        ┃       │ ┃ │       ┃     │   ┃\n   *                └───┘             └───┘\n   *        ┗ ━ ━ ━ ━ ┛         ┗ ━ ━ ━ ━ ┛\n   *\n   */\n  if (\n    (elementEdgeEnd > scrollingEdgeEnd && elementSize < scrollingSize) ||\n    (elementEdgeStart < scrollingEdgeStart && elementSize > scrollingSize)\n  ) {\n    return elementEdgeEnd - scrollingEdgeEnd + scrollingBorderEnd\n  }\n\n  return 0\n}\n\nconst getParentElement = (element: Node): Element | null => {\n  const parent = element.parentElement\n  if (parent == null) {\n    return (element.getRootNode() as ShadowRoot).host || null\n  }\n  return parent\n}\n\nconst getScrollMargins = (target: Element) => {\n  const computedStyle = window.getComputedStyle(target)\n  return {\n    top: parseFloat(computedStyle.scrollMarginTop) || 0,\n    right: parseFloat(computedStyle.scrollMarginRight) || 0,\n    bottom: parseFloat(computedStyle.scrollMarginBottom) || 0,\n    left: parseFloat(computedStyle.scrollMarginLeft) || 0,\n  }\n}\n\n/** @public */\nexport const compute = (target: Element, options: Options): ScrollAction[] => {\n  if (typeof document === 'undefined') {\n    // If there's no DOM we assume it's not in a browser environment\n    return []\n  }\n\n  const { scrollMode, block, inline, boundary, skipOverflowHiddenElements } =\n    options\n  // Allow using a callback to check the boundary\n  // The default behavior is to check if the current target matches the boundary element or not\n  // If undefined it'll check that target is never undefined (can happen as we recurse up the tree)\n  const checkBoundary =\n    typeof boundary === 'function' ? boundary : (node: any) => node !== boundary\n\n  if (!isElement(target)) {\n    throw new TypeError('Invalid target')\n  }\n\n  // Used to handle the top most element that can be scrolled\n  const scrollingElement = document.scrollingElement || document.documentElement\n\n  // Collect all the scrolling boxes, as defined in the spec: https://drafts.csswg.org/cssom-view/#scrolling-box\n  const frames: Element[] = []\n  let cursor: Element | null = target\n  while (isElement(cursor) && checkBoundary(cursor)) {\n    // Move cursor to parent\n    cursor = getParentElement(cursor)\n\n    // Stop when we reach the viewport\n    if (cursor === scrollingElement) {\n      frames.push(cursor)\n      break\n    }\n\n    // Skip document.body if it's not the scrollingElement and documentElement isn't independently scrollable\n    if (\n      cursor != null &&\n      cursor === document.body &&\n      isScrollable(cursor) &&\n      !isScrollable(document.documentElement)\n    ) {\n      continue\n    }\n\n    // Now we check if the element is scrollable, this code only runs if the loop haven't already hit the viewport or a custom boundary\n    if (cursor != null && isScrollable(cursor, skipOverflowHiddenElements)) {\n      frames.push(cursor)\n    }\n  }\n\n  // Support pinch-zooming properly, making sure elements scroll into the visual viewport\n  // Browsers that don't support visualViewport will report the layout viewport dimensions on document.documentElement.clientWidth/Height\n  // and viewport dimensions on window.innerWidth/Height\n  // https://www.quirksmode.org/mobile/viewports2.html\n  // https://bokand.github.io/viewport/index.html\n  const viewportWidth = window.visualViewport?.width ?? innerWidth\n  const viewportHeight = window.visualViewport?.height ?? innerHeight\n  const { scrollX, scrollY } = window\n\n  const {\n    height: targetHeight,\n    width: targetWidth,\n    top: targetTop,\n    right: targetRight,\n    bottom: targetBottom,\n    left: targetLeft,\n  } = target.getBoundingClientRect()\n  const {\n    top: marginTop,\n    right: marginRight,\n    bottom: marginBottom,\n    left: marginLeft,\n  } = getScrollMargins(target)\n\n  // These values mutate as we loop through and generate scroll coordinates\n  let targetBlock: number =\n    block === 'start' || block === 'nearest'\n      ? targetTop - marginTop\n      : block === 'end'\n      ? targetBottom + marginBottom\n      : targetTop + targetHeight / 2 - marginTop + marginBottom // block === 'center\n  let targetInline: number =\n    inline === 'center'\n      ? targetLeft + targetWidth / 2 - marginLeft + marginRight\n      : inline === 'end'\n      ? targetRight + marginRight\n      : targetLeft - marginLeft // inline === 'start || inline === 'nearest\n\n  // Collect new scroll positions\n  const computations: ScrollAction[] = []\n  // In chrome there's no longer a difference between caching the `frames.length` to a var or not, so we don't in this case (size > speed anyways)\n  for (let index = 0; index < frames.length; index++) {\n    const frame = frames[index]\n\n    // @TODO add a shouldScroll hook here that allows userland code to take control\n\n    const { height, width, top, right, bottom, left } =\n      frame.getBoundingClientRect()\n\n    // If the element is already visible we can end it here\n    // @TODO targetBlock and targetInline should be taken into account to be compliant with https://github.com/w3c/csswg-drafts/pull/1805/files#diff-3c17f0e43c20f8ecf89419d49e7ef5e0R1333\n    if (\n      scrollMode === 'if-needed' &&\n      targetTop >= 0 &&\n      targetLeft >= 0 &&\n      targetBottom <= viewportHeight &&\n      targetRight <= viewportWidth &&\n      // scrollingElement is added to the frames array even if it's not scrollable, in which case checking its bounds is not required\n      ((frame === scrollingElement && !isScrollable(frame)) ||\n        (targetTop >= top &&\n          targetBottom <= bottom &&\n          targetLeft >= left &&\n          targetRight <= right))\n    ) {\n      // Break the loop and return the computations for things that are not fully visible\n      return computations\n    }\n\n    const frameStyle = getComputedStyle(frame)\n    const borderLeft = parseInt(frameStyle.borderLeftWidth as string, 10)\n    const borderTop = parseInt(frameStyle.borderTopWidth as string, 10)\n    const borderRight = parseInt(frameStyle.borderRightWidth as string, 10)\n    const borderBottom = parseInt(frameStyle.borderBottomWidth as string, 10)\n\n    let blockScroll: number = 0\n    let inlineScroll: number = 0\n\n    // The property existance checks for offfset[Width|Height] is because only HTMLElement objects have them, but any Element might pass by here\n    // @TODO find out if the \"as HTMLElement\" overrides can be dropped\n    const scrollbarWidth =\n      'offsetWidth' in frame\n        ? (frame as HTMLElement).offsetWidth -\n          (frame as HTMLElement).clientWidth -\n          borderLeft -\n          borderRight\n        : 0\n    const scrollbarHeight =\n      'offsetHeight' in frame\n        ? (frame as HTMLElement).offsetHeight -\n          (frame as HTMLElement).clientHeight -\n          borderTop -\n          borderBottom\n        : 0\n\n    const scaleX =\n      'offsetWidth' in frame\n        ? (frame as HTMLElement).offsetWidth === 0\n          ? 0\n          : width / (frame as HTMLElement).offsetWidth\n        : 0\n    const scaleY =\n      'offsetHeight' in frame\n        ? (frame as HTMLElement).offsetHeight === 0\n          ? 0\n          : height / (frame as HTMLElement).offsetHeight\n        : 0\n\n    if (scrollingElement === frame) {\n      // Handle viewport logic (document.documentElement or document.body)\n\n      if (block === 'start') {\n        blockScroll = targetBlock\n      } else if (block === 'end') {\n        blockScroll = targetBlock - viewportHeight\n      } else if (block === 'nearest') {\n        blockScroll = alignNearest(\n          scrollY,\n          scrollY + viewportHeight,\n          viewportHeight,\n          borderTop,\n          borderBottom,\n          scrollY + targetBlock,\n          scrollY + targetBlock + targetHeight,\n          targetHeight\n        )\n      } else {\n        // block === 'center' is the default\n        blockScroll = targetBlock - viewportHeight / 2\n      }\n\n      if (inline === 'start') {\n        inlineScroll = targetInline\n      } else if (inline === 'center') {\n        inlineScroll = targetInline - viewportWidth / 2\n      } else if (inline === 'end') {\n        inlineScroll = targetInline - viewportWidth\n      } else {\n        // inline === 'nearest' is the default\n        inlineScroll = alignNearest(\n          scrollX,\n          scrollX + viewportWidth,\n          viewportWidth,\n          borderLeft,\n          borderRight,\n          scrollX + targetInline,\n          scrollX + targetInline + targetWidth,\n          targetWidth\n        )\n      }\n\n      // Apply scroll position offsets and ensure they are within bounds\n      // @TODO add more test cases to cover this 100%\n      blockScroll = Math.max(0, blockScroll + scrollY)\n      inlineScroll = Math.max(0, inlineScroll + scrollX)\n    } else {\n      // Handle each scrolling frame that might exist between the target and the viewport\n      if (block === 'start') {\n        blockScroll = targetBlock - top - borderTop\n      } else if (block === 'end') {\n        blockScroll = targetBlock - bottom + borderBottom + scrollbarHeight\n      } else if (block === 'nearest') {\n        blockScroll = alignNearest(\n          top,\n          bottom,\n          height,\n          borderTop,\n          borderBottom + scrollbarHeight,\n          targetBlock,\n          targetBlock + targetHeight,\n          targetHeight\n        )\n      } else {\n        // block === 'center' is the default\n        blockScroll = targetBlock - (top + height / 2) + scrollbarHeight / 2\n      }\n\n      if (inline === 'start') {\n        inlineScroll = targetInline - left - borderLeft\n      } else if (inline === 'center') {\n        inlineScroll = targetInline - (left + width / 2) + scrollbarWidth / 2\n      } else if (inline === 'end') {\n        inlineScroll = targetInline - right + borderRight + scrollbarWidth\n      } else {\n        // inline === 'nearest' is the default\n        inlineScroll = alignNearest(\n          left,\n          right,\n          width,\n          borderLeft,\n          borderRight + scrollbarWidth,\n          targetInline,\n          targetInline + targetWidth,\n          targetWidth\n        )\n      }\n\n      const { scrollLeft, scrollTop } = frame\n      // Ensure scroll coordinates are not out of bounds while applying scroll offsets\n      blockScroll =\n        scaleY === 0\n          ? 0\n          : Math.max(\n              0,\n              Math.min(\n                scrollTop + blockScroll / scaleY,\n                frame.scrollHeight - height / scaleY + scrollbarHeight\n              )\n            )\n      inlineScroll =\n        scaleX === 0\n          ? 0\n          : Math.max(\n              0,\n              Math.min(\n                scrollLeft + inlineScroll / scaleX,\n                frame.scrollWidth - width / scaleX + scrollbarWidth\n              )\n            )\n\n      // Cache the offset so that parent frames can scroll this into view correctly\n      targetBlock += scrollTop - blockScroll\n      targetInline += scrollLeft - inlineScroll\n    }\n\n    computations.push({ el: frame, top: blockScroll, left: inlineScroll })\n  }\n\n  return computations\n}\n", "import { compute } from 'compute-scroll-into-view'\nimport type {\n  Options as BaseOptions,\n  ScrollAction,\n} from 'compute-scroll-into-view'\n\n/** @public */\nexport type Options<T = unknown> =\n  | StandardBehaviorOptions\n  | CustomBehaviorOptions<T>\n\n/**\n * Only scrolls if the `node` is partially out of view:\n * ```ts\n * scrollIntoView(node, { scrollMode: 'if-needed' })\n * ```\n * Skips scrolling `overflow: hidden` elements:\n * ```ts\n * scrollIntoView(node, { skipOverflowHiddenElements: true })\n * ```\n * When scrolling is needed do the least and smoothest scrolling possible:\n * ```ts\n * scrollIntoView(node, {\n *   behavior: 'smooth',\n *   scrollMode: 'if-needed',\n *   block: 'nearest',\n *   inline: 'nearest',\n * })\n * ```\n * @public\n */\nexport interface StandardBehaviorOptions extends BaseOptions {\n  /**\n   * @defaultValue 'auto\n   */\n  behavior?: ScrollBehavior\n}\n\n/** @public */\nexport interface CustomBehaviorOptions<T = unknown> extends BaseOptions {\n  behavior: CustomScrollBehaviorCallback<T>\n}\n\n/** @public */\nexport type CustomScrollBehaviorCallback<T = unknown> = (\n  actions: ScrollAction[]\n) => T\n\nconst isStandardScrollBehavior = (\n  options: any\n): options is StandardBehaviorOptions =>\n  options === Object(options) && Object.keys(options).length !== 0\n\nconst isCustomScrollBehavior = <T = unknown>(\n  options: any\n): options is CustomBehaviorOptions<T> =>\n  typeof options === 'object' ? typeof options.behavior === 'function' : false\n\nconst getOptions = (options: any): StandardBehaviorOptions => {\n  // Handle alignToTop for legacy reasons, to be compatible with the spec\n  if (options === false) {\n    return { block: 'end', inline: 'nearest' }\n  }\n\n  if (isStandardScrollBehavior(options)) {\n    // compute.ts ensures the defaults are block: 'center' and inline: 'nearest', to conform to the spec\n    return options\n  }\n\n  // if options = {}, options = true or options = null, based on w3c web platform test\n  return { block: 'start', inline: 'nearest' }\n}\n\nconst getScrollMargins = (target: Element) => {\n  const computedStyle = window.getComputedStyle(target)\n  return {\n    top: parseFloat(computedStyle.scrollMarginTop) || 0,\n    right: parseFloat(computedStyle.scrollMarginRight) || 0,\n    bottom: parseFloat(computedStyle.scrollMarginBottom) || 0,\n    left: parseFloat(computedStyle.scrollMarginLeft) || 0,\n  }\n}\n\n// Determine if the element is part of the document (including shadow dom)\n// Derived from code of Andy Desmarais\n// https://terodox.tech/how-to-tell-if-an-element-is-in-the-dom-including-the-shadow-dom/\nconst isInDocument = (element: Node) => {\n  let currentElement = element\n  while (currentElement && currentElement.parentNode) {\n    if (currentElement.parentNode === document) {\n      return true\n    } else if (currentElement.parentNode instanceof ShadowRoot) {\n      currentElement = (currentElement.parentNode as ShadowRoot).host\n    } else {\n      currentElement = currentElement.parentNode\n    }\n  }\n  return false\n}\n\n/**\n * Scrolls the given element into view, with options for when, and how.\n * Supports the same `options` as [`Element.prototype.scrollIntoView`](https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView) with additions such as `scrollMode`, `behavior: Function` and `skipOverflowHiddenElements`.\n * @public\n */\nfunction scrollIntoView(\n  target: Element,\n  options?: StandardBehaviorOptions | boolean\n): void\n/**\n * Scrolls the given element into view, with options for when, and how.\n * Supports the same `options` as [`Element.prototype.scrollIntoView`](https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollIntoView) with additions such as `scrollMode`, `behavior: Function` and `skipOverflowHiddenElements`.\n *\n * You can set the expected return type for `behavior: Function`:\n * ```ts\n * await scrollIntoView<Promise<boolean[]>>(node, {\n *   behavior: async actions => {\n *     return Promise.all(actions.map(\n *       // animate() resolves to `true` if anything was animated, `false` if the element already were in the end state\n *       ({ el, left, top }) => animate(el, {scroll: {left, top}})\n *     ))\n *   }\n * })\n * ```\n * @public\n */\nfunction scrollIntoView<T>(\n  target: Element,\n  options: CustomBehaviorOptions<T>\n): T\nfunction scrollIntoView<T = unknown>(\n  target: Element,\n  options?: StandardBehaviorOptions | CustomBehaviorOptions<T> | boolean\n): T | void {\n  // Browsers treats targets that aren't in the dom as a no-op and so should we\n  if (!target.isConnected || !isInDocument(target)) {\n    return\n  }\n\n  const margins = getScrollMargins(target)\n\n  if (isCustomScrollBehavior<T>(options)) {\n    return options.behavior(compute(target, options))\n  }\n\n  const behavior = typeof options === 'boolean' ? undefined : options?.behavior\n\n  for (const { el, top, left } of compute(target, getOptions(options))) {\n    const adjustedTop = top - margins.top + margins.bottom\n    const adjustedLeft = left - margins.left + margins.right\n    el.scroll({ top: adjustedTop, left: adjustedLeft, behavior })\n  }\n}\n\nexport default scrollIntoView\n", "import scrollIntoView, {\n  Options,\n  StandardBehaviorOptions,\n  CustomBehaviorOptions,\n} from 'scroll-into-view-if-needed'\n\nexport interface CustomEasing {\n  (t: number): number\n}\n\ntype OnScrollChangeCallback = (scrollState: {\n  target: Element\n  elapsed: number\n  value: number\n  left: number\n  top: number\n}) => void\n\nexport type SmoothBehaviorOptions = Options & {\n  behavior?: 'smooth'\n  duration?: number\n  ease?: CustomEasing\n  onScrollChange?: OnScrollChangeCallback\n}\n\n// Memoize so we're much more friendly to non-dom envs\nlet memoizedNow: () => number\nconst now = () => {\n  if (!memoizedNow) {\n    memoizedNow =\n      'performance' in window ? performance.now.bind(performance) : Date.now\n  }\n  return memoizedNow()\n}\n\ntype SmoothScrollAction = {\n  el: Element\n  // [start, end] tuples of the distance animated\n  left: [number, number]\n  top: [number, number]\n}\n\ntype Context = {\n  scrollable: Element\n  method: (elapsed: number, value: number, x: number, y: number) => void\n  startTime: number\n  startX: number\n  startY: number\n  x: number\n  y: number\n  duration: number\n  ease: CustomEasing\n  cb: Function\n}\nfunction step(context: Context) {\n  const time = now()\n  const elapsed = Math.min((time - context.startTime) / context.duration, 1)\n  // apply easing to elapsed time\n  const value = context.ease(elapsed)\n\n  const currentX = context.startX + (context.x - context.startX) * value\n  const currentY = context.startY + (context.y - context.startY) * value\n\n  context.method(currentX, currentY, elapsed, value)\n\n  // scroll more if we have not reached our destination\n  if (currentX !== context.x || currentY !== context.y) {\n    requestAnimationFrame(() => step(context))\n  } else {\n    // If nothing left to scroll lets fire the callback\n    context.cb()\n  }\n}\n\nfunction smoothScroll(\n  el: Element,\n  x: number,\n  y: number,\n  duration = 600,\n  ease: CustomEasing = (t) => 1 + --t * t * t * t * t,\n  cb: Function,\n  onScrollChange?: OnScrollChangeCallback\n) {\n  // define scroll context\n  const scrollable = el\n  const startX = el.scrollLeft\n  const startY = el.scrollTop\n  const method = (x: number, y: number, elapsed: number, value: number) => {\n    // @TODO use Element.scroll if it exists, as it is potentially better performing\n    // use ceil to include the the fractional part of the number for the scrolling\n    const left = Math.ceil(x)\n    const top = Math.ceil(y)\n\n    el.scrollLeft = left\n    el.scrollTop = top\n\n    onScrollChange?.({\n      target: el,\n      elapsed,\n      value,\n      left,\n      top,\n    })\n  }\n\n  // scroll looping over a frame if needed\n  step({\n    scrollable: scrollable,\n    method: method,\n    startTime: now(),\n    startX: startX,\n    startY: startY,\n    x: x,\n    y: y,\n    duration,\n    ease,\n    cb,\n  })\n}\n\nconst shouldSmoothScroll = <T>(options: any): options is T => {\n  return (options && !options.behavior) || options.behavior === 'smooth'\n}\n\nfunction scroll(target: Element, options?: SmoothBehaviorOptions): Promise<any>\nfunction scroll<T>(target: Element, options: CustomBehaviorOptions<T>): T\nfunction scroll(target: Element, options: StandardBehaviorOptions): void\nfunction scroll<T>(target: Element, options?: any) {\n  const overrides = options || {}\n  if (shouldSmoothScroll<SmoothBehaviorOptions>(overrides)) {\n    return scrollIntoView<Promise<SmoothScrollAction[]>>(target, {\n      block: overrides.block,\n      inline: overrides.inline,\n      scrollMode: overrides.scrollMode,\n      boundary: overrides.boundary,\n      skipOverflowHiddenElements: overrides.skipOverflowHiddenElements,\n      behavior: (actions) =>\n        Promise.all(\n          actions.reduce(\n            (results: Promise<SmoothScrollAction>[], { el, left, top }) => {\n              const startLeft = el.scrollLeft\n              const startTop = el.scrollTop\n              if (startLeft === left && startTop === top) {\n                return results\n              }\n\n              return [\n                ...results,\n                new Promise((resolve) => {\n                  return smoothScroll(\n                    el,\n                    left,\n                    top,\n                    overrides.duration,\n                    overrides.ease,\n                    () =>\n                      resolve({\n                        el,\n                        left: [startLeft, left],\n                        top: [startTop, top],\n                      }),\n                    overrides.onScrollChange\n                  )\n                }),\n              ]\n            },\n            []\n          )\n        ),\n    })\n  }\n\n  return Promise.resolve(scrollIntoView<T>(target, options))\n}\n\n// re-assign here makes the flowtype generation work\nconst smoothScrollIntoView = scroll\n\nexport default smoothScrollIntoView\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AA6EA;IAAAA,eAAA;IAAAA,eAAA;IAAMC,IAAaC,CAAAA,OACH,YAAA,OAAPA,MAAyB,QAANA,MAA8B,MAAhBA,GAAGC;AAD7C,IAGMC,IAAcA,CAClBC,IACAC,QAAAA,CAEIA,MAA2C,aAAbD,QAId,cAAbA,MAAuC,WAAbA;AAXnC,IAqCME,IAAeA,CAACL,IAAaI,OAAAA;AACjC,MAAIJ,GAAGM,eAAeN,GAAGO,gBAAgBP,GAAGQ,cAAcR,GAAGS,aAAa;AAClE,UAAAC,KAAQC,iBAAiBX,IAAI,IAAA;AAEjC,WAAAE,EAAYQ,GAAME,WAAWR,EAAAA,KAC7BF,EAAYQ,GAAMG,WAAWT,EAAAA,MAhBVJ,CAAAA,OAAAA;AACjB,YAAAc,MAbiBd,CAAAA,OAAAA;AACvB,YAAA,CAAKA,GAAGe,iBAAAA,CAAkBf,GAAGe,cAAcC,YAClC,QAAA;AAGL,YAAA;AACK,iBAAAhB,GAAGe,cAAcC,YAAYC;QAAAA,SAC7BC,IAAAA;AACA,iBAAA;QACT;MAAA,GAI8BlB,EAAAA;AAC9B,aAAA,CAAA,CAAKc,OAKHA,GAAMR,eAAeN,GAAGO,gBAAgBO,GAAMN,cAAcR,GAAGS;IAAA,GAU7CT,EAAAA;EAEpB;AAEO,SAAA;AAAA;AA/CT,IA0DMmB,IAAeA,CACnBC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,GACAC,OAsBGF,KAAmBL,MAClBM,IAAiBL,MAClBI,KAAmBL,MAAsBM,IAAiBL,KAEpD,IA2CNI,MAAoBL,MAAsBO,MAAeL,MACzDI,KAAkBL,MAAoBM,MAAeL,KAE/CG,KAAmBL,KAAqBG,KA4C9CG,IAAiBL,MAAoBM,KAAcL,MACnDG,KAAmBL,MAAsBO,KAAcL,KAEjDI,IAAiBL,KAAmBG,KAGtC;AA5LT,IA+LMI,IAAoBC,CAAAA,OAAAA;AACxB,QAAMC,KAASD,GAAQE;AACvB,SAAc,QAAVD,KACMD,GAAQG,YAAAA,EAA6BC,QAAQ,OAEhDH;AAAA;AApMT,IAkNaI,IAAUA,CAACC,IAAiBC,OAAAA;AA/RzC,MAAAC,GAAAC,IAAAC,GAAAC;AAgSM,MAAoB,eAAA,OAAbC,SAET,QAAO,CAAA;AAGT,QAAA,EAAMC,YAAEA,GAAYC,OAAAA,GAAAC,QAAOA,GAAQC,UAAAA,GAAAzC,4BAAUA,EAAAA,IAC3CgC,IAIIU,IACgB,cAAA,OAAbD,IAA0BA,IAAYE,CAAAA,OAAcA,OAASF;AAElE,MAAA,CAAC9C,EAAUoC,EAAAA,EACP,OAAA,IAAIa,UAAU,gBAAA;AAIhB,QAAAC,IAAmBR,SAASQ,oBAAoBR,SAASS,iBAGzDC,IAAoB,CAAA;AAC1B,MAAIC,IAAyBjB;AAC7B,SAAOpC,EAAUqD,CAAAA,KAAWN,EAAcM,CAAAA,KAAS;AAKjD,QAHAA,IAASxB,EAAiBwB,CAAAA,GAGtBA,MAAWH,GAAkB;AAC/BE,QAAOE,KAAKD,CAAAA;AACZ;IACF;AAIY,YAAVA,KACAA,MAAWX,SAASa,QACpBjD,EAAa+C,CAAAA,KAAAA,CACZ/C,EAAaoC,SAASS,eAAAA,KAMX,QAAVE,KAAkB/C,EAAa+C,GAAQhD,CAAAA,KACzC+C,EAAOE,KAAKD,CAAAA;EAEhB;AAOA,QAAMG,IAAgB,SAAAjB,KAAA,SAAAD,IAAAmB,OAAOC,kBAAAA,SAAPpB,EAAuBqB,SAASpB,KAAAqB,YAChDC,IAAiB,SAAApB,IAAA,SAAAD,IAAAiB,OAAOC,kBAAAA,SAAPlB,EAAuBsB,UAAUrB,IAAAsB,aAAAA,EAClDC,SAAEA,GAASC,SAAAA,EAAAA,IAAYR,QAAAA,EAG3BK,QAAQI,GACRP,OAAOQ,GACPC,KAAKC,GACLC,OAAOC,GACPC,QAAQC,GACRC,MAAMC,EAAAA,IACJvC,GAAOwC,sBAAAA,GAAAA,EAETR,KAAKS,GACLP,OAAOQ,GACPN,QAAQO,GACRL,MAAMM,EAAAA,KAlFgB5C,CAAAA,OAAAA;AAClB,UAAA6C,KAAgBxB,OAAO7C,iBAAiBwB,EAAAA;AACvC,WAAA,EACLgC,KAAKc,WAAWD,GAAcE,eAAAA,KAAoB,GAClDb,OAAOY,WAAWD,GAAcG,iBAAAA,KAAsB,GACtDZ,QAAQU,WAAWD,GAAcI,kBAAAA,KAAuB,GACxDX,MAAMQ,WAAWD,GAAcK,gBAAAA,KAAqB,EAAA;EACtD,GA4EqBlD,EAAAA;AAGrB,MAAImD,IACQ,YAAV3C,KAA+B,cAAVA,IACjByB,IAAYQ,IACF,UAAVjC,IACA6B,IAAeM,IACfV,IAAYH,IAAe,IAAIW,IAAYE,GAC7CS,IACS,aAAX3C,IACI8B,IAAaR,IAAc,IAAIa,IAAaF,IACjC,UAAXjC,IACA0B,IAAcO,IACdH,IAAaK;AAGnB,QAAMS,IAA+B,CAAA;AAErC,WAASC,KAAQ,GAAGA,KAAQtC,EAAOuC,QAAQD,MAAS;AAC5C,UAAA3E,KAAQqC,EAAOsC,EAAAA,GAAAA,EAIf5B,QAAEA,IAAAA,OAAQH,IAAOS,KAAAA,IAAAE,OAAKA,IAAAA,QAAOE,IAAQE,MAAAA,GAAAA,IACzC3D,GAAM6D,sBAAAA;AAKN,QAAe,gBAAfjC,KACA0B,KAAa,KACbM,KAAc,KACdF,KAAgBZ,KAChBU,KAAef,MAEbzC,OAAUmC,KAAAA,CAAqB5C,EAAaS,EAAAA,KAC3CsD,KAAaD,MACZK,KAAgBD,MAChBG,KAAcD,MACdH,KAAeD,IAGZ,QAAAmB;AAGH,UAAAG,KAAahF,iBAAiBG,EAAAA,GAC9B8E,KAAaC,SAASF,GAAWG,iBAA2B,EAAA,GAC5DC,KAAYF,SAASF,GAAWK,gBAA0B,EAAA,GAC1DC,KAAcJ,SAASF,GAAWO,kBAA4B,EAAA,GAC9DC,KAAeN,SAASF,GAAWS,mBAA6B,EAAA;AAEtE,QAAIC,KAAsB,GACtBC,KAAuB;AAIrB,UAAAC,KACJ,iBAAiBzF,KACZA,GAAsB0F,cACtB1F,GAAsBN,cACvBoF,KACAK,KACA,GACAQ,IACJ,kBAAkB3F,KACbA,GAAsB4F,eACtB5F,GAAsBR,eACvByF,KACAI,KACA,GAEAQ,IACJ,iBAAiB7F,KAC0B,MAAtCA,GAAsB0F,cACrB,IACA9C,KAAS5C,GAAsB0F,cACjC,GACAI,IACJ,kBAAkB9F,KAC0B,MAAvCA,GAAsB4F,eACrB,IACA7C,KAAU/C,GAAsB4F,eAClC;AAEN,QAAIzD,MAAqBnC,GAIPuF,CAAAA,KADF,YAAV1D,IACY2C,IACK,UAAV3C,IACK2C,IAAc1B,IACT,cAAVjB,IACKxB,EACZ6C,GACAA,IAAUJ,GACVA,GACAmC,IACAI,IACAnC,IAAUsB,GACVtB,IAAUsB,IAAcrB,GACxBA,CAAAA,IAIYqB,IAAc1B,IAAiB,GAI9B0C,KADF,YAAX1D,IACa2C,IACK,aAAX3C,IACM2C,IAAehC,IAAgB,IAC1B,UAAXX,IACM2C,IAAehC,IAGfpC,EACb4C,GACAA,IAAUR,GACVA,GACAqC,IACAK,IACAlC,IAAUwB,GACVxB,IAAUwB,IAAerB,GACzBA,CAAAA,GAMJmC,KAAcQ,KAAKC,IAAI,GAAGT,KAAcrC,CAAAA,GACxCsC,KAAeO,KAAKC,IAAI,GAAGR,KAAevC,CAAAA;SACrC;AAGHsC,MAAAA,KADY,YAAV1D,IACY2C,IAAcnB,KAAM4B,KACf,UAAVpD,IACK2C,IAAcf,KAAS4B,KAAeM,IACjC,cAAV9D,IACKxB,EACZgD,IACAI,IACAV,IACAkC,IACAI,KAAeM,GACfnB,GACAA,IAAcrB,GACdA,CAAAA,IAIYqB,KAAenB,KAAMN,KAAS,KAAK4C,IAAkB,GAInEH,KADa,YAAX1D,IACa2C,IAAed,KAAOmB,KACjB,aAAXhD,IACM2C,KAAgBd,KAAOf,KAAQ,KAAK6C,KAAiB,IAChD,UAAX3D,IACM2C,IAAelB,KAAQ4B,KAAcM,KAGrCpF,EACbsD,IACAJ,IACAX,IACAkC,IACAK,KAAcM,IACdhB,GACAA,IAAerB,GACfA,CAAAA;AAIE,YAAA,EAAA6C,YAAEA,IAAYC,WAAAA,GAAAA,IAAclG;AAGhCuF,MAAAA,KAAW,MAAXO,IACI,IACAC,KAAKC,IACH,GACAD,KAAKI,IACHD,KAAYX,KAAcO,GAC1B9F,GAAMP,eAAesD,KAAS+C,IAASH,CAAAA,CAAAA,GAI/CH,KAAW,MAAXK,IACI,IACAE,KAAKC,IACH,GACAD,KAAKI,IACHF,KAAaT,KAAeK,GAC5B7F,GAAML,cAAciD,KAAQiD,IAASJ,EAAAA,CAAAA,GAK/CjB,KAAe0B,KAAYX,IAC3Bd,KAAgBwB,KAAaT;IAC/B;AAEad,MAAAnC,KAAK,EAAErD,IAAIc,IAAOqD,KAAKkC,IAAa5B,MAAM6B,GAAAA,CAAAA;EACzD;AAEO,SAAAd;AAAA;;;ACpgBT,IAUM0B,KAAcC,CAAAA,OAAAA,UAEdA,KACK,EAAEC,OAAO,OAAOC,QAAQ,UAAA,KAZjCF,CAAAA,OAEAA,OAAYG,OAAOH,EAAAA,KAA4C,MAAhCG,OAAOC,KAAKJ,EAAAA,EAASK,QAavBL,EAAAA,IAEpBA,KAIF,EAAEC,OAAO,SAASC,QAAQ,UAAA;AA4DnC,SAASI,GACPC,IACAP,IAAAA;AAGA,MAAA,CAAKO,GAAOC,eAAAA,EAjDQC,CAAAA,OAAAA;AACpB,QAAIC,KAAiBD;AACd,WAAAC,MAAkBA,GAAeC,cAAY;AAC9C,UAAAD,GAAeC,eAAeC,SACzB,QAAA;AAEPF,MAAAA,KADSA,GAAeC,sBAAsBE,aAC5BH,GAAeC,WAA0BG,OAE1CJ,GAAeC;IAEpC;AACO,WAAA;EAAA,GAsCkCJ,EAAAA,EACvC;AAGI,QAAAQ,MAlEkBR,CAAAA,OAAAA;AAClB,UAAAS,KAAgBC,OAAOC,iBAAiBX,EAAAA;AACvC,WAAA,EACLY,KAAKC,WAAWJ,GAAcK,eAAAA,KAAoB,GAClDC,OAAOF,WAAWJ,GAAcO,iBAAAA,KAAsB,GACtDC,QAAQJ,WAAWJ,GAAcS,kBAAAA,KAAuB,GACxDC,MAAMN,WAAWJ,GAAcW,gBAAAA,KAAqB,EAAA;EACtD,GA2DiCpB,EAAAA;AAE7B,OAvFJP,CAAAA,OAEmB,YAAA,OAAZA,MAAmD,cAAA,OAArBA,GAAQ4B,UAqFf5B,EAAAA,EAC5B,QAAOA,GAAQ4B,SAASC,EAAQtB,IAAQP,EAAAA,CAAAA;AAG1C,QAAM4B,KAA8B,aAAA,OAAZ5B,MAA6C,QAATA,KAAAA,SAASA,GAAA4B;AAE1D,aAAA,EAAAE,IAAEA,GAAIX,KAAAA,GAAAO,MAAKA,GAAAA,KAAUG,EAAQtB,IAAQR,GAAWC,EAAAA,CAAAA,GAAW;AACpE,UAAM+B,KAAcZ,IAAMJ,GAAQI,MAAMJ,GAAQS,QAC1CQ,KAAeN,KAAOX,GAAQW,OAAOX,GAAQO;AACnDQ,MAAGG,OAAO,EAAEd,KAAKY,IAAaL,MAAMM,IAAcJ,UAAAA,GAAAA,CAAAA;EACpD;AACF;;;AC9HA,IAAIM;AACJ,IAAMC,KAAMA,OACLD,OACHA,KACE,iBAAiBE,SAASC,YAAYF,IAAIG,KAAKD,WAAAA,IAAeE,KAAKJ,MAEhED,GAAAA;AAsBT,SAASM,GAAKC,IAAAA;AACZ,QAAMC,KAAOP,GAAAA,GACPQ,KAAUC,KAAKC,KAAKH,KAAOD,GAAQK,aAAaL,GAAQM,UAAU,CAAA,GAElEC,KAAQP,GAAQQ,KAAKN,EAAAA,GAErBO,KAAWT,GAAQU,UAAUV,GAAQW,IAAIX,GAAQU,UAAUH,IAC3DK,IAAWZ,GAAQa,UAAUb,GAAQc,IAAId,GAAQa,UAAUN;AAEjEP,EAAAA,GAAQe,OAAON,IAAUG,GAAUV,IAASK,EAAAA,GAGxCE,OAAaT,GAAQW,KAAKC,MAAaZ,GAAQc,IAC3BE,sBAAA,MAAMjB,GAAKC,EAAAA,CAAAA,IAGjCA,GAAQiB,GAAAA;AAEZ;AAEA,SAASC,GACPC,IACAR,IACAG,IAAAA;AAKA,MAJAR,KAAAc,UAAAC,SAAA,KAAA,WAAAD,UAAA,CAAA,IAAAA,UAAA,CAAA,IAAW,KACXZ,KAAqBY,UAAAC,SAAAA,KAAAA,WAAAD,UAAA,CAAA,IAAAA,UAAA,CAAA,IAACE,CAAAA,OAAM,IAAA,EAAMA,KAAIA,KAAIA,KAAIA,KAAIA,IAClDL,IAAAA,UAAAA,SAAAA,IAAAA,UAAAA,CAAAA,IAAAA,QACAM,IACAH,UAAAC,SAAAD,IAAAA,UAAAA,CAAAA,IAAAA;AAEA,QAAMI,IAAaL,IACbT,IAASS,GAAGM,YACZZ,IAASM,GAAGO;AAoBb3B,EAAAA,GAAA,EACHyB,YAAAA,GACAT,QArBaA,CAACJ,IAAWG,IAAWZ,IAAiBK,OAAAA;AAG/C,UAAAoB,KAAOxB,KAAKyB,KAAKjB,EAAAA,GACjBkB,KAAM1B,KAAKyB,KAAKd,EAAAA;AAEtBK,IAAAA,GAAGM,aAAaE,IAChBR,GAAGO,YAAYG,IAEE,QAAAN,KAAAA,EAAA,EACfO,QAAQX,IACRjB,SAAAA,IACAK,OAAAA,IACAoB,MAAAA,IACAE,KAAAA,GAAAA,CAAAA;EACF,GAOAxB,WAAWX,GAAAA,GACXgB,QAAAA,GACAG,QAAAA,GACAF,GAAAA,IACAG,GAAAA,IACAR,UAAAA,IACAE,MAAAA,IACAS,IAAAA,EAAAA,CAAAA;AAEJ;AAEA,IAAMc,KAAyBC,CAAAA,OACrBA,MAAAA,CAAYA,GAAQC,YAAkC,aAArBD,GAAQC;AAuDnD,IAAMC,IAjDN,SAAmBJ,IAAiBE,IAAAA;AAC5B,QAAAG,KAAYH,MAAW,CAAA;AACzB,SAAAD,GAA0CI,EAAAA,IACrCC,GAA8CN,IAAQ,EAC3DO,OAAOF,GAAUE,OACjBC,QAAQH,GAAUG,QAClBC,YAAYJ,GAAUI,YACtBC,UAAUL,GAAUK,UACpBC,4BAA4BN,GAAUM,4BACtCR,UAAWS,CAAAA,OACTC,QAAQC,IACNF,GAAQG,OACN,CAACC,IAAwCC,OAAAA;AAAsB,QAAA,EAAtB5B,IAAEA,IAAIQ,MAAAA,IAAAE,KAAMA,GAAAA,IAAUkB;AAC7D,UAAMC,IAAY7B,GAAGM,YACfwB,IAAW9B,GAAGO;AAChB,WAAAsB,MAAcrB,MAAQsB,MAAapB,KAC9BiB,KAGF,CAAA,GACFA,IACH,IAAIH,QAASO,CAAAA,OACJhC,GACLC,IACAQ,IACAE,IACAM,GAAU7B,UACV6B,GAAU3B,MACV,MACE0C,GAAQ,EACN/B,IAAAA,IACAQ,MAAM,CAACqB,GAAWrB,EAAAA,GAClBE,KAAK,CAACoB,GAAUpB,EAAAA,EAAAA,CAAAA,GAEpBM,GAAUZ,cAAAA,CAAAA,CAAAA;EAGhB,GAEF,CAAA,CAAA,CAAA,EAAA,CAAA,IAMHoB,QAAQO,QAAQd,GAAkBN,IAAQE,EAAAA,CAAAA;AACnD;", "names": ["import_dist", "isElement", "el", "nodeType", "canOverflow", "overflow", "skipOverflowHiddenElements", "isScrollable", "clientHeight", "scrollHeight", "clientWidth", "scrollWidth", "style", "getComputedStyle", "overflowY", "overflowX", "frame", "ownerDocument", "defaultView", "frameElement", "e", "alignNearest", "scrollingEdgeStart", "scrollingEdgeEnd", "scrollingSize", "scrollingBorderStart", "scrollingBorderEnd", "elementEdgeStart", "elementEdgeEnd", "elementSize", "getParentElement", "element", "parent", "parentElement", "getRootNode", "host", "compute", "target", "options", "_a", "_b", "_c", "_d", "document", "scrollMode", "block", "inline", "boundary", "checkBoundary", "node", "TypeError", "scrollingElement", "documentElement", "frames", "cursor", "push", "body", "viewportWidth", "window", "visualViewport", "width", "innerWidth", "viewportHeight", "height", "innerHeight", "scrollX", "scrollY", "targetHeight", "targetWidth", "top", "targetTop", "right", "targetRight", "bottom", "targetBottom", "left", "targetLeft", "getBoundingClientRect", "marginTop", "marginRight", "marginBottom", "marginLeft", "computedStyle", "parseFloat", "scrollMarginTop", "scrollMarginRight", "scrollMarginBottom", "scrollMarginLeft", "targetBlock", "targetInline", "computations", "index", "length", "frameStyle", "borderLeft", "parseInt", "borderLeftWidth", "borderTop", "borderTopWidth", "borderRight", "borderRightWidth", "borderBottom", "borderBottomWidth", "blockScroll", "inlineScroll", "scrollbarWidth", "offsetWidth", "scrollbarHeight", "offsetHeight", "scaleX", "scaleY", "Math", "max", "scrollLeft", "scrollTop", "min", "getOptions", "options", "block", "inline", "Object", "keys", "length", "scrollIntoView", "target", "isConnected", "element", "currentElement", "parentNode", "document", "ShadowRoot", "host", "margins", "computedStyle", "window", "getComputedStyle", "top", "parseFloat", "scrollMarginTop", "right", "scrollMarginRight", "bottom", "scrollMarginBottom", "left", "scrollMarginLeft", "behavior", "compute", "el", "adjustedTop", "adjustedLeft", "scroll", "memoizedNow", "now", "window", "performance", "bind", "Date", "step", "context", "time", "elapsed", "Math", "min", "startTime", "duration", "value", "ease", "currentX", "startX", "x", "currentY", "startY", "y", "method", "requestAnimationFrame", "cb", "smoothScroll", "el", "arguments", "length", "t", "onScrollChange", "scrollable", "scrollLeft", "scrollTop", "left", "ceil", "top", "target", "shouldSmoothScroll", "options", "behavior", "smoothScrollIntoView", "overrides", "scrollIntoView", "block", "inline", "scrollMode", "boundary", "skipOverflowHiddenElements", "actions", "Promise", "all", "reduce", "results", "_ref", "startLeft", "startTop", "resolve"]}