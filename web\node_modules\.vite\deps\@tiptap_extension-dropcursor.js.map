{"version": 3, "sources": ["../../@tiptap/pm/dropcursor/dist/index.js", "../../@tiptap/extension-dropcursor/src/dropcursor.ts"], "sourcesContent": ["// dropcursor/index.ts\nexport * from \"prosemirror-dropcursor\";\n", "import { Extension } from '@tiptap/core'\nimport { dropCursor } from '@tiptap/pm/dropcursor'\n\nexport interface DropcursorOptions {\n  /**\n   * The color of the drop cursor\n   * @default 'currentColor'\n   * @example 'red'\n   */\n  color: string | undefined,\n\n  /**\n   * The width of the drop cursor\n   * @default 1\n   * @example 2\n  */\n  width: number | undefined,\n\n  /**\n   * The class of the drop cursor\n   * @default undefined\n   * @example 'drop-cursor'\n  */\n  class: string | undefined,\n}\n\n/**\n * This extension allows you to add a drop cursor to your editor.\n * A drop cursor is a line that appears when you drag and drop content\n * inbetween nodes.\n * @see https://tiptap.dev/api/extensions/dropcursor\n */\nexport const Dropcursor = Extension.create<DropcursorOptions>({\n  name: 'dropCursor',\n\n  addOptions() {\n    return {\n      color: 'currentColor',\n      width: 1,\n      class: undefined,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      dropCursor(this.options),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACgCa,IAAA,aAAa,UAAU,OAA0B;EAC5D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,OAAO;MACP,OAAO;MACP,OAAO;;;EAIX,wBAAqB;AACnB,WAAO;MACL,WAAW,KAAK,OAAO;;;AAG5B,CAAA;", "names": ["import_dist"]}