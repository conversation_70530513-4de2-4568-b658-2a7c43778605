{"version": 3, "sources": ["../../@tiptap/extension-highlight/src/highlight.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface HighlightOptions {\n  /**\n   * Allow multiple highlight colors\n   * @default false\n   * @example true\n   */\n  multicolor: boolean,\n\n  /**\n   * HTML attributes to add to the highlight element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    highlight: {\n      /**\n       * Set a highlight mark\n       * @param attributes The highlight attributes\n       * @example editor.commands.setHighlight({ color: 'red' })\n       */\n      setHighlight: (attributes?: { color: string }) => ReturnType,\n      /**\n       * Toggle a highlight mark\n       * @param attributes The highlight attributes\n       * @example editor.commands.toggleHighlight({ color: 'red' })\n       */\n      toggleHighlight: (attributes?: { color: string }) => ReturnType,\n      /**\n       * Unset a highlight mark\n       * @example editor.commands.unsetHighlight()\n       */\n      unsetHighlight: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a highlight to a ==highlight== on input.\n */\nexport const inputRegex = /(?:^|\\s)(==(?!\\s+==)((?:[^=]+))==(?!\\s+==))$/\n\n/**\n * Matches a highlight to a ==highlight== on paste.\n */\nexport const pasteRegex = /(?:^|\\s)(==(?!\\s+==)((?:[^=]+))==(?!\\s+==))/g\n\n/**\n * This extension allows you to highlight text.\n * @see https://www.tiptap.dev/api/marks/highlight\n */\nexport const Highlight = Mark.create<HighlightOptions>({\n  name: 'highlight',\n\n  addOptions() {\n    return {\n      multicolor: false,\n      HTMLAttributes: {},\n    }\n  },\n\n  addAttributes() {\n    if (!this.options.multicolor) {\n      return {}\n    }\n\n    return {\n      color: {\n        default: null,\n        parseHTML: element => element.getAttribute('data-color') || element.style.backgroundColor,\n        renderHTML: attributes => {\n          if (!attributes.color) {\n            return {}\n          }\n\n          return {\n            'data-color': attributes.color,\n            style: `background-color: ${attributes.color}; color: inherit`,\n          }\n        },\n      },\n    }\n  },\n\n  parseHTML() {\n    return [\n      {\n        tag: 'mark',\n      },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['mark', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setHighlight: attributes => ({ commands }) => {\n        return commands.setMark(this.name, attributes)\n      },\n      toggleHighlight: attributes => ({ commands }) => {\n        return commands.toggleMark(this.name, attributes)\n      },\n      unsetHighlight: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-h': () => this.editor.commands.toggleHighlight(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkDO,IAAM,aAAa;AAKnB,IAAM,aAAa;AAMb,IAAA,YAAY,KAAK,OAAyB;EACrD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,YAAY;MACZ,gBAAgB,CAAA;;;EAIpB,gBAAa;AACX,QAAI,CAAC,KAAK,QAAQ,YAAY;AAC5B,aAAO,CAAA;;AAGT,WAAO;MACL,OAAO;QACL,SAAS;QACT,WAAW,aAAW,QAAQ,aAAa,YAAY,KAAK,QAAQ,MAAM;QAC1E,YAAY,gBAAa;AACvB,cAAI,CAAC,WAAW,OAAO;AACrB,mBAAO,CAAA;;AAGT,iBAAO;YACL,cAAc,WAAW;YACzB,OAAO,qBAAqB,WAAW,KAAK;;;MAGjD;;;EAIL,YAAS;AACP,WAAO;MACL;QACE,KAAK;MACN;;;EAIL,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,QAAQ,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAGjF,cAAW;AACT,WAAO;MACL,cAAc,gBAAc,CAAC,EAAE,SAAQ,MAAM;AAC3C,eAAO,SAAS,QAAQ,KAAK,MAAM,UAAU;;MAE/C,iBAAiB,gBAAc,CAAC,EAAE,SAAQ,MAAM;AAC9C,eAAO,SAAS,WAAW,KAAK,MAAM,UAAU;;MAElD,gBAAgB,MAAM,CAAC,EAAE,SAAQ,MAAM;AACrC,eAAO,SAAS,UAAU,KAAK,IAAI;;;;EAKzC,uBAAoB;AAClB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,gBAAe;;;EAI7D,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;EAIL,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;AAGN,CAAA;", "names": []}