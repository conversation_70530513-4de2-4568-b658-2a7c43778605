<template>
  <div class="click-menu-test">
    <h1>点击菜单专项测试</h1>
    
    <div class="test-instructions">
      <h3>🎯 测试说明</h3>
      <ul>
        <li><strong>悬停测试</strong>：将鼠标悬停在段落左侧，应该看到 "+" 和拖拽按钮</li>
        <li><strong>添加功能</strong>：点击 "+" 按钮应该在当前段落后插入新段落</li>
        <li><strong>拖拽功能</strong>：拖拽六点图标可以重新排列段落（开发中）</li>
        <li><strong>编辑模式</strong>：只有在编辑模式下才显示点击菜单</li>
      </ul>
    </div>

    <div class="controls">
      <button @click="toggleEditable" :class="{ active: isEditable }">
        {{ isEditable ? '编辑模式' : '只读模式' }}
      </button>
      <button @click="addTestContent">添加测试内容</button>
      <button @click="clearContent">清空内容</button>
    </div>

    <div class="editor-wrapper">
      <div ref="editorRef" class="editor"></div>
    </div>

    <div class="debug-info">
      <h3>调试信息</h3>
      <div class="debug-item">
        <strong>编辑模式：</strong>{{ isEditable ? '是' : '否' }}
      </div>
      <div class="debug-item">
        <strong>段落数量：</strong>{{ paragraphCount }}
      </div>
      <div class="debug-item">
        <strong>最后操作：</strong>{{ lastAction }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import { onMounted, onUnmounted, ref } from 'vue'

import { ClickMenuExtension } from '@/components/tiptap/extensions/click-menu'

const editorRef = ref<HTMLElement>()
const isEditable = ref(true)
const paragraphCount = ref(0)
const lastAction = ref('初始化')
let editor: Editor | null = null

const testContent = `
  <h1>点击菜单测试标题</h1>
  <p>这是第一个段落。将鼠标悬停在左侧查看点击菜单。</p>
  <p>这是第二个段落。点击 "+" 按钮可以添加新段落。</p>
  <h2>二级标题</h2>
  <p>这是标题下的段落。</p>
  <blockquote>
    <p>这是一个引用块。</p>
  </blockquote>
  <ul>
    <li>列表项 1</li>
    <li>列表项 2</li>
    <li>列表项 3</li>
  </ul>
  <p>最后一个段落。尝试拖拽重新排列。</p>
`

onMounted(() => {
  if (!editorRef.value) return

  editor = new Editor({
    element: editorRef.value,
    extensions: [
      StarterKit,
      ClickMenuExtension.configure({
        showDragHandle: true,
        showAddButton: true,
        onAddClick: (editor, pos) => {
          lastAction.value = `在位置 ${pos} 添加新段落`
          // 在指定位置后插入新段落
          const newPos = pos + 1
          editor.chain().focus().insertContentAt(newPos, '<p>新添加的段落</p>').run()
          updateParagraphCount()
        },
        onDragStart: (editor, pos) => {
          lastAction.value = `开始拖拽位置 ${pos} 的元素`
          console.log('拖拽开始:', pos)
        },
        onDragEnd: (editor, pos) => {
          lastAction.value = `结束拖拽位置 ${pos} 的元素`
          console.log('拖拽结束:', pos)
        },
      }),
    ],
    content: testContent,
    editable: isEditable.value,
    onUpdate: () => {
      updateParagraphCount()
    },
  })

  updateParagraphCount()
})

onUnmounted(() => {
  if (editor) {
    editor.destroy()
  }
})

const updateParagraphCount = () => {
  if (editor) {
    const doc = editor.getJSON()
    let count = 0
    const countNodes = (node: any) => {
      if (node.type === 'paragraph' || node.type === 'heading') {
        count++
      }
      if (node.content) {
        node.content.forEach(countNodes)
      }
    }
    if (doc.content) {
      doc.content.forEach(countNodes)
    }
    paragraphCount.value = count
  }
}

const toggleEditable = () => {
  isEditable.value = !isEditable.value
  if (editor) {
    editor.setEditable(isEditable.value)
    lastAction.value = `切换到${isEditable.value ? '编辑' : '只读'}模式`
  }
}

const addTestContent = () => {
  if (editor) {
    const newContent = `<p>这是新添加的测试段落 ${Date.now()}</p>`
    editor.chain().focus().insertContent(newContent).run()
    lastAction.value = '添加测试内容'
    updateParagraphCount()
  }
}

const clearContent = () => {
  if (editor) {
    editor.commands.clearContent()
    lastAction.value = '清空内容'
    updateParagraphCount()
  }
}
</script>

<style scoped lang="scss">
.click-menu-test {
  max-width: 1000px;
  margin: 0 auto;
  padding: 2rem;

  h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 2rem;
  }

  .test-instructions {
    background-color: #e3f2fd;
    border: 1px solid #2196f3;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;

    h3 {
      margin-top: 0;
      color: #1976d2;
    }

    ul {
      margin-bottom: 0;
    }

    li {
      margin-bottom: 0.5rem;
      line-height: 1.5;
    }

    strong {
      color: #1565c0;
    }
  }

  .controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: center;

    button {
      padding: 0.75rem 1.5rem;
      border: 1px solid #007bff;
      border-radius: 0.375rem;
      background-color: white;
      color: #007bff;
      cursor: pointer;
      transition: all 0.15s ease;
      font-weight: 500;

      &:hover {
        background-color: #007bff;
        color: white;
      }

      &.active {
        background-color: #007bff;
        color: white;
      }
    }
  }

  .editor-wrapper {
    border: 2px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 2rem;
    margin-bottom: 2rem;
    background-color: white;
    min-height: 500px;
    position: relative;

    .editor {
      min-height: 450px;

      :deep(.ProseMirror) {
        outline: none;
        padding: 1rem;
        line-height: 1.6;
        font-size: 1rem;

        h1, h2, h3 {
          margin: 1.5rem 0 1rem 0;
          color: #2c3e50;
          font-weight: 600;
        }

        h1 { font-size: 2rem; }
        h2 { font-size: 1.5rem; }

        p {
          margin: 1rem 0;
          min-height: 1.5rem;
        }

        ul, ol {
          padding-left: 2rem;
          margin: 1rem 0;
        }

        li {
          margin: 0.5rem 0;
        }

        blockquote {
          border-left: 4px solid #007bff;
          padding-left: 1rem;
          margin: 1.5rem 0;
          color: #6c757d;
          font-style: italic;
        }
      }
    }
  }

  .debug-info {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1.5rem;

    h3 {
      margin-top: 0;
      color: #495057;
    }

    .debug-item {
      margin: 0.5rem 0;
      font-family: monospace;
      font-size: 0.9rem;

      strong {
        color: #007bff;
      }
    }
  }
}

// 暗色主题支持
.dark-theme .click-menu-test {
  .test-instructions {
    background-color: #1e3a8a;
    border-color: #3b82f6;
    color: #e2e8f0;

    h3 {
      color: #93c5fd;
    }

    strong {
      color: #bfdbfe;
    }
  }

  .editor-wrapper {
    background-color: #2d3748;
    border-color: #4a5568;
  }

  .debug-info {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;

    h3 {
      color: #e2e8f0;
    }

    .debug-item strong {
      color: #63b3ed;
    }
  }
}
</style>
