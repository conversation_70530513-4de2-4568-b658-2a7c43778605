{"version": 3, "sources": ["../../@tiptap/pm/keymap/dist/index.js", "../../@tiptap/pm/transform/dist/index.js", "../../@tiptap/pm/commands/dist/index.js", "../../@tiptap/pm/schema-list/dist/index.js", "../../@tiptap/core/src/helpers/createChainableState.ts", "../../@tiptap/core/src/CommandManager.ts", "../../@tiptap/core/src/EventEmitter.ts", "../../@tiptap/core/src/helpers/getExtensionField.ts", "../../@tiptap/core/src/helpers/splitExtensions.ts", "../../@tiptap/core/src/helpers/getAttributesFromExtensions.ts", "../../@tiptap/core/src/helpers/getNodeType.ts", "../../@tiptap/core/src/utilities/mergeAttributes.ts", "../../@tiptap/core/src/helpers/getRenderedAttributes.ts", "../../@tiptap/core/src/utilities/isFunction.ts", "../../@tiptap/core/src/utilities/callOrReturn.ts", "../../@tiptap/core/src/utilities/isEmptyObject.ts", "../../@tiptap/core/src/utilities/fromString.ts", "../../@tiptap/core/src/helpers/injectExtensionAttributesToParseRule.ts", "../../@tiptap/core/src/helpers/getSchemaByResolvedExtensions.ts", "../../@tiptap/core/src/helpers/getSchemaTypeByName.ts", "../../@tiptap/core/src/helpers/isExtensionRulesEnabled.ts", "../../@tiptap/core/src/helpers/getHTMLFromFragment.ts", "../../@tiptap/core/src/helpers/getTextContentFromNodes.ts", "../../@tiptap/core/src/utilities/isRegExp.ts", "../../@tiptap/core/src/InputRule.ts", "../../@tiptap/core/src/utilities/isPlainObject.ts", "../../@tiptap/core/src/utilities/mergeDeep.ts", "../../@tiptap/core/src/Mark.ts", "../../@tiptap/core/src/utilities/isNumber.ts", "../../@tiptap/core/src/PasteRule.ts", "../../@tiptap/core/src/utilities/findDuplicates.ts", "../../@tiptap/core/src/ExtensionManager.ts", "../../@tiptap/core/src/Extension.ts", "../../@tiptap/core/src/helpers/getTextBetween.ts", "../../@tiptap/core/src/helpers/getTextSerializersFromSchema.ts", "../../@tiptap/core/src/extensions/clipboardTextSerializer.ts", "../../@tiptap/core/src/commands/blur.ts", "../../@tiptap/core/src/commands/clearContent.ts", "../../@tiptap/core/src/commands/clearNodes.ts", "../../@tiptap/core/src/commands/command.ts", "../../@tiptap/core/src/commands/createParagraphNear.ts", "../../@tiptap/core/src/commands/cut.ts", "../../@tiptap/core/src/commands/deleteCurrentNode.ts", "../../@tiptap/core/src/commands/deleteNode.ts", "../../@tiptap/core/src/commands/deleteRange.ts", "../../@tiptap/core/src/commands/deleteSelection.ts", "../../@tiptap/core/src/commands/enter.ts", "../../@tiptap/core/src/commands/exitCode.ts", "../../@tiptap/core/src/utilities/objectIncludes.ts", "../../@tiptap/core/src/helpers/getMarkRange.ts", "../../@tiptap/core/src/helpers/getMarkType.ts", "../../@tiptap/core/src/commands/extendMarkRange.ts", "../../@tiptap/core/src/commands/first.ts", "../../@tiptap/core/src/helpers/isTextSelection.ts", "../../@tiptap/core/src/utilities/minMax.ts", "../../@tiptap/core/src/helpers/resolveFocusPosition.ts", "../../@tiptap/core/src/utilities/isAndroid.ts", "../../@tiptap/core/src/utilities/isiOS.ts", "../../@tiptap/core/src/commands/focus.ts", "../../@tiptap/core/src/commands/forEach.ts", "../../@tiptap/core/src/commands/insertContent.ts", "../../@tiptap/core/src/utilities/elementFromString.ts", "../../@tiptap/core/src/helpers/createNodeFromContent.ts", "../../@tiptap/core/src/helpers/selectionToInsertionEnd.ts", "../../@tiptap/core/src/commands/insertContentAt.ts", "../../@tiptap/core/src/commands/join.ts", "../../@tiptap/core/src/commands/joinItemBackward.ts", "../../@tiptap/core/src/commands/joinItemForward.ts", "../../@tiptap/core/src/commands/joinTextblockBackward.ts", "../../@tiptap/core/src/commands/joinTextblockForward.ts", "../../@tiptap/core/src/utilities/isMacOS.ts", "../../@tiptap/core/src/commands/keyboardShortcut.ts", "../../@tiptap/core/src/helpers/isNodeActive.ts", "../../@tiptap/core/src/commands/lift.ts", "../../@tiptap/core/src/commands/liftEmptyBlock.ts", "../../@tiptap/core/src/commands/liftListItem.ts", "../../@tiptap/core/src/commands/newlineInCode.ts", "../../@tiptap/core/src/helpers/getSchemaTypeNameByName.ts", "../../@tiptap/core/src/utilities/deleteProps.ts", "../../@tiptap/core/src/commands/resetAttributes.ts", "../../@tiptap/core/src/commands/scrollIntoView.ts", "../../@tiptap/core/src/commands/selectAll.ts", "../../@tiptap/core/src/commands/selectNodeBackward.ts", "../../@tiptap/core/src/commands/selectNodeForward.ts", "../../@tiptap/core/src/commands/selectParentNode.ts", "../../@tiptap/core/src/commands/selectTextblockEnd.ts", "../../@tiptap/core/src/commands/selectTextblockStart.ts", "../../@tiptap/core/src/helpers/createDocument.ts", "../../@tiptap/core/src/commands/setContent.ts", "../../@tiptap/core/src/helpers/getMarkAttributes.ts", "../../@tiptap/core/src/helpers/combineTransactionSteps.ts", "../../@tiptap/core/src/helpers/defaultBlockAt.ts", "../../@tiptap/core/src/helpers/findChildren.ts", "../../@tiptap/core/src/helpers/findChildrenInRange.ts", "../../@tiptap/core/src/helpers/findParentNodeClosestToPos.ts", "../../@tiptap/core/src/helpers/findParentNode.ts", "../../@tiptap/core/src/helpers/getSchema.ts", "../../@tiptap/core/src/helpers/generateHTML.ts", "../../@tiptap/core/src/helpers/generateJSON.ts", "../../@tiptap/core/src/helpers/getText.ts", "../../@tiptap/core/src/helpers/generateText.ts", "../../@tiptap/core/src/helpers/getNodeAttributes.ts", "../../@tiptap/core/src/helpers/getAttributes.ts", "../../@tiptap/core/src/utilities/removeDuplicates.ts", "../../@tiptap/core/src/helpers/getChangedRanges.ts", "../../@tiptap/core/src/helpers/getDebugJSON.ts", "../../@tiptap/core/src/helpers/getMarksBetween.ts", "../../@tiptap/core/src/helpers/getNodeAtPosition.ts", "../../@tiptap/core/src/helpers/getSplittedAttributes.ts", "../../@tiptap/core/src/helpers/isMarkActive.ts", "../../@tiptap/core/src/helpers/isActive.ts", "../../@tiptap/core/src/helpers/isAtEndOfNode.ts", "../../@tiptap/core/src/helpers/isAtStartOfNode.ts", "../../@tiptap/core/src/helpers/isList.ts", "../../@tiptap/core/src/helpers/isNodeEmpty.ts", "../../@tiptap/core/src/helpers/isNodeSelection.ts", "../../@tiptap/core/src/helpers/posToDOMRect.ts", "../../@tiptap/core/src/helpers/rewriteUnknownContent.ts", "../../@tiptap/core/src/commands/setMark.ts", "../../@tiptap/core/src/commands/setMeta.ts", "../../@tiptap/core/src/commands/setNode.ts", "../../@tiptap/core/src/commands/setNodeSelection.ts", "../../@tiptap/core/src/commands/setTextSelection.ts", "../../@tiptap/core/src/commands/sinkListItem.ts", "../../@tiptap/core/src/commands/splitBlock.ts", "../../@tiptap/core/src/commands/splitListItem.ts", "../../@tiptap/core/src/commands/toggleList.ts", "../../@tiptap/core/src/commands/toggleMark.ts", "../../@tiptap/core/src/commands/toggleNode.ts", "../../@tiptap/core/src/commands/toggleWrap.ts", "../../@tiptap/core/src/commands/undoInputRule.ts", "../../@tiptap/core/src/commands/unsetAllMarks.ts", "../../@tiptap/core/src/commands/unsetMark.ts", "../../@tiptap/core/src/commands/updateAttributes.ts", "../../@tiptap/core/src/commands/wrapIn.ts", "../../@tiptap/core/src/commands/wrapInList.ts", "../../@tiptap/core/src/extensions/commands.ts", "../../@tiptap/core/src/extensions/drop.ts", "../../@tiptap/core/src/extensions/editable.ts", "../../@tiptap/core/src/extensions/focusEvents.ts", "../../@tiptap/core/src/extensions/keymap.ts", "../../@tiptap/core/src/extensions/paste.ts", "../../@tiptap/core/src/extensions/tabindex.ts", "../../@tiptap/core/src/NodePos.ts", "../../@tiptap/core/src/style.ts", "../../@tiptap/core/src/utilities/createStyleTag.ts", "../../@tiptap/core/src/Editor.ts", "../../@tiptap/core/src/inputRules/markInputRule.ts", "../../@tiptap/core/src/inputRules/nodeInputRule.ts", "../../@tiptap/core/src/inputRules/textblockTypeInputRule.ts", "../../@tiptap/core/src/inputRules/textInputRule.ts", "../../@tiptap/core/src/inputRules/wrappingInputRule.ts", "../../@tiptap/core/src/Node.ts", "../../@tiptap/core/src/NodeView.ts", "../../@tiptap/core/src/pasteRules/markPasteRule.ts", "../../@tiptap/core/src/utilities/escapeForRegEx.ts", "../../@tiptap/core/src/utilities/isString.ts", "../../@tiptap/core/src/pasteRules/nodePasteRule.ts", "../../@tiptap/core/src/pasteRules/textPasteRule.ts", "../../@tiptap/core/src/Tracker.ts"], "sourcesContent": ["// keymap/index.ts\nexport * from \"prosemirror-keymap\";\n", "// transform/index.ts\nexport * from \"prosemirror-transform\";\n", "// commands/index.ts\nexport * from \"prosemirror-commands\";\n", "// schema-list/index.ts\nexport * from \"prosemirror-schema-list\";\n", "import { EditorState, Transaction } from '@tiptap/pm/state'\n\n/**\n * Takes a Transaction & Editor State and turns it into a chainable state object\n * @param config The transaction and state to create the chainable state from\n * @returns A chainable Editor state object\n */\nexport function createChainableState(config: {\n  transaction: Transaction\n  state: EditorState\n}): EditorState {\n  const { state, transaction } = config\n  let { selection } = transaction\n  let { doc } = transaction\n  let { storedMarks } = transaction\n\n  return {\n    ...state,\n    apply: state.apply.bind(state),\n    applyTransaction: state.applyTransaction.bind(state),\n    plugins: state.plugins,\n    schema: state.schema,\n    reconfigure: state.reconfigure.bind(state),\n    toJSON: state.toJSON.bind(state),\n    get storedMarks() {\n      return storedMarks\n    },\n    get selection() {\n      return selection\n    },\n    get doc() {\n      return doc\n    },\n    get tr() {\n      selection = transaction.selection\n      doc = transaction.doc\n      storedMarks = transaction.storedMarks\n\n      return transaction\n    },\n  }\n}\n", "import { EditorState, Transaction } from '@tiptap/pm/state'\n\nimport { Editor } from './Editor.js'\nimport { createChainableState } from './helpers/createChainableState.js'\nimport {\n  AnyCommands, CanCommands, ChainedCommands, CommandProps, SingleCommands,\n} from './types.js'\n\nexport class CommandManager {\n  editor: Editor\n\n  rawCommands: AnyCommands\n\n  customState?: EditorState\n\n  constructor(props: { editor: Editor; state?: EditorState }) {\n    this.editor = props.editor\n    this.rawCommands = this.editor.extensionManager.commands\n    this.customState = props.state\n  }\n\n  get hasCustomState(): boolean {\n    return !!this.customState\n  }\n\n  get state(): EditorState {\n    return this.customState || this.editor.state\n  }\n\n  get commands(): SingleCommands {\n    const { rawCommands, editor, state } = this\n    const { view } = editor\n    const { tr } = state\n    const props = this.buildProps(tr)\n\n    return Object.fromEntries(\n      Object.entries(rawCommands).map(([name, command]) => {\n        const method = (...args: any[]) => {\n          const callback = command(...args)(props)\n\n          if (!tr.getMeta('preventDispatch') && !this.hasCustomState) {\n            view.dispatch(tr)\n          }\n\n          return callback\n        }\n\n        return [name, method]\n      }),\n    ) as unknown as SingleCommands\n  }\n\n  get chain(): () => ChainedCommands {\n    return () => this.createChain()\n  }\n\n  get can(): () => CanCommands {\n    return () => this.createCan()\n  }\n\n  public createChain(startTr?: Transaction, shouldDispatch = true): ChainedCommands {\n    const { rawCommands, editor, state } = this\n    const { view } = editor\n    const callbacks: boolean[] = []\n    const hasStartTransaction = !!startTr\n    const tr = startTr || state.tr\n\n    const run = () => {\n      if (\n        !hasStartTransaction\n        && shouldDispatch\n        && !tr.getMeta('preventDispatch')\n        && !this.hasCustomState\n      ) {\n        view.dispatch(tr)\n      }\n\n      return callbacks.every(callback => callback === true)\n    }\n\n    const chain = {\n      ...Object.fromEntries(\n        Object.entries(rawCommands).map(([name, command]) => {\n          const chainedCommand = (...args: never[]) => {\n            const props = this.buildProps(tr, shouldDispatch)\n            const callback = command(...args)(props)\n\n            callbacks.push(callback)\n\n            return chain\n          }\n\n          return [name, chainedCommand]\n        }),\n      ),\n      run,\n    } as unknown as ChainedCommands\n\n    return chain\n  }\n\n  public createCan(startTr?: Transaction): CanCommands {\n    const { rawCommands, state } = this\n    const dispatch = false\n    const tr = startTr || state.tr\n    const props = this.buildProps(tr, dispatch)\n    const formattedCommands = Object.fromEntries(\n      Object.entries(rawCommands).map(([name, command]) => {\n        return [name, (...args: never[]) => command(...args)({ ...props, dispatch: undefined })]\n      }),\n    ) as unknown as SingleCommands\n\n    return {\n      ...formattedCommands,\n      chain: () => this.createChain(tr, dispatch),\n    } as CanCommands\n  }\n\n  public buildProps(tr: Transaction, shouldDispatch = true): CommandProps {\n    const { rawCommands, editor, state } = this\n    const { view } = editor\n\n    const props: CommandProps = {\n      tr,\n      editor,\n      view,\n      state: createChainableState({\n        state,\n        transaction: tr,\n      }),\n      dispatch: shouldDispatch ? () => undefined : undefined,\n      chain: () => this.createChain(tr, shouldDispatch),\n      can: () => this.createCan(tr),\n      get commands() {\n        return Object.fromEntries(\n          Object.entries(rawCommands).map(([name, command]) => {\n            return [name, (...args: never[]) => command(...args)(props)]\n          }),\n        ) as unknown as SingleCommands\n      },\n    }\n\n    return props\n  }\n}\n", "type StringKeyOf<T> = Extract<keyof T, string>\ntype CallbackType<\n  T extends Record<string, any>,\n  EventName extends StringKeyOf<T>,\n> = T[EventName] extends any[] ? T[EventName] : [T[EventName]]\ntype CallbackFunction<\n  T extends Record<string, any>,\n  EventName extends StringKeyOf<T>,\n> = (...props: CallbackType<T, EventName>) => any\n\nexport class EventEmitter<T extends Record<string, any>> {\n\n  private callbacks: { [key: string]: Array<(...args: any[])=>void> } = {}\n\n  public on<EventName extends StringKeyOf<T>>(event: EventName, fn: CallbackFunction<T, EventName>): this {\n    if (!this.callbacks[event]) {\n      this.callbacks[event] = []\n    }\n\n    this.callbacks[event].push(fn)\n\n    return this\n  }\n\n  public emit<EventName extends StringKeyOf<T>>(event: EventName, ...args: CallbackType<T, EventName>): this {\n    const callbacks = this.callbacks[event]\n\n    if (callbacks) {\n      callbacks.forEach(callback => callback.apply(this, args))\n    }\n\n    return this\n  }\n\n  public off<EventName extends StringKeyOf<T>>(event: EventName, fn?: CallbackFunction<T, EventName>): this {\n    const callbacks = this.callbacks[event]\n\n    if (callbacks) {\n      if (fn) {\n        this.callbacks[event] = callbacks.filter(callback => callback !== fn)\n      } else {\n        delete this.callbacks[event]\n      }\n    }\n\n    return this\n  }\n\n  public once<EventName extends StringKeyOf<T>>(event: EventName, fn: CallbackFunction<T, EventName>): this {\n    const onceFn = (...args: CallbackType<T, EventName>) => {\n      this.off(event, onceFn)\n      fn.apply(this, args)\n    }\n\n    return this.on(event, onceFn)\n  }\n\n  public removeAllListeners(): void {\n    this.callbacks = {}\n  }\n}\n", "import { AnyExtension, MaybeThisParameterType, RemoveThis } from '../types.js'\n\n/**\n * Returns a field from an extension\n * @param extension The Tiptap extension\n * @param field The field, for example `renderHTML` or `priority`\n * @param context The context object that should be passed as `this` into the function\n * @returns The field value\n */\nexport function getExtensionField<T = any>(\n  extension: AnyExtension,\n  field: string,\n  context?: Omit<MaybeThisParameterType<T>, 'parent'>,\n): RemoveThis<T> {\n\n  if (extension.config[field] === undefined && extension.parent) {\n    return getExtensionField(extension.parent, field, context)\n  }\n\n  if (typeof extension.config[field] === 'function') {\n    const value = extension.config[field].bind({\n      ...context,\n      parent: extension.parent\n        ? getExtensionField(extension.parent, field, context)\n        : null,\n    })\n\n    return value\n  }\n\n  return extension.config[field]\n}\n", "import { Extension } from '../Extension.js'\nimport { Mark } from '../Mark.js'\nimport { Node } from '../Node.js'\nimport { Extensions } from '../types.js'\n\nexport function splitExtensions(extensions: Extensions) {\n  const baseExtensions = extensions.filter(extension => extension.type === 'extension') as Extension[]\n  const nodeExtensions = extensions.filter(extension => extension.type === 'node') as Node[]\n  const markExtensions = extensions.filter(extension => extension.type === 'mark') as Mark[]\n\n  return {\n    baseExtensions,\n    nodeExtensions,\n    markExtensions,\n  }\n}\n", "import { MarkConfig, NodeConfig } from '../index.js'\nimport {\n  AnyConfig,\n  Attribute,\n  Attributes,\n  ExtensionAttribute,\n  Extensions,\n} from '../types.js'\nimport { getExtensionField } from './getExtensionField.js'\nimport { splitExtensions } from './splitExtensions.js'\n\n/**\n * Get a list of all extension attributes defined in `addAttribute` and `addGlobalAttribute`.\n * @param extensions List of extensions\n */\nexport function getAttributesFromExtensions(extensions: Extensions): ExtensionAttribute[] {\n  const extensionAttributes: ExtensionAttribute[] = []\n  const { nodeExtensions, markExtensions } = splitExtensions(extensions)\n  const nodeAndMarkExtensions = [...nodeExtensions, ...markExtensions]\n  const defaultAttribute: Required<Attribute> = {\n    default: null,\n    rendered: true,\n    renderHTML: null,\n    parseHTML: null,\n    keepOnSplit: true,\n    isRequired: false,\n  }\n\n  extensions.forEach(extension => {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n      extensions: nodeAndMarkExtensions,\n    }\n\n    const addGlobalAttributes = getExtensionField<AnyConfig['addGlobalAttributes']>(\n      extension,\n      'addGlobalAttributes',\n      context,\n    )\n\n    if (!addGlobalAttributes) {\n      return\n    }\n\n    const globalAttributes = addGlobalAttributes()\n\n    globalAttributes.forEach(globalAttribute => {\n      globalAttribute.types.forEach(type => {\n        Object\n          .entries(globalAttribute.attributes)\n          .forEach(([name, attribute]) => {\n            extensionAttributes.push({\n              type,\n              name,\n              attribute: {\n                ...defaultAttribute,\n                ...attribute,\n              },\n            })\n          })\n      })\n    })\n  })\n\n  nodeAndMarkExtensions.forEach(extension => {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    const addAttributes = getExtensionField<NodeConfig['addAttributes'] | MarkConfig['addAttributes']>(\n      extension,\n      'addAttributes',\n      context,\n    )\n\n    if (!addAttributes) {\n      return\n    }\n\n    // TODO: remove `as Attributes`\n    const attributes = addAttributes() as Attributes\n\n    Object\n      .entries(attributes)\n      .forEach(([name, attribute]) => {\n        const mergedAttr = {\n          ...defaultAttribute,\n          ...attribute,\n        }\n\n        if (typeof mergedAttr?.default === 'function') {\n          mergedAttr.default = mergedAttr.default()\n        }\n\n        if (mergedAttr?.isRequired && mergedAttr?.default === undefined) {\n          delete mergedAttr.default\n        }\n\n        extensionAttributes.push({\n          type: extension.name,\n          name,\n          attribute: mergedAttr,\n        })\n      })\n  })\n\n  return extensionAttributes\n}\n", "import { NodeType, Schema } from '@tiptap/pm/model'\n\nexport function getNodeType(nameOrType: string | NodeType, schema: Schema): NodeType {\n  if (typeof nameOrType === 'string') {\n    if (!schema.nodes[nameOrType]) {\n      throw Error(\n        `There is no node type named '${nameOrType}'. Maybe you forgot to add the extension?`,\n      )\n    }\n\n    return schema.nodes[nameOrType]\n  }\n\n  return nameOrType\n}\n", "export function mergeAttributes(...objects: Record<string, any>[]): Record<string, any> {\n  return objects\n    .filter(item => !!item)\n    .reduce((items, item) => {\n      const mergedAttributes = { ...items }\n\n      Object.entries(item).forEach(([key, value]) => {\n        const exists = mergedAttributes[key]\n\n        if (!exists) {\n          mergedAttributes[key] = value\n\n          return\n        }\n\n        if (key === 'class') {\n          const valueClasses: string[] = value ? String(value).split(' ') : []\n          const existingClasses: string[] = mergedAttributes[key] ? mergedAttributes[key].split(' ') : []\n\n          const insertClasses = valueClasses.filter(\n            valueClass => !existingClasses.includes(valueClass),\n          )\n\n          mergedAttributes[key] = [...existingClasses, ...insertClasses].join(' ')\n        } else if (key === 'style') {\n          const newStyles: string[] = value ? value.split(';').map((style: string) => style.trim()).filter(Boolean) : []\n          const existingStyles: string[] = mergedAttributes[key] ? mergedAttributes[key].split(';').map((style: string) => style.trim()).filter(Boolean) : []\n\n          const styleMap = new Map<string, string>()\n\n          existingStyles.forEach(style => {\n            const [property, val] = style.split(':').map(part => part.trim())\n\n            styleMap.set(property, val)\n          })\n\n          newStyles.forEach(style => {\n            const [property, val] = style.split(':').map(part => part.trim())\n\n            styleMap.set(property, val)\n          })\n\n          mergedAttributes[key] = Array.from(styleMap.entries()).map(([property, val]) => `${property}: ${val}`).join('; ')\n        } else {\n          mergedAttributes[key] = value\n        }\n      })\n\n      return mergedAttributes\n    }, {})\n}\n", "import { Mark, Node } from '@tiptap/pm/model'\n\nimport { ExtensionAttribute } from '../types.js'\nimport { mergeAttributes } from '../utilities/mergeAttributes.js'\n\nexport function getRenderedAttributes(\n  nodeOrMark: Node | Mark,\n  extensionAttributes: ExtensionAttribute[],\n): Record<string, any> {\n  return extensionAttributes\n    .filter(\n      attribute => attribute.type === nodeOrMark.type.name,\n    )\n    .filter(item => item.attribute.rendered)\n    .map(item => {\n      if (!item.attribute.renderHTML) {\n        return {\n          [item.name]: nodeOrMark.attrs[item.name],\n        }\n      }\n\n      return item.attribute.renderHTML(nodeOrMark.attrs) || {}\n    })\n    .reduce((attributes, attribute) => mergeAttributes(attributes, attribute), {})\n}\n", "// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type\nexport function isFunction(value: any): value is Function {\n  return typeof value === 'function'\n}\n", "import { MaybeReturnType } from '../types.js'\nimport { isFunction } from './isFunction.js'\n\n/**\n * Optionally calls `value` as a function.\n * Otherwise it is returned directly.\n * @param value Function or any value.\n * @param context Optional context to bind to function.\n * @param props Optional props to pass to function.\n */\nexport function callOrReturn<T>(value: T, context: any = undefined, ...props: any[]): MaybeReturnType<T> {\n  if (isFunction(value)) {\n    if (context) {\n      return value.bind(context)(...props)\n    }\n\n    return value(...props)\n  }\n\n  return value as MaybeReturnType<T>\n}\n", "export function isEmptyObject(value = {}): boolean {\n  return Object.keys(value).length === 0 && value.constructor === Object\n}\n", "export function fromString(value: any): any {\n  if (typeof value !== 'string') {\n    return value\n  }\n\n  if (value.match(/^[+-]?(?:\\d*\\.)?\\d+$/)) {\n    return Number(value)\n  }\n\n  if (value === 'true') {\n    return true\n  }\n\n  if (value === 'false') {\n    return false\n  }\n\n  return value\n}\n", "import { ParseRule } from '@tiptap/pm/model'\n\nimport { ExtensionAttribute } from '../types.js'\nimport { fromString } from '../utilities/fromString.js'\n\n/**\n * This function merges extension attributes into parserule attributes (`attrs` or `getAttrs`).\n * Cancels when `getAttrs` returned `false`.\n * @param parseRule ProseMirror ParseRule\n * @param extensionAttributes List of attributes to inject\n */\nexport function injectExtensionAttributesToParseRule(\n  parseRule: ParseRule,\n  extensionAttributes: ExtensionAttribute[],\n): ParseRule {\n  if ('style' in parseRule) {\n    return parseRule\n  }\n\n  return {\n    ...parseRule,\n    getAttrs: (node: HTMLElement) => {\n      const oldAttributes = parseRule.getAttrs ? parseRule.getAttrs(node) : parseRule.attrs\n\n      if (oldAttributes === false) {\n        return false\n      }\n\n      const newAttributes = extensionAttributes.reduce((items, item) => {\n        const value = item.attribute.parseHTML\n          ? item.attribute.parseHTML(node)\n          : fromString((node).getAttribute(item.name))\n\n        if (value === null || value === undefined) {\n          return items\n        }\n\n        return {\n          ...items,\n          [item.name]: value,\n        }\n      }, {})\n\n      return { ...oldAttributes, ...newAttributes }\n    },\n  }\n}\n", "import {\n  <PERSON><PERSON><PERSON>, <PERSON>de<PERSON><PERSON>, <PERSON>hem<PERSON>, TagParseRule,\n} from '@tiptap/pm/model'\n\nimport { Editor, MarkConfig, NodeConfig } from '../index.js'\nimport { AnyConfig, Extensions } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\nimport { isEmptyObject } from '../utilities/isEmptyObject.js'\nimport { getAttributesFromExtensions } from './getAttributesFromExtensions.js'\nimport { getExtensionField } from './getExtensionField.js'\nimport { getRenderedAttributes } from './getRenderedAttributes.js'\nimport { injectExtensionAttributesToParseRule } from './injectExtensionAttributesToParseRule.js'\nimport { splitExtensions } from './splitExtensions.js'\n\nfunction cleanUpSchemaItem<T>(data: T) {\n  return Object.fromEntries(\n    // @ts-ignore\n    Object.entries(data).filter(([key, value]) => {\n      if (key === 'attrs' && isEmptyObject(value as object | undefined)) {\n        return false\n      }\n\n      return value !== null && value !== undefined\n    }),\n  ) as T\n}\n\n/**\n * Creates a new Prosemirror schema based on the given extensions.\n * @param extensions An array of Tiptap extensions\n * @param editor The editor instance\n * @returns A Prosemirror schema\n */\nexport function getSchemaByResolvedExtensions(extensions: Extensions, editor?: Editor): Schema {\n  const allAttributes = getAttributesFromExtensions(extensions)\n  const { nodeExtensions, markExtensions } = splitExtensions(extensions)\n  const topNode = nodeExtensions.find(extension => getExtensionField(extension, 'topNode'))?.name\n\n  const nodes = Object.fromEntries(\n    nodeExtensions.map(extension => {\n      const extensionAttributes = allAttributes.filter(\n        attribute => attribute.type === extension.name,\n      )\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor,\n      }\n\n      const extraNodeFields = extensions.reduce((fields, e) => {\n        const extendNodeSchema = getExtensionField<AnyConfig['extendNodeSchema']>(\n          e,\n          'extendNodeSchema',\n          context,\n        )\n\n        return {\n          ...fields,\n          ...(extendNodeSchema ? extendNodeSchema(extension) : {}),\n        }\n      }, {})\n\n      const schema: NodeSpec = cleanUpSchemaItem({\n        ...extraNodeFields,\n        content: callOrReturn(\n          getExtensionField<NodeConfig['content']>(extension, 'content', context),\n        ),\n        marks: callOrReturn(getExtensionField<NodeConfig['marks']>(extension, 'marks', context)),\n        group: callOrReturn(getExtensionField<NodeConfig['group']>(extension, 'group', context)),\n        inline: callOrReturn(getExtensionField<NodeConfig['inline']>(extension, 'inline', context)),\n        atom: callOrReturn(getExtensionField<NodeConfig['atom']>(extension, 'atom', context)),\n        selectable: callOrReturn(\n          getExtensionField<NodeConfig['selectable']>(extension, 'selectable', context),\n        ),\n        draggable: callOrReturn(\n          getExtensionField<NodeConfig['draggable']>(extension, 'draggable', context),\n        ),\n        code: callOrReturn(getExtensionField<NodeConfig['code']>(extension, 'code', context)),\n        whitespace: callOrReturn(getExtensionField<NodeConfig['whitespace']>(extension, 'whitespace', context)),\n        linebreakReplacement: callOrReturn(getExtensionField<NodeConfig['linebreakReplacement']>(extension, 'linebreakReplacement', context)),\n        defining: callOrReturn(\n          getExtensionField<NodeConfig['defining']>(extension, 'defining', context),\n        ),\n        isolating: callOrReturn(\n          getExtensionField<NodeConfig['isolating']>(extension, 'isolating', context),\n        ),\n        attrs: Object.fromEntries(\n          extensionAttributes.map(extensionAttribute => {\n            return [extensionAttribute.name, { default: extensionAttribute?.attribute?.default }]\n          }),\n        ),\n      })\n\n      const parseHTML = callOrReturn(\n        getExtensionField<NodeConfig['parseHTML']>(extension, 'parseHTML', context),\n      )\n\n      if (parseHTML) {\n        schema.parseDOM = parseHTML.map(parseRule => injectExtensionAttributesToParseRule(parseRule, extensionAttributes)) as TagParseRule[]\n      }\n\n      const renderHTML = getExtensionField<NodeConfig['renderHTML']>(\n        extension,\n        'renderHTML',\n        context,\n      )\n\n      if (renderHTML) {\n        schema.toDOM = node => renderHTML({\n          node,\n          HTMLAttributes: getRenderedAttributes(node, extensionAttributes),\n        })\n      }\n\n      const renderText = getExtensionField<NodeConfig['renderText']>(\n        extension,\n        'renderText',\n        context,\n      )\n\n      if (renderText) {\n        schema.toText = renderText\n      }\n\n      return [extension.name, schema]\n    }),\n  )\n\n  const marks = Object.fromEntries(\n    markExtensions.map(extension => {\n      const extensionAttributes = allAttributes.filter(\n        attribute => attribute.type === extension.name,\n      )\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor,\n      }\n\n      const extraMarkFields = extensions.reduce((fields, e) => {\n        const extendMarkSchema = getExtensionField<AnyConfig['extendMarkSchema']>(\n          e,\n          'extendMarkSchema',\n          context,\n        )\n\n        return {\n          ...fields,\n          ...(extendMarkSchema ? extendMarkSchema(extension as any) : {}),\n        }\n      }, {})\n\n      const schema: MarkSpec = cleanUpSchemaItem({\n        ...extraMarkFields,\n        inclusive: callOrReturn(\n          getExtensionField<MarkConfig['inclusive']>(extension, 'inclusive', context),\n        ),\n        excludes: callOrReturn(\n          getExtensionField<MarkConfig['excludes']>(extension, 'excludes', context),\n        ),\n        group: callOrReturn(getExtensionField<MarkConfig['group']>(extension, 'group', context)),\n        spanning: callOrReturn(\n          getExtensionField<MarkConfig['spanning']>(extension, 'spanning', context),\n        ),\n        code: callOrReturn(getExtensionField<MarkConfig['code']>(extension, 'code', context)),\n        attrs: Object.fromEntries(\n          extensionAttributes.map(extensionAttribute => {\n            return [extensionAttribute.name, { default: extensionAttribute?.attribute?.default }]\n          }),\n        ),\n      })\n\n      const parseHTML = callOrReturn(\n        getExtensionField<MarkConfig['parseHTML']>(extension, 'parseHTML', context),\n      )\n\n      if (parseHTML) {\n        schema.parseDOM = parseHTML.map(parseRule => injectExtensionAttributesToParseRule(parseRule, extensionAttributes))\n      }\n\n      const renderHTML = getExtensionField<MarkConfig['renderHTML']>(\n        extension,\n        'renderHTML',\n        context,\n      )\n\n      if (renderHTML) {\n        schema.toDOM = mark => renderHTML({\n          mark,\n          HTMLAttributes: getRenderedAttributes(mark, extensionAttributes),\n        })\n      }\n\n      return [extension.name, schema]\n    }),\n  )\n\n  return new Schema({\n    topNode,\n    nodes,\n    marks,\n  })\n}\n", "import { MarkType, NodeType, Schema } from '@tiptap/pm/model'\n\n/**\n * Tries to get a node or mark type by its name.\n * @param name The name of the node or mark type\n * @param schema The Prosemiror schema to search in\n * @returns The node or mark type, or null if it doesn't exist\n */\nexport function getSchemaTypeByName(name: string, schema: Schema): NodeType | MarkType | null {\n  return schema.nodes[name] || schema.marks[name] || null\n}\n", "import { AnyExtension, EnableRules } from '../types.js'\n\nexport function isExtensionRulesEnabled(extension: AnyExtension, enabled: EnableRules): boolean {\n  if (Array.isArray(enabled)) {\n    return enabled.some(enabledExtension => {\n      const name = typeof enabledExtension === 'string'\n        ? enabledExtension\n        : enabledExtension.name\n\n      return name === extension.name\n    })\n  }\n\n  return enabled\n}\n", "import { DOMSerializer, Fragment, Schema } from '@tiptap/pm/model'\n\nexport function getHTMLFromFragment(fragment: Fragment, schema: Schema): string {\n  const documentFragment = DOMSerializer.fromSchema(schema).serializeFragment(fragment)\n\n  const temporaryDocument = document.implementation.createHTMLDocument()\n  const container = temporaryDocument.createElement('div')\n\n  container.appendChild(documentFragment)\n\n  return container.innerHTML\n}\n", "import { ResolvedPos } from '@tiptap/pm/model'\n\n/**\n * Returns the text content of a resolved prosemirror position\n * @param $from The resolved position to get the text content from\n * @param maxMatch The maximum number of characters to match\n * @returns The text content\n */\nexport const getTextContentFromNodes = ($from: ResolvedPos, maxMatch = 500) => {\n  let textBefore = ''\n\n  const sliceEndPos = $from.parentOffset\n\n  $from.parent.nodesBetween(\n    Math.max(0, sliceEndPos - maxMatch),\n    sliceEndPos,\n    (node, pos, parent, index) => {\n      const chunk = node.type.spec.toText?.({\n        node,\n        pos,\n        parent,\n        index,\n      })\n        || node.textContent\n        || '%leaf%'\n\n      textBefore += node.isAtom && !node.isText ? chunk : chunk.slice(0, Math.max(0, sliceEndPos - pos))\n    },\n  )\n\n  return textBefore\n}\n", "export function isRegExp(value: any): value is RegExp {\n  return Object.prototype.toString.call(value) === '[object RegExp]'\n}\n", "import { Fragment, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { EditorState, Plugin, TextSelection } from '@tiptap/pm/state'\n\nimport { CommandManager } from './CommandManager.js'\nimport { Editor } from './Editor.js'\nimport { createChainableState } from './helpers/createChainableState.js'\nimport { getHTMLFromFragment } from './helpers/getHTMLFromFragment.js'\nimport { getTextContentFromNodes } from './helpers/getTextContentFromNodes.js'\nimport {\n  CanCommands,\n  ChainedCommands,\n  ExtendedRegExpMatchArray,\n  Range,\n  SingleCommands,\n} from './types.js'\nimport { isRegExp } from './utilities/isRegExp.js'\n\nexport type InputRuleMatch = {\n  index: number;\n  text: string;\n  replaceWith?: string;\n  match?: RegExpMatchArray;\n  data?: Record<string, any>;\n};\n\nexport type InputRuleFinder = RegExp | ((text: string) => InputRuleMatch | null);\n\nexport class InputRule {\n  find: InputRuleFinder\n\n  handler: (props: {\n    state: EditorState;\n    range: Range;\n    match: ExtendedRegExpMatchArray;\n    commands: SingleCommands;\n    chain: () => ChainedCommands;\n    can: () => CanCommands;\n  }) => void | null\n\n  constructor(config: {\n    find: InputRuleFinder;\n    handler: (props: {\n      state: EditorState;\n      range: Range;\n      match: ExtendedRegExpMatchArray;\n      commands: SingleCommands;\n      chain: () => ChainedCommands;\n      can: () => CanCommands;\n    }) => void | null;\n  }) {\n    this.find = config.find\n    this.handler = config.handler\n  }\n}\n\nconst inputRuleMatcherHandler = (\n  text: string,\n  find: InputRuleFinder,\n): ExtendedRegExpMatchArray | null => {\n  if (isRegExp(find)) {\n    return find.exec(text)\n  }\n\n  const inputRuleMatch = find(text)\n\n  if (!inputRuleMatch) {\n    return null\n  }\n\n  const result: ExtendedRegExpMatchArray = [inputRuleMatch.text]\n\n  result.index = inputRuleMatch.index\n  result.input = text\n  result.data = inputRuleMatch.data\n\n  if (inputRuleMatch.replaceWith) {\n    if (!inputRuleMatch.text.includes(inputRuleMatch.replaceWith)) {\n      console.warn(\n        '[tiptap warn]: \"inputRuleMatch.replaceWith\" must be part of \"inputRuleMatch.text\".',\n      )\n    }\n\n    result.push(inputRuleMatch.replaceWith)\n  }\n\n  return result\n}\n\nfunction run(config: {\n  editor: Editor;\n  from: number;\n  to: number;\n  text: string;\n  rules: InputRule[];\n  plugin: Plugin;\n}): boolean {\n  const {\n    editor, from, to, text, rules, plugin,\n  } = config\n  const { view } = editor\n\n  if (view.composing) {\n    return false\n  }\n\n  const $from = view.state.doc.resolve(from)\n\n  if (\n    // check for code node\n    $from.parent.type.spec.code\n    // check for code mark\n    || !!($from.nodeBefore || $from.nodeAfter)?.marks.find(mark => mark.type.spec.code)\n  ) {\n    return false\n  }\n\n  let matched = false\n\n  const textBefore = getTextContentFromNodes($from) + text\n\n  rules.forEach(rule => {\n    if (matched) {\n      return\n    }\n\n    const match = inputRuleMatcherHandler(textBefore, rule.find)\n\n    if (!match) {\n      return\n    }\n\n    const tr = view.state.tr\n    const state = createChainableState({\n      state: view.state,\n      transaction: tr,\n    })\n    const range = {\n      from: from - (match[0].length - text.length),\n      to,\n    }\n\n    const { commands, chain, can } = new CommandManager({\n      editor,\n      state,\n    })\n\n    const handler = rule.handler({\n      state,\n      range,\n      match,\n      commands,\n      chain,\n      can,\n    })\n\n    // stop if there are no changes\n    if (handler === null || !tr.steps.length) {\n      return\n    }\n\n    // store transform as meta data\n    // so we can undo input rules within the `undoInputRules` command\n    tr.setMeta(plugin, {\n      transform: tr,\n      from,\n      to,\n      text,\n    })\n\n    view.dispatch(tr)\n    matched = true\n  })\n\n  return matched\n}\n\n/**\n * Create an input rules plugin. When enabled, it will cause text\n * input that matches any of the given rules to trigger the rule’s\n * action.\n */\nexport function inputRulesPlugin(props: { editor: Editor; rules: InputRule[] }): Plugin {\n  const { editor, rules } = props\n  const plugin = new Plugin({\n    state: {\n      init() {\n        return null\n      },\n      apply(tr, prev, state) {\n        const stored = tr.getMeta(plugin)\n\n        if (stored) {\n          return stored\n        }\n\n        // if InputRule is triggered by insertContent()\n        const simulatedInputMeta = tr.getMeta('applyInputRules') as\n          | undefined\n          | {\n              from: number;\n              text: string | ProseMirrorNode | Fragment;\n            }\n        const isSimulatedInput = !!simulatedInputMeta\n\n        if (isSimulatedInput) {\n          setTimeout(() => {\n            let { text } = simulatedInputMeta\n\n            if (typeof text === 'string') {\n              text = text as string\n            } else {\n              text = getHTMLFromFragment(Fragment.from(text), state.schema)\n            }\n\n            const { from } = simulatedInputMeta\n            const to = from + text.length\n\n            run({\n              editor,\n              from,\n              to,\n              text,\n              rules,\n              plugin,\n            })\n          })\n        }\n\n        return tr.selectionSet || tr.docChanged ? null : prev\n      },\n    },\n\n    props: {\n      handleTextInput(view, from, to, text) {\n        return run({\n          editor,\n          from,\n          to,\n          text,\n          rules,\n          plugin,\n        })\n      },\n\n      handleDOMEvents: {\n        compositionend: view => {\n          setTimeout(() => {\n            const { $cursor } = view.state.selection as TextSelection\n\n            if ($cursor) {\n              run({\n                editor,\n                from: $cursor.pos,\n                to: $cursor.pos,\n                text: '',\n                rules,\n                plugin,\n              })\n            }\n          })\n\n          return false\n        },\n      },\n\n      // add support for input rules to trigger on enter\n      // this is useful for example for code blocks\n      handleKeyDown(view, event) {\n        if (event.key !== 'Enter') {\n          return false\n        }\n\n        const { $cursor } = view.state.selection as TextSelection\n\n        if ($cursor) {\n          return run({\n            editor,\n            from: $cursor.pos,\n            to: $cursor.pos,\n            text: '\\n',\n            rules,\n            plugin,\n          })\n        }\n\n        return false\n      },\n    },\n\n    // @ts-ignore\n    isInputRules: true,\n  }) as Plugin\n\n  return plugin\n}\n", "// see: https://github.com/mesqueeb/is-what/blob/88d6e4ca92fb2baab6003c54e02eedf4e729e5ab/src/index.ts\n\nfunction getType(value: any): string {\n  return Object.prototype.toString.call(value).slice(8, -1)\n}\n\nexport function isPlainObject(value: any): value is Record<string, any> {\n  if (getType(value) !== 'Object') {\n    return false\n  }\n\n  return value.constructor === Object && Object.getPrototypeOf(value) === Object.prototype\n}\n", "import { isPlainObject } from './isPlainObject.js'\n\nexport function mergeDeep(target: Record<string, any>, source: Record<string, any>): Record<string, any> {\n  const output = { ...target }\n\n  if (isPlainObject(target) && isPlainObject(source)) {\n    Object.keys(source).forEach(key => {\n      if (isPlainObject(source[key]) && isPlainObject(target[key])) {\n        output[key] = mergeDeep(target[key], source[key])\n      } else {\n        output[key] = source[key]\n      }\n    })\n  }\n\n  return output\n}\n", "import {\n  DOMOutputSpec, Mark as <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Mark<PERSON><PERSON>,\n} from '@tiptap/pm/model'\nimport { Plugin, Transaction } from '@tiptap/pm/state'\n\nimport { Editor } from './Editor.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport { MarkConfig } from './index.js'\nimport { InputRule } from './InputRule.js'\nimport { Node } from './Node.js'\nimport { PasteRule } from './PasteRule.js'\nimport {\n  AnyConfig,\n  Attributes,\n  Extensions,\n  GlobalAttributes,\n  KeyboardShortcutCommand,\n  ParentConfig,\n  RawCommands,\n} from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { mergeDeep } from './utilities/mergeDeep.js'\n\ndeclare module '@tiptap/core' {\n  export interface MarkConfig<Options = any, Storage = any> {\n    // @ts-ignore - this is a dynamic key\n    [key: string]: any\n\n    /**\n     * The extension name - this must be unique.\n     * It will be used to identify the extension.\n     *\n     * @example 'myExtension'\n     */\n    name: string\n\n    /**\n     * The priority of your extension. The higher, the earlier it will be called\n     * and will take precedence over other extensions with a lower priority.\n     * @default 100\n     * @example 101\n     */\n    priority?: number\n\n    /**\n     * The default options for this extension.\n     * @example\n     * defaultOptions: {\n     *   myOption: 'foo',\n     *   myOtherOption: 10,\n     * }\n     */\n    defaultOptions?: Options\n\n    /**\n     * This method will add options to this extension\n     * @see https://tiptap.dev/guide/custom-extensions#settings\n     * @example\n     * addOptions() {\n     *  return {\n     *    myOption: 'foo',\n     *    myOtherOption: 10,\n     * }\n     */\n    addOptions?: (this: {\n      name: string\n      parent: Exclude<ParentConfig<MarkConfig<Options, Storage>>['addOptions'], undefined>\n    }) => Options\n\n    /**\n     * The default storage this extension can save data to.\n     * @see https://tiptap.dev/guide/custom-extensions#storage\n     * @example\n     * defaultStorage: {\n     *   prefetchedUsers: [],\n     *   loading: false,\n     * }\n     */\n    addStorage?: (this: {\n      name: string\n      options: Options\n      parent: Exclude<ParentConfig<MarkConfig<Options, Storage>>['addStorage'], undefined>\n    }) => Storage\n\n    /**\n     * This function adds globalAttributes to specific nodes.\n     * @see https://tiptap.dev/guide/custom-extensions#global-attributes\n     * @example\n     * addGlobalAttributes() {\n     *   return [\n     *     {\n             // Extend the following extensions\n     *       types: [\n     *         'heading',\n     *         'paragraph',\n     *       ],\n     *       // … with those attributes\n     *       attributes: {\n     *         textAlign: {\n     *           default: 'left',\n     *           renderHTML: attributes => ({\n     *             style: `text-align: ${attributes.textAlign}`,\n     *           }),\n     *           parseHTML: element => element.style.textAlign || 'left',\n     *         },\n     *       },\n     *     },\n     *   ]\n     * }\n     */\n    addGlobalAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      extensions: (Node | Mark)[]\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addGlobalAttributes']\n    }) => GlobalAttributes\n\n    /**\n     * This function adds commands to the editor\n     * @see https://tiptap.dev/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addCommands() {\n     *   return {\n     *     myCommand: () => ({ chain }) => chain().setMark('type', 'foo').run(),\n     *   }\n     * }\n     */\n    addCommands?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addCommands']\n    }) => Partial<RawCommands>\n\n    /**\n     * This function registers keyboard shortcuts.\n     * @see https://tiptap.dev/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addKeyboardShortcuts() {\n     *   return {\n     *     'Mod-l': () => this.editor.commands.toggleBulletList(),\n     *   }\n     * },\n     */\n    addKeyboardShortcuts?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addKeyboardShortcuts']\n    }) => {\n      [key: string]: KeyboardShortcutCommand\n    }\n\n    /**\n     * This function adds input rules to the editor.\n     * @see https://tiptap.dev/guide/custom-extensions#input-rules\n     * @example\n     * addInputRules() {\n     *   return [\n     *     markInputRule({\n     *       find: inputRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addInputRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addInputRules']\n    }) => InputRule[]\n\n    /**\n     * This function adds paste rules to the editor.\n     * @see https://tiptap.dev/guide/custom-extensions#paste-rules\n     * @example\n     * addPasteRules() {\n     *   return [\n     *     markPasteRule({\n     *       find: pasteRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addPasteRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addPasteRules']\n    }) => PasteRule[]\n\n    /**\n     * This function adds Prosemirror plugins to the editor\n     * @see https://tiptap.dev/guide/custom-extensions#prosemirror-plugins\n     * @example\n     * addProseMirrorPlugins() {\n     *   return [\n     *     customPlugin(),\n     *   ]\n     * }\n     */\n    addProseMirrorPlugins?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: MarkType\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addProseMirrorPlugins']\n    }) => Plugin[]\n\n    /**\n     * This function adds additional extensions to the editor. This is useful for\n     * building extension kits.\n     * @example\n     * addExtensions() {\n     *   return [\n     *     BulletList,\n     *     OrderedList,\n     *     ListItem\n     *   ]\n     * }\n     */\n    addExtensions?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addExtensions']\n    }) => Extensions\n\n    /**\n     * This function extends the schema of the node.\n     * @example\n     * extendNodeSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendNodeSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<MarkConfig<Options, Storage>>['extendNodeSchema']\n          },\n          extension: Node,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * This function extends the schema of the mark.\n     * @example\n     * extendMarkSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendMarkSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<MarkConfig<Options, Storage>>['extendMarkSchema']\n          },\n          extension: Mark,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * The editor is not ready yet.\n     */\n    onBeforeCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onBeforeCreate']\n        }) => void)\n      | null\n\n    /**\n     * The editor is ready.\n     */\n    onCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onCreate']\n        }) => void)\n      | null\n\n    /**\n     * The content has changed.\n     */\n    onUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The selection has changed.\n     */\n    onSelectionUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onSelectionUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The editor state has changed.\n     */\n    onTransaction?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: MarkType\n            parent: ParentConfig<MarkConfig<Options, Storage>>['onTransaction']\n          },\n          props: {\n            editor: Editor\n            transaction: Transaction\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is focused.\n     */\n    onFocus?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: MarkType\n            parent: ParentConfig<MarkConfig<Options, Storage>>['onFocus']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor isn’t focused anymore.\n     */\n    onBlur?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: MarkType\n            parent: ParentConfig<MarkConfig<Options, Storage>>['onBlur']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is destroyed.\n     */\n    onDestroy?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: MarkType\n          parent: ParentConfig<MarkConfig<Options, Storage>>['onDestroy']\n        }) => void)\n      | null\n\n    /**\n     * Keep mark after split node\n     */\n    keepOnSplit?: boolean | (() => boolean)\n\n    /**\n     * Inclusive\n     */\n    inclusive?:\n      | MarkSpec['inclusive']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['inclusive']\n          editor?: Editor\n        }) => MarkSpec['inclusive'])\n\n    /**\n     * Excludes\n     */\n    excludes?:\n      | MarkSpec['excludes']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['excludes']\n          editor?: Editor\n        }) => MarkSpec['excludes'])\n\n    /**\n     * Marks this Mark as exitable\n     */\n    exitable?: boolean | (() => boolean)\n\n    /**\n     * Group\n     */\n    group?:\n      | MarkSpec['group']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['group']\n          editor?: Editor\n        }) => MarkSpec['group'])\n\n    /**\n     * Spanning\n     */\n    spanning?:\n      | MarkSpec['spanning']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['spanning']\n          editor?: Editor\n        }) => MarkSpec['spanning'])\n\n    /**\n     * Code\n     */\n    code?:\n      | boolean\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<MarkConfig<Options, Storage>>['code']\n          editor?: Editor\n        }) => boolean)\n\n    /**\n     * Parse HTML\n     */\n    parseHTML?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<MarkConfig<Options, Storage>>['parseHTML']\n      editor?: Editor\n    }) => MarkSpec['parseDOM']\n\n    /**\n     * Render HTML\n     */\n    renderHTML?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<MarkConfig<Options, Storage>>['renderHTML']\n            editor?: Editor\n          },\n          props: {\n            mark: ProseMirrorMark\n            HTMLAttributes: Record<string, any>\n          },\n        ) => DOMOutputSpec)\n      | null\n\n    /**\n     * Attributes\n     */\n    addAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<MarkConfig<Options, Storage>>['addAttributes']\n      editor?: Editor\n    // eslint-disable-next-line @typescript-eslint/no-empty-object-type\n    }) => Attributes | {}\n  }\n}\n\n/**\n * The Mark class is used to create custom mark extensions.\n * @see https://tiptap.dev/api/extensions#create-a-new-extension\n */\nexport class Mark<Options = any, Storage = any> {\n  type = 'mark'\n\n  name = 'mark'\n\n  parent: Mark | null = null\n\n  child: Mark | null = null\n\n  options: Options\n\n  storage: Storage\n\n  config: MarkConfig = {\n    name: this.name,\n    defaultOptions: {},\n  }\n\n  constructor(config: Partial<MarkConfig<Options, Storage>> = {}) {\n    this.config = {\n      ...this.config,\n      ...config,\n    }\n\n    this.name = this.config.name\n\n    if (config.defaultOptions && Object.keys(config.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${this.name}\".`,\n      )\n    }\n\n    // TODO: remove `addOptions` fallback\n    this.options = this.config.defaultOptions\n\n    if (this.config.addOptions) {\n      this.options = callOrReturn(\n        getExtensionField<AnyConfig['addOptions']>(this, 'addOptions', {\n          name: this.name,\n        }),\n      )\n    }\n\n    this.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(this, 'addStorage', {\n        name: this.name,\n        options: this.options,\n      }),\n    ) || {}\n  }\n\n  static create<O = any, S = any>(config: Partial<MarkConfig<O, S>> = {}) {\n    return new Mark<O, S>(config)\n  }\n\n  configure(options: Partial<Options> = {}) {\n    // return a new instance so we can use the same extension\n    // with different calls of `configure`\n    const extension = this.extend<Options, Storage>({\n      ...this.config,\n      addOptions: () => {\n        return mergeDeep(this.options as Record<string, any>, options) as Options\n      },\n    })\n\n    // Always preserve the current name\n    extension.name = this.name\n    // Set the parent to be our parent\n    extension.parent = this.parent\n\n    return extension\n  }\n\n  extend<ExtendedOptions = Options, ExtendedStorage = Storage>(\n    extendedConfig: Partial<MarkConfig<ExtendedOptions, ExtendedStorage>> = {},\n  ) {\n    const extension = new Mark<ExtendedOptions, ExtendedStorage>(extendedConfig)\n\n    extension.parent = this\n\n    this.child = extension\n\n    extension.name = extendedConfig.name ? extendedConfig.name : extension.parent.name\n\n    if (extendedConfig.defaultOptions && Object.keys(extendedConfig.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${extension.name}\".`,\n      )\n    }\n\n    extension.options = callOrReturn(\n      getExtensionField<AnyConfig['addOptions']>(extension, 'addOptions', {\n        name: extension.name,\n      }),\n    )\n\n    extension.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(extension, 'addStorage', {\n        name: extension.name,\n        options: extension.options,\n      }),\n    )\n\n    return extension\n  }\n\n  static handleExit({ editor, mark }: { editor: Editor; mark: Mark }) {\n    const { tr } = editor.state\n    const currentPos = editor.state.selection.$from\n    const isAtEnd = currentPos.pos === currentPos.end()\n\n    if (isAtEnd) {\n      const currentMarks = currentPos.marks()\n      const isInMark = !!currentMarks.find(m => m?.type.name === mark.name)\n\n      if (!isInMark) {\n        return false\n      }\n\n      const removeMark = currentMarks.find(m => m?.type.name === mark.name)\n\n      if (removeMark) {\n        tr.removeStoredMark(removeMark)\n      }\n      tr.insertText(' ', currentPos.pos)\n\n      editor.view.dispatch(tr)\n\n      return true\n    }\n\n    return false\n  }\n}\n", "export function isNumber(value: any): value is number {\n  return typeof value === 'number'\n}\n", "import { Fragment, Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { EditorState, Plugin } from '@tiptap/pm/state'\n\nimport { CommandManager } from './CommandManager.js'\nimport { Editor } from './Editor.js'\nimport { createChainableState } from './helpers/createChainableState.js'\nimport { getHTMLFromFragment } from './helpers/getHTMLFromFragment.js'\nimport {\n  CanCommands,\n  ChainedCommands,\n  ExtendedRegExpMatchArray,\n  Range,\n  SingleCommands,\n} from './types.js'\nimport { isNumber } from './utilities/isNumber.js'\nimport { isRegExp } from './utilities/isRegExp.js'\n\nexport type PasteRuleMatch = {\n  index: number;\n  text: string;\n  replaceWith?: string;\n  match?: RegExpMatchArray;\n  data?: Record<string, any>;\n};\n\nexport type PasteRuleFinder =\n  | RegExp\n  | ((text: string, event?: ClipboardEvent | null) => PasteRuleMatch[] | null | undefined);\n\n/**\n * Paste rules are used to react to pasted content.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport class PasteRule {\n  find: PasteRuleFinder\n\n  handler: (props: {\n    state: EditorState;\n    range: Range;\n    match: ExtendedRegExpMatchArray;\n    commands: SingleCommands;\n    chain: () => ChainedCommands;\n    can: () => CanCommands;\n    pasteEvent: ClipboardEvent | null;\n    dropEvent: DragEvent | null;\n  }) => void | null\n\n  constructor(config: {\n    find: PasteRuleFinder;\n    handler: (props: {\n      can: () => CanCommands;\n      chain: () => ChainedCommands;\n      commands: SingleCommands;\n      dropEvent: DragEvent | null;\n      match: ExtendedRegExpMatchArray;\n      pasteEvent: ClipboardEvent | null;\n      range: Range;\n      state: EditorState;\n    }) => void | null;\n  }) {\n    this.find = config.find\n    this.handler = config.handler\n  }\n}\n\nconst pasteRuleMatcherHandler = (\n  text: string,\n  find: PasteRuleFinder,\n  event?: ClipboardEvent | null,\n): ExtendedRegExpMatchArray[] => {\n  if (isRegExp(find)) {\n    return [...text.matchAll(find)]\n  }\n\n  const matches = find(text, event)\n\n  if (!matches) {\n    return []\n  }\n\n  return matches.map(pasteRuleMatch => {\n    const result: ExtendedRegExpMatchArray = [pasteRuleMatch.text]\n\n    result.index = pasteRuleMatch.index\n    result.input = text\n    result.data = pasteRuleMatch.data\n\n    if (pasteRuleMatch.replaceWith) {\n      if (!pasteRuleMatch.text.includes(pasteRuleMatch.replaceWith)) {\n        console.warn(\n          '[tiptap warn]: \"pasteRuleMatch.replaceWith\" must be part of \"pasteRuleMatch.text\".',\n        )\n      }\n\n      result.push(pasteRuleMatch.replaceWith)\n    }\n\n    return result\n  })\n}\n\nfunction run(config: {\n  editor: Editor;\n  state: EditorState;\n  from: number;\n  to: number;\n  rule: PasteRule;\n  pasteEvent: ClipboardEvent | null;\n  dropEvent: DragEvent | null;\n}): boolean {\n  const {\n    editor, state, from, to, rule, pasteEvent, dropEvent,\n  } = config\n\n  const { commands, chain, can } = new CommandManager({\n    editor,\n    state,\n  })\n\n  const handlers: (void | null)[] = []\n\n  state.doc.nodesBetween(from, to, (node, pos) => {\n    if (!node.isTextblock || node.type.spec.code) {\n      return\n    }\n\n    const resolvedFrom = Math.max(from, pos)\n    const resolvedTo = Math.min(to, pos + node.content.size)\n    const textToMatch = node.textBetween(resolvedFrom - pos, resolvedTo - pos, undefined, '\\ufffc')\n\n    const matches = pasteRuleMatcherHandler(textToMatch, rule.find, pasteEvent)\n\n    matches.forEach(match => {\n      if (match.index === undefined) {\n        return\n      }\n\n      const start = resolvedFrom + match.index + 1\n      const end = start + match[0].length\n      const range = {\n        from: state.tr.mapping.map(start),\n        to: state.tr.mapping.map(end),\n      }\n\n      const handler = rule.handler({\n        state,\n        range,\n        match,\n        commands,\n        chain,\n        can,\n        pasteEvent,\n        dropEvent,\n      })\n\n      handlers.push(handler)\n    })\n  })\n\n  const success = handlers.every(handler => handler !== null)\n\n  return success\n}\n\n// When dragging across editors, must get another editor instance to delete selection content.\nlet tiptapDragFromOtherEditor: Editor | null = null\n\nconst createClipboardPasteEvent = (text: string) => {\n  const event = new ClipboardEvent('paste', {\n    clipboardData: new DataTransfer(),\n  })\n\n  event.clipboardData?.setData('text/html', text)\n\n  return event\n}\n\n/**\n * Create an paste rules plugin. When enabled, it will cause pasted\n * text that matches any of the given rules to trigger the rule’s\n * action.\n */\nexport function pasteRulesPlugin(props: { editor: Editor; rules: PasteRule[] }): Plugin[] {\n  const { editor, rules } = props\n  let dragSourceElement: Element | null = null\n  let isPastedFromProseMirror = false\n  let isDroppedFromProseMirror = false\n  let pasteEvent = typeof ClipboardEvent !== 'undefined' ? new ClipboardEvent('paste') : null\n  let dropEvent: DragEvent | null\n\n  try {\n    dropEvent = typeof DragEvent !== 'undefined' ? new DragEvent('drop') : null\n  } catch {\n    dropEvent = null\n  }\n\n  const processEvent = ({\n    state,\n    from,\n    to,\n    rule,\n    pasteEvt,\n  }: {\n    state: EditorState;\n    from: number;\n    to: { b: number };\n    rule: PasteRule;\n    pasteEvt: ClipboardEvent | null;\n  }) => {\n    const tr = state.tr\n    const chainableState = createChainableState({\n      state,\n      transaction: tr,\n    })\n\n    const handler = run({\n      editor,\n      state: chainableState,\n      from: Math.max(from - 1, 0),\n      to: to.b - 1,\n      rule,\n      pasteEvent: pasteEvt,\n      dropEvent,\n    })\n\n    if (!handler || !tr.steps.length) {\n      return\n    }\n\n    try {\n      dropEvent = typeof DragEvent !== 'undefined' ? new DragEvent('drop') : null\n    } catch {\n      dropEvent = null\n    }\n    pasteEvent = typeof ClipboardEvent !== 'undefined' ? new ClipboardEvent('paste') : null\n\n    return tr\n  }\n\n  const plugins = rules.map(rule => {\n    return new Plugin({\n      // we register a global drag handler to track the current drag source element\n      view(view) {\n        const handleDragstart = (event: DragEvent) => {\n          dragSourceElement = view.dom.parentElement?.contains(event.target as Element)\n            ? view.dom.parentElement\n            : null\n\n          if (dragSourceElement) {\n            tiptapDragFromOtherEditor = editor\n          }\n        }\n\n        const handleDragend = () => {\n          if (tiptapDragFromOtherEditor) {\n            tiptapDragFromOtherEditor = null\n          }\n        }\n\n        window.addEventListener('dragstart', handleDragstart)\n        window.addEventListener('dragend', handleDragend)\n\n        return {\n          destroy() {\n            window.removeEventListener('dragstart', handleDragstart)\n            window.removeEventListener('dragend', handleDragend)\n          },\n        }\n      },\n\n      props: {\n        handleDOMEvents: {\n          drop: (view, event: Event) => {\n            isDroppedFromProseMirror = dragSourceElement === view.dom.parentElement\n            dropEvent = event as DragEvent\n\n            if (!isDroppedFromProseMirror) {\n              const dragFromOtherEditor = tiptapDragFromOtherEditor\n\n              if (dragFromOtherEditor) {\n                // setTimeout to avoid the wrong content after drop, timeout arg can't be empty or 0\n                setTimeout(() => {\n                  const selection = dragFromOtherEditor.state.selection\n\n                  if (selection) {\n                    dragFromOtherEditor.commands.deleteRange({ from: selection.from, to: selection.to })\n                  }\n                }, 10)\n              }\n            }\n            return false\n          },\n\n          paste: (_view, event: Event) => {\n            const html = (event as ClipboardEvent).clipboardData?.getData('text/html')\n\n            pasteEvent = event as ClipboardEvent\n\n            isPastedFromProseMirror = !!html?.includes('data-pm-slice')\n\n            return false\n          },\n        },\n      },\n\n      appendTransaction: (transactions, oldState, state) => {\n        const transaction = transactions[0]\n        const isPaste = transaction.getMeta('uiEvent') === 'paste' && !isPastedFromProseMirror\n        const isDrop = transaction.getMeta('uiEvent') === 'drop' && !isDroppedFromProseMirror\n\n        // if PasteRule is triggered by insertContent()\n        const simulatedPasteMeta = transaction.getMeta('applyPasteRules') as\n          | undefined\n          | { from: number; text: string | ProseMirrorNode | Fragment }\n        const isSimulatedPaste = !!simulatedPasteMeta\n\n        if (!isPaste && !isDrop && !isSimulatedPaste) {\n          return\n        }\n\n        // Handle simulated paste\n        if (isSimulatedPaste) {\n          let { text } = simulatedPasteMeta\n\n          if (typeof text === 'string') {\n            text = text as string\n          } else {\n            text = getHTMLFromFragment(Fragment.from(text), state.schema)\n          }\n\n          const { from } = simulatedPasteMeta\n          const to = from + text.length\n\n          const pasteEvt = createClipboardPasteEvent(text)\n\n          return processEvent({\n            rule,\n            state,\n            from,\n            to: { b: to },\n            pasteEvt,\n          })\n        }\n\n        // handle actual paste/drop\n        const from = oldState.doc.content.findDiffStart(state.doc.content)\n        const to = oldState.doc.content.findDiffEnd(state.doc.content)\n\n        // stop if there is no changed range\n        if (!isNumber(from) || !to || from === to.b) {\n          return\n        }\n\n        return processEvent({\n          rule,\n          state,\n          from,\n          to,\n          pasteEvt: pasteEvent,\n        })\n      },\n    })\n  })\n\n  return plugins\n}\n", "export function findDuplicates(items: any[]): any[] {\n  const filtered = items.filter((el, index) => items.indexOf(el) !== index)\n\n  return Array.from(new Set(filtered))\n}\n", "import { keymap } from '@tiptap/pm/keymap'\nimport { Schema } from '@tiptap/pm/model'\nimport { Plugin } from '@tiptap/pm/state'\nimport { NodeViewConstructor } from '@tiptap/pm/view'\n\nimport type { Editor } from './Editor.js'\nimport { getAttributesFromExtensions } from './helpers/getAttributesFromExtensions.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport { getNodeType } from './helpers/getNodeType.js'\nimport { getRenderedAttributes } from './helpers/getRenderedAttributes.js'\nimport { getSchemaByResolvedExtensions } from './helpers/getSchemaByResolvedExtensions.js'\nimport { getSchemaTypeByName } from './helpers/getSchemaTypeByName.js'\nimport { isExtensionRulesEnabled } from './helpers/isExtensionRulesEnabled.js'\nimport { splitExtensions } from './helpers/splitExtensions.js'\nimport type { NodeConfig } from './index.js'\nimport { InputRule, inputRulesPlugin } from './InputRule.js'\nimport { Mark } from './Mark.js'\nimport { PasteRule, pasteRulesPlugin } from './PasteRule.js'\nimport { AnyConfig, Extensions, RawCommands } from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { findDuplicates } from './utilities/findDuplicates.js'\n\nexport class ExtensionManager {\n  editor: Editor\n\n  schema: Schema\n\n  extensions: Extensions\n\n  splittableMarks: string[] = []\n\n  constructor(extensions: Extensions, editor: Editor) {\n    this.editor = editor\n    this.extensions = ExtensionManager.resolve(extensions)\n    this.schema = getSchemaByResolvedExtensions(this.extensions, editor)\n    this.setupExtensions()\n  }\n\n  /**\n   * Returns a flattened and sorted extension list while\n   * also checking for duplicated extensions and warns the user.\n   * @param extensions An array of Tiptap extensions\n   * @returns An flattened and sorted array of Tiptap extensions\n   */\n  static resolve(extensions: Extensions): Extensions {\n    const resolvedExtensions = ExtensionManager.sort(ExtensionManager.flatten(extensions))\n    const duplicatedNames = findDuplicates(resolvedExtensions.map(extension => extension.name))\n\n    if (duplicatedNames.length) {\n      console.warn(\n        `[tiptap warn]: Duplicate extension names found: [${duplicatedNames\n          .map(item => `'${item}'`)\n          .join(', ')}]. This can lead to issues.`,\n      )\n    }\n\n    return resolvedExtensions\n  }\n\n  /**\n   * Create a flattened array of extensions by traversing the `addExtensions` field.\n   * @param extensions An array of Tiptap extensions\n   * @returns A flattened array of Tiptap extensions\n   */\n  static flatten(extensions: Extensions): Extensions {\n    return (\n      extensions\n        .map(extension => {\n          const context = {\n            name: extension.name,\n            options: extension.options,\n            storage: extension.storage,\n          }\n\n          const addExtensions = getExtensionField<AnyConfig['addExtensions']>(\n            extension,\n            'addExtensions',\n            context,\n          )\n\n          if (addExtensions) {\n            return [extension, ...this.flatten(addExtensions())]\n          }\n\n          return extension\n        })\n        // `Infinity` will break TypeScript so we set a number that is probably high enough\n        .flat(10)\n    )\n  }\n\n  /**\n   * Sort extensions by priority.\n   * @param extensions An array of Tiptap extensions\n   * @returns A sorted array of Tiptap extensions by priority\n   */\n  static sort(extensions: Extensions): Extensions {\n    const defaultPriority = 100\n\n    return extensions.sort((a, b) => {\n      const priorityA = getExtensionField<AnyConfig['priority']>(a, 'priority') || defaultPriority\n      const priorityB = getExtensionField<AnyConfig['priority']>(b, 'priority') || defaultPriority\n\n      if (priorityA > priorityB) {\n        return -1\n      }\n\n      if (priorityA < priorityB) {\n        return 1\n      }\n\n      return 0\n    })\n  }\n\n  /**\n   * Get all commands from the extensions.\n   * @returns An object with all commands where the key is the command name and the value is the command function\n   */\n  get commands(): RawCommands {\n    return this.extensions.reduce((commands, extension) => {\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor: this.editor,\n        type: getSchemaTypeByName(extension.name, this.schema),\n      }\n\n      const addCommands = getExtensionField<AnyConfig['addCommands']>(\n        extension,\n        'addCommands',\n        context,\n      )\n\n      if (!addCommands) {\n        return commands\n      }\n\n      return {\n        ...commands,\n        ...addCommands(),\n      }\n    }, {} as RawCommands)\n  }\n\n  /**\n   * Get all registered Prosemirror plugins from the extensions.\n   * @returns An array of Prosemirror plugins\n   */\n  get plugins(): Plugin[] {\n    const { editor } = this\n\n    // With ProseMirror, first plugins within an array are executed first.\n    // In Tiptap, we provide the ability to override plugins,\n    // so it feels more natural to run plugins at the end of an array first.\n    // That’s why we have to reverse the `extensions` array and sort again\n    // based on the `priority` option.\n    const extensions = ExtensionManager.sort([...this.extensions].reverse())\n\n    const inputRules: InputRule[] = []\n    const pasteRules: PasteRule[] = []\n\n    const allPlugins = extensions\n      .map(extension => {\n        const context = {\n          name: extension.name,\n          options: extension.options,\n          storage: extension.storage,\n          editor,\n          type: getSchemaTypeByName(extension.name, this.schema),\n        }\n\n        const plugins: Plugin[] = []\n\n        const addKeyboardShortcuts = getExtensionField<AnyConfig['addKeyboardShortcuts']>(\n          extension,\n          'addKeyboardShortcuts',\n          context,\n        )\n\n        let defaultBindings: Record<string, () => boolean> = {}\n\n        // bind exit handling\n        if (extension.type === 'mark' && getExtensionField<AnyConfig['exitable']>(extension, 'exitable', context)) {\n          defaultBindings.ArrowRight = () => Mark.handleExit({ editor, mark: extension as Mark })\n        }\n\n        if (addKeyboardShortcuts) {\n          const bindings = Object.fromEntries(\n            Object.entries(addKeyboardShortcuts()).map(([shortcut, method]) => {\n              return [shortcut, () => method({ editor })]\n            }),\n          )\n\n          defaultBindings = { ...defaultBindings, ...bindings }\n        }\n\n        const keyMapPlugin = keymap(defaultBindings)\n\n        plugins.push(keyMapPlugin)\n\n        const addInputRules = getExtensionField<AnyConfig['addInputRules']>(\n          extension,\n          'addInputRules',\n          context,\n        )\n\n        if (isExtensionRulesEnabled(extension, editor.options.enableInputRules) && addInputRules) {\n          inputRules.push(...addInputRules())\n        }\n\n        const addPasteRules = getExtensionField<AnyConfig['addPasteRules']>(\n          extension,\n          'addPasteRules',\n          context,\n        )\n\n        if (isExtensionRulesEnabled(extension, editor.options.enablePasteRules) && addPasteRules) {\n          pasteRules.push(...addPasteRules())\n        }\n\n        const addProseMirrorPlugins = getExtensionField<AnyConfig['addProseMirrorPlugins']>(\n          extension,\n          'addProseMirrorPlugins',\n          context,\n        )\n\n        if (addProseMirrorPlugins) {\n          const proseMirrorPlugins = addProseMirrorPlugins()\n\n          plugins.push(...proseMirrorPlugins)\n        }\n\n        return plugins\n      })\n      .flat()\n\n    return [\n      inputRulesPlugin({\n        editor,\n        rules: inputRules,\n      }),\n      ...pasteRulesPlugin({\n        editor,\n        rules: pasteRules,\n      }),\n      ...allPlugins,\n    ]\n  }\n\n  /**\n   * Get all attributes from the extensions.\n   * @returns An array of attributes\n   */\n  get attributes() {\n    return getAttributesFromExtensions(this.extensions)\n  }\n\n  /**\n   * Get all node views from the extensions.\n   * @returns An object with all node views where the key is the node name and the value is the node view function\n   */\n  get nodeViews(): Record<string, NodeViewConstructor> {\n    const { editor } = this\n    const { nodeExtensions } = splitExtensions(this.extensions)\n\n    return Object.fromEntries(\n      nodeExtensions\n        .filter(extension => !!getExtensionField(extension, 'addNodeView'))\n        .map(extension => {\n          const extensionAttributes = this.attributes.filter(\n            attribute => attribute.type === extension.name,\n          )\n          const context = {\n            name: extension.name,\n            options: extension.options,\n            storage: extension.storage,\n            editor,\n            type: getNodeType(extension.name, this.schema),\n          }\n          const addNodeView = getExtensionField<NodeConfig['addNodeView']>(\n            extension,\n            'addNodeView',\n            context,\n          )\n\n          if (!addNodeView) {\n            return []\n          }\n\n          const nodeview: NodeViewConstructor = (\n            node,\n            view,\n            getPos,\n            decorations,\n            innerDecorations,\n          ) => {\n            const HTMLAttributes = getRenderedAttributes(node, extensionAttributes)\n\n            return addNodeView()({\n              // pass-through\n              node,\n              view,\n              getPos: getPos as () => number,\n              decorations,\n              innerDecorations,\n              // tiptap-specific\n              editor,\n              extension,\n              HTMLAttributes,\n            })\n          }\n\n          return [extension.name, nodeview]\n        }),\n    )\n  }\n\n  /**\n   * Go through all extensions, create extension storages & setup marks\n   * & bind editor event listener.\n   */\n  private setupExtensions() {\n    this.extensions.forEach(extension => {\n      // store extension storage in editor\n      this.editor.extensionStorage[extension.name] = extension.storage\n\n      const context = {\n        name: extension.name,\n        options: extension.options,\n        storage: extension.storage,\n        editor: this.editor,\n        type: getSchemaTypeByName(extension.name, this.schema),\n      }\n\n      if (extension.type === 'mark') {\n        const keepOnSplit = callOrReturn(getExtensionField(extension, 'keepOnSplit', context)) ?? true\n\n        if (keepOnSplit) {\n          this.splittableMarks.push(extension.name)\n        }\n      }\n\n      const onBeforeCreate = getExtensionField<AnyConfig['onBeforeCreate']>(\n        extension,\n        'onBeforeCreate',\n        context,\n      )\n      const onCreate = getExtensionField<AnyConfig['onCreate']>(extension, 'onCreate', context)\n      const onUpdate = getExtensionField<AnyConfig['onUpdate']>(extension, 'onUpdate', context)\n      const onSelectionUpdate = getExtensionField<AnyConfig['onSelectionUpdate']>(\n        extension,\n        'onSelectionUpdate',\n        context,\n      )\n      const onTransaction = getExtensionField<AnyConfig['onTransaction']>(\n        extension,\n        'onTransaction',\n        context,\n      )\n      const onFocus = getExtensionField<AnyConfig['onFocus']>(extension, 'onFocus', context)\n      const onBlur = getExtensionField<AnyConfig['onBlur']>(extension, 'onBlur', context)\n      const onDestroy = getExtensionField<AnyConfig['onDestroy']>(extension, 'onDestroy', context)\n\n      if (onBeforeCreate) {\n        this.editor.on('beforeCreate', onBeforeCreate)\n      }\n\n      if (onCreate) {\n        this.editor.on('create', onCreate)\n      }\n\n      if (onUpdate) {\n        this.editor.on('update', onUpdate)\n      }\n\n      if (onSelectionUpdate) {\n        this.editor.on('selectionUpdate', onSelectionUpdate)\n      }\n\n      if (onTransaction) {\n        this.editor.on('transaction', onTransaction)\n      }\n\n      if (onFocus) {\n        this.editor.on('focus', onFocus)\n      }\n\n      if (onBlur) {\n        this.editor.on('blur', onBlur)\n      }\n\n      if (onDestroy) {\n        this.editor.on('destroy', onDestroy)\n      }\n    })\n  }\n}\n", "import { Plugin, Transaction } from '@tiptap/pm/state'\n\nimport { Editor } from './Editor.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport { ExtensionConfig } from './index.js'\nimport { InputRule } from './InputRule.js'\nimport { Mark } from './Mark.js'\nimport { Node } from './Node.js'\nimport { PasteRule } from './PasteRule.js'\nimport {\n  AnyConfig,\n  Extensions,\n  GlobalAttributes,\n  KeyboardShortcutCommand,\n  ParentConfig,\n  RawCommands,\n} from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { mergeDeep } from './utilities/mergeDeep.js'\n\ndeclare module '@tiptap/core' {\n  interface ExtensionConfig<Options = any, Storage = any> {\n    // @ts-ignore - this is a dynamic key\n    [key: string]: any\n\n    /**\n     * The extension name - this must be unique.\n     * It will be used to identify the extension.\n     *\n     * @example 'myExtension'\n     */\n    name: string\n\n    /**\n     * The priority of your extension. The higher, the earlier it will be called\n     * and will take precedence over other extensions with a lower priority.\n     * @default 100\n     * @example 101\n     */\n    priority?: number\n\n    /**\n     * The default options for this extension.\n     * @example\n     * defaultOptions: {\n     *   myOption: 'foo',\n     *   myOtherOption: 10,\n     * }\n     */\n    defaultOptions?: Options\n\n    /**\n     * This method will add options to this extension\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#settings\n     * @example\n     * addOptions() {\n     *  return {\n     *    myOption: 'foo',\n     *    myOtherOption: 10,\n     * }\n     */\n    addOptions?: (this: {\n      name: string\n      parent: Exclude<ParentConfig<ExtensionConfig<Options, Storage>>['addOptions'], undefined>\n    }) => Options\n\n    /**\n     * The default storage this extension can save data to.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#storage\n     * @example\n     * defaultStorage: {\n     *   prefetchedUsers: [],\n     *   loading: false,\n     * }\n     */\n    addStorage?: (this: {\n      name: string\n      options: Options\n      parent: Exclude<ParentConfig<ExtensionConfig<Options, Storage>>['addStorage'], undefined>\n    }) => Storage\n\n    /**\n     * This function adds globalAttributes to specific nodes.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#global-attributes\n     * @example\n     * addGlobalAttributes() {\n     *   return [\n     *     {\n             // Extend the following extensions\n     *       types: [\n     *         'heading',\n     *         'paragraph',\n     *       ],\n     *       // … with those attributes\n     *       attributes: {\n     *         textAlign: {\n     *           default: 'left',\n     *           renderHTML: attributes => ({\n     *             style: `text-align: ${attributes.textAlign}`,\n     *           }),\n     *           parseHTML: element => element.style.textAlign || 'left',\n     *         },\n     *       },\n     *     },\n     *   ]\n     * }\n     */\n    addGlobalAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      extensions: (Node | Mark)[]\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addGlobalAttributes']\n    }) => GlobalAttributes\n\n    /**\n     * This function adds commands to the editor\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#commands\n     * @example\n     * addCommands() {\n     *   return {\n     *     myCommand: () => ({ chain }) => chain().setMark('type', 'foo').run(),\n     *   }\n     * }\n     */\n    addCommands?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addCommands']\n    }) => Partial<RawCommands>\n\n    /**\n     * This function registers keyboard shortcuts.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addKeyboardShortcuts() {\n     *   return {\n     *     'Mod-l': () => this.editor.commands.toggleBulletList(),\n     *   }\n     * },\n     */\n    addKeyboardShortcuts?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addKeyboardShortcuts']\n    }) => {\n      [key: string]: KeyboardShortcutCommand\n    }\n\n    /**\n     * This function adds input rules to the editor.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#input-rules\n     * @example\n     * addInputRules() {\n     *   return [\n     *     markInputRule({\n     *       find: inputRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addInputRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addInputRules']\n    }) => InputRule[]\n\n    /**\n     * This function adds paste rules to the editor.\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#paste-rules\n     * @example\n     * addPasteRules() {\n     *   return [\n     *     markPasteRule({\n     *       find: pasteRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addPasteRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addPasteRules']\n    }) => PasteRule[]\n\n    /**\n     * This function adds Prosemirror plugins to the editor\n     * @see https://tiptap.dev/docs/editor/guide/custom-extensions#prosemirror-plugins\n     * @example\n     * addProseMirrorPlugins() {\n     *   return [\n     *     customPlugin(),\n     *   ]\n     * }\n     */\n    addProseMirrorPlugins?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addProseMirrorPlugins']\n    }) => Plugin[]\n\n    /**\n     * This function adds additional extensions to the editor. This is useful for\n     * building extension kits.\n     * @example\n     * addExtensions() {\n     *   return [\n     *     BulletList,\n     *     OrderedList,\n     *     ListItem\n     *   ]\n     * }\n     */\n    addExtensions?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<ExtensionConfig<Options, Storage>>['addExtensions']\n    }) => Extensions\n\n    /**\n     * This function extends the schema of the node.\n     * @example\n     * extendNodeSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendNodeSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['extendNodeSchema']\n          },\n          extension: Node,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * This function extends the schema of the mark.\n     * @example\n     * extendMarkSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendMarkSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['extendMarkSchema']\n          },\n          extension: Mark,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * The editor is not ready yet.\n     */\n    onBeforeCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onBeforeCreate']\n        }) => void)\n      | null\n\n    /**\n     * The editor is ready.\n     */\n    onCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onCreate']\n        }) => void)\n      | null\n\n    /**\n     * The content has changed.\n     */\n    onUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The selection has changed.\n     */\n    onSelectionUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onSelectionUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The editor state has changed.\n     */\n    onTransaction?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['onTransaction']\n          },\n          props: {\n            editor: Editor\n            transaction: Transaction\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is focused.\n     */\n    onFocus?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['onFocus']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor isn’t focused anymore.\n     */\n    onBlur?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            parent: ParentConfig<ExtensionConfig<Options, Storage>>['onBlur']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is destroyed.\n     */\n    onDestroy?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          parent: ParentConfig<ExtensionConfig<Options, Storage>>['onDestroy']\n        }) => void)\n      | null\n  }\n}\n\n/**\n * The Extension class is the base class for all extensions.\n * @see https://tiptap.dev/api/extensions#create-a-new-extension\n */\nexport class Extension<Options = any, Storage = any> {\n  type = 'extension'\n\n  name = 'extension'\n\n  parent: Extension | null = null\n\n  child: Extension | null = null\n\n  options: Options\n\n  storage: Storage\n\n  config: ExtensionConfig = {\n    name: this.name,\n    defaultOptions: {},\n  }\n\n  constructor(config: Partial<ExtensionConfig<Options, Storage>> = {}) {\n    this.config = {\n      ...this.config,\n      ...config,\n    }\n\n    this.name = this.config.name\n\n    if (config.defaultOptions && Object.keys(config.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${this.name}\".`,\n      )\n    }\n\n    // TODO: remove `addOptions` fallback\n    this.options = this.config.defaultOptions\n\n    if (this.config.addOptions) {\n      this.options = callOrReturn(\n        getExtensionField<AnyConfig['addOptions']>(this, 'addOptions', {\n          name: this.name,\n        }),\n      )\n    }\n\n    this.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(this, 'addStorage', {\n        name: this.name,\n        options: this.options,\n      }),\n    ) || {}\n  }\n\n  static create<O = any, S = any>(config: Partial<ExtensionConfig<O, S>> = {}) {\n    return new Extension<O, S>(config)\n  }\n\n  configure(options: Partial<Options> = {}) {\n    // return a new instance so we can use the same extension\n    // with different calls of `configure`\n    const extension = this.extend<Options, Storage>({\n      ...this.config,\n      addOptions: () => {\n        return mergeDeep(this.options as Record<string, any>, options) as Options\n      },\n    })\n\n    // Always preserve the current name\n    extension.name = this.name\n    // Set the parent to be our parent\n    extension.parent = this.parent\n\n    return extension\n  }\n\n  extend<ExtendedOptions = Options, ExtendedStorage = Storage>(\n    extendedConfig: Partial<ExtensionConfig<ExtendedOptions, ExtendedStorage>> = {},\n  ) {\n    const extension = new Extension<ExtendedOptions, ExtendedStorage>({ ...this.config, ...extendedConfig })\n\n    extension.parent = this\n\n    this.child = extension\n\n    extension.name = extendedConfig.name ? extendedConfig.name : extension.parent.name\n\n    if (extendedConfig.defaultOptions && Object.keys(extendedConfig.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${extension.name}\".`,\n      )\n    }\n\n    extension.options = callOrReturn(\n      getExtensionField<AnyConfig['addOptions']>(extension, 'addOptions', {\n        name: extension.name,\n      }),\n    )\n\n    extension.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(extension, 'addStorage', {\n        name: extension.name,\n        options: extension.options,\n      }),\n    )\n\n    return extension\n  }\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { Range, TextSerializer } from '../types.js'\n\n/**\n * Gets the text between two positions in a Prosemirror node\n * and serializes it using the given text serializers and block separator (see getText)\n * @param startNode The Prosemirror node to start from\n * @param range The range of the text to get\n * @param options Options for the text serializer & block separator\n * @returns The text between the two positions\n */\nexport function getTextBetween(\n  startNode: ProseMirrorNode,\n  range: Range,\n  options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  },\n): string {\n  const { from, to } = range\n  const { blockSeparator = '\\n\\n', textSerializers = {} } = options || {}\n  let text = ''\n\n  startNode.nodesBetween(from, to, (node, pos, parent, index) => {\n    if (node.isBlock && pos > from) {\n      text += blockSeparator\n    }\n\n    const textSerializer = textSerializers?.[node.type.name]\n\n    if (textSerializer) {\n      if (parent) {\n        text += textSerializer({\n          node,\n          pos,\n          parent,\n          index,\n          range,\n        })\n      }\n      // do not descend into child nodes when there exists a serializer\n      return false\n    }\n\n    if (node.isText) {\n      text += node?.text?.slice(Math.max(from, pos) - pos, to - pos) // eslint-disable-line\n    }\n  })\n\n  return text\n}\n", "import { Schema } from '@tiptap/pm/model'\n\nimport { TextSerializer } from '../types.js'\n\n/**\n * Find text serializers `toText` in a Prosemirror schema\n * @param schema The Prosemirror schema to search in\n * @returns A record of text serializers by node name\n */\nexport function getTextSerializersFromSchema(schema: Schema): Record<string, TextSerializer> {\n  return Object.fromEntries(\n    Object.entries(schema.nodes)\n      .filter(([, node]) => node.spec.toText)\n      .map(([name, node]) => [name, node.spec.toText]),\n  )\n}\n", "import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\nimport { getTextBetween } from '../helpers/getTextBetween.js'\nimport { getTextSerializersFromSchema } from '../helpers/getTextSerializersFromSchema.js'\n\nexport type ClipboardTextSerializerOptions = {\n  blockSeparator?: string,\n}\n\nexport const ClipboardTextSerializer = Extension.create<ClipboardTextSerializerOptions>({\n  name: 'clipboardTextSerializer',\n\n  addOptions() {\n    return {\n      blockSeparator: undefined,\n    }\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('clipboardTextSerializer'),\n        props: {\n          clipboardTextSerializer: () => {\n            const { editor } = this\n            const { state, schema } = editor\n            const { doc, selection } = state\n            const { ranges } = selection\n            const from = Math.min(...ranges.map(range => range.$from.pos))\n            const to = Math.max(...ranges.map(range => range.$to.pos))\n            const textSerializers = getTextSerializersFromSchema(schema)\n            const range = { from, to }\n\n            return getTextBetween(doc, range, {\n              ...(this.options.blockSeparator !== undefined\n                ? { blockSeparator: this.options.blockSeparator }\n                : {}),\n              textSerializers,\n            })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    blur: {\n      /**\n       * Removes focus from the editor.\n       * @example editor.commands.blur()\n       */\n      blur: () => ReturnType,\n    }\n  }\n}\n\nexport const blur: RawCommands['blur'] = () => ({ editor, view }) => {\n  requestAnimationFrame(() => {\n    if (!editor.isDestroyed) {\n      (view.dom as HTMLElement).blur()\n\n      // Browsers should remove the caret on blur but safari does not.\n      // See: https://github.com/ueberdosis/tiptap/issues/2405\n      window?.getSelection()?.removeAllRanges()\n    }\n  })\n\n  return true\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    clearContent: {\n      /**\n       * Clear the whole document.\n       * @param emitUpdate Whether to emit an update event.\n       * @example editor.commands.clearContent()\n       */\n      clearContent: (emitUpdate?: boolean) => ReturnType,\n    }\n  }\n}\n\nexport const clearContent: RawCommands['clearContent'] = (emitUpdate = false) => ({ commands }) => {\n  return commands.setContent('', emitUpdate)\n}\n", "import { liftTarget } from '@tiptap/pm/transform'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    clearNodes: {\n      /**\n       * Normalize nodes to a simple paragraph.\n       * @example editor.commands.clearNodes()\n       */\n      clearNodes: () => ReturnType,\n    }\n  }\n}\n\nexport const clearNodes: RawCommands['clearNodes'] = () => ({ state, tr, dispatch }) => {\n  const { selection } = tr\n  const { ranges } = selection\n\n  if (!dispatch) {\n    return true\n  }\n\n  ranges.forEach(({ $from, $to }) => {\n    state.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n      if (node.type.isText) {\n        return\n      }\n\n      const { doc, mapping } = tr\n      const $mappedFrom = doc.resolve(mapping.map(pos))\n      const $mappedTo = doc.resolve(mapping.map(pos + node.nodeSize))\n      const nodeRange = $mappedFrom.blockRange($mappedTo)\n\n      if (!nodeRange) {\n        return\n      }\n\n      const targetLiftDepth = liftTarget(nodeRange)\n\n      if (node.type.isTextblock) {\n        const { defaultType } = $mappedFrom.parent.contentMatchAt($mappedFrom.index())\n\n        tr.setNodeMarkup(nodeRange.start, defaultType)\n      }\n\n      if (targetLiftDepth || targetLiftDepth === 0) {\n        tr.lift(nodeRange, targetLiftDepth)\n      }\n    })\n  })\n\n  return true\n}\n", "import { Command, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    command: {\n      /**\n       * Define a command inline.\n       * @param fn The command function.\n       * @example\n       * editor.commands.command(({ tr, state }) => {\n       *   ...\n       *   return true\n       * })\n       */\n      command: (fn: (props: Parameters<Command>[0]) => boolean) => ReturnType,\n    }\n  }\n}\n\nexport const command: RawCommands['command'] = fn => props => {\n  return fn(props)\n}\n", "import { createParagraphNear as originalCreateParagraph<PERSON>ear } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    createParagraphNear: {\n      /**\n       * Create a paragraph nearby.\n       * @example editor.commands.createParagraphNear()\n       */\n      createParagraphNear: () => ReturnType\n    }\n  }\n}\n\nexport const createParagraphNear: RawCommands['createParagraphNear'] = () => ({ state, dispatch }) => {\n  return originalCreateParagraphNear(state, dispatch)\n}\n", "import { TextSelection } from '@tiptap/pm/state'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    cut: {\n      /**\n       * Cuts content from a range and inserts it at a given position.\n       * @param range The range to cut.\n       * @param range.from The start position of the range.\n       * @param range.to The end position of the range.\n       * @param targetPos The position to insert the content at.\n       * @example editor.commands.cut({ from: 1, to: 3 }, 5)\n       */\n      cut: ({ from, to }: { from: number, to: number }, targetPos: number) => ReturnType,\n    }\n  }\n}\n\nexport const cut: RawCommands['cut'] = (originRange, targetPos) => ({ editor, tr }) => {\n  const { state } = editor\n\n  const contentSlice = state.doc.slice(originRange.from, originRange.to)\n\n  tr.deleteRange(originRange.from, originRange.to)\n  const newPos = tr.mapping.map(targetPos)\n\n  tr.insert(newPos, contentSlice.content)\n\n  tr.setSelection(new TextSelection(tr.doc.resolve(newPos - 1)))\n\n  return true\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteCurrentNode: {\n      /**\n       * Delete the node that currently has the selection anchor.\n       * @example editor.commands.deleteCurrentNode()\n       */\n      deleteCurrentNode: () => ReturnType,\n    }\n  }\n}\n\nexport const deleteCurrentNode: RawCommands['deleteCurrentNode'] = () => ({ tr, dispatch }) => {\n  const { selection } = tr\n  const currentNode = selection.$anchor.node()\n\n  // if there is content inside the current node, break out of this command\n  if (currentNode.content.size > 0) {\n    return false\n  }\n\n  const $pos = tr.selection.$anchor\n\n  for (let depth = $pos.depth; depth > 0; depth -= 1) {\n    const node = $pos.node(depth)\n\n    if (node.type === currentNode.type) {\n      if (dispatch) {\n        const from = $pos.before(depth)\n        const to = $pos.after(depth)\n\n        tr.delete(from, to).scrollIntoView()\n      }\n\n      return true\n    }\n  }\n\n  return false\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteNode: {\n      /**\n       * Delete a node with a given type or name.\n       * @param typeOrName The type or name of the node.\n       * @example editor.commands.deleteNode('paragraph')\n       */\n      deleteNode: (typeOrName: string | NodeType) => ReturnType,\n    }\n  }\n}\n\nexport const deleteNode: RawCommands['deleteNode'] = typeOrName => ({ tr, state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const $pos = tr.selection.$anchor\n\n  for (let depth = $pos.depth; depth > 0; depth -= 1) {\n    const node = $pos.node(depth)\n\n    if (node.type === type) {\n      if (dispatch) {\n        const from = $pos.before(depth)\n        const to = $pos.after(depth)\n\n        tr.delete(from, to).scrollIntoView()\n      }\n\n      return true\n    }\n  }\n\n  return false\n}\n", "import { Range, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteRange: {\n      /**\n       * Delete a given range.\n       * @param range The range to delete.\n       * @example editor.commands.deleteRange({ from: 1, to: 3 })\n       */\n      deleteRange: (range: Range) => ReturnType,\n    }\n  }\n}\n\nexport const deleteRange: RawCommands['deleteRange'] = range => ({ tr, dispatch }) => {\n  const { from, to } = range\n\n  if (dispatch) {\n    tr.delete(from, to)\n  }\n\n  return true\n}\n", "import { deleteSelection as originalDeleteSelection } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    deleteSelection: {\n      /**\n       * Delete the selection, if there is one.\n       * @example editor.commands.deleteSelection()\n       */\n      deleteSelection: () => ReturnType\n    }\n  }\n}\n\nexport const deleteSelection: RawCommands['deleteSelection'] = () => ({ state, dispatch }) => {\n  return originalDeleteSelection(state, dispatch)\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    enter: {\n      /**\n       * Trigger enter.\n       * @example editor.commands.enter()\n       */\n      enter: () => ReturnType,\n    }\n  }\n}\n\nexport const enter: RawCommands['enter'] = () => ({ commands }) => {\n  return commands.keyboardShortcut('Enter')\n}\n", "import { exitCode as originalExitCode } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    exitCode: {\n      /**\n       * Exit from a code block.\n       * @example editor.commands.exitCode()\n       */\n      exitCode: () => ReturnType\n    }\n  }\n}\n\nexport const exitCode: RawCommands['exitCode'] = () => ({ state, dispatch }) => {\n  return originalExitCode(state, dispatch)\n}\n", "import { isRegExp } from './isRegExp.js'\n\n/**\n * Check if object1 includes object2\n * @param object1 Object\n * @param object2 Object\n */\nexport function objectIncludes(\n  object1: Record<string, any>,\n  object2: Record<string, any>,\n  options: { strict: boolean } = { strict: true },\n): boolean {\n  const keys = Object.keys(object2)\n\n  if (!keys.length) {\n    return true\n  }\n\n  return keys.every(key => {\n    if (options.strict) {\n      return object2[key] === object1[key]\n    }\n\n    if (isRegExp(object2[key])) {\n      return object2[key].test(object1[key])\n    }\n\n    return object2[key] === object1[key]\n  })\n}\n", "import { Mark as ProseMirrorMark, MarkType, ResolvedPos } from '@tiptap/pm/model'\n\nimport { Range } from '../types.js'\nimport { objectIncludes } from '../utilities/objectIncludes.js'\n\nfunction findMarkInSet(\n  marks: ProseMirrorMark[],\n  type: MarkType,\n  attributes: Record<string, any> = {},\n): ProseMirrorMark | undefined {\n  return marks.find(item => {\n    return (\n      item.type === type\n      && objectIncludes(\n        // Only check equality for the attributes that are provided\n        Object.fromEntries(Object.keys(attributes).map(k => [k, item.attrs[k]])),\n        attributes,\n      )\n    )\n  })\n}\n\nfunction isMarkInSet(\n  marks: ProseMirrorMark[],\n  type: MarkType,\n  attributes: Record<string, any> = {},\n): boolean {\n  return !!findMarkInSet(marks, type, attributes)\n}\n\n/**\n * Get the range of a mark at a resolved position.\n */\nexport function getMarkRange(\n  /**\n   * The position to get the mark range for.\n   */\n  $pos: ResolvedPos,\n  /**\n   * The mark type to get the range for.\n   */\n  type: MarkType,\n  /**\n   * The attributes to match against.\n   * If not provided, only the first mark at the position will be matched.\n   */\n  attributes?: Record<string, any>,\n): Range | void {\n  if (!$pos || !type) {\n    return\n  }\n  let start = $pos.parent.childAfter($pos.parentOffset)\n\n  // If the cursor is at the start of a text node that does not have the mark, look backward\n  if (!start.node || !start.node.marks.some(mark => mark.type === type)) {\n    start = $pos.parent.childBefore($pos.parentOffset)\n  }\n\n  // If there is no text node with the mark even backward, return undefined\n  if (!start.node || !start.node.marks.some(mark => mark.type === type)) {\n    return\n  }\n\n  // Default to only matching against the first mark's attributes\n  attributes = attributes || start.node.marks[0]?.attrs\n\n  // We now know that the cursor is either at the start, middle or end of a text node with the specified mark\n  // so we can look it up on the targeted mark\n  const mark = findMarkInSet([...start.node.marks], type, attributes)\n\n  if (!mark) {\n    return\n  }\n\n  let startIndex = start.index\n  let startPos = $pos.start() + start.offset\n  let endIndex = startIndex + 1\n  let endPos = startPos + start.node.nodeSize\n\n  while (\n    startIndex > 0\n    && isMarkInSet([...$pos.parent.child(startIndex - 1).marks], type, attributes)\n  ) {\n    startIndex -= 1\n    startPos -= $pos.parent.child(startIndex).nodeSize\n  }\n\n  while (\n    endIndex < $pos.parent.childCount\n    && isMarkInSet([...$pos.parent.child(endIndex).marks], type, attributes)\n  ) {\n    endPos += $pos.parent.child(endIndex).nodeSize\n    endIndex += 1\n  }\n\n  return {\n    from: startPos,\n    to: endPos,\n  }\n}\n", "import { MarkType, Schema } from '@tiptap/pm/model'\n\nexport function getMarkType(nameOrType: string | MarkType, schema: Schema): MarkType {\n  if (typeof nameOrType === 'string') {\n    if (!schema.marks[nameOrType]) {\n      throw Error(\n        `There is no mark type named '${nameOrType}'. Maybe you forgot to add the extension?`,\n      )\n    }\n\n    return schema.marks[nameOrType]\n  }\n\n  return nameOrType\n}\n", "import { MarkType } from '@tiptap/pm/model'\nimport { TextSelection } from '@tiptap/pm/state'\n\nimport { getMarkRange } from '../helpers/getMarkRange.js'\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    extendMarkRange: {\n      /**\n       * Extends the text selection to the current mark by type or name.\n       * @param typeOrName The type or name of the mark.\n       * @param attributes The attributes of the mark.\n       * @example editor.commands.extendMarkRange('bold')\n       * @example editor.commands.extendMarkRange('mention', { userId: \"1\" })\n       */\n      extendMarkRange: (\n        /**\n         * The type or name of the mark.\n         */\n        typeOrName: string | MarkType,\n\n        /**\n         * The attributes of the mark.\n         */\n        attributes?: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const extendMarkRange: RawCommands['extendMarkRange'] = (typeOrName, attributes = {}) => ({ tr, state, dispatch }) => {\n  const type = getMarkType(typeOrName, state.schema)\n  const { doc, selection } = tr\n  const { $from, from, to } = selection\n\n  if (dispatch) {\n    const range = getMarkRange($from, type, attributes)\n\n    if (range && range.from <= from && range.to >= to) {\n      const newSelection = TextSelection.create(doc, range.from, range.to)\n\n      tr.setSelection(newSelection)\n    }\n  }\n\n  return true\n}\n", "import { Command, CommandProps, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    first: {\n      /**\n       * Runs one command after the other and stops at the first which returns true.\n       * @param commands The commands to run.\n       * @example editor.commands.first([command1, command2])\n       */\n      first: (commands: Command[] | ((props: CommandProps) => Command[])) => ReturnType,\n    }\n  }\n}\n\nexport const first: RawCommands['first'] = commands => props => {\n  const items = typeof commands === 'function'\n    ? commands(props)\n    : commands\n\n  for (let i = 0; i < items.length; i += 1) {\n    if (items[i](props)) {\n      return true\n    }\n  }\n\n  return false\n}\n", "import { TextSelection } from '@tiptap/pm/state'\n\nexport function isTextSelection(value: unknown): value is TextSelection {\n  return value instanceof TextSelection\n}\n", "export function minMax(value = 0, min = 0, max = 0): number {\n  return Math.min(Math.max(value, min), max)\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { Selection, TextSelection } from '@tiptap/pm/state'\n\nimport { FocusPosition } from '../types.js'\nimport { minMax } from '../utilities/minMax.js'\n\nexport function resolveFocusPosition(\n  doc: ProseMirrorNode,\n  position: FocusPosition = null,\n): Selection | null {\n  if (!position) {\n    return null\n  }\n\n  const selectionAtStart = Selection.atStart(doc)\n  const selectionAtEnd = Selection.atEnd(doc)\n\n  if (position === 'start' || position === true) {\n    return selectionAtStart\n  }\n\n  if (position === 'end') {\n    return selectionAtEnd\n  }\n\n  const minPos = selectionAtStart.from\n  const maxPos = selectionAtEnd.to\n\n  if (position === 'all') {\n    return TextSelection.create(\n      doc,\n      minMax(0, minPos, maxPos),\n      minMax(doc.content.size, minPos, maxPos),\n    )\n  }\n\n  return TextSelection.create(\n    doc,\n    minMax(position, minPos, maxPos),\n    minMax(position, minPos, maxPos),\n  )\n}\n", "export function isAndroid(): boolean {\n  return navigator.platform === 'Android' || /android/i.test(navigator.userAgent)\n}\n", "export function isiOS(): boolean {\n  return [\n    'iPad Simulator',\n    'iPhone Simulator',\n    'iPod Simulator',\n    'iPad',\n    'iPhone',\n    'iPod',\n  ].includes(navigator.platform)\n  // iPad on iOS 13 detection\n  || (navigator.userAgent.includes('Mac') && 'ontouchend' in document)\n}\n", "import { isTextSelection } from '../helpers/isTextSelection.js'\nimport { resolveFocusPosition } from '../helpers/resolveFocusPosition.js'\nimport { FocusPosition, RawCommands } from '../types.js'\nimport { isAndroid } from '../utilities/isAndroid.js'\nimport { isiOS } from '../utilities/isiOS.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    focus: {\n      /**\n       * Focus the editor at the given position.\n       * @param position The position to focus at.\n       * @param options.scrollIntoView Scroll the focused position into view after focusing\n       * @example editor.commands.focus()\n       * @example editor.commands.focus(32, { scrollIntoView: false })\n       */\n      focus: (\n        /**\n         * The position to focus at.\n         */\n        position?: FocusPosition,\n\n        /**\n         * Optional options\n         * @default { scrollIntoView: true }\n         */\n        options?: {\n          scrollIntoView?: boolean,\n        },\n      ) => ReturnType,\n    }\n  }\n}\n\nexport const focus: RawCommands['focus'] = (position = null, options = {}) => ({\n  editor,\n  view,\n  tr,\n  dispatch,\n}) => {\n  options = {\n    scrollIntoView: true,\n    ...options,\n  }\n\n  const delayedFocus = () => {\n    // focus within `requestAnimationFrame` breaks focus on iOS and Android\n    // so we have to call this\n    if (isiOS() || isAndroid()) {\n      (view.dom as HTMLElement).focus()\n    }\n\n    // For React we have to focus asynchronously. Otherwise wild things happen.\n    // see: https://github.com/ueberdosis/tiptap/issues/1520\n    requestAnimationFrame(() => {\n      if (!editor.isDestroyed) {\n        view.focus()\n\n        if (options?.scrollIntoView) {\n          editor.commands.scrollIntoView()\n        }\n      }\n    })\n  }\n\n  if ((view.hasFocus() && position === null) || position === false) {\n    return true\n  }\n\n  // we don’t try to resolve a NodeSelection or CellSelection\n  if (dispatch && position === null && !isTextSelection(editor.state.selection)) {\n    delayedFocus()\n    return true\n  }\n\n  // pass through tr.doc instead of editor.state.doc\n  // since transactions could change the editors state before this command has been run\n  const selection = resolveFocusPosition(tr.doc, position) || editor.state.selection\n  const isSameSelection = editor.state.selection.eq(selection)\n\n  if (dispatch) {\n    if (!isSameSelection) {\n      tr.setSelection(selection)\n    }\n\n    // `tr.setSelection` resets the stored marks\n    // so we’ll restore them if the selection is the same as before\n    if (isSameSelection && tr.storedMarks) {\n      tr.setStoredMarks(tr.storedMarks)\n    }\n\n    delayedFocus()\n  }\n\n  return true\n}\n", "import { CommandProps, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    forEach: {\n      /**\n       * Loop through an array of items.\n       */\n      forEach: <T>(\n        items: T[],\n        fn: (\n          item: T,\n          props: CommandProps & {\n            index: number,\n          },\n        ) => boolean,\n      ) => ReturnType,\n    }\n  }\n}\n\nexport const forEach: RawCommands['forEach'] = (items, fn) => props => {\n  return items.every((item, index) => fn(item, { ...props, index }))\n}\n", "import { Fragment, Node as ProseMirrorNode, ParseOptions } from '@tiptap/pm/model'\n\nimport { Content, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    insertContent: {\n      /**\n       * Insert a node or string of HTML at the current position.\n       * @example editor.commands.insertContent('<h1>Example</h1>')\n       * @example editor.commands.insertContent('<h1>Example</h1>', { updateSelection: false })\n       */\n      insertContent: (\n        /**\n         * The ProseMirror content to insert.\n         */\n        value: Content | ProseMirrorNode | Fragment,\n\n        /**\n         * Optional options\n         */\n        options?: {\n          /**\n           * Options for parsing the content.\n           */\n          parseOptions?: ParseOptions;\n\n          /**\n           * Whether to update the selection after inserting the content.\n           */\n          updateSelection?: boolean;\n          applyInputRules?: boolean;\n          applyPasteRules?: boolean;\n        }\n      ) => ReturnType;\n    };\n  }\n}\n\nexport const insertContent: RawCommands['insertContent'] = (value, options) => ({ tr, commands }) => {\n  return commands.insertContentAt(\n    { from: tr.selection.from, to: tr.selection.to },\n    value,\n    options,\n  )\n}\n", "const removeWhitespaces = (node: HTMLElement) => {\n  const children = node.childNodes\n\n  for (let i = children.length - 1; i >= 0; i -= 1) {\n    const child = children[i]\n\n    if (child.nodeType === 3 && child.nodeValue && /^(\\n\\s\\s|\\n)$/.test(child.nodeValue)) {\n      node.removeChild(child)\n    } else if (child.nodeType === 1) {\n      removeWhitespaces(child as HTMLElement)\n    }\n  }\n\n  return node\n}\n\nexport function elementFromString(value: string): HTMLElement {\n  // add a wrapper to preserve leading and trailing whitespace\n  const wrappedValue = `<body>${value}</body>`\n\n  const html = new window.DOMParser().parseFromString(wrappedValue, 'text/html').body\n\n  return removeWhitespaces(html)\n}\n", "import {\n  <PERSON><PERSON><PERSON><PERSON><PERSON>,\n  Frag<PERSON>,\n  Node as ProseMirrorNode,\n  ParseOptions,\n  Schema,\n} from '@tiptap/pm/model'\n\nimport { Content } from '../types.js'\nimport { elementFromString } from '../utilities/elementFromString.js'\n\nexport type CreateNodeFromContentOptions = {\n  slice?: boolean\n  parseOptions?: ParseOptions\n  errorOnInvalidContent?: boolean\n}\n\n/**\n * Takes a JSON or HTML content and creates a Prosemirror node or fragment from it.\n * @param content The JSON or HTML content to create the node from\n * @param schema The Prosemirror schema to use for the node\n * @param options Options for the parser\n * @returns The created Prosemirror node or fragment\n */\nexport function createNodeFromContent(\n  content: Content | ProseMirrorNode | Fragment,\n  schema: Schema,\n  options?: CreateNodeFromContentOptions,\n): ProseMirrorNode | Fragment {\n  if (content instanceof ProseMirrorNode || content instanceof Fragment) {\n    return content\n  }\n  options = {\n    slice: true,\n    parseOptions: {},\n    ...options,\n  }\n\n  const isJSONContent = typeof content === 'object' && content !== null\n  const isTextContent = typeof content === 'string'\n\n  if (isJSONContent) {\n    try {\n      const isArrayContent = Array.isArray(content) && content.length > 0\n\n      // if the JSON Content is an array of nodes, create a fragment for each node\n      if (isArrayContent) {\n        return Fragment.fromArray(content.map(item => schema.nodeFromJSON(item)))\n      }\n\n      const node = schema.nodeFromJSON(content)\n\n      if (options.errorOnInvalidContent) {\n        node.check()\n      }\n\n      return node\n    } catch (error) {\n      if (options.errorOnInvalidContent) {\n        throw new Error('[tiptap error]: Invalid JSON content', { cause: error as Error })\n      }\n\n      console.warn('[tiptap warn]: Invalid content.', 'Passed value:', content, 'Error:', error)\n\n      return createNodeFromContent('', schema, options)\n    }\n  }\n\n  if (isTextContent) {\n\n    // Check for invalid content\n    if (options.errorOnInvalidContent) {\n      let hasInvalidContent = false\n      let invalidContent = ''\n\n      // A copy of the current schema with a catch-all node at the end\n      const contentCheckSchema = new Schema({\n        topNode: schema.spec.topNode,\n        marks: schema.spec.marks,\n        // Prosemirror's schemas are executed such that: the last to execute, matches last\n        // This means that we can add a catch-all node at the end of the schema to catch any content that we don't know how to handle\n        nodes: schema.spec.nodes.append({\n          __tiptap__private__unknown__catch__all__node: {\n            content: 'inline*',\n            group: 'block',\n            parseDOM: [\n              {\n                tag: '*',\n                getAttrs: e => {\n                  // If this is ever called, we know that the content has something that we don't know how to handle in the schema\n                  hasInvalidContent = true\n                  // Try to stringify the element for a more helpful error message\n                  invalidContent = typeof e === 'string' ? e : e.outerHTML\n                  return null\n                },\n              },\n            ],\n          },\n        }),\n      })\n\n      if (options.slice) {\n        DOMParser.fromSchema(contentCheckSchema).parseSlice(elementFromString(content), options.parseOptions)\n      } else {\n        DOMParser.fromSchema(contentCheckSchema).parse(elementFromString(content), options.parseOptions)\n      }\n\n      if (options.errorOnInvalidContent && hasInvalidContent) {\n        throw new Error('[tiptap error]: Invalid HTML content', { cause: new Error(`Invalid element found: ${invalidContent}`) })\n      }\n    }\n\n    const parser = DOMParser.fromSchema(schema)\n\n    if (options.slice) {\n      return parser.parseSlice(elementFromString(content), options.parseOptions).content\n    }\n\n    return parser.parse(elementFromString(content), options.parseOptions)\n\n  }\n\n  return createNodeFromContent('', schema, options)\n}\n", "import { Selection, Transaction } from '@tiptap/pm/state'\nimport { ReplaceAroundStep, ReplaceStep } from '@tiptap/pm/transform'\n\n// source: https://github.com/ProseMirror/prosemirror-state/blob/master/src/selection.js#L466\nexport function selectionToInsertionEnd(tr: Transaction, startLen: number, bias: number) {\n  const last = tr.steps.length - 1\n\n  if (last < startLen) {\n    return\n  }\n\n  const step = tr.steps[last]\n\n  if (!(step instanceof ReplaceStep || step instanceof ReplaceAroundStep)) {\n    return\n  }\n\n  const map = tr.mapping.maps[last]\n  let end = 0\n\n  map.forEach((_from, _to, _newFrom, newTo) => {\n    if (end === 0) {\n      end = newTo\n    }\n  })\n\n  tr.setSelection(Selection.near(tr.doc.resolve(end), bias))\n}\n", "import { Fragment, Node as ProseMirrorNode, ParseOptions } from '@tiptap/pm/model'\n\nimport { createNodeFromContent } from '../helpers/createNodeFromContent.js'\nimport { selectionToInsertionEnd } from '../helpers/selectionToInsertionEnd.js'\nimport { Content, Range, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    insertContentAt: {\n      /**\n       * Insert a node or string of HTML at a specific position.\n       * @example editor.commands.insertContentAt(0, '<h1>Example</h1>')\n       */\n      insertContentAt: (\n        /**\n         * The position to insert the content at.\n         */\n        position: number | Range,\n\n        /**\n         * The ProseMirror content to insert.\n         */\n        value: Content | ProseMirrorNode | Fragment,\n\n        /**\n         * Optional options\n         */\n        options?: {\n          /**\n           * Options for parsing the content.\n           */\n          parseOptions?: ParseOptions\n\n          /**\n           * Whether to update the selection after inserting the content.\n           */\n          updateSelection?: boolean\n\n          /**\n           * Whether to apply input rules after inserting the content.\n           */\n          applyInputRules?: boolean\n\n          /**\n           * Whether to apply paste rules after inserting the content.\n           */\n          applyPasteRules?: boolean\n\n          /**\n           * Whether to throw an error if the content is invalid.\n           */\n          errorOnInvalidContent?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nconst isFragment = (nodeOrFragment: ProseMirrorNode | Fragment): nodeOrFragment is Fragment => {\n  return !('type' in nodeOrFragment)\n}\n\nexport const insertContentAt: RawCommands['insertContentAt'] = (position, value, options) => ({ tr, dispatch, editor }) => {\n  if (dispatch) {\n    options = {\n      parseOptions: editor.options.parseOptions,\n      updateSelection: true,\n      applyInputRules: false,\n      applyPasteRules: false,\n      ...options,\n    }\n\n    let content: Fragment | ProseMirrorNode\n\n    try {\n      content = createNodeFromContent(value, editor.schema, {\n        parseOptions: {\n          preserveWhitespace: 'full',\n          ...options.parseOptions,\n        },\n        errorOnInvalidContent: options.errorOnInvalidContent ?? editor.options.enableContentCheck,\n      })\n    } catch (e) {\n      editor.emit('contentError', {\n        editor,\n        error: e as Error,\n        disableCollaboration: () => {\n          if (editor.storage.collaboration) {\n            editor.storage.collaboration.isDisabled = true\n          }\n        },\n      })\n      return false\n    }\n\n    let { from, to } = typeof position === 'number' ? { from: position, to: position } : { from: position.from, to: position.to }\n\n    let isOnlyTextContent = true\n    let isOnlyBlockContent = true\n    const nodes = isFragment(content) ? content : [content]\n\n    nodes.forEach(node => {\n      // check if added node is valid\n      node.check()\n\n      isOnlyTextContent = isOnlyTextContent ? node.isText && node.marks.length === 0 : false\n\n      isOnlyBlockContent = isOnlyBlockContent ? node.isBlock : false\n    })\n\n    // check if we can replace the wrapping node by\n    // the newly inserted content\n    // example:\n    // replace an empty paragraph by an inserted image\n    // instead of inserting the image below the paragraph\n    if (from === to && isOnlyBlockContent) {\n      const { parent } = tr.doc.resolve(from)\n      const isEmptyTextBlock = parent.isTextblock && !parent.type.spec.code && !parent.childCount\n\n      if (isEmptyTextBlock) {\n        from -= 1\n        to += 1\n      }\n    }\n\n    let newContent\n\n    // if there is only plain text we have to use `insertText`\n    // because this will keep the current marks\n    if (isOnlyTextContent) {\n      // if value is string, we can use it directly\n      // otherwise if it is an array, we have to join it\n      if (Array.isArray(value)) {\n        newContent = value.map(v => v.text || '').join('')\n      } else if (value instanceof Fragment) {\n        let text = ''\n\n        value.forEach(node => {\n          if (node.text) {\n            text += node.text\n          }\n        })\n\n        newContent = text\n      } else if (typeof value === 'object' && !!value && !!value.text) {\n        newContent = value.text\n      } else {\n        newContent = value as string\n      }\n\n      tr.insertText(newContent, from, to)\n    } else {\n      newContent = content\n\n      tr.replaceWith(from, to, newContent)\n    }\n\n    // set cursor at end of inserted content\n    if (options.updateSelection) {\n      selectionToInsertionEnd(tr, tr.steps.length - 1, -1)\n    }\n\n    if (options.applyInputRules) {\n      tr.setMeta('applyInputRules', { from, text: newContent })\n    }\n\n    if (options.applyPasteRules) {\n      tr.setMeta('applyPasteRules', { from, text: newContent })\n    }\n  }\n\n  return true\n}\n", "import {\n  joinBackward as original<PERSON>oi<PERSON><PERSON><PERSON><PERSON>,\n  joinDown as original<PERSON>oinDown,\n  joinForward as original<PERSON>oin<PERSON><PERSON><PERSON>,\n  joinUp as original<PERSON>oinUp,\n} from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinUp: {\n      /**\n       * Join the selected block or, if there is a text selection, the closest ancestor block of the selection that can be joined, with the sibling above it.\n       * @example editor.commands.joinUp()\n       */\n      joinUp: () => ReturnType\n    }\n    joinDown: {\n      /**\n       * Join the selected block, or the closest ancestor of the selection that can be joined, with the sibling after it.\n       * @example editor.commands.joinDown()\n       */\n      joinDown: () => ReturnType\n    }\n    joinBackward: {\n      /**\n       * If the selection is empty and at the start of a textblock, try to reduce the distance between that block and the one before it—if there's a block directly before it that can be joined, join them.\n       * If not, try to move the selected block closer to the next one in the document structure by lifting it out of its\n       * parent or moving it into a parent of the previous block. Will use the view for accurate (bidi-aware) start-of-textblock detection if given.\n       * @example editor.commands.joinBackward()\n       */\n      joinBackward: () => ReturnType\n    }\n    joinForward: {\n      /**\n       * If the selection is empty and the cursor is at the end of a textblock, try to reduce or remove the boundary between that block and the one after it,\n       * either by joining them or by moving the other block closer to this one in the tree structure.\n       * Will use the view for accurate start-of-textblock detection if given.\n       * @example editor.commands.joinForward()\n       */\n      joinForward: () => ReturnType\n    }\n  }\n}\n\nexport const joinUp: RawCommands['joinUp'] = () => ({ state, dispatch }) => {\n  return originalJoinUp(state, dispatch)\n}\n\nexport const joinDown: RawCommands['joinDown'] = () => ({ state, dispatch }) => {\n  return originalJoinDown(state, dispatch)\n}\n\nexport const joinBackward: RawCommands['joinBackward'] = () => ({ state, dispatch }) => {\n  return originalJoinBackward(state, dispatch)\n}\n\nexport const joinForward: RawCommands['joinForward'] = () => ({ state, dispatch }) => {\n  return originalJoinForward(state, dispatch)\n}\n", "import { joinPoint } from '@tiptap/pm/transform'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinItemBackward: {\n      /**\n       * Join two items backward.\n       * @example editor.commands.joinItemBackward()\n       */\n      joinItemBackward: () => ReturnType\n    }\n  }\n}\n\nexport const joinItemBackward: RawCommands['joinItemBackward'] = () => ({\n  state,\n  dispatch,\n  tr,\n}) => {\n  try {\n    const point = joinPoint(state.doc, state.selection.$from.pos, -1)\n\n    if (point === null || point === undefined) {\n      return false\n    }\n\n    tr.join(point, 2)\n\n    if (dispatch) {\n      dispatch(tr)\n    }\n\n    return true\n  } catch {\n    return false\n  }\n}\n", "import { joinPoint } from '@tiptap/pm/transform'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinItemForward: {\n      /**\n       * Join two items Forwards.\n       * @example editor.commands.joinItemForward()\n       */\n      joinItemForward: () => ReturnType\n    }\n  }\n}\n\nexport const joinItemForward: RawCommands['joinItemForward'] = () => ({\n  state,\n  dispatch,\n  tr,\n}) => {\n  try {\n    const point = joinPoint(state.doc, state.selection.$from.pos, +1)\n\n    if (point === null || point === undefined) {\n      return false\n    }\n\n    tr.join(point, 2)\n\n    if (dispatch) {\n      dispatch(tr)\n    }\n\n    return true\n  } catch {\n    return false\n  }\n}\n", "import { joinTextblockBackward as originalCommand } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinTextblockBackward: {\n      /**\n       * A more limited form of joinBackward that only tries to join the current textblock to the one before it, if the cursor is at the start of a textblock.\n       */\n      joinTextblockBackward: () => ReturnType\n    }\n  }\n}\n\nexport const joinTextblockBackward: RawCommands['joinTextblockBackward'] = () => ({ state, dispatch }) => {\n  return originalCommand(state, dispatch)\n}\n", "import { joinTextblockForward as originalCommand } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    joinTextblockForward: {\n      /**\n       * A more limited form of joinForward that only tries to join the current textblock to the one after it, if the cursor is at the end of a textblock.\n       */\n      joinTextblockForward: () => ReturnType\n    }\n  }\n}\n\nexport const joinTextblockForward: RawCommands['joinTextblockForward'] = () => ({ state, dispatch }) => {\n  return originalCommand(state, dispatch)\n}\n", "export function isMacOS(): boolean {\n  return typeof navigator !== 'undefined'\n    ? /Mac/.test(navigator.platform)\n    : false\n}\n", "import { RawCommands } from '../types.js'\nimport { isiOS } from '../utilities/isiOS.js'\nimport { isMacOS } from '../utilities/isMacOS.js'\n\nfunction normalizeKeyName(name: string) {\n  const parts = name.split(/-(?!$)/)\n  let result = parts[parts.length - 1]\n\n  if (result === 'Space') {\n    result = ' '\n  }\n\n  let alt\n  let ctrl\n  let shift\n  let meta\n\n  for (let i = 0; i < parts.length - 1; i += 1) {\n    const mod = parts[i]\n\n    if (/^(cmd|meta|m)$/i.test(mod)) {\n      meta = true\n    } else if (/^a(lt)?$/i.test(mod)) {\n      alt = true\n    } else if (/^(c|ctrl|control)$/i.test(mod)) {\n      ctrl = true\n    } else if (/^s(hift)?$/i.test(mod)) {\n      shift = true\n    } else if (/^mod$/i.test(mod)) {\n      if (isiOS() || isMacOS()) {\n        meta = true\n      } else {\n        ctrl = true\n      }\n    } else {\n      throw new Error(`Unrecognized modifier name: ${mod}`)\n    }\n  }\n\n  if (alt) {\n    result = `Alt-${result}`\n  }\n\n  if (ctrl) {\n    result = `Ctrl-${result}`\n  }\n\n  if (meta) {\n    result = `Meta-${result}`\n  }\n\n  if (shift) {\n    result = `Shift-${result}`\n  }\n\n  return result\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    keyboardShortcut: {\n      /**\n       * Trigger a keyboard shortcut.\n       * @param name The name of the keyboard shortcut.\n       * @example editor.commands.keyboardShortcut('Mod-b')\n       */\n      keyboardShortcut: (name: string) => ReturnType,\n    }\n  }\n}\n\nexport const keyboardShortcut: RawCommands['keyboardShortcut'] = name => ({\n  editor,\n  view,\n  tr,\n  dispatch,\n}) => {\n  const keys = normalizeKeyName(name).split(/-(?!$)/)\n  const key = keys.find(item => !['Alt', 'Ctrl', 'Meta', 'Shift'].includes(item))\n  const event = new KeyboardEvent('keydown', {\n    key: key === 'Space'\n      ? ' '\n      : key,\n    altKey: keys.includes('Alt'),\n    ctrlKey: keys.includes('Ctrl'),\n    metaKey: keys.includes('Meta'),\n    shiftKey: keys.includes('Shift'),\n    bubbles: true,\n    cancelable: true,\n  })\n\n  const capturedTransaction = editor.captureTransaction(() => {\n    view.someProp('handleKeyDown', f => f(view, event))\n  })\n\n  capturedTransaction?.steps.forEach(step => {\n    const newStep = step.map(tr.mapping)\n\n    if (newStep && dispatch) {\n      tr.maybeStep(newStep)\n    }\n  })\n\n  return true\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { NodeRange } from '../types.js'\nimport { objectIncludes } from '../utilities/objectIncludes.js'\nimport { getNodeType } from './getNodeType.js'\n\nexport function isNodeActive(\n  state: EditorState,\n  typeOrName: NodeType | string | null,\n  attributes: Record<string, any> = {},\n): boolean {\n  const { from, to, empty } = state.selection\n  const type = typeOrName ? getNodeType(typeOrName, state.schema) : null\n\n  const nodeRanges: NodeRange[] = []\n\n  state.doc.nodesBetween(from, to, (node, pos) => {\n    if (node.isText) {\n      return\n    }\n\n    const relativeFrom = Math.max(from, pos)\n    const relativeTo = Math.min(to, pos + node.nodeSize)\n\n    nodeRanges.push({\n      node,\n      from: relativeFrom,\n      to: relativeTo,\n    })\n  })\n\n  const selectionRange = to - from\n  const matchedNodeRanges = nodeRanges\n    .filter(nodeRange => {\n      if (!type) {\n        return true\n      }\n\n      return type.name === nodeRange.node.type.name\n    })\n    .filter(nodeRange => objectIncludes(nodeRange.node.attrs, attributes, { strict: false }))\n\n  if (empty) {\n    return !!matchedNodeRanges.length\n  }\n\n  const range = matchedNodeRanges.reduce((sum, nodeRange) => sum + nodeRange.to - nodeRange.from, 0)\n\n  return range >= selectionRange\n}\n", "import { lift as originalLift } from '@tiptap/pm/commands'\nimport { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isNodeActive } from '../helpers/isNodeActive.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    lift: {\n      /**\n       * Removes an existing wrap if possible lifting the node out of it\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.lift('paragraph')\n       * @example editor.commands.lift('heading', { level: 1 })\n       */\n      lift: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const lift: RawCommands['lift'] = (typeOrName, attributes = {}) => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const isActive = isNodeActive(state, type, attributes)\n\n  if (!isActive) {\n    return false\n  }\n\n  return originalLift(state, dispatch)\n}\n", "import { liftEmptyBlock as originalLiftEmptyBlock } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    liftEmptyBlock: {\n      /**\n       * If the cursor is in an empty textblock that can be lifted, lift the block.\n       * @example editor.commands.liftEmptyBlock()\n       */\n      liftEmptyBlock: () => ReturnType,\n    }\n  }\n}\n\nexport const liftEmptyBlock: RawCommands['liftEmptyBlock'] = () => ({ state, dispatch }) => {\n  return originalLiftEmptyBlock(state, dispatch)\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { liftListItem as originalLiftListItem } from '@tiptap/pm/schema-list'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    liftListItem: {\n      /**\n       * Create a command to lift the list item around the selection up into a wrapping list.\n       * @param typeOrName The type or name of the node.\n       * @example editor.commands.liftListItem('listItem')\n       */\n      liftListItem: (typeOrName: string | NodeType) => ReturnType\n    }\n  }\n}\n\nexport const liftListItem: RawCommands['liftListItem'] = typeOrName => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  return originalLiftListItem(type)(state, dispatch)\n}\n", "import { newlineInCode as originalNewlineInCode } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    newlineInCode: {\n      /**\n       * Add a newline character in code.\n       * @example editor.commands.newlineInCode()\n       */\n      newlineInCode: () => ReturnType\n    }\n  }\n}\n\nexport const newlineInCode: RawCommands['newlineInCode'] = () => ({ state, dispatch }) => {\n  return originalNewlineInCode(state, dispatch)\n}\n", "import { Schema } from '@tiptap/pm/model'\n\n/**\n * Get the type of a schema item by its name.\n * @param name The name of the schema item\n * @param schema The Prosemiror schema to search in\n * @returns The type of the schema item (`node` or `mark`), or null if it doesn't exist\n */\nexport function getSchemaTypeNameByName(name: string, schema: Schema): 'node' | 'mark' | null {\n  if (schema.nodes[name]) {\n    return 'node'\n  }\n\n  if (schema.marks[name]) {\n    return 'mark'\n  }\n\n  return null\n}\n", "/**\n * Remove a property or an array of properties from an object\n * @param obj Object\n * @param key Key to remove\n */\nexport function deleteProps(obj: Record<string, any>, propOrProps: string | string[]): Record<string, any> {\n  const props = typeof propOrProps === 'string'\n    ? [propOrProps]\n    : propOrProps\n\n  return Object\n    .keys(obj)\n    .reduce((newObj: Record<string, any>, prop) => {\n      if (!props.includes(prop)) {\n        newObj[prop] = obj[prop]\n      }\n\n      return newObj\n    }, {})\n}\n", "import { MarkType, NodeType } from '@tiptap/pm/model'\n\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { getSchemaTypeNameByName } from '../helpers/getSchemaTypeNameByName.js'\nimport { RawCommands } from '../types.js'\nimport { deleteProps } from '../utilities/deleteProps.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    resetAttributes: {\n      /**\n       * Resets some node attributes to the default value.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node to reset.\n       * @example editor.commands.resetAttributes('heading', 'level')\n       */\n      resetAttributes: (\n        typeOrName: string | NodeType | MarkType,\n        attributes: string | string[],\n      ) => ReturnType\n    }\n  }\n}\n\nexport const resetAttributes: RawCommands['resetAttributes'] = (typeOrName, attributes) => ({ tr, state, dispatch }) => {\n  let nodeType: NodeType | null = null\n  let markType: MarkType | null = null\n\n  const schemaType = getSchemaTypeNameByName(\n    typeof typeOrName === 'string' ? typeOrName : typeOrName.name,\n    state.schema,\n  )\n\n  if (!schemaType) {\n    return false\n  }\n\n  if (schemaType === 'node') {\n    nodeType = getNodeType(typeOrName as NodeType, state.schema)\n  }\n\n  if (schemaType === 'mark') {\n    markType = getMarkType(typeOrName as MarkType, state.schema)\n  }\n\n  if (dispatch) {\n    tr.selection.ranges.forEach(range => {\n      state.doc.nodesBetween(range.$from.pos, range.$to.pos, (node, pos) => {\n        if (nodeType && nodeType === node.type) {\n          tr.setNodeMarkup(pos, undefined, deleteProps(node.attrs, attributes))\n        }\n\n        if (markType && node.marks.length) {\n          node.marks.forEach(mark => {\n            if (markType === mark.type) {\n              tr.addMark(\n                pos,\n                pos + node.nodeSize,\n                markType.create(deleteProps(mark.attrs, attributes)),\n              )\n            }\n          })\n        }\n      })\n    })\n  }\n\n  return true\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    scrollIntoView: {\n      /**\n       * Scroll the selection into view.\n       * @example editor.commands.scrollIntoView()\n       */\n      scrollIntoView: () => ReturnType,\n    }\n  }\n}\n\nexport const scrollIntoView: RawCommands['scrollIntoView'] = () => ({ tr, dispatch }) => {\n  if (dispatch) {\n    tr.scrollIntoView()\n  }\n\n  return true\n}\n", "import { AllSelection } from '@tiptap/pm/state'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectAll: {\n      /**\n       * Select the whole document.\n       * @example editor.commands.selectAll()\n       */\n      selectAll: () => ReturnType,\n    }\n  }\n}\n\nexport const selectAll: RawCommands['selectAll'] = () => ({ tr, dispatch }) => {\n  if (dispatch) {\n    const selection = new AllSelection(tr.doc)\n\n    tr.setSelection(selection)\n  }\n\n  return true\n}\n", "import { selectNodeBackward as originalSelectNodeBackward } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectNodeBackward: {\n      /**\n       * Select a node backward.\n       * @example editor.commands.selectNodeBackward()\n       */\n      selectNodeBackward: () => ReturnType\n    }\n  }\n}\n\nexport const selectNodeBackward: RawCommands['selectNodeBackward'] = () => ({ state, dispatch }) => {\n  return originalSelectNodeBackward(state, dispatch)\n}\n", "import { selectNodeForward as originalSelectNodeForward } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectNodeForward: {\n      /**\n       * Select a node forward.\n       * @example editor.commands.selectNodeForward()\n       */\n      selectNodeForward: () => ReturnType\n    }\n  }\n}\n\nexport const selectNodeForward: RawCommands['selectNodeForward'] = () => ({ state, dispatch }) => {\n  return originalSelectNodeForward(state, dispatch)\n}\n", "import { selectParentNode as originalSelectParentNode } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectParentNode: {\n      /**\n       * Select the parent node.\n       * @example editor.commands.selectParentNode()\n       */\n      selectParentNode: () => ReturnType\n    }\n  }\n}\n\nexport const selectParentNode: RawCommands['selectParentNode'] = () => ({ state, dispatch }) => {\n  return originalSelectParentNode(state, dispatch)\n}\n", "// @ts-ignore\n// TODO: add types to @types/prosemirror-commands\nimport { selectTextblockEnd as originalSelectTextblockEnd } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectTextblockEnd: {\n      /**\n       * Moves the cursor to the end of current text block.\n       * @example editor.commands.selectTextblockEnd()\n       */\n      selectTextblockEnd: () => ReturnType\n    }\n  }\n}\n\nexport const selectTextblockEnd: RawCommands['selectTextblockEnd'] = () => ({ state, dispatch }) => {\n  return originalSelectTextblockEnd(state, dispatch)\n}\n", "// @ts-ignore\n// TODO: add types to @types/prosemirror-commands\nimport { selectTextblockStart as originalSelectTextblockStart } from '@tiptap/pm/commands'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    selectTextblockStart: {\n      /**\n       * Moves the cursor to the start of current text block.\n       * @example editor.commands.selectTextblockStart()\n       */\n      selectTextblockStart: () => ReturnType\n    }\n  }\n}\n\nexport const selectTextblockStart: RawCommands['selectTextblockStart'] = () => ({ state, dispatch }) => {\n  return originalSelectTextblockStart(state, dispatch)\n}\n", "import {\n  Fragment, Node as ProseMirrorNode, ParseOptions, Schema,\n} from '@tiptap/pm/model'\n\nimport { Content } from '../types.js'\nimport { createNodeFromContent } from './createNodeFromContent.js'\n\n/**\n * Create a new Prosemirror document node from content.\n * @param content The JSON or HTML content to create the document from\n * @param schema The Prosemirror schema to use for the document\n * @param parseOptions Options for the parser\n * @returns The created Prosemirror document node\n */\nexport function createDocument(\n  content: Content | ProseMirrorNode | Fragment,\n  schema: Schema,\n  parseOptions: ParseOptions = {},\n  options: { errorOnInvalidContent?: boolean } = {},\n): ProseMirrorNode {\n  return createNodeFromContent(content, schema, {\n    slice: false,\n    parseOptions,\n    errorOnInvalidContent: options.errorOnInvalidContent,\n  }) as ProseMirrorNode\n}\n", "import { Fragment, Node as ProseMirrorNode, ParseOptions } from '@tiptap/pm/model'\n\nimport { createDocument } from '../helpers/createDocument.js'\nimport { Content, RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setContent: {\n      /**\n       * Replace the whole document with new content.\n       * @param content The new content.\n       * @param emitUpdate Whether to emit an update event.\n       * @param parseOptions Options for parsing the content.\n       * @example editor.commands.setContent('<p>Example text</p>')\n       */\n      setContent: (\n        /**\n         * The new content.\n         */\n        content: Content | Fragment | ProseMirrorNode,\n\n        /**\n         * Whether to emit an update event.\n         * @default false\n         */\n        emitUpdate?: boolean,\n\n        /**\n         * Options for parsing the content.\n         * @default {}\n         */\n        parseOptions?: ParseOptions,\n        /**\n         * Options for `setContent`.\n         */\n        options?: {\n          /**\n           * Whether to throw an error if the content is invalid.\n           */\n          errorOnInvalidContent?: boolean;\n        }\n      ) => ReturnType;\n    };\n  }\n}\n\nexport const setContent: RawCommands['setContent'] = (content, emitUpdate = false, parseOptions = {}, options = {}) => ({\n  editor, tr, dispatch, commands,\n}) => {\n  const { doc } = tr\n\n  // This is to keep backward compatibility with the previous behavior\n  // TODO remove this in the next major version\n  if (parseOptions.preserveWhitespace !== 'full') {\n    const document = createDocument(content, editor.schema, parseOptions, {\n      errorOnInvalidContent: options.errorOnInvalidContent ?? editor.options.enableContentCheck,\n    })\n\n    if (dispatch) {\n      tr.replaceWith(0, doc.content.size, document).setMeta('preventUpdate', !emitUpdate)\n    }\n    return true\n  }\n\n  if (dispatch) {\n    tr.setMeta('preventUpdate', !emitUpdate)\n  }\n\n  return commands.insertContentAt({ from: 0, to: doc.content.size }, content, {\n    parseOptions,\n    errorOnInvalidContent: options.errorOnInvalidContent ?? editor.options.enableContentCheck,\n  })\n}\n", "import { Mark, MarkType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { getMarkType } from './getMarkType.js'\n\nexport function getMarkAttributes(\n  state: EditorState,\n  typeOrName: string | MarkType,\n): Record<string, any> {\n  const type = getMarkType(typeOrName, state.schema)\n  const { from, to, empty } = state.selection\n  const marks: Mark[] = []\n\n  if (empty) {\n    if (state.storedMarks) {\n      marks.push(...state.storedMarks)\n    }\n\n    marks.push(...state.selection.$head.marks())\n  } else {\n    state.doc.nodesBetween(from, to, node => {\n      marks.push(...node.marks)\n    })\n  }\n\n  const mark = marks.find(markItem => markItem.type.name === type.name)\n\n  if (!mark) {\n    return {}\n  }\n\n  return { ...mark.attrs }\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { Transaction } from '@tiptap/pm/state'\nimport { Transform } from '@tiptap/pm/transform'\n\n/**\n * Returns a new `Transform` based on all steps of the passed transactions.\n * @param oldDoc The Prosemirror node to start from\n * @param transactions The transactions to combine\n * @returns A new `Transform` with all steps of the passed transactions\n */\nexport function combineTransactionSteps(\n  oldDoc: ProseMirrorNode,\n  transactions: Transaction[],\n): Transform {\n  const transform = new Transform(oldDoc)\n\n  transactions.forEach(transaction => {\n    transaction.steps.forEach(step => {\n      transform.step(step)\n    })\n  })\n\n  return transform\n}\n", "import { ContentMatch, NodeType } from '@tiptap/pm/model'\n\n/**\n * Gets the default block type at a given match\n * @param match The content match to get the default block type from\n * @returns The default block type or null\n */\nexport function defaultBlockAt(match: ContentMatch): NodeType | null {\n  for (let i = 0; i < match.edgeCount; i += 1) {\n    const { type } = match.edge(i)\n\n    if (type.isTextblock && !type.hasRequiredAttrs()) {\n      return type\n    }\n  }\n\n  return null\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { NodeWithPos, Predicate } from '../types.js'\n\n/**\n * Find children inside a Prosemirror node that match a predicate.\n * @param node The Prosemirror node to search in\n * @param predicate The predicate to match\n * @returns An array of nodes with their positions\n */\nexport function findChildren(node: ProseMirrorNode, predicate: Predicate): NodeWithPos[] {\n  const nodesWithPos: NodeWithPos[] = []\n\n  node.descendants((child, pos) => {\n    if (predicate(child)) {\n      nodesWithPos.push({\n        node: child,\n        pos,\n      })\n    }\n  })\n\n  return nodesWithPos\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { NodeWithPos, Predicate, Range } from '../types.js'\n\n/**\n * Same as `find<PERSON><PERSON>dren` but searches only within a `range`.\n * @param node The Prosemirror node to search in\n * @param range The range to search in\n * @param predicate The predicate to match\n * @returns An array of nodes with their positions\n */\nexport function findChildrenInRange(\n  node: ProseMirrorNode,\n  range: Range,\n  predicate: Predicate,\n): NodeWithPos[] {\n  const nodesWithPos: NodeWithPos[] = []\n\n  // if (range.from === range.to) {\n  //   const nodeAt = node.nodeAt(range.from)\n\n  //   if (nodeAt) {\n  //     nodesWithPos.push({\n  //       node: nodeAt,\n  //       pos: range.from,\n  //     })\n  //   }\n  // }\n\n  node.nodesBetween(range.from, range.to, (child, pos) => {\n    if (predicate(child)) {\n      nodesWithPos.push({\n        node: child,\n        pos,\n      })\n    }\n  })\n\n  return nodesWithPos\n}\n", "import { Node as ProseMirrorNode, ResolvedPos } from '@tiptap/pm/model'\n\nimport { Predicate } from '../types.js'\n\n/**\n * Finds the closest parent node to a resolved position that matches a predicate.\n * @param $pos The resolved position to search from\n * @param predicate The predicate to match\n * @returns The closest parent node to the resolved position that matches the predicate\n * @example ```js\n * findParentNodeClosestToPos($from, node => node.type.name === 'paragraph')\n * ```\n */\nexport function findParentNodeClosestToPos(\n  $pos: ResolvedPos,\n  predicate: Predicate,\n):\n  | {\n      pos: number\n      start: number\n      depth: number\n      node: ProseMirrorNode\n    }\n  | undefined {\n  for (let i = $pos.depth; i > 0; i -= 1) {\n    const node = $pos.node(i)\n\n    if (predicate(node)) {\n      return {\n        pos: i > 0 ? $pos.before(i) : 0,\n        start: $pos.start(i),\n        depth: i,\n        node,\n      }\n    }\n  }\n}\n", "import { Selection } from '@tiptap/pm/state'\n\nimport { Predicate } from '../types.js'\nimport { findParentNodeClosestToPos } from './findParentNodeClosestToPos.js'\n\n/**\n * Finds the closest parent node to the current selection that matches a predicate.\n * @param predicate The predicate to match\n * @returns A command that finds the closest parent node to the current selection that matches the predicate\n * @example ```js\n * findParentNode(node => node.type.name === 'paragraph')\n * ```\n */\nexport function findParentNode(predicate: Predicate) {\n  return (selection: Selection) => findParentNodeClosestToPos(selection.$from, predicate)\n}\n", "import { Schema } from '@tiptap/pm/model'\n\nimport { Editor } from '../Editor.js'\nimport { ExtensionManager } from '../ExtensionManager.js'\nimport { Extensions } from '../types.js'\nimport { getSchemaByResolvedExtensions } from './getSchemaByResolvedExtensions.js'\n\nexport function getSchema(extensions: Extensions, editor?: Editor): Schema {\n  const resolvedExtensions = ExtensionManager.resolve(extensions)\n\n  return getSchemaByResolvedExtensions(resolvedExtensions, editor)\n}\n", "import { Node } from '@tiptap/pm/model'\n\nimport { Extensions, JSONContent } from '../types.js'\nimport { getHTMLFromFragment } from './getHTMLFromFragment.js'\nimport { getSchema } from './getSchema.js'\n\n/**\n * Generate HTML from a JSONContent\n * @param doc The JSONContent to generate HTML from\n * @param extensions The extensions to use for the schema\n * @returns The generated HTML\n */\nexport function generateHTML(doc: JSONContent, extensions: Extensions): string {\n  const schema = getSchema(extensions)\n  const contentNode = Node.fromJSON(schema, doc)\n\n  return getHTMLFromFragment(contentNode.content, schema)\n}\n", "import { DOMParser } from '@tiptap/pm/model'\n\nimport { Extensions } from '../types.js'\nimport { elementFromString } from '../utilities/elementFromString.js'\nimport { getSchema } from './getSchema.js'\n\n/**\n * Generate JSONContent from HTML\n * @param html The HTML to generate J<PERSON><PERSON>ontent from\n * @param extensions The extensions to use for the schema\n * @returns The generated JSONContent\n */\nexport function generateJSON(html: string, extensions: Extensions): Record<string, any> {\n  const schema = getSchema(extensions)\n  const dom = elementFromString(html)\n\n  return DOMParser.fromSchema(schema).parse(dom).toJSON()\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { TextSerializer } from '../types.js'\nimport { getTextBetween } from './getTextBetween.js'\n\n/**\n * Gets the text of a Prosemirror node\n * @param node The Prosemirror node\n * @param options Options for the text serializer & block separator\n * @returns The text of the node\n * @example ```js\n * const text = getText(node, { blockSeparator: '\\n' })\n * ```\n */\nexport function getText(\n  node: ProseMirrorNode,\n  options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  },\n) {\n  const range = {\n    from: 0,\n    to: node.content.size,\n  }\n\n  return getTextBetween(node, range, options)\n}\n", "import { Node } from '@tiptap/pm/model'\n\nimport { Extensions, JSONContent, TextSerializer } from '../types.js'\nimport { getSchema } from './getSchema.js'\nimport { getText } from './getText.js'\nimport { getTextSerializersFromSchema } from './getTextSerializersFromSchema.js'\n\n/**\n * Generate raw text from a JSONContent\n * @param doc The JSONContent to generate text from\n * @param extensions The extensions to use for the schema\n * @param options Options for the text generation f.e. blockSeparator or textSerializers\n * @returns The generated text\n */\nexport function generateText(\n  doc: JSONContent,\n  extensions: Extensions,\n  options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  },\n): string {\n  const { blockSeparator = '\\n\\n', textSerializers = {} } = options || {}\n  const schema = getSchema(extensions)\n  const contentNode = Node.fromJSON(schema, doc)\n\n  return getText(contentNode, {\n    blockSeparator,\n    textSerializers: {\n      ...getTextSerializersFromSchema(schema),\n      ...textSerializers,\n    },\n  })\n}\n", "import { Node, NodeType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { getNodeType } from './getNodeType.js'\n\nexport function getNodeAttributes(\n  state: EditorState,\n  typeOrName: string | NodeType,\n): Record<string, any> {\n  const type = getNodeType(typeOrName, state.schema)\n  const { from, to } = state.selection\n  const nodes: Node[] = []\n\n  state.doc.nodesBetween(from, to, node => {\n    nodes.push(node)\n  })\n\n  const node = nodes.reverse().find(nodeItem => nodeItem.type.name === type.name)\n\n  if (!node) {\n    return {}\n  }\n\n  return { ...node.attrs }\n}\n", "import { MarkType, NodeType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { getMarkAttributes } from './getMarkAttributes.js'\nimport { getNodeAttributes } from './getNodeAttributes.js'\nimport { getSchemaTypeNameByName } from './getSchemaTypeNameByName.js'\n\n/**\n * Get node or mark attributes by type or name on the current editor state\n * @param state The current editor state\n * @param typeOrName The node or mark type or name\n * @returns The attributes of the node or mark or an empty object\n */\nexport function getAttributes(\n  state: EditorState,\n  typeOrName: string | NodeType | MarkType,\n): Record<string, any> {\n  const schemaType = getSchemaTypeNameByName(\n    typeof typeOrName === 'string' ? typeOrName : typeOrName.name,\n    state.schema,\n  )\n\n  if (schemaType === 'node') {\n    return getNodeAttributes(state, typeOrName as NodeType)\n  }\n\n  if (schemaType === 'mark') {\n    return getMarkAttributes(state, typeOrName as MarkType)\n  }\n\n  return {}\n}\n", "/**\n * Removes duplicated values within an array.\n * Supports numbers, strings and objects.\n */\nexport function removeDuplicates<T>(array: T[], by = JSON.stringify): T[] {\n  const seen: Record<any, any> = {}\n\n  return array.filter(item => {\n    const key = by(item)\n\n    return Object.prototype.hasOwnProperty.call(seen, key)\n      ? false\n      : (seen[key] = true)\n  })\n}\n", "import { Step, Transform } from '@tiptap/pm/transform'\n\nimport { Range } from '../types.js'\nimport { removeDuplicates } from '../utilities/removeDuplicates.js'\n\nexport type ChangedRange = {\n  oldRange: Range,\n  newRange: Range,\n}\n\n/**\n * Removes duplicated ranges and ranges that are\n * fully captured by other ranges.\n */\nfunction simplifyChangedRanges(changes: ChangedRange[]): ChangedRange[] {\n  const uniqueChanges = removeDuplicates(changes)\n\n  return uniqueChanges.length === 1\n    ? uniqueChanges\n    : uniqueChanges.filter((change, index) => {\n      const rest = uniqueChanges.filter((_, i) => i !== index)\n\n      return !rest.some(otherChange => {\n        return change.oldRange.from >= otherChange.oldRange.from\n          && change.oldRange.to <= otherChange.oldRange.to\n          && change.newRange.from >= otherChange.newRange.from\n          && change.newRange.to <= otherChange.newRange.to\n      })\n    })\n}\n\n/**\n * Returns a list of changed ranges\n * based on the first and last state of all steps.\n */\nexport function getChangedRanges(transform: Transform): ChangedRange[] {\n  const { mapping, steps } = transform\n  const changes: ChangedRange[] = []\n\n  mapping.maps.forEach((stepMap, index) => {\n    const ranges: Range[] = []\n\n    // This accounts for step changes where no range was actually altered\n    // e.g. when setting a mark, node attribute, etc.\n    // @ts-ignore\n    if (!stepMap.ranges.length) {\n      const { from, to } = steps[index] as Step & {\n        from?: number,\n        to?: number,\n      }\n\n      if (from === undefined || to === undefined) {\n        return\n      }\n\n      ranges.push({ from, to })\n    } else {\n      stepMap.forEach((from, to) => {\n        ranges.push({ from, to })\n      })\n    }\n\n    ranges.forEach(({ from, to }) => {\n      const newStart = mapping.slice(index).map(from, -1)\n      const newEnd = mapping.slice(index).map(to)\n      const oldStart = mapping.invert().map(newStart, -1)\n      const oldEnd = mapping.invert().map(newEnd)\n\n      changes.push({\n        oldRange: {\n          from: oldStart,\n          to: oldEnd,\n        },\n        newRange: {\n          from: newStart,\n          to: newEnd,\n        },\n      })\n    })\n  })\n\n  return simplifyChangedRanges(changes)\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { JSONContent } from '../types.js'\n\ninterface DebugJSONContent extends JSO<PERSON>ontent {\n  from: number\n  to: number\n}\n\nexport function getDebugJSON(node: ProseMirrorNode, startOffset = 0): DebugJSONContent {\n  const isTopNode = node.type === node.type.schema.topNodeType\n  const increment = isTopNode ? 0 : 1\n  const from = startOffset\n  const to = from + node.nodeSize\n  const marks = node.marks.map(mark => {\n    const output: { type: string; attrs?: Record<string, any> } = {\n      type: mark.type.name,\n    }\n\n    if (Object.keys(mark.attrs).length) {\n      output.attrs = { ...mark.attrs }\n    }\n\n    return output\n  })\n  const attrs = { ...node.attrs }\n  const output: DebugJSONContent = {\n    type: node.type.name,\n    from,\n    to,\n  }\n\n  if (Object.keys(attrs).length) {\n    output.attrs = attrs\n  }\n\n  if (marks.length) {\n    output.marks = marks\n  }\n\n  if (node.content.childCount) {\n    output.content = []\n\n    node.forEach((child, offset) => {\n      output.content?.push(getDebugJSON(child, startOffset + offset + increment))\n    })\n  }\n\n  if (node.text) {\n    output.text = node.text\n  }\n\n  return output\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\nimport { MarkRange } from '../types.js'\nimport { getMarkRange } from './getMarkRange.js'\n\nexport function getMarksBetween(from: number, to: number, doc: ProseMirrorNode): MarkRange[] {\n  const marks: MarkRange[] = []\n\n  // get all inclusive marks on empty selection\n  if (from === to) {\n    doc\n      .resolve(from)\n      .marks()\n      .forEach(mark => {\n        const $pos = doc.resolve(from)\n        const range = getMarkRange($pos, mark.type)\n\n        if (!range) {\n          return\n        }\n\n        marks.push({\n          mark,\n          ...range,\n        })\n      })\n  } else {\n    doc.nodesBetween(from, to, (node, pos) => {\n      if (!node || node?.nodeSize === undefined) {\n        return\n      }\n\n      marks.push(\n        ...node.marks.map(mark => ({\n          from: pos,\n          to: pos + node.nodeSize,\n          mark,\n        })),\n      )\n    })\n  }\n\n  return marks\n}\n", "import { Node, NodeType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\n/**\n * Finds the first node of a given type or name in the current selection.\n * @param state The editor state.\n * @param typeOrName The node type or name.\n * @param pos The position to start searching from.\n * @param maxDepth The maximum depth to search.\n * @returns The node and the depth as an array.\n */\nexport const getNodeAtPosition = (state: EditorState, typeOrName: string | NodeType, pos: number, maxDepth = 20) => {\n  const $pos = state.doc.resolve(pos)\n\n  let currentDepth = maxDepth\n  let node: Node | null = null\n\n  while (currentDepth > 0 && node === null) {\n    const currentNode = $pos.node(currentDepth)\n\n    if (currentNode?.type.name === typeOrName) {\n      node = currentNode\n    } else {\n      currentDepth -= 1\n    }\n  }\n\n  return [node, currentDepth] as [Node | null, number]\n}\n", "import { ExtensionAttribute } from '../types.js'\n\n/**\n * Return attributes of an extension that should be splitted by keepOnSplit flag\n * @param extensionAttributes Array of extension attributes\n * @param typeName The type of the extension\n * @param attributes The attributes of the extension\n * @returns The splitted attributes\n */\nexport function getSplittedAttributes(\n  extensionAttributes: ExtensionAttribute[],\n  typeName: string,\n  attributes: Record<string, any>,\n): Record<string, any> {\n  return Object.fromEntries(Object\n    .entries(attributes)\n    .filter(([name]) => {\n      const extensionAttribute = extensionAttributes.find(item => {\n        return item.type === typeName && item.name === name\n      })\n\n      if (!extensionAttribute) {\n        return false\n      }\n\n      return extensionAttribute.attribute.keepOnSplit\n    }))\n}\n", "import { MarkType } from '@tiptap/pm/model'\nimport { EditorState } from '@tiptap/pm/state'\n\nimport { MarkRange } from '../types.js'\nimport { objectIncludes } from '../utilities/objectIncludes.js'\nimport { getMarkType } from './getMarkType.js'\n\nexport function isMarkActive(\n  state: EditorState,\n  typeOrName: MarkType | string | null,\n  attributes: Record<string, any> = {},\n): boolean {\n  const { empty, ranges } = state.selection\n  const type = typeOrName ? getMarkType(typeOrName, state.schema) : null\n\n  if (empty) {\n    return !!(state.storedMarks || state.selection.$from.marks())\n      .filter(mark => {\n        if (!type) {\n          return true\n        }\n\n        return type.name === mark.type.name\n      })\n      .find(mark => objectIncludes(mark.attrs, attributes, { strict: false }))\n  }\n\n  let selectionRange = 0\n  const markRanges: MarkRange[] = []\n\n  ranges.forEach(({ $from, $to }) => {\n    const from = $from.pos\n    const to = $to.pos\n\n    state.doc.nodesBetween(from, to, (node, pos) => {\n      if (!node.isText && !node.marks.length) {\n        return\n      }\n\n      const relativeFrom = Math.max(from, pos)\n      const relativeTo = Math.min(to, pos + node.nodeSize)\n      const range = relativeTo - relativeFrom\n\n      selectionRange += range\n\n      markRanges.push(\n        ...node.marks.map(mark => ({\n          mark,\n          from: relativeFrom,\n          to: relativeTo,\n        })),\n      )\n    })\n  })\n\n  if (selectionRange === 0) {\n    return false\n  }\n\n  // calculate range of matched mark\n  const matchedRange = markRanges\n    .filter(markRange => {\n      if (!type) {\n        return true\n      }\n\n      return type.name === markRange.mark.type.name\n    })\n    .filter(markRange => objectIncludes(markRange.mark.attrs, attributes, { strict: false }))\n    .reduce((sum, markRange) => sum + markRange.to - markRange.from, 0)\n\n  // calculate range of marks that excludes the searched mark\n  // for example `code` doesn’t allow any other marks\n  const excludedRange = markRanges\n    .filter(markRange => {\n      if (!type) {\n        return true\n      }\n\n      return markRange.mark.type !== type && markRange.mark.type.excludes(type)\n    })\n    .reduce((sum, markRange) => sum + markRange.to - markRange.from, 0)\n\n  // we only include the result of `excludedRange`\n  // if there is a match at all\n  const range = matchedRange > 0 ? matchedRange + excludedRange : matchedRange\n\n  return range >= selectionRange\n}\n", "import { EditorState } from '@tiptap/pm/state'\n\nimport { getSchemaTypeNameByName } from './getSchemaTypeNameByName.js'\nimport { isMarkActive } from './isMarkActive.js'\nimport { isNodeActive } from './isNodeActive.js'\n\nexport function isActive(\n  state: EditorState,\n  name: string | null,\n  attributes: Record<string, any> = {},\n): boolean {\n  if (!name) {\n    return isNodeActive(state, null, attributes) || isMarkActive(state, null, attributes)\n  }\n\n  const schemaType = getSchemaTypeNameByName(name, state.schema)\n\n  if (schemaType === 'node') {\n    return isNodeActive(state, name, attributes)\n  }\n\n  if (schemaType === 'mark') {\n    return isMarkActive(state, name, attributes)\n  }\n\n  return false\n}\n", "import { EditorState } from '@tiptap/pm/state'\n\nimport { findParentNode } from './findParentNode.js'\n\nexport const isAtEndOfNode = (state: EditorState, nodeType?: string) => {\n  const { $from, $to, $anchor } = state.selection\n\n  if (nodeType) {\n    const parentNode = findParentNode(node => node.type.name === nodeType)(state.selection)\n\n    if (!parentNode) {\n      return false\n    }\n\n    const $parentPos = state.doc.resolve(parentNode.pos + 1)\n\n    if ($anchor.pos + 1 === $parentPos.end()) {\n      return true\n    }\n\n    return false\n  }\n\n  if ($to.parentOffset < $to.parent.nodeSize - 2 || $from.pos !== $to.pos) {\n    return false\n  }\n\n  return true\n}\n", "import { EditorState } from '@tiptap/pm/state'\n\nexport const isAtStartOfNode = (state: EditorState) => {\n  const { $from, $to } = state.selection\n\n  if ($from.parentOffset > 0 || $from.pos !== $to.pos) {\n    return false\n  }\n\n  return true\n}\n", "import { getExtensionField } from '../helpers/getExtensionField.js'\nimport { NodeConfig } from '../index.js'\nimport { Extensions } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\nimport { splitExtensions } from './splitExtensions.js'\n\nexport function isList(name: string, extensions: Extensions): boolean {\n  const { nodeExtensions } = splitExtensions(extensions)\n  const extension = nodeExtensions.find(item => item.name === name)\n\n  if (!extension) {\n    return false\n  }\n\n  const context = {\n    name: extension.name,\n    options: extension.options,\n    storage: extension.storage,\n  }\n  const group = callOrReturn(getExtensionField<NodeConfig['group']>(extension, 'group', context))\n\n  if (typeof group !== 'string') {\n    return false\n  }\n\n  return group.split(' ').includes('list')\n}\n", "import { Node as ProseMirrorNode } from '@tiptap/pm/model'\n\n/**\n * Returns true if the given prosemirror node is empty.\n */\nexport function isNodeEmpty(\n  node: ProseMirrorNode,\n  {\n    checkChildren = true,\n    ignoreWhitespace = false,\n  }: {\n    /**\n     * When true (default), it will also check if all children are empty.\n     */\n    checkChildren?: boolean;\n    /**\n     * When true, it will ignore whitespace when checking for emptiness.\n     */\n    ignoreWhitespace?: boolean;\n  } = {},\n): boolean {\n  if (ignoreWhitespace) {\n    if (node.type.name === 'hardBreak') {\n      // Hard breaks are considered empty\n      return true\n    }\n    if (node.isText) {\n      return /^\\s*$/m.test(node.text ?? '')\n    }\n  }\n\n  if (node.isText) {\n    return !node.text\n  }\n\n  if (node.isAtom || node.isLeaf) {\n    return false\n  }\n\n  if (node.content.childCount === 0) {\n    return true\n  }\n\n  if (checkChildren) {\n    let isContentEmpty = true\n\n    node.content.forEach(childNode => {\n      if (isContentEmpty === false) {\n        // Exit early for perf\n        return\n      }\n\n      if (!isNodeEmpty(childNode, { ignoreWhitespace, checkChildren })) {\n        isContentEmpty = false\n      }\n    })\n\n    return isContentEmpty\n  }\n\n  return false\n}\n", "import { NodeSelection } from '@tiptap/pm/state'\n\nexport function isNodeSelection(value: unknown): value is NodeSelection {\n  return value instanceof NodeSelection\n}\n", "import { EditorView } from '@tiptap/pm/view'\n\nimport { minMax } from '../utilities/minMax.js'\n\nexport function posToDOMRect(view: EditorView, from: number, to: number): DOMRect {\n  const minPos = 0\n  const maxPos = view.state.doc.content.size\n  const resolvedFrom = minMax(from, minPos, maxPos)\n  const resolvedEnd = minMax(to, minPos, maxPos)\n  const start = view.coordsAtPos(resolvedFrom)\n  const end = view.coordsAtPos(resolvedEnd, -1)\n  const top = Math.min(start.top, end.top)\n  const bottom = Math.max(start.bottom, end.bottom)\n  const left = Math.min(start.left, end.left)\n  const right = Math.max(start.right, end.right)\n  const width = right - left\n  const height = bottom - top\n  const x = left\n  const y = top\n  const data = {\n    top,\n    bottom,\n    left,\n    right,\n    width,\n    height,\n    x,\n    y,\n  }\n\n  return {\n    ...data,\n    toJSON: () => data,\n  }\n}\n", "import type { Schema } from '@tiptap/pm/model'\n\nimport type { JSONContent } from '../types.js'\n\ntype RewriteUnknownContentOptions = {\n  /**\n   * If true, unknown nodes will be treated as paragraphs\n   * @default true\n   */\n  fallbackToParagraph?: boolean;\n};\n\ntype RewrittenContent = {\n  /**\n   * The original JSON content that was rewritten\n   */\n  original: JSONContent;\n  /**\n   * The name of the node or mark that was unsupported\n   */\n  unsupported: string;\n}[];\n\n/**\n * The actual implementation of the rewriteUnknownContent function\n */\nfunction rewriteUnknownContentInner({\n  json,\n  validMarks,\n  validNodes,\n  options,\n  rewrittenContent = [],\n}: {\n  json: JSONContent;\n  validMarks: Set<string>;\n  validNodes: Set<string>;\n  options?: RewriteUnknownContentOptions;\n  rewrittenContent?: RewrittenContent;\n}): {\n  /**\n   * The cleaned JSON content\n   */\n  json: JSONContent | null;\n  /**\n   * The array of nodes and marks that were rewritten\n   */\n  rewrittenContent: RewrittenContent;\n} {\n  if (json.marks && Array.isArray(json.marks)) {\n    json.marks = json.marks.filter(mark => {\n      const name = typeof mark === 'string' ? mark : mark.type\n\n      if (validMarks.has(name)) {\n        return true\n      }\n\n      rewrittenContent.push({\n        original: JSON.parse(JSON.stringify(mark)),\n        unsupported: name,\n      })\n      // Just ignore any unknown marks\n      return false\n    })\n  }\n\n  if (json.content && Array.isArray(json.content)) {\n    json.content = json.content\n      .map(\n        value => rewriteUnknownContentInner({\n          json: value,\n          validMarks,\n          validNodes,\n          options,\n          rewrittenContent,\n        }).json,\n      )\n      .filter(a => a !== null && a !== undefined)\n  }\n\n  if (json.type && !validNodes.has(json.type)) {\n    rewrittenContent.push({\n      original: JSON.parse(JSON.stringify(json)),\n      unsupported: json.type,\n    })\n\n    if (json.content && Array.isArray(json.content) && (options?.fallbackToParagraph !== false)) {\n      // Just treat it like a paragraph and hope for the best\n      json.type = 'paragraph'\n\n      return {\n        json,\n        rewrittenContent,\n      }\n    }\n\n    // or just omit it entirely\n    return {\n      json: null,\n      rewrittenContent,\n    }\n  }\n\n  return { json, rewrittenContent }\n}\n\n/**\n * Rewrite unknown nodes and marks within JSON content\n * Allowing for user within the editor\n */\nexport function rewriteUnknownContent(\n  /**\n   * The JSON content to clean of unknown nodes and marks\n   */\n  json: JSONContent,\n  /**\n   * The schema to use for validation\n   */\n  schema: Schema,\n  /**\n   * Options for the cleaning process\n   */\n  options?: RewriteUnknownContentOptions,\n): {\n  /**\n   * The cleaned JSON content\n   */\n  json: JSONContent | null;\n  /**\n   * The array of nodes and marks that were rewritten\n   */\n  rewrittenContent: {\n    /**\n     * The original JSON content that was rewritten\n     */\n    original: JSONContent;\n    /**\n     * The name of the node or mark that was unsupported\n     */\n    unsupported: string;\n  }[];\n} {\n  return rewriteUnknownContentInner({\n    json,\n    validNodes: new Set(Object.keys(schema.nodes)),\n    validMarks: new Set(Object.keys(schema.marks)),\n    options,\n  })\n}\n", "import { MarkType, ResolvedPos } from '@tiptap/pm/model'\nimport { EditorState, Transaction } from '@tiptap/pm/state'\n\nimport { getMarkAttributes } from '../helpers/getMarkAttributes.js'\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { isTextSelection } from '../helpers/index.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setMark: {\n      /**\n       * Add a mark with new attributes.\n       * @param typeOrName The mark type or name.\n       * @example editor.commands.setMark('bold', { level: 1 })\n       */\n      setMark: (typeOrName: string | MarkType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nfunction canSetMark(state: EditorState, tr: Transaction, newMarkType: MarkType) {\n  const { selection } = tr\n  let cursor: ResolvedPos | null = null\n\n  if (isTextSelection(selection)) {\n    cursor = selection.$cursor\n  }\n\n  if (cursor) {\n    const currentMarks = state.storedMarks ?? cursor.marks()\n\n    // There can be no current marks that exclude the new mark\n    return (\n      !!newMarkType.isInSet(currentMarks)\n      || !currentMarks.some(mark => mark.type.excludes(newMarkType))\n    )\n  }\n\n  const { ranges } = selection\n\n  return ranges.some(({ $from, $to }) => {\n    let someNodeSupportsMark = $from.depth === 0\n      ? state.doc.inlineContent && state.doc.type.allowsMarkType(newMarkType)\n      : false\n\n    state.doc.nodesBetween($from.pos, $to.pos, (node, _pos, parent) => {\n      // If we already found a mark that we can enable, return false to bypass the remaining search\n      if (someNodeSupportsMark) {\n        return false\n      }\n\n      if (node.isInline) {\n        const parentAllowsMarkType = !parent || parent.type.allowsMarkType(newMarkType)\n        const currentMarksAllowMarkType = !!newMarkType.isInSet(node.marks)\n          || !node.marks.some(otherMark => otherMark.type.excludes(newMarkType))\n\n        someNodeSupportsMark = parentAllowsMarkType && currentMarksAllowMarkType\n      }\n      return !someNodeSupportsMark\n    })\n\n    return someNodeSupportsMark\n  })\n}\nexport const setMark: RawCommands['setMark'] = (typeOrName, attributes = {}) => ({ tr, state, dispatch }) => {\n  const { selection } = tr\n  const { empty, ranges } = selection\n  const type = getMarkType(typeOrName, state.schema)\n\n  if (dispatch) {\n    if (empty) {\n      const oldAttributes = getMarkAttributes(state, type)\n\n      tr.addStoredMark(\n        type.create({\n          ...oldAttributes,\n          ...attributes,\n        }),\n      )\n    } else {\n      ranges.forEach(range => {\n        const from = range.$from.pos\n        const to = range.$to.pos\n\n        state.doc.nodesBetween(from, to, (node, pos) => {\n          const trimmedFrom = Math.max(pos, from)\n          const trimmedTo = Math.min(pos + node.nodeSize, to)\n          const someHasMark = node.marks.find(mark => mark.type === type)\n\n          // if there is already a mark of this type\n          // we know that we have to merge its attributes\n          // otherwise we add a fresh new mark\n          if (someHasMark) {\n            node.marks.forEach(mark => {\n              if (type === mark.type) {\n                tr.addMark(\n                  trimmedFrom,\n                  trimmedTo,\n                  type.create({\n                    ...mark.attrs,\n                    ...attributes,\n                  }),\n                )\n              }\n            })\n          } else {\n            tr.addMark(trimmedFrom, trimmedTo, type.create(attributes))\n          }\n        })\n      })\n    }\n  }\n\n  return canSetMark(state, tr, type)\n}\n", "import type { Plug<PERSON>, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setMeta: {\n      /**\n       * Store a metadata property in the current transaction.\n       * @param key The key of the metadata property.\n       * @param value The value to store.\n       * @example editor.commands.setMeta('foo', 'bar')\n       */\n      setMeta: (key: string | Plugin | PluginKey, value: any) => ReturnType,\n    }\n  }\n}\n\nexport const setMeta: RawCommands['setMeta'] = (key, value) => ({ tr }) => {\n  tr.setMeta(key, value)\n\n  return true\n}\n", "import { setBlockType } from '@tiptap/pm/commands'\nimport { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setNode: {\n      /**\n       * Replace a given range with a node.\n       * @param typeOrName The type or name of the node\n       * @param attributes The attributes of the node\n       * @example editor.commands.setNode('paragraph')\n       */\n      setNode: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const setNode: RawCommands['setNode'] = (typeOrName, attributes = {}) => ({ state, dispatch, chain }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  let attributesToCopy: Record<string, any> | undefined\n\n  if (state.selection.$anchor.sameParent(state.selection.$head)) {\n    // only copy attributes if the selection is pointing to a node of the same type\n    attributesToCopy = state.selection.$anchor.parent.attrs\n  }\n\n  // TODO: use a fallback like insertContent?\n  if (!type.isTextblock) {\n    console.warn('[tiptap warn]: Currently \"setNode()\" only supports text block nodes.')\n\n    return false\n  }\n\n  return (\n    chain()\n    // try to convert node to default node if needed\n      .command(({ commands }) => {\n        const canSetBlock = setBlockType(type, { ...attributesToCopy, ...attributes })(state)\n\n        if (canSetBlock) {\n          return true\n        }\n\n        return commands.clearNodes()\n      })\n      .command(({ state: updatedState }) => {\n        return setBlockType(type, { ...attributesToCopy, ...attributes })(updatedState, dispatch)\n      })\n      .run()\n  )\n}\n", "import { NodeSelection } from '@tiptap/pm/state'\n\nimport { RawCommands } from '../types.js'\nimport { minMax } from '../utilities/minMax.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setNodeSelection: {\n      /**\n       * Creates a NodeSelection.\n       * @param position - Position of the node.\n       * @example editor.commands.setNodeSelection(10)\n       */\n      setNodeSelection: (position: number) => ReturnType\n    }\n  }\n}\n\nexport const setNodeSelection: RawCommands['setNodeSelection'] = position => ({ tr, dispatch }) => {\n  if (dispatch) {\n    const { doc } = tr\n    const from = minMax(position, 0, doc.content.size)\n    const selection = NodeSelection.create(doc, from)\n\n    tr.setSelection(selection)\n  }\n\n  return true\n}\n", "import { TextSelection } from '@tiptap/pm/state'\n\nimport { Range, RawCommands } from '../types.js'\nimport { minMax } from '../utilities/minMax.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    setTextSelection: {\n      /**\n       * Creates a TextSelection.\n       * @param position The position of the selection.\n       * @example editor.commands.setTextSelection(10)\n       */\n      setTextSelection: (position: number | Range) => ReturnType\n    }\n  }\n}\n\nexport const setTextSelection: RawCommands['setTextSelection'] = position => ({ tr, dispatch }) => {\n  if (dispatch) {\n    const { doc } = tr\n    const { from, to } = typeof position === 'number' ? { from: position, to: position } : position\n    const minPos = TextSelection.atStart(doc).from\n    const maxPos = TextSelection.atEnd(doc).to\n    const resolvedFrom = minMax(from, minPos, maxPos)\n    const resolvedEnd = minMax(to, minPos, maxPos)\n    const selection = TextSelection.create(doc, resolvedFrom, resolvedEnd)\n\n    tr.setSelection(selection)\n  }\n\n  return true\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { sinkListItem as originalSinkListItem } from '@tiptap/pm/schema-list'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    sinkListItem: {\n      /**\n       * Sink the list item down into an inner list.\n       * @param typeOrName The type or name of the node.\n       * @example editor.commands.sinkListItem('listItem')\n       */\n      sinkListItem: (typeOrName: string | NodeType) => ReturnType\n    }\n  }\n}\n\nexport const sinkListItem: RawCommands['sinkListItem'] = typeOrName => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  return originalSinkListItem(type)(state, dispatch)\n}\n", "import { EditorState, NodeSelection, TextSelection } from '@tiptap/pm/state'\nimport { canSplit } from '@tiptap/pm/transform'\n\nimport { defaultBlockAt } from '../helpers/defaultBlockAt.js'\nimport { getSplittedAttributes } from '../helpers/getSplittedAttributes.js'\nimport { RawCommands } from '../types.js'\n\nfunction ensureMarks(state: EditorState, splittableMarks?: string[]) {\n  const marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks())\n\n  if (marks) {\n    const filteredMarks = marks.filter(mark => splittableMarks?.includes(mark.type.name))\n\n    state.tr.ensureMarks(filteredMarks)\n  }\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    splitBlock: {\n      /**\n       * Forks a new node from an existing node.\n       * @param options.keepMarks Keep marks from the previous node.\n       * @example editor.commands.splitBlock()\n       * @example editor.commands.splitBlock({ keepMarks: true })\n       */\n      splitBlock: (options?: { keepMarks?: boolean }) => ReturnType\n    }\n  }\n}\n\nexport const splitBlock: RawCommands['splitBlock'] = ({ keepMarks = true } = {}) => ({\n  tr, state, dispatch, editor,\n}) => {\n  const { selection, doc } = tr\n  const { $from, $to } = selection\n  const extensionAttributes = editor.extensionManager.attributes\n  const newAttributes = getSplittedAttributes(\n    extensionAttributes,\n    $from.node().type.name,\n    $from.node().attrs,\n  )\n\n  if (selection instanceof NodeSelection && selection.node.isBlock) {\n    if (!$from.parentOffset || !canSplit(doc, $from.pos)) {\n      return false\n    }\n\n    if (dispatch) {\n      if (keepMarks) {\n        ensureMarks(state, editor.extensionManager.splittableMarks)\n      }\n\n      tr.split($from.pos).scrollIntoView()\n    }\n\n    return true\n  }\n\n  if (!$from.parent.isBlock) {\n    return false\n  }\n\n  const atEnd = $to.parentOffset === $to.parent.content.size\n\n  const deflt = $from.depth === 0\n    ? undefined\n    : defaultBlockAt($from.node(-1).contentMatchAt($from.indexAfter(-1)))\n\n  let types = atEnd && deflt\n    ? [\n      {\n        type: deflt,\n        attrs: newAttributes,\n      },\n    ]\n    : undefined\n\n  let can = canSplit(tr.doc, tr.mapping.map($from.pos), 1, types)\n\n  if (\n    !types\n      && !can\n      && canSplit(tr.doc, tr.mapping.map($from.pos), 1, deflt ? [{ type: deflt }] : undefined)\n  ) {\n    can = true\n    types = deflt\n      ? [\n        {\n          type: deflt,\n          attrs: newAttributes,\n        },\n      ]\n      : undefined\n  }\n\n  if (dispatch) {\n    if (can) {\n      if (selection instanceof TextSelection) {\n        tr.deleteSelection()\n      }\n\n      tr.split(tr.mapping.map($from.pos), 1, types)\n\n      if (deflt && !atEnd && !$from.parentOffset && $from.parent.type !== deflt) {\n        const first = tr.mapping.map($from.before())\n        const $first = tr.doc.resolve(first)\n\n        if ($from.node(-1).canReplaceWith($first.index(), $first.index() + 1, deflt)) {\n          tr.setNodeMarkup(tr.mapping.map($from.before()), deflt)\n        }\n      }\n    }\n\n    if (keepMarks) {\n      ensureMarks(state, editor.extensionManager.splittableMarks)\n    }\n\n    tr.scrollIntoView()\n  }\n\n  return can\n}\n", "import {\n  Fragment, Node as ProseMirrorNode, NodeType, Slice,\n} from '@tiptap/pm/model'\nimport { TextSelection } from '@tiptap/pm/state'\nimport { canSplit } from '@tiptap/pm/transform'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { getSplittedAttributes } from '../helpers/getSplittedAttributes.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    splitListItem: {\n      /**\n       * Splits one list item into two list items.\n       * @param typeOrName The type or name of the node.\n       * @param overrideAttrs The attributes to ensure on the new node.\n       * @example editor.commands.splitListItem('listItem')\n       */\n      splitListItem: (typeOrName: string | NodeType, overrideAttrs?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const splitListItem: RawCommands['splitListItem'] = (typeOrName, overrideAttrs = {}) => ({\n  tr, state, dispatch, editor,\n}) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const { $from, $to } = state.selection\n\n  // @ts-ignore\n  // eslint-disable-next-line\n    const node: ProseMirrorNode = state.selection.node\n\n  if ((node && node.isBlock) || $from.depth < 2 || !$from.sameParent($to)) {\n    return false\n  }\n\n  const grandParent = $from.node(-1)\n\n  if (grandParent.type !== type) {\n    return false\n  }\n\n  const extensionAttributes = editor.extensionManager.attributes\n\n  if ($from.parent.content.size === 0 && $from.node(-1).childCount === $from.indexAfter(-1)) {\n    // In an empty block. If this is a nested list, the wrapping\n    // list item should be split. Otherwise, bail out and let next\n    // command handle lifting.\n    if (\n      $from.depth === 2\n        || $from.node(-3).type !== type\n        || $from.index(-2) !== $from.node(-2).childCount - 1\n    ) {\n      return false\n    }\n\n    if (dispatch) {\n      let wrap = Fragment.empty\n      // eslint-disable-next-line\n        const depthBefore = $from.index(-1) ? 1 : $from.index(-2) ? 2 : 3\n\n      // Build a fragment containing empty versions of the structure\n      // from the outer list item to the parent node of the cursor\n      for (let d = $from.depth - depthBefore; d >= $from.depth - 3; d -= 1) {\n        wrap = Fragment.from($from.node(d).copy(wrap))\n      }\n\n      // eslint-disable-next-line\n        const depthAfter = $from.indexAfter(-1) < $from.node(-2).childCount ? 1 : $from.indexAfter(-2) < $from.node(-3).childCount ? 2 : 3\n\n      // Add a second list item with an empty default start node\n      const newNextTypeAttributes = {\n        ...getSplittedAttributes(\n          extensionAttributes,\n          $from.node().type.name,\n          $from.node().attrs,\n        ),\n        ...overrideAttrs,\n      }\n      const nextType = type.contentMatch.defaultType?.createAndFill(newNextTypeAttributes) || undefined\n\n      wrap = wrap.append(Fragment.from(type.createAndFill(null, nextType) || undefined))\n\n      const start = $from.before($from.depth - (depthBefore - 1))\n\n      tr.replace(start, $from.after(-depthAfter), new Slice(wrap, 4 - depthBefore, 0))\n\n      let sel = -1\n\n      tr.doc.nodesBetween(start, tr.doc.content.size, (n, pos) => {\n        if (sel > -1) {\n          return false\n        }\n\n        if (n.isTextblock && n.content.size === 0) {\n          sel = pos + 1\n        }\n      })\n\n      if (sel > -1) {\n        tr.setSelection(TextSelection.near(tr.doc.resolve(sel)))\n      }\n\n      tr.scrollIntoView()\n    }\n\n    return true\n  }\n\n  const nextType = $to.pos === $from.end() ? grandParent.contentMatchAt(0).defaultType : null\n\n  const newTypeAttributes = {\n    ...getSplittedAttributes(\n      extensionAttributes,\n      grandParent.type.name,\n      grandParent.attrs,\n    ),\n    ...overrideAttrs,\n  }\n  const newNextTypeAttributes = {\n    ...getSplittedAttributes(\n      extensionAttributes,\n      $from.node().type.name,\n      $from.node().attrs,\n    ),\n    ...overrideAttrs,\n  }\n\n  tr.delete($from.pos, $to.pos)\n\n  const types = nextType\n    ? [\n      { type, attrs: newTypeAttributes },\n      { type: nextType, attrs: newNextTypeAttributes },\n    ]\n    : [{ type, attrs: newTypeAttributes }]\n\n  if (!canSplit(tr.doc, $from.pos, 2)) {\n    return false\n  }\n\n  if (dispatch) {\n    const { selection, storedMarks } = state\n    const { splittableMarks } = editor.extensionManager\n    const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n    tr.split($from.pos, 2, types).scrollIntoView()\n\n    if (!marks || !dispatch) {\n      return true\n    }\n\n    const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n    tr.ensureMarks(filteredMarks)\n  }\n\n  return true\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { Transaction } from '@tiptap/pm/state'\nimport { canJoin } from '@tiptap/pm/transform'\n\nimport { findParentNode } from '../helpers/findParentNode.js'\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isList } from '../helpers/isList.js'\nimport { RawCommands } from '../types.js'\n\nconst joinListBackwards = (tr: Transaction, listType: NodeType): boolean => {\n  const list = findParentNode(node => node.type === listType)(tr.selection)\n\n  if (!list) {\n    return true\n  }\n\n  const before = tr.doc.resolve(Math.max(0, list.pos - 1)).before(list.depth)\n\n  if (before === undefined) {\n    return true\n  }\n\n  const nodeBefore = tr.doc.nodeAt(before)\n  const canJoinBackwards = list.node.type === nodeBefore?.type && canJoin(tr.doc, list.pos)\n\n  if (!canJoinBackwards) {\n    return true\n  }\n\n  tr.join(list.pos)\n\n  return true\n}\n\nconst joinListForwards = (tr: Transaction, listType: NodeType): boolean => {\n  const list = findParentNode(node => node.type === listType)(tr.selection)\n\n  if (!list) {\n    return true\n  }\n\n  const after = tr.doc.resolve(list.start).after(list.depth)\n\n  if (after === undefined) {\n    return true\n  }\n\n  const nodeAfter = tr.doc.nodeAt(after)\n  const canJoinForwards = list.node.type === nodeAfter?.type && canJoin(tr.doc, after)\n\n  if (!canJoinForwards) {\n    return true\n  }\n\n  tr.join(after)\n\n  return true\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleList: {\n      /**\n       * Toggle between different list types.\n       * @param listTypeOrName The type or name of the list.\n       * @param itemTypeOrName The type or name of the list item.\n       * @param keepMarks Keep marks when toggling.\n       * @param attributes Attributes for the new list.\n       * @example editor.commands.toggleList('bulletList', 'listItem')\n       */\n      toggleList: (listTypeOrName: string | NodeType, itemTypeOrName: string | NodeType, keepMarks?: boolean, attributes?: Record<string, any>) => ReturnType;\n    }\n  }\n}\n\nexport const toggleList: RawCommands['toggleList'] = (listTypeOrName, itemTypeOrName, keepMarks, attributes = {}) => ({\n  editor, tr, state, dispatch, chain, commands, can,\n}) => {\n  const { extensions, splittableMarks } = editor.extensionManager\n  const listType = getNodeType(listTypeOrName, state.schema)\n  const itemType = getNodeType(itemTypeOrName, state.schema)\n  const { selection, storedMarks } = state\n  const { $from, $to } = selection\n  const range = $from.blockRange($to)\n\n  const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n  if (!range) {\n    return false\n  }\n\n  const parentList = findParentNode(node => isList(node.type.name, extensions))(selection)\n\n  if (range.depth >= 1 && parentList && range.depth - parentList.depth <= 1) {\n    // remove list\n    if (parentList.node.type === listType) {\n      return commands.liftListItem(itemType)\n    }\n\n    // change list type\n    if (\n      isList(parentList.node.type.name, extensions)\n        && listType.validContent(parentList.node.content)\n        && dispatch\n    ) {\n      return chain()\n        .command(() => {\n          tr.setNodeMarkup(parentList.pos, listType)\n\n          return true\n        })\n        .command(() => joinListBackwards(tr, listType))\n        .command(() => joinListForwards(tr, listType))\n        .run()\n    }\n  }\n  if (!keepMarks || !marks || !dispatch) {\n\n    return chain()\n      // try to convert node to default node if needed\n      .command(() => {\n        const canWrapInList = can().wrapInList(listType, attributes)\n\n        if (canWrapInList) {\n          return true\n        }\n\n        return commands.clearNodes()\n      })\n      .wrapInList(listType, attributes)\n      .command(() => joinListBackwards(tr, listType))\n      .command(() => joinListForwards(tr, listType))\n      .run()\n  }\n\n  return (\n    chain()\n    // try to convert node to default node if needed\n      .command(() => {\n        const canWrapInList = can().wrapInList(listType, attributes)\n\n        const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n        tr.ensureMarks(filteredMarks)\n\n        if (canWrapInList) {\n          return true\n        }\n\n        return commands.clearNodes()\n      })\n      .wrapInList(listType, attributes)\n      .command(() => joinListBackwards(tr, listType))\n      .command(() => joinListForwards(tr, listType))\n      .run()\n  )\n}\n", "import { MarkType } from '@tiptap/pm/model'\n\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { isMarkActive } from '../helpers/isMarkActive.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleMark: {\n      /**\n       * Toggle a mark on and off.\n       * @param typeOrName The mark type or name.\n       * @param attributes The attributes of the mark.\n       * @param options.extendEmptyMarkRange Removes the mark even across the current selection. Defaults to `false`.\n       * @example editor.commands.toggleMark('bold')\n       */\n      toggleMark: (\n        /**\n         * The mark type or name.\n         */\n        typeOrName: string | MarkType,\n\n        /**\n         * The attributes of the mark.\n         */\n        attributes?: Record<string, any>,\n\n        options?: {\n          /**\n           * Removes the mark even across the current selection. Defaults to `false`.\n           */\n          extendEmptyMarkRange?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nexport const toggleMark: RawCommands['toggleMark'] = (typeOrName, attributes = {}, options = {}) => ({ state, commands }) => {\n  const { extendEmptyMarkRange = false } = options\n  const type = getMarkType(typeOrName, state.schema)\n  const isActive = isMarkActive(state, type, attributes)\n\n  if (isActive) {\n    return commands.unsetMark(type, { extendEmptyMarkRange })\n  }\n\n  return commands.setMark(type, attributes)\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isNodeActive } from '../helpers/isNodeActive.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleNode: {\n      /**\n       * Toggle a node with another node.\n       * @param typeOrName The type or name of the node.\n       * @param toggleTypeOrName The type or name of the node to toggle.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.toggleNode('heading', 'paragraph')\n       */\n      toggleNode: (\n        typeOrName: string | NodeType,\n        toggleTypeOrName: string | NodeType,\n        attributes?: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const toggleNode: RawCommands['toggleNode'] = (typeOrName, toggleTypeOrName, attributes = {}) => ({ state, commands }) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const toggleType = getNodeType(toggleTypeOrName, state.schema)\n  const isActive = isNodeActive(state, type, attributes)\n\n  let attributesToCopy: Record<string, any> | undefined\n\n  if (state.selection.$anchor.sameParent(state.selection.$head)) {\n    // only copy attributes if the selection is pointing to a node of the same type\n    attributesToCopy = state.selection.$anchor.parent.attrs\n  }\n\n  if (isActive) {\n    return commands.setNode(toggleType, attributesToCopy)\n  }\n\n  // If the node is not active, we want to set the new node type with the given attributes\n  // Copying over the attributes from the current node if the selection is pointing to a node of the same type\n  return commands.setNode(type, { ...attributesToCopy, ...attributes })\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { isNodeActive } from '../helpers/isNodeActive.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    toggleWrap: {\n      /**\n       * Wraps nodes in another node, or removes an existing wrap.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.toggleWrap('blockquote')\n       */\n      toggleWrap: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const toggleWrap: RawCommands['toggleWrap'] = (typeOrName, attributes = {}) => ({ state, commands }) => {\n  const type = getNodeType(typeOrName, state.schema)\n  const isActive = isNodeActive(state, type, attributes)\n\n  if (isActive) {\n    return commands.lift(type)\n  }\n\n  return commands.wrapIn(type, attributes)\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    undoInputRule: {\n      /**\n       * Undo an input rule.\n       * @example editor.commands.undoInputRule()\n       */\n      undoInputRule: () => ReturnType,\n    }\n  }\n}\n\nexport const undoInputRule: RawCommands['undoInputRule'] = () => ({ state, dispatch }) => {\n  const plugins = state.plugins\n\n  for (let i = 0; i < plugins.length; i += 1) {\n    const plugin = plugins[i]\n    let undoable\n\n    // @ts-ignore\n    // eslint-disable-next-line\n    if (plugin.spec.isInputRules && (undoable = plugin.getState(state))) {\n      if (dispatch) {\n        const tr = state.tr\n        const toUndo = undoable.transform\n\n        for (let j = toUndo.steps.length - 1; j >= 0; j -= 1) {\n          tr.step(toUndo.steps[j].invert(toUndo.docs[j]))\n        }\n\n        if (undoable.text) {\n          const marks = tr.doc.resolve(undoable.from).marks()\n\n          tr.replaceWith(undoable.from, undoable.to, state.schema.text(undoable.text, marks))\n        } else {\n          tr.delete(undoable.from, undoable.to)\n        }\n      }\n\n      return true\n    }\n  }\n\n  return false\n}\n", "import { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    unsetAllMarks: {\n      /**\n       * Remove all marks in the current selection.\n       * @example editor.commands.unsetAllMarks()\n       */\n      unsetAllMarks: () => ReturnType,\n    }\n  }\n}\n\nexport const unsetAllMarks: RawCommands['unsetAllMarks'] = () => ({ tr, dispatch }) => {\n  const { selection } = tr\n  const { empty, ranges } = selection\n\n  if (empty) {\n    return true\n  }\n\n  if (dispatch) {\n    ranges.forEach(range => {\n      tr.removeMark(range.$from.pos, range.$to.pos)\n    })\n  }\n\n  return true\n}\n", "import { MarkType } from '@tiptap/pm/model'\n\nimport { getMarkRange } from '../helpers/getMarkRange.js'\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    unsetMark: {\n      /**\n       * Remove all marks in the current selection.\n       * @param typeOrName The mark type or name.\n       * @param options.extendEmptyMarkRange Removes the mark even across the current selection. Defaults to `false`.\n       * @example editor.commands.unsetMark('bold')\n       */\n      unsetMark: (\n        /**\n         * The mark type or name.\n         */\n        typeOrName: string | MarkType,\n\n        options?: {\n          /**\n           * Removes the mark even across the current selection. Defaults to `false`.\n           */\n          extendEmptyMarkRange?: boolean\n        },\n      ) => ReturnType\n    }\n  }\n}\n\nexport const unsetMark: RawCommands['unsetMark'] = (typeOrName, options = {}) => ({ tr, state, dispatch }) => {\n  const { extendEmptyMarkRange = false } = options\n  const { selection } = tr\n  const type = getMarkType(typeOrName, state.schema)\n  const { $from, empty, ranges } = selection\n\n  if (!dispatch) {\n    return true\n  }\n\n  if (empty && extendEmptyMarkRange) {\n    let { from, to } = selection\n    const attrs = $from.marks().find(mark => mark.type === type)?.attrs\n    const range = getMarkRange($from, type, attrs)\n\n    if (range) {\n      from = range.from\n      to = range.to\n    }\n\n    tr.removeMark(from, to, type)\n  } else {\n    ranges.forEach(range => {\n      tr.removeMark(range.$from.pos, range.$to.pos, type)\n    })\n  }\n\n  tr.removeStoredMark(type)\n\n  return true\n}\n", "import {\n  Mark, MarkType, Node, NodeType,\n} from '@tiptap/pm/model'\nimport { SelectionRange } from '@tiptap/pm/state'\n\nimport { getMarkType } from '../helpers/getMarkType.js'\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { getSchemaTypeNameByName } from '../helpers/getSchemaTypeNameByName.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    updateAttributes: {\n      /**\n       * Update attributes of a node or mark.\n       * @param typeOrName The type or name of the node or mark.\n       * @param attributes The attributes of the node or mark.\n       * @example editor.commands.updateAttributes('mention', { userId: \"2\" })\n       */\n      updateAttributes: (\n        /**\n         * The type or name of the node or mark.\n         */\n        typeOrName: string | NodeType | MarkType,\n\n        /**\n         * The attributes of the node or mark.\n         */\n        attributes: Record<string, any>,\n      ) => ReturnType\n    }\n  }\n}\n\nexport const updateAttributes: RawCommands['updateAttributes'] = (typeOrName, attributes = {}) => ({ tr, state, dispatch }) => {\n\n  let nodeType: NodeType | null = null\n  let markType: MarkType | null = null\n\n  const schemaType = getSchemaTypeNameByName(\n    typeof typeOrName === 'string' ? typeOrName : typeOrName.name,\n    state.schema,\n  )\n\n  if (!schemaType) {\n    return false\n  }\n\n  if (schemaType === 'node') {\n    nodeType = getNodeType(typeOrName as NodeType, state.schema)\n  }\n\n  if (schemaType === 'mark') {\n    markType = getMarkType(typeOrName as MarkType, state.schema)\n  }\n\n  if (dispatch) {\n    tr.selection.ranges.forEach((range: SelectionRange) => {\n\n      const from = range.$from.pos\n      const to = range.$to.pos\n\n      let lastPos: number | undefined\n      let lastNode: Node | undefined\n      let trimmedFrom: number\n      let trimmedTo: number\n\n      if (tr.selection.empty) {\n        state.doc.nodesBetween(from, to, (node: Node, pos: number) => {\n\n          if (nodeType && nodeType === node.type) {\n            trimmedFrom = Math.max(pos, from)\n            trimmedTo = Math.min(pos + node.nodeSize, to)\n            lastPos = pos\n            lastNode = node\n          }\n        })\n      } else {\n        state.doc.nodesBetween(from, to, (node: Node, pos: number) => {\n\n          if (pos < from && nodeType && nodeType === node.type) {\n            trimmedFrom = Math.max(pos, from)\n            trimmedTo = Math.min(pos + node.nodeSize, to)\n            lastPos = pos\n            lastNode = node\n          }\n\n          if (pos >= from && pos <= to) {\n\n            if (nodeType && nodeType === node.type) {\n              tr.setNodeMarkup(pos, undefined, {\n                ...node.attrs,\n                ...attributes,\n              })\n            }\n\n            if (markType && node.marks.length) {\n              node.marks.forEach((mark: Mark) => {\n\n                if (markType === mark.type) {\n                  const trimmedFrom2 = Math.max(pos, from)\n                  const trimmedTo2 = Math.min(pos + node.nodeSize, to)\n\n                  tr.addMark(\n                    trimmedFrom2,\n                    trimmedTo2,\n                    markType.create({\n                      ...mark.attrs,\n                      ...attributes,\n                    }),\n                  )\n                }\n              })\n            }\n          }\n        })\n      }\n\n      if (lastNode) {\n\n        if (lastPos !== undefined) {\n          tr.setNodeMarkup(lastPos, undefined, {\n            ...lastNode.attrs,\n            ...attributes,\n          })\n        }\n\n        if (markType && lastNode.marks.length) {\n          lastNode.marks.forEach((mark: Mark) => {\n\n            if (markType === mark.type) {\n              tr.addMark(\n                trimmedFrom,\n                trimmedTo,\n                markType.create({\n                  ...mark.attrs,\n                  ...attributes,\n                }),\n              )\n            }\n          })\n        }\n      }\n    })\n  }\n\n  return true\n}\n", "import { wrapIn as originalWrapIn } from '@tiptap/pm/commands'\nimport { NodeType } from '@tiptap/pm/model'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    wrapIn: {\n      /**\n       * Wraps nodes in another node.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.wrapIn('blockquote')\n       */\n      wrapIn: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const wrapIn: RawCommands['wrapIn'] = (typeOrName, attributes = {}) => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  return originalWrapIn(type, attributes)(state, dispatch)\n}\n", "import { NodeType } from '@tiptap/pm/model'\nimport { wrapInList as originalWrapInList } from '@tiptap/pm/schema-list'\n\nimport { getNodeType } from '../helpers/getNodeType.js'\nimport { RawCommands } from '../types.js'\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    wrapInList: {\n      /**\n       * Wrap a node in a list.\n       * @param typeOrName The type or name of the node.\n       * @param attributes The attributes of the node.\n       * @example editor.commands.wrapInList('bulletList')\n       */\n      wrapInList: (typeOrName: string | NodeType, attributes?: Record<string, any>) => ReturnType\n    }\n  }\n}\n\nexport const wrapInList: RawCommands['wrapInList'] = (typeOrName, attributes = {}) => ({ state, dispatch }) => {\n  const type = getNodeType(typeOrName, state.schema)\n\n  return originalWrapInList(type, attributes)(state, dispatch)\n}\n", "import * as commands from '../commands/index.js'\nimport { Extension } from '../Extension.js'\n\nexport * from '../commands/index.js'\n\nexport const Commands = Extension.create({\n  name: 'commands',\n\n  addCommands() {\n    return {\n      ...commands,\n    }\n  },\n})\n", "import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Drop = Extension.create({\n  name: 'drop',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('tiptapDrop'),\n\n        props: {\n          handleDrop: (_, e, slice, moved) => {\n            this.editor.emit('drop', {\n              editor: this.editor,\n              event: e,\n              slice,\n              moved,\n            })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Editable = Extension.create({\n  name: 'editable',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('editable'),\n        props: {\n          editable: () => this.editor.options.editable,\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const focusEventsPluginKey = new PluginKey('focusEvents')\n\nexport const FocusEvents = Extension.create({\n  name: 'focusEvents',\n\n  addProseMirrorPlugins() {\n    const { editor } = this\n\n    return [\n      new Plugin({\n        key: focusEventsPluginKey,\n        props: {\n          handleDOMEvents: {\n            focus: (view, event: Event) => {\n              editor.isFocused = true\n\n              const transaction = editor.state.tr\n                .setMeta('focus', { event })\n                .setMeta('addToHistory', false)\n\n              view.dispatch(transaction)\n\n              return false\n            },\n            blur: (view, event: Event) => {\n              editor.isFocused = false\n\n              const transaction = editor.state.tr\n                .setMeta('blur', { event })\n                .setMeta('addToHistory', false)\n\n              view.dispatch(transaction)\n\n              return false\n            },\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON><PERSON>, <PERSON> } from '@tiptap/pm/state'\n\nimport { CommandManager } from '../CommandManager.js'\nimport { Extension } from '../Extension.js'\nimport { createChainableState } from '../helpers/createChainableState.js'\nimport { isNodeEmpty } from '../helpers/isNodeEmpty.js'\nimport { isiOS } from '../utilities/isiOS.js'\nimport { isMacOS } from '../utilities/isMacOS.js'\n\nexport const Keymap = Extension.create({\n  name: 'keymap',\n\n  addKeyboardShortcuts() {\n    const handleBackspace = () => this.editor.commands.first(({ commands }) => [\n      () => commands.undoInputRule(),\n\n      // maybe convert first text block node to default node\n      () => commands.command(({ tr }) => {\n        const { selection, doc } = tr\n        const { empty, $anchor } = selection\n        const { pos, parent } = $anchor\n        const $parentPos = $anchor.parent.isTextblock && pos > 0 ? tr.doc.resolve(pos - 1) : $anchor\n        const parentIsIsolating = $parentPos.parent.type.spec.isolating\n\n        const parentPos = $anchor.pos - $anchor.parentOffset\n\n        const isAtStart = (parentIsIsolating && $parentPos.parent.childCount === 1)\n          ? parentPos === $anchor.pos\n          : Selection.atStart(doc).from === pos\n\n        if (\n          !empty\n          || !parent.type.isTextblock\n          || parent.textContent.length\n          || !isAtStart\n          || (isAtStart && $anchor.parent.type.name === 'paragraph') // prevent clearNodes when no nodes to clear, otherwise history stack is appended\n        ) {\n          return false\n        }\n\n        return commands.clearNodes()\n      }),\n\n      () => commands.deleteSelection(),\n      () => commands.joinBackward(),\n      () => commands.selectNodeBackward(),\n    ])\n\n    const handleDelete = () => this.editor.commands.first(({ commands }) => [\n      () => commands.deleteSelection(),\n      () => commands.deleteCurrentNode(),\n      () => commands.joinForward(),\n      () => commands.selectNodeForward(),\n    ])\n\n    const handleEnter = () => this.editor.commands.first(({ commands }) => [\n      () => commands.newlineInCode(),\n      () => commands.createParagraphNear(),\n      () => commands.liftEmptyBlock(),\n      () => commands.splitBlock(),\n    ])\n\n    const baseKeymap = {\n      Enter: handleEnter,\n      'Mod-Enter': () => this.editor.commands.exitCode(),\n      Backspace: handleBackspace,\n      'Mod-Backspace': handleBackspace,\n      'Shift-Backspace': handleBackspace,\n      Delete: handleDelete,\n      'Mod-Delete': handleDelete,\n      'Mod-a': () => this.editor.commands.selectAll(),\n    }\n\n    const pcKeymap = {\n      ...baseKeymap,\n    }\n\n    const macKeymap = {\n      ...baseKeymap,\n      'Ctrl-h': handleBackspace,\n      'Alt-Backspace': handleBackspace,\n      'Ctrl-d': handleDelete,\n      'Ctrl-Alt-Backspace': handleDelete,\n      'Alt-Delete': handleDelete,\n      'Alt-d': handleDelete,\n      'Ctrl-a': () => this.editor.commands.selectTextblockStart(),\n      'Ctrl-e': () => this.editor.commands.selectTextblockEnd(),\n    }\n\n    if (isiOS() || isMacOS()) {\n      return macKeymap\n    }\n\n    return pcKeymap\n  },\n\n  addProseMirrorPlugins() {\n    return [\n      // With this plugin we check if the whole document was selected and deleted.\n      // In this case we will additionally call `clearNodes()` to convert e.g. a heading\n      // to a paragraph if necessary.\n      // This is an alternative to ProseMirror's `AllSelection`, which doesn’t work well\n      // with many other commands.\n      new Plugin({\n        key: new PluginKey('clearDocument'),\n        appendTransaction: (transactions, oldState, newState) => {\n          if (transactions.some(tr => tr.getMeta('composition'))) {\n            return\n          }\n\n          const docChanges = transactions.some(transaction => transaction.docChanged)\n            && !oldState.doc.eq(newState.doc)\n\n          const ignoreTr = transactions.some(transaction => transaction.getMeta('preventClearDocument'))\n\n          if (!docChanges || ignoreTr) {\n            return\n          }\n\n          const { empty, from, to } = oldState.selection\n          const allFrom = Selection.atStart(oldState.doc).from\n          const allEnd = Selection.atEnd(oldState.doc).to\n          const allWasSelected = from === allFrom && to === allEnd\n\n          if (empty || !allWasSelected) {\n            return\n          }\n\n          const isEmpty = isNodeEmpty(newState.doc)\n\n          if (!isEmpty) {\n            return\n          }\n\n          const tr = newState.tr\n          const state = createChainableState({\n            state: newState,\n            transaction: tr,\n          })\n          const { commands } = new CommandManager({\n            editor: this.editor,\n            state,\n          })\n\n          commands.clearNodes()\n\n          if (!tr.steps.length) {\n            return\n          }\n\n          return tr\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Paste = Extension.create({\n  name: 'paste',\n\n  addProseMirrorPlugins() {\n\n    return [\n      new Plugin({\n        key: new PluginKey('tiptapPaste'),\n\n        props: {\n          handlePaste: (_view, e, slice) => {\n            this.editor.emit('paste', {\n              editor: this.editor,\n              event: e,\n              slice,\n            })\n          },\n        },\n      }),\n    ]\n  },\n})\n", "import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'\n\nimport { Extension } from '../Extension.js'\n\nexport const Tabindex = Extension.create({\n  name: 'tabindex',\n\n  addProseMirrorPlugins() {\n    return [\n      new Plugin({\n        key: new PluginKey('tabindex'),\n        props: {\n          attributes: (): { [name: string]: string; } => (this.editor.isEditable ? { tabindex: '0' } : {}),\n        },\n      }),\n    ]\n  },\n})\n", "import {\n  Fragment, Node, ResolvedPos,\n} from '@tiptap/pm/model'\n\nimport { Editor } from './Editor.js'\nimport { Content, Range } from './types.js'\n\nexport class NodePos {\n  private resolvedPos: ResolvedPos\n\n  private isBlock: boolean\n\n  private editor: Editor\n\n  private get name(): string {\n    return this.node.type.name\n  }\n\n  constructor(pos: ResolvedPos, editor: Editor, isBlock = false, node: Node | null = null) {\n    this.isBlock = isBlock\n    this.resolvedPos = pos\n    this.editor = editor\n    this.currentNode = node\n  }\n\n  private currentNode: Node | null = null\n\n  get node(): Node {\n    return this.currentNode || this.resolvedPos.node()\n  }\n\n  get element(): HTMLElement {\n    return this.editor.view.domAtPos(this.pos).node as HTMLElement\n  }\n\n  public actualDepth: number | null = null\n\n  get depth(): number {\n    return this.actualDepth ?? this.resolvedPos.depth\n  }\n\n  get pos(): number {\n    return this.resolvedPos.pos\n  }\n\n  get content(): Fragment {\n    return this.node.content\n  }\n\n  set content(content: Content) {\n    let from = this.from\n    let to = this.to\n\n    if (this.isBlock) {\n      if (this.content.size === 0) {\n        console.error(`You can’t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`)\n        return\n      }\n\n      from = this.from + 1\n      to = this.to - 1\n    }\n\n    this.editor.commands.insertContentAt({ from, to }, content)\n  }\n\n  get attributes(): { [key: string]: any } {\n    return this.node.attrs\n  }\n\n  get textContent(): string {\n    return this.node.textContent\n  }\n\n  get size(): number {\n    return this.node.nodeSize\n  }\n\n  get from(): number {\n    if (this.isBlock) {\n      return this.pos\n    }\n\n    return this.resolvedPos.start(this.resolvedPos.depth)\n  }\n\n  get range(): Range {\n    return {\n      from: this.from,\n      to: this.to,\n    }\n  }\n\n  get to(): number {\n    if (this.isBlock) {\n      return this.pos + this.size\n    }\n\n    return this.resolvedPos.end(this.resolvedPos.depth) + (this.node.isText ? 0 : 1)\n  }\n\n  get parent(): NodePos | null {\n    if (this.depth === 0) {\n      return null\n    }\n\n    const parentPos = this.resolvedPos.start(this.resolvedPos.depth - 1)\n    const $pos = this.resolvedPos.doc.resolve(parentPos)\n\n    return new NodePos($pos, this.editor)\n  }\n\n  get before(): NodePos | null {\n    let $pos = this.resolvedPos.doc.resolve(this.from - (this.isBlock ? 1 : 2))\n\n    if ($pos.depth !== this.depth) {\n      $pos = this.resolvedPos.doc.resolve(this.from - 3)\n    }\n\n    return new NodePos($pos, this.editor)\n  }\n\n  get after(): NodePos | null {\n    let $pos = this.resolvedPos.doc.resolve(this.to + (this.isBlock ? 2 : 1))\n\n    if ($pos.depth !== this.depth) {\n      $pos = this.resolvedPos.doc.resolve(this.to + 3)\n    }\n\n    return new NodePos($pos, this.editor)\n  }\n\n  get children(): NodePos[] {\n    const children: NodePos[] = []\n\n    this.node.content.forEach((node, offset) => {\n      const isBlock = node.isBlock && !node.isTextblock\n      const isNonTextAtom = node.isAtom && !node.isText\n\n      const targetPos = this.pos + offset + (isNonTextAtom ? 0 : 1)\n      const $pos = this.resolvedPos.doc.resolve(targetPos)\n\n      if (!isBlock && $pos.depth <= this.depth) {\n        return\n      }\n\n      const childNodePos = new NodePos($pos, this.editor, isBlock, isBlock ? node : null)\n\n      if (isBlock) {\n        childNodePos.actualDepth = this.depth + 1\n      }\n\n      children.push(new NodePos($pos, this.editor, isBlock, isBlock ? node : null))\n    })\n\n    return children\n  }\n\n  get firstChild(): NodePos | null {\n    return this.children[0] || null\n  }\n\n  get lastChild(): NodePos | null {\n    const children = this.children\n\n    return children[children.length - 1] || null\n  }\n\n  closest(selector: string, attributes: { [key: string]: any } = {}): NodePos | null {\n    let node: NodePos | null = null\n    let currentNode = this.parent\n\n    while (currentNode && !node) {\n      if (currentNode.node.type.name === selector) {\n        if (Object.keys(attributes).length > 0) {\n          const nodeAttributes = currentNode.node.attrs\n          const attrKeys = Object.keys(attributes)\n\n          for (let index = 0; index < attrKeys.length; index += 1) {\n            const key = attrKeys[index]\n\n            if (nodeAttributes[key] !== attributes[key]) {\n              break\n            }\n          }\n        } else {\n          node = currentNode\n        }\n      }\n\n      currentNode = currentNode.parent\n    }\n\n    return node\n  }\n\n  querySelector(selector: string, attributes: { [key: string]: any } = {}): NodePos | null {\n    return this.querySelectorAll(selector, attributes, true)[0] || null\n  }\n\n  querySelectorAll(selector: string, attributes: { [key: string]: any } = {}, firstItemOnly = false): NodePos[] {\n    let nodes: NodePos[] = []\n\n    if (!this.children || this.children.length === 0) {\n      return nodes\n    }\n    const attrKeys = Object.keys(attributes)\n\n    /**\n     * Finds all children recursively that match the selector and attributes\n     * If firstItemOnly is true, it will return the first item found\n     */\n    this.children.forEach(childPos => {\n      // If we already found a node and we only want the first item, we dont need to keep going\n      if (firstItemOnly && nodes.length > 0) {\n        return\n      }\n\n      if (childPos.node.type.name === selector) {\n        const doesAllAttributesMatch = attrKeys.every(key => attributes[key] === childPos.node.attrs[key])\n\n        if (doesAllAttributesMatch) {\n          nodes.push(childPos)\n        }\n      }\n\n      // If we already found a node and we only want the first item, we can stop here and skip the recursion\n      if (firstItemOnly && nodes.length > 0) {\n        return\n      }\n\n      nodes = nodes.concat(childPos.querySelectorAll(selector, attributes, firstItemOnly))\n    })\n\n    return nodes\n  }\n\n  setAttribute(attributes: { [key: string]: any }) {\n    const { tr } = this.editor.state\n\n    tr.setNodeMarkup(this.from, undefined, {\n      ...this.node.attrs,\n      ...attributes,\n    })\n\n    this.editor.view.dispatch(tr)\n  }\n}\n", "export const style = `.ProseMirror {\n  position: relative;\n}\n\n.ProseMirror {\n  word-wrap: break-word;\n  white-space: pre-wrap;\n  white-space: break-spaces;\n  -webkit-font-variant-ligatures: none;\n  font-variant-ligatures: none;\n  font-feature-settings: \"liga\" 0; /* the above doesn't seem to work in Edge */\n}\n\n.ProseMirror [contenteditable=\"false\"] {\n  white-space: normal;\n}\n\n.ProseMirror [contenteditable=\"false\"] [contenteditable=\"true\"] {\n  white-space: pre-wrap;\n}\n\n.ProseMirror pre {\n  white-space: pre-wrap;\n}\n\nimg.ProseMirror-separator {\n  display: inline !important;\n  border: none !important;\n  margin: 0 !important;\n  width: 0 !important;\n  height: 0 !important;\n}\n\n.ProseMirror-gapcursor {\n  display: none;\n  pointer-events: none;\n  position: absolute;\n  margin: 0;\n}\n\n.ProseMirror-gapcursor:after {\n  content: \"\";\n  display: block;\n  position: absolute;\n  top: -2px;\n  width: 20px;\n  border-top: 1px solid black;\n  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;\n}\n\n@keyframes ProseMirror-cursor-blink {\n  to {\n    visibility: hidden;\n  }\n}\n\n.ProseMirror-hideselection *::selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection *::-moz-selection {\n  background: transparent;\n}\n\n.ProseMirror-hideselection * {\n  caret-color: transparent;\n}\n\n.ProseMirror-focused .ProseMirror-gapcursor {\n  display: block;\n}\n\n.tippy-box[data-animation=fade][data-state=hidden] {\n  opacity: 0\n}`\n", "export function createStyleTag(style: string, nonce?: string, suffix?: string): HTMLStyleElement {\n  const tiptapStyleTag = (<HTMLStyleElement>document.querySelector(`style[data-tiptap-style${suffix ? `-${suffix}` : ''}]`))\n\n  if (tiptapStyleTag !== null) {\n    return tiptapStyleTag\n  }\n\n  const styleNode = document.createElement('style')\n\n  if (nonce) {\n    styleNode.setAttribute('nonce', nonce)\n  }\n\n  styleNode.setAttribute(`data-tiptap-style${suffix ? `-${suffix}` : ''}`, '')\n  styleNode.innerHTML = style\n  document.getElementsByTagName('head')[0].appendChild(styleNode)\n\n  return styleNode\n}\n", "/* eslint-disable @typescript-eslint/no-empty-object-type */\nimport {\n  MarkType,\n  Node as ProseMirrorNode,\n  NodeType,\n  Schema,\n} from '@tiptap/pm/model'\nimport {\n  EditorState, Plugin, PluginKey, Transaction,\n} from '@tiptap/pm/state'\nimport { EditorView } from '@tiptap/pm/view'\n\nimport { CommandManager } from './CommandManager.js'\nimport { EventEmitter } from './EventEmitter.js'\nimport { ExtensionManager } from './ExtensionManager.js'\nimport {\n  ClipboardTextSerializer, Commands, Drop, Editable, FocusEvents, Keymap, Paste,\n  Tabindex,\n} from './extensions/index.js'\nimport { createDocument } from './helpers/createDocument.js'\nimport { getAttributes } from './helpers/getAttributes.js'\nimport { getHTMLFromFragment } from './helpers/getHTMLFromFragment.js'\nimport { getText } from './helpers/getText.js'\nimport { getTextSerializersFromSchema } from './helpers/getTextSerializersFromSchema.js'\nimport { isActive } from './helpers/isActive.js'\nimport { isNodeEmpty } from './helpers/isNodeEmpty.js'\nimport { resolveFocusPosition } from './helpers/resolveFocusPosition.js'\nimport { NodePos } from './NodePos.js'\nimport { style } from './style.js'\nimport {\n  CanCommands,\n  ChainedCommands,\n  EditorEvents,\n  EditorOptions,\n  JSONContent,\n  SingleCommands,\n  TextSerializer,\n} from './types.js'\nimport { createStyleTag } from './utilities/createStyleTag.js'\nimport { isFunction } from './utilities/isFunction.js'\n\nexport * as extensions from './extensions/index.js'\n\n// @ts-ignore\nexport interface TiptapEditorHTMLElement extends HTMLElement {\n  editor?: Editor\n}\n\nexport class Editor extends EventEmitter<EditorEvents> {\n  private commandManager!: CommandManager\n\n  public extensionManager!: ExtensionManager\n\n  private css!: HTMLStyleElement\n\n  public schema!: Schema\n\n  public view!: EditorView\n\n  public isFocused = false\n\n  /**\n   * The editor is considered initialized after the `create` event has been emitted.\n   */\n  public isInitialized = false\n\n  public extensionStorage: Record<string, any> = {}\n\n  public options: EditorOptions = {\n    element: document.createElement('div'),\n    content: '',\n    injectCSS: true,\n    injectNonce: undefined,\n    extensions: [],\n    autofocus: false,\n    editable: true,\n    editorProps: {},\n    parseOptions: {},\n    coreExtensionOptions: {},\n    enableInputRules: true,\n    enablePasteRules: true,\n    enableCoreExtensions: true,\n    enableContentCheck: false,\n    onBeforeCreate: () => null,\n    onCreate: () => null,\n    onUpdate: () => null,\n    onSelectionUpdate: () => null,\n    onTransaction: () => null,\n    onFocus: () => null,\n    onBlur: () => null,\n    onDestroy: () => null,\n    onContentError: ({ error }) => { throw error },\n    onPaste: () => null,\n    onDrop: () => null,\n  }\n\n  constructor(options: Partial<EditorOptions> = {}) {\n    super()\n    this.setOptions(options)\n    this.createExtensionManager()\n    this.createCommandManager()\n    this.createSchema()\n    this.on('beforeCreate', this.options.onBeforeCreate)\n    this.emit('beforeCreate', { editor: this })\n    this.on('contentError', this.options.onContentError)\n    this.createView()\n    this.injectCSS()\n    this.on('create', this.options.onCreate)\n    this.on('update', this.options.onUpdate)\n    this.on('selectionUpdate', this.options.onSelectionUpdate)\n    this.on('transaction', this.options.onTransaction)\n    this.on('focus', this.options.onFocus)\n    this.on('blur', this.options.onBlur)\n    this.on('destroy', this.options.onDestroy)\n    this.on('drop', ({ event, slice, moved }) => this.options.onDrop(event, slice, moved))\n    this.on('paste', ({ event, slice }) => this.options.onPaste(event, slice))\n\n    window.setTimeout(() => {\n      if (this.isDestroyed) {\n        return\n      }\n\n      this.commands.focus(this.options.autofocus)\n      this.emit('create', { editor: this })\n      this.isInitialized = true\n    }, 0)\n  }\n\n  /**\n   * Returns the editor storage.\n   */\n  public get storage(): Record<string, any> {\n    return this.extensionStorage\n  }\n\n  /**\n   * An object of all registered commands.\n   */\n  public get commands(): SingleCommands {\n    return this.commandManager.commands\n  }\n\n  /**\n   * Create a command chain to call multiple commands at once.\n   */\n  public chain(): ChainedCommands {\n    return this.commandManager.chain()\n  }\n\n  /**\n   * Check if a command or a command chain can be executed. Without executing it.\n   */\n  public can(): CanCommands {\n    return this.commandManager.can()\n  }\n\n  /**\n   * Inject CSS styles.\n   */\n  private injectCSS(): void {\n    if (this.options.injectCSS && document) {\n      this.css = createStyleTag(style, this.options.injectNonce)\n    }\n  }\n\n  /**\n   * Update editor options.\n   *\n   * @param options A list of options\n   */\n  public setOptions(options: Partial<EditorOptions> = {}): void {\n    this.options = {\n      ...this.options,\n      ...options,\n    }\n\n    if (!this.view || !this.state || this.isDestroyed) {\n      return\n    }\n\n    if (this.options.editorProps) {\n      this.view.setProps(this.options.editorProps)\n    }\n\n    this.view.updateState(this.state)\n  }\n\n  /**\n   * Update editable state of the editor.\n   */\n  public setEditable(editable: boolean, emitUpdate = true): void {\n    this.setOptions({ editable })\n\n    if (emitUpdate) {\n      this.emit('update', { editor: this, transaction: this.state.tr })\n    }\n  }\n\n  /**\n   * Returns whether the editor is editable.\n   */\n  public get isEditable(): boolean {\n    // since plugins are applied after creating the view\n    // `editable` is always `true` for one tick.\n    // that’s why we also have to check for `options.editable`\n    return this.options.editable && this.view && this.view.editable\n  }\n\n  /**\n   * Returns the editor state.\n   */\n  public get state(): EditorState {\n    return this.view.state\n  }\n\n  /**\n   * Register a ProseMirror plugin.\n   *\n   * @param plugin A ProseMirror plugin\n   * @param handlePlugins Control how to merge the plugin into the existing plugins.\n   * @returns The new editor state\n   */\n  public registerPlugin(\n    plugin: Plugin,\n    handlePlugins?: (newPlugin: Plugin, plugins: Plugin[]) => Plugin[],\n  ): EditorState {\n    const plugins = isFunction(handlePlugins)\n      ? handlePlugins(plugin, [...this.state.plugins])\n      : [...this.state.plugins, plugin]\n\n    const state = this.state.reconfigure({ plugins })\n\n    this.view.updateState(state)\n\n    return state\n  }\n\n  /**\n   * Unregister a ProseMirror plugin.\n   *\n   * @param nameOrPluginKeyToRemove The plugins name\n   * @returns The new editor state or undefined if the editor is destroyed\n   */\n  public unregisterPlugin(nameOrPluginKeyToRemove: string | PluginKey | (string | PluginKey)[]): EditorState | undefined {\n    if (this.isDestroyed) {\n      return undefined\n    }\n\n    const prevPlugins = this.state.plugins\n    let plugins = prevPlugins;\n\n    ([] as (string | PluginKey)[]).concat(nameOrPluginKeyToRemove).forEach(nameOrPluginKey => {\n      // @ts-ignore\n      const name = typeof nameOrPluginKey === 'string' ? `${nameOrPluginKey}$` : nameOrPluginKey.key\n\n      // @ts-ignore\n      plugins = plugins.filter(plugin => !plugin.key.startsWith(name))\n    })\n\n    if (prevPlugins.length === plugins.length) {\n      // No plugin was removed, so we don’t need to update the state\n      return undefined\n    }\n\n    const state = this.state.reconfigure({\n      plugins,\n    })\n\n    this.view.updateState(state)\n\n    return state\n  }\n\n  /**\n   * Creates an extension manager.\n   */\n  private createExtensionManager(): void {\n\n    const coreExtensions = this.options.enableCoreExtensions ? [\n      Editable,\n      ClipboardTextSerializer.configure({\n        blockSeparator: this.options.coreExtensionOptions?.clipboardTextSerializer?.blockSeparator,\n      }),\n      Commands,\n      FocusEvents,\n      Keymap,\n      Tabindex,\n      Drop,\n      Paste,\n    ].filter(ext => {\n      if (typeof this.options.enableCoreExtensions === 'object') {\n        return this.options.enableCoreExtensions[ext.name as keyof typeof this.options.enableCoreExtensions] !== false\n      }\n      return true\n    }) : []\n    const allExtensions = [...coreExtensions, ...this.options.extensions].filter(extension => {\n      return ['extension', 'node', 'mark'].includes(extension?.type)\n    })\n\n    this.extensionManager = new ExtensionManager(allExtensions, this)\n  }\n\n  /**\n   * Creates an command manager.\n   */\n  private createCommandManager(): void {\n    this.commandManager = new CommandManager({\n      editor: this,\n    })\n  }\n\n  /**\n   * Creates a ProseMirror schema.\n   */\n  private createSchema(): void {\n    this.schema = this.extensionManager.schema\n  }\n\n  /**\n   * Creates a ProseMirror view.\n   */\n  private createView(): void {\n    let doc: ProseMirrorNode\n\n    try {\n      doc = createDocument(\n        this.options.content,\n        this.schema,\n        this.options.parseOptions,\n        { errorOnInvalidContent: this.options.enableContentCheck },\n      )\n    } catch (e) {\n      if (!(e instanceof Error) || !['[tiptap error]: Invalid JSON content', '[tiptap error]: Invalid HTML content'].includes(e.message)) {\n        // Not the content error we were expecting\n        throw e\n      }\n      this.emit('contentError', {\n        editor: this,\n        error: e as Error,\n        disableCollaboration: () => {\n          if (this.storage.collaboration) {\n            this.storage.collaboration.isDisabled = true\n          }\n          // To avoid syncing back invalid content, reinitialize the extensions without the collaboration extension\n          this.options.extensions = this.options.extensions.filter(extension => extension.name !== 'collaboration')\n\n          // Restart the initialization process by recreating the extension manager with the new set of extensions\n          this.createExtensionManager()\n        },\n      })\n\n      // Content is invalid, but attempt to create it anyway, stripping out the invalid parts\n      doc = createDocument(\n        this.options.content,\n        this.schema,\n        this.options.parseOptions,\n        { errorOnInvalidContent: false },\n      )\n    }\n    const selection = resolveFocusPosition(doc, this.options.autofocus)\n\n    this.view = new EditorView(this.options.element, {\n      ...this.options.editorProps,\n      attributes: {\n        // add `role=\"textbox\"` to the editor element\n        role: 'textbox',\n        ...this.options.editorProps?.attributes,\n      },\n      dispatchTransaction: this.dispatchTransaction.bind(this),\n      state: EditorState.create({\n        doc,\n        selection: selection || undefined,\n      }),\n    })\n\n    // `editor.view` is not yet available at this time.\n    // Therefore we will add all plugins and node views directly afterwards.\n    const newState = this.state.reconfigure({\n      plugins: this.extensionManager.plugins,\n    })\n\n    this.view.updateState(newState)\n\n    this.createNodeViews()\n    this.prependClass()\n\n    // Let’s store the editor instance in the DOM element.\n    // So we’ll have access to it for tests.\n    // @ts-ignore\n    const dom = this.view.dom as TiptapEditorHTMLElement\n\n    dom.editor = this\n  }\n\n  /**\n   * Creates all node views.\n   */\n  public createNodeViews(): void {\n    if (this.view.isDestroyed) {\n      return\n    }\n\n    this.view.setProps({\n      nodeViews: this.extensionManager.nodeViews,\n    })\n  }\n\n  /**\n   * Prepend class name to element.\n   */\n  public prependClass(): void {\n    this.view.dom.className = `tiptap ${this.view.dom.className}`\n  }\n\n  public isCapturingTransaction = false\n\n  private capturedTransaction: Transaction | null = null\n\n  public captureTransaction(fn: () => void) {\n    this.isCapturingTransaction = true\n    fn()\n    this.isCapturingTransaction = false\n\n    const tr = this.capturedTransaction\n\n    this.capturedTransaction = null\n\n    return tr\n  }\n\n  /**\n   * The callback over which to send transactions (state updates) produced by the view.\n   *\n   * @param transaction An editor state transaction\n   */\n  private dispatchTransaction(transaction: Transaction): void {\n    // if the editor / the view of the editor was destroyed\n    // the transaction should not be dispatched as there is no view anymore.\n    if (this.view.isDestroyed) {\n      return\n    }\n\n    if (this.isCapturingTransaction) {\n      if (!this.capturedTransaction) {\n        this.capturedTransaction = transaction\n\n        return\n      }\n\n      transaction.steps.forEach(step => this.capturedTransaction?.step(step))\n\n      return\n    }\n\n    const state = this.state.apply(transaction)\n    const selectionHasChanged = !this.state.selection.eq(state.selection)\n\n    this.emit('beforeTransaction', {\n      editor: this,\n      transaction,\n      nextState: state,\n    })\n    this.view.updateState(state)\n    this.emit('transaction', {\n      editor: this,\n      transaction,\n    })\n\n    if (selectionHasChanged) {\n      this.emit('selectionUpdate', {\n        editor: this,\n        transaction,\n      })\n    }\n\n    const focus = transaction.getMeta('focus')\n    const blur = transaction.getMeta('blur')\n\n    if (focus) {\n      this.emit('focus', {\n        editor: this,\n        event: focus.event,\n        transaction,\n      })\n    }\n\n    if (blur) {\n      this.emit('blur', {\n        editor: this,\n        event: blur.event,\n        transaction,\n      })\n    }\n\n    if (!transaction.docChanged || transaction.getMeta('preventUpdate')) {\n      return\n    }\n\n    this.emit('update', {\n      editor: this,\n      transaction,\n    })\n  }\n\n  /**\n   * Get attributes of the currently selected node or mark.\n   */\n  public getAttributes(nameOrType: string | NodeType | MarkType): Record<string, any> {\n    return getAttributes(this.state, nameOrType)\n  }\n\n  /**\n   * Returns if the currently selected node or mark is active.\n   *\n   * @param name Name of the node or mark\n   * @param attributes Attributes of the node or mark\n   */\n  public isActive(name: string, attributes?: {}): boolean\n  public isActive(attributes: {}): boolean\n  public isActive(nameOrAttributes: string, attributesOrUndefined?: {}): boolean {\n    const name = typeof nameOrAttributes === 'string' ? nameOrAttributes : null\n\n    const attributes = typeof nameOrAttributes === 'string' ? attributesOrUndefined : nameOrAttributes\n\n    return isActive(this.state, name, attributes)\n  }\n\n  /**\n   * Get the document as JSON.\n   */\n  public getJSON(): JSONContent {\n    return this.state.doc.toJSON()\n  }\n\n  /**\n   * Get the document as HTML.\n   */\n  public getHTML(): string {\n    return getHTMLFromFragment(this.state.doc.content, this.schema)\n  }\n\n  /**\n   * Get the document as text.\n   */\n  public getText(options?: {\n    blockSeparator?: string\n    textSerializers?: Record<string, TextSerializer>\n  }): string {\n    const { blockSeparator = '\\n\\n', textSerializers = {} } = options || {}\n\n    return getText(this.state.doc, {\n      blockSeparator,\n      textSerializers: {\n        ...getTextSerializersFromSchema(this.schema),\n        ...textSerializers,\n      },\n    })\n  }\n\n  /**\n   * Check if there is no content.\n   */\n  public get isEmpty(): boolean {\n    return isNodeEmpty(this.state.doc)\n  }\n\n  /**\n   * Get the number of characters for the current document.\n   *\n   * @deprecated\n   */\n  public getCharacterCount(): number {\n    console.warn(\n      '[tiptap warn]: \"editor.getCharacterCount()\" is deprecated. Please use \"editor.storage.characterCount.characters()\" instead.',\n    )\n\n    return this.state.doc.content.size - 2\n  }\n\n  /**\n   * Destroy the editor.\n   */\n  public destroy(): void {\n    this.emit('destroy')\n\n    if (this.view) {\n      // Cleanup our reference to prevent circular references which caused memory leaks\n      // @ts-ignore\n      const dom = this.view.dom as TiptapEditorHTMLElement\n\n      if (dom && dom.editor) {\n        delete dom.editor\n      }\n      this.view.destroy()\n    }\n\n    this.removeAllListeners()\n  }\n\n  /**\n   * Check if the editor is already destroyed.\n   */\n  public get isDestroyed(): boolean {\n    // @ts-ignore\n    return !this.view?.docView\n  }\n\n  public $node(selector: string, attributes?: { [key: string]: any }): NodePos | null {\n    return this.$doc?.querySelector(selector, attributes) || null\n  }\n\n  public $nodes(selector: string, attributes?: { [key: string]: any }): NodePos[] | null {\n    return this.$doc?.querySelectorAll(selector, attributes) || null\n  }\n\n  public $pos(pos: number) {\n    const $pos = this.state.doc.resolve(pos)\n\n    return new NodePos($pos, this)\n  }\n\n  get $doc() {\n    return this.$pos(0)\n  }\n}\n", "import { MarkType } from '@tiptap/pm/model'\n\nimport { getMarksBetween } from '../helpers/getMarksBetween.js'\nimport { InputRule, InputRuleFinder } from '../InputRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule that adds a mark when the\n * matched text is typed into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function markInputRule(config: {\n  find: InputRuleFinder\n  type: MarkType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match)\n\n      if (attributes === false || attributes === null) {\n        return null\n      }\n\n      const { tr } = state\n      const captureGroup = match[match.length - 1]\n      const fullMatch = match[0]\n\n      if (captureGroup) {\n        const startSpaces = fullMatch.search(/\\S/)\n        const textStart = range.from + fullMatch.indexOf(captureGroup)\n        const textEnd = textStart + captureGroup.length\n\n        const excludedMarks = getMarksBetween(range.from, range.to, state.doc)\n          .filter(item => {\n            // @ts-ignore\n            const excluded = item.mark.type.excluded as MarkType[]\n\n            return excluded.find(type => type === config.type && type !== item.mark.type)\n          })\n          .filter(item => item.to > textStart)\n\n        if (excludedMarks.length) {\n          return null\n        }\n\n        if (textEnd < range.to) {\n          tr.delete(textEnd, range.to)\n        }\n\n        if (textStart > range.from) {\n          tr.delete(range.from + startSpaces, textStart)\n        }\n\n        const markEnd = range.from + startSpaces + captureGroup.length\n\n        tr.addMark(range.from + startSpaces, markEnd, config.type.create(attributes || {}))\n\n        tr.removeStoredMark(config.type)\n      }\n    },\n  })\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { InputRule, InputRuleFinder } from '../InputRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule that adds a node when the\n * matched text is typed into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function nodeInputRule(config: {\n  /**\n   * The regex to match.\n   */\n  find: InputRuleFinder\n\n  /**\n   * The node type to add.\n   */\n  type: NodeType\n\n  /**\n   * A function that returns the attributes for the node\n   * can also be an object of attributes\n   */\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match) || {}\n      const { tr } = state\n      const start = range.from\n      let end = range.to\n\n      const newNode = config.type.create(attributes)\n\n      if (match[1]) {\n        const offset = match[0].lastIndexOf(match[1])\n        let matchStart = start + offset\n\n        if (matchStart > end) {\n          matchStart = end\n        } else {\n          end = matchStart + match[1].length\n        }\n\n        // insert last typed character\n        const lastChar = match[0][match[0].length - 1]\n\n        tr.insertText(lastChar, start + match[0].length - 1)\n\n        // insert node from input rule\n        tr.replaceWith(matchStart, end, newNode)\n      } else if (match[0]) {\n        const insertionStart = config.type.isInline ? start : start - 1\n\n        tr.insert(insertionStart, config.type.create(attributes)).delete(\n          tr.mapping.map(start),\n          tr.mapping.map(end),\n        )\n      }\n\n      tr.scrollIntoView()\n    },\n  })\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { InputRule, InputRuleFinder } from '../InputRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule that changes the type of a textblock when the\n * matched text is typed into it. When using a regular expresion you’ll\n * probably want the regexp to start with `^`, so that the pattern can\n * only occur at the start of a textblock.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function textblockTypeInputRule(config: {\n  find: InputRuleFinder\n  type: NodeType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      const $start = state.doc.resolve(range.from)\n      const attributes = callOrReturn(config.getAttributes, undefined, match) || {}\n\n      if (!$start.node(-1).canReplaceWith($start.index(-1), $start.indexAfter(-1), config.type)) {\n        return null\n      }\n\n      state.tr\n        .delete(range.from, range.to)\n        .setBlockType(range.from, range.from, config.type, attributes)\n    },\n  })\n}\n", "import { InputRule, InputRuleFinder } from '../InputRule.js'\n\n/**\n * Build an input rule that replaces text when the\n * matched text is typed into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function textInputRule(config: {\n  find: InputRuleFinder,\n  replace: string,\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      let insert = config.replace\n      let start = range.from\n      const end = range.to\n\n      if (match[1]) {\n        const offset = match[0].lastIndexOf(match[1])\n\n        insert += match[0].slice(offset + match[1].length)\n        start += offset\n\n        const cutOff = start - end\n\n        if (cutOff > 0) {\n          insert = match[0].slice(offset - cutOff, offset) + insert\n          start = end\n        }\n      }\n\n      state.tr.insertText(insert, start, end)\n    },\n  })\n}\n", "import { Node as ProseMir<PERSON>rNode, NodeType } from '@tiptap/pm/model'\nimport { canJoin, findWrapping } from '@tiptap/pm/transform'\n\nimport { Editor } from '../Editor.js'\nimport { InputRule, InputRuleFinder } from '../InputRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an input rule for automatically wrapping a textblock when a\n * given string is typed. When using a regular expresion you’ll\n * probably want the regexp to start with `^`, so that the pattern can\n * only occur at the start of a textblock.\n *\n * `type` is the type of node to wrap in.\n *\n * By default, if there’s a node with the same type above the newly\n * wrapped node, the rule will try to join those\n * two nodes. You can pass a join predicate, which takes a regular\n * expression match and the node before the wrapped node, and can\n * return a boolean to indicate whether a join should happen.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#input-rules\n */\nexport function wrappingInputRule(config: {\n  find: InputRuleFinder,\n  type: NodeType,\n  keepMarks?: boolean,\n  keepAttributes?: boolean,\n  editor?: Editor\n  getAttributes?:\n  | Record<string, any>\n  | ((match: ExtendedRegExpMatchArray) => Record<string, any>)\n  | false\n  | null\n  ,\n  joinPredicate?: (match: ExtendedRegExpMatchArray, node: ProseMirrorNode) => boolean,\n}) {\n  return new InputRule({\n    find: config.find,\n    handler: ({\n      state, range, match, chain,\n    }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match) || {}\n      const tr = state.tr.delete(range.from, range.to)\n      const $start = tr.doc.resolve(range.from)\n      const blockRange = $start.blockRange()\n      const wrapping = blockRange && findWrapping(blockRange, config.type, attributes)\n\n      if (!wrapping) {\n        return null\n      }\n\n      tr.wrap(blockRange, wrapping)\n\n      if (config.keepMarks && config.editor) {\n        const { selection, storedMarks } = state\n        const { splittableMarks } = config.editor.extensionManager\n        const marks = storedMarks || (selection.$to.parentOffset && selection.$from.marks())\n\n        if (marks) {\n          const filteredMarks = marks.filter(mark => splittableMarks.includes(mark.type.name))\n\n          tr.ensureMarks(filteredMarks)\n        }\n      }\n      if (config.keepAttributes) {\n        /** If the nodeType is `bulletList` or `orderedList` set the `nodeType` as `listItem` */\n        const nodeType = config.type.name === 'bulletList' || config.type.name === 'orderedList' ? 'listItem' : 'taskList'\n\n        chain().updateAttributes(nodeType, attributes).run()\n      }\n\n      const before = tr.doc.resolve(range.from - 1).nodeBefore\n\n      if (\n        before\n        && before.type === config.type\n        && canJoin(tr.doc, range.from - 1)\n        && (!config.joinPredicate || config.joinPredicate(match, before))\n      ) {\n        tr.join(range.from - 1)\n      }\n    },\n  })\n}\n", "import {\n  DOMOutputSpec, Node as ProseMirror<PERSON>ode, NodeSpec, NodeType,\n} from '@tiptap/pm/model'\nimport { Plugin, Transaction } from '@tiptap/pm/state'\n\nimport { Editor } from './Editor.js'\nimport { getExtensionField } from './helpers/getExtensionField.js'\nimport { NodeConfig } from './index.js'\nimport { InputRule } from './InputRule.js'\nimport { Mark } from './Mark.js'\nimport { PasteRule } from './PasteRule.js'\nimport {\n  AnyConfig,\n  Attributes,\n  Extensions,\n  GlobalAttributes,\n  KeyboardShortcutCommand,\n  NodeViewRenderer,\n  ParentConfig,\n  RawCommands,\n} from './types.js'\nimport { callOrReturn } from './utilities/callOrReturn.js'\nimport { mergeDeep } from './utilities/mergeDeep.js'\n\ndeclare module '@tiptap/core' {\n  interface NodeConfig<Options = any, Storage = any> {\n    // @ts-ignore - this is a dynamic key\n    [key: string]: any\n\n    /**\n     * The extension name - this must be unique.\n     * It will be used to identify the extension.\n     *\n     * @example 'myExtension'\n     */\n    name: string\n\n    /**\n     * The priority of your extension. The higher, the earlier it will be called\n     * and will take precedence over other extensions with a lower priority.\n     * @default 100\n     * @example 101\n     */\n    priority?: number\n\n    /**\n     * The default options for this extension.\n     * @example\n     * defaultOptions: {\n     *   myOption: 'foo',\n     *   myOtherOption: 10,\n     * }\n     */\n    defaultOptions?: Options\n\n    /**\n     * This method will add options to this extension\n     * @see https://tiptap.dev/guide/custom-extensions#settings\n     * @example\n     * addOptions() {\n     *  return {\n     *    myOption: 'foo',\n     *    myOtherOption: 10,\n     * }\n     */\n    addOptions?: (this: {\n      name: string\n      parent: Exclude<ParentConfig<NodeConfig<Options, Storage>>['addOptions'], undefined>\n    }) => Options\n\n    /**\n     * The default storage this extension can save data to.\n     * @see https://tiptap.dev/guide/custom-extensions#storage\n     * @example\n     * defaultStorage: {\n     *   prefetchedUsers: [],\n     *   loading: false,\n     * }\n     */\n    addStorage?: (this: {\n      name: string\n      options: Options\n      parent: Exclude<ParentConfig<NodeConfig<Options, Storage>>['addStorage'], undefined>\n    }) => Storage\n\n    /**\n     * This function adds globalAttributes to specific nodes.\n     * @see https://tiptap.dev/guide/custom-extensions#global-attributes\n     * @example\n     * addGlobalAttributes() {\n     *   return [\n     *     {\n             // Extend the following extensions\n     *       types: [\n     *         'heading',\n     *         'paragraph',\n     *       ],\n     *       // … with those attributes\n     *       attributes: {\n     *         textAlign: {\n     *           default: 'left',\n     *           renderHTML: attributes => ({\n     *             style: `text-align: ${attributes.textAlign}`,\n     *           }),\n     *           parseHTML: element => element.style.textAlign || 'left',\n     *         },\n     *       },\n     *     },\n     *   ]\n     * }\n     */\n    addGlobalAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      extensions: (Node | Mark)[]\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addGlobalAttributes']\n    }) => GlobalAttributes\n\n    /**\n     * This function adds commands to the editor\n     * @see https://tiptap.dev/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addCommands() {\n     *   return {\n     *     myCommand: () => ({ chain }) => chain().setMark('type', 'foo').run(),\n     *   }\n     * }\n     */\n    addCommands?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addCommands']\n    }) => Partial<RawCommands>\n\n    /**\n     * This function registers keyboard shortcuts.\n     * @see https://tiptap.dev/guide/custom-extensions#keyboard-shortcuts\n     * @example\n     * addKeyboardShortcuts() {\n     *   return {\n     *     'Mod-l': () => this.editor.commands.toggleBulletList(),\n     *   }\n     * },\n     */\n    addKeyboardShortcuts?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addKeyboardShortcuts']\n    }) => {\n      [key: string]: KeyboardShortcutCommand\n    }\n\n    /**\n     * This function adds input rules to the editor.\n     * @see https://tiptap.dev/guide/custom-extensions#input-rules\n     * @example\n     * addInputRules() {\n     *   return [\n     *     markInputRule({\n     *       find: inputRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addInputRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addInputRules']\n    }) => InputRule[]\n\n    /**\n     * This function adds paste rules to the editor.\n     * @see https://tiptap.dev/guide/custom-extensions#paste-rules\n     * @example\n     * addPasteRules() {\n     *   return [\n     *     markPasteRule({\n     *       find: pasteRegex,\n     *       type: this.type,\n     *     }),\n     *   ]\n     * },\n     */\n    addPasteRules?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addPasteRules']\n    }) => PasteRule[]\n\n    /**\n     * This function adds Prosemirror plugins to the editor\n     * @see https://tiptap.dev/guide/custom-extensions#prosemirror-plugins\n     * @example\n     * addProseMirrorPlugins() {\n     *   return [\n     *     customPlugin(),\n     *   ]\n     * }\n     */\n    addProseMirrorPlugins?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      editor: Editor\n      type: NodeType\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addProseMirrorPlugins']\n    }) => Plugin[]\n\n    /**\n     * This function adds additional extensions to the editor. This is useful for\n     * building extension kits.\n     * @example\n     * addExtensions() {\n     *   return [\n     *     BulletList,\n     *     OrderedList,\n     *     ListItem\n     *   ]\n     * }\n     */\n    addExtensions?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addExtensions']\n    }) => Extensions\n\n    /**\n     * This function extends the schema of the node.\n     * @example\n     * extendNodeSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendNodeSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<NodeConfig<Options, Storage>>['extendNodeSchema']\n          },\n          extension: Node,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * This function extends the schema of the mark.\n     * @example\n     * extendMarkSchema() {\n     *   return {\n     *     group: 'inline',\n     *     selectable: false,\n     *   }\n     * }\n     */\n    extendMarkSchema?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<NodeConfig<Options, Storage>>['extendMarkSchema']\n            editor?: Editor\n          },\n          extension: Node,\n        ) => Record<string, any>)\n      | null\n\n    /**\n     * The editor is not ready yet.\n     */\n    onBeforeCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onBeforeCreate']\n        }) => void)\n      | null\n\n    /**\n     * The editor is ready.\n     */\n    onCreate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onCreate']\n        }) => void)\n      | null\n\n    /**\n     * The content has changed.\n     */\n    onUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The selection has changed.\n     */\n    onSelectionUpdate?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onSelectionUpdate']\n        }) => void)\n      | null\n\n    /**\n     * The editor state has changed.\n     */\n    onTransaction?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: NodeType\n            parent: ParentConfig<NodeConfig<Options, Storage>>['onTransaction']\n          },\n          props: {\n            editor: Editor\n            transaction: Transaction\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is focused.\n     */\n    onFocus?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: NodeType\n            parent: ParentConfig<NodeConfig<Options, Storage>>['onFocus']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor isn’t focused anymore.\n     */\n    onBlur?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            editor: Editor\n            type: NodeType\n            parent: ParentConfig<NodeConfig<Options, Storage>>['onBlur']\n          },\n          props: {\n            event: FocusEvent\n          },\n        ) => void)\n      | null\n\n    /**\n     * The editor is destroyed.\n     */\n    onDestroy?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['onDestroy']\n        }) => void)\n      | null\n\n    /**\n     * Node View\n     */\n    addNodeView?:\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          editor: Editor\n          type: NodeType\n          parent: ParentConfig<NodeConfig<Options, Storage>>['addNodeView']\n        }) => NodeViewRenderer)\n      | null\n\n    /**\n     * Defines if this node should be a top level node (doc)\n     * @default false\n     * @example true\n     */\n    topNode?: boolean\n\n    /**\n     * The content expression for this node, as described in the [schema\n     * guide](/docs/guide/#schema.content_expressions). When not given,\n     * the node does not allow any content.\n     *\n     * You can read more about it on the Prosemirror documentation here\n     * @see https://prosemirror.net/docs/guide/#schema.content_expressions\n     * @default undefined\n     * @example content: 'block+'\n     * @example content: 'headline paragraph block*'\n     */\n    content?:\n      | NodeSpec['content']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['content']\n          editor?: Editor\n        }) => NodeSpec['content'])\n\n    /**\n     * The marks that are allowed inside of this node. May be a\n     * space-separated string referring to mark names or groups, `\"_\"`\n     * to explicitly allow all marks, or `\"\"` to disallow marks. When\n     * not given, nodes with inline content default to allowing all\n     * marks, other nodes default to not allowing marks.\n     *\n     * @example marks: 'strong em'\n     */\n    marks?:\n      | NodeSpec['marks']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['marks']\n          editor?: Editor\n        }) => NodeSpec['marks'])\n\n    /**\n     * The group or space-separated groups to which this node belongs,\n     * which can be referred to in the content expressions for the\n     * schema.\n     *\n     * By default Tiptap uses the groups 'block' and 'inline' for nodes. You\n     * can also use custom groups if you want to group specific nodes together\n     * and handle them in your schema.\n     * @example group: 'block'\n     * @example group: 'inline'\n     * @example group: 'customBlock' // this uses a custom group\n     */\n    group?:\n      | NodeSpec['group']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['group']\n          editor?: Editor\n        }) => NodeSpec['group'])\n\n    /**\n     * Should be set to true for inline nodes. (Implied for text nodes.)\n     */\n    inline?:\n      | NodeSpec['inline']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['inline']\n          editor?: Editor\n        }) => NodeSpec['inline'])\n\n    /**\n     * Can be set to true to indicate that, though this isn't a [leaf\n     * node](https://prosemirror.net/docs/ref/#model.NodeType.isLeaf), it doesn't have directly editable\n     * content and should be treated as a single unit in the view.\n     *\n     * @example atom: true\n     */\n    atom?:\n      | NodeSpec['atom']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['atom']\n          editor?: Editor\n        }) => NodeSpec['atom'])\n\n    /**\n     * Controls whether nodes of this type can be selected as a [node\n     * selection](https://prosemirror.net/docs/ref/#state.NodeSelection). Defaults to true for non-text\n     * nodes.\n     *\n     * @default true\n     * @example selectable: false\n     */\n    selectable?:\n      | NodeSpec['selectable']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['selectable']\n          editor?: Editor\n        }) => NodeSpec['selectable'])\n\n    /**\n     * Determines whether nodes of this type can be dragged without\n     * being selected. Defaults to false.\n     *\n     * @default: false\n     * @example: draggable: true\n     */\n    draggable?:\n      | NodeSpec['draggable']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['draggable']\n          editor?: Editor\n        }) => NodeSpec['draggable'])\n\n    /**\n     * Can be used to indicate that this node contains code, which\n     * causes some commands to behave differently.\n     */\n    code?:\n      | NodeSpec['code']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['code']\n          editor?: Editor\n        }) => NodeSpec['code'])\n\n    /**\n     * Controls way whitespace in this a node is parsed. The default is\n     * `\"normal\"`, which causes the [DOM parser](https://prosemirror.net/docs/ref/#model.DOMParser) to\n     * collapse whitespace in normal mode, and normalize it (replacing\n     * newlines and such with spaces) otherwise. `\"pre\"` causes the\n     * parser to preserve spaces inside the node. When this option isn't\n     * given, but [`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) is true, `whitespace`\n     * will default to `\"pre\"`. Note that this option doesn't influence\n     * the way the node is rendered—that should be handled by `toDOM`\n     * and/or styling.\n     */\n    whitespace?:\n      | NodeSpec['whitespace']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['whitespace']\n          editor?: Editor\n        }) => NodeSpec['whitespace'])\n\n    /**\n     * Allows a **single** node to be set as linebreak equivalent (e.g. hardBreak).\n     * When converting between block types that have whitespace set to \"pre\"\n     * and don't support the linebreak node (e.g. codeBlock) and other block types\n     * that do support the linebreak node (e.g. paragraphs) - this node will be used\n     * as the linebreak instead of stripping the newline.\n     *\n     * See [linebreakReplacement](https://prosemirror.net/docs/ref/#model.NodeSpec.linebreakReplacement).\n     */\n    linebreakReplacement?:\n      | NodeSpec['linebreakReplacement']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['linebreakReplacement']\n          editor?: Editor\n        }) => NodeSpec['linebreakReplacement'])\n\n    /**\n     * When enabled, enables both\n     * [`definingAsContext`](https://prosemirror.net/docs/ref/#model.NodeSpec.definingAsContext) and\n     * [`definingForContent`](https://prosemirror.net/docs/ref/#model.NodeSpec.definingForContent).\n     *\n     * @default false\n     * @example isolating: true\n     */\n    defining?:\n      | NodeSpec['defining']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['defining']\n          editor?: Editor\n        }) => NodeSpec['defining'])\n\n    /**\n     * When enabled (default is false), the sides of nodes of this type\n     * count as boundaries that regular editing operations, like\n     * backspacing or lifting, won't cross. An example of a node that\n     * should probably have this enabled is a table cell.\n     */\n    isolating?:\n      | NodeSpec['isolating']\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options, Storage>>['isolating']\n          editor?: Editor\n        }) => NodeSpec['isolating'])\n\n    /**\n     * Associates DOM parser information with this node, which can be\n     * used by [`DOMParser.fromSchema`](https://prosemirror.net/docs/ref/#model.DOMParser^fromSchema) to\n     * automatically derive a parser. The `node` field in the rules is\n     * implied (the name of this node will be filled in automatically).\n     * If you supply your own parser, you do not need to also specify\n     * parsing rules in your schema.\n     *\n     * @example parseHTML: [{ tag: 'div', attrs: { 'data-id': 'my-block' } }]\n     */\n    parseHTML?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<NodeConfig<Options, Storage>>['parseHTML']\n      editor?: Editor\n    }) => NodeSpec['parseDOM']\n\n    /**\n     * A description of a DOM structure. Can be either a string, which is\n     * interpreted as a text node, a DOM node, which is interpreted as\n     * itself, a `{dom, contentDOM}` object, or an array.\n     *\n     * An array describes a DOM element. The first value in the array\n     * should be a string—the name of the DOM element, optionally prefixed\n     * by a namespace URL and a space. If the second element is plain\n     * object, it is interpreted as a set of attributes for the element.\n     * Any elements after that (including the 2nd if it's not an attribute\n     * object) are interpreted as children of the DOM elements, and must\n     * either be valid `DOMOutputSpec` values, or the number zero.\n     *\n     * The number zero (pronounced “hole”) is used to indicate the place\n     * where a node's child nodes should be inserted. If it occurs in an\n     * output spec, it should be the only child element in its parent\n     * node.\n     *\n     * @example toDOM: ['div[data-id=\"my-block\"]', { class: 'my-block' }, 0]\n     */\n    renderHTML?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<NodeConfig<Options, Storage>>['renderHTML']\n            editor?: Editor\n          },\n          props: {\n            node: ProseMirrorNode\n            HTMLAttributes: Record<string, any>\n          },\n        ) => DOMOutputSpec)\n      | null\n\n    /**\n     * renders the node as text\n     * @example renderText: () => 'foo\n     */\n    renderText?:\n      | ((\n          this: {\n            name: string\n            options: Options\n            storage: Storage\n            parent: ParentConfig<NodeConfig<Options, Storage>>['renderText']\n            editor?: Editor\n          },\n          props: {\n            node: ProseMirrorNode\n            pos: number\n            parent: ProseMirrorNode\n            index: number\n          },\n        ) => string)\n      | null\n\n    /**\n     * Add attributes to the node\n     * @example addAttributes: () => ({ class: 'foo' })\n     */\n    addAttributes?: (this: {\n      name: string\n      options: Options\n      storage: Storage\n      parent: ParentConfig<NodeConfig<Options, Storage>>['addAttributes']\n      editor?: Editor\n    // eslint-disable-next-line @typescript-eslint/no-empty-object-type\n    }) => Attributes | {}\n  }\n}\n\n/**\n * The Node class is used to create custom node extensions.\n * @see https://tiptap.dev/api/extensions#create-a-new-extension\n */\nexport class Node<Options = any, Storage = any> {\n  type = 'node'\n\n  name = 'node'\n\n  parent: Node | null = null\n\n  child: Node | null = null\n\n  options: Options\n\n  storage: Storage\n\n  config: NodeConfig = {\n    name: this.name,\n    defaultOptions: {},\n  }\n\n  constructor(config: Partial<NodeConfig<Options, Storage>> = {}) {\n    this.config = {\n      ...this.config,\n      ...config,\n    }\n\n    this.name = this.config.name\n\n    if (config.defaultOptions && Object.keys(config.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${this.name}\".`,\n      )\n    }\n\n    // TODO: remove `addOptions` fallback\n    this.options = this.config.defaultOptions\n\n    if (this.config.addOptions) {\n      this.options = callOrReturn(\n        getExtensionField<AnyConfig['addOptions']>(this, 'addOptions', {\n          name: this.name,\n        }),\n      )\n    }\n\n    this.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(this, 'addStorage', {\n        name: this.name,\n        options: this.options,\n      }),\n    ) || {}\n  }\n\n  static create<O = any, S = any>(config: Partial<NodeConfig<O, S>> = {}) {\n    return new Node<O, S>(config)\n  }\n\n  configure(options: Partial<Options> = {}) {\n    // return a new instance so we can use the same extension\n    // with different calls of `configure`\n    const extension = this.extend<Options, Storage>({\n      ...this.config,\n      addOptions: () => {\n        return mergeDeep(this.options as Record<string, any>, options) as Options\n      },\n    })\n\n    // Always preserve the current name\n    extension.name = this.name\n    // Set the parent to be our parent\n    extension.parent = this.parent\n\n    return extension\n  }\n\n  extend<ExtendedOptions = Options, ExtendedStorage = Storage>(\n    extendedConfig: Partial<NodeConfig<ExtendedOptions, ExtendedStorage>> = {},\n  ) {\n    const extension = new Node<ExtendedOptions, ExtendedStorage>(extendedConfig)\n\n    extension.parent = this\n\n    this.child = extension\n\n    extension.name = extendedConfig.name ? extendedConfig.name : extension.parent.name\n\n    if (extendedConfig.defaultOptions && Object.keys(extendedConfig.defaultOptions).length > 0) {\n      console.warn(\n        `[tiptap warn]: BREAKING CHANGE: \"defaultOptions\" is deprecated. Please use \"addOptions\" instead. Found in extension: \"${extension.name}\".`,\n      )\n    }\n\n    extension.options = callOrReturn(\n      getExtensionField<AnyConfig['addOptions']>(extension, 'addOptions', {\n        name: extension.name,\n      }),\n    )\n\n    extension.storage = callOrReturn(\n      getExtensionField<AnyConfig['addStorage']>(extension, 'addStorage', {\n        name: extension.name,\n        options: extension.options,\n      }),\n    )\n\n    return extension\n  }\n}\n", "import { NodeSelection } from '@tiptap/pm/state'\nimport { NodeView as ProseMirrorNodeView, ViewMutationRecord } from '@tiptap/pm/view'\n\nimport { Editor as CoreEditor } from './Editor.js'\nimport { DecorationWithType, NodeViewRendererOptions, NodeViewRendererProps } from './types.js'\nimport { isAndroid } from './utilities/isAndroid.js'\nimport { isiOS } from './utilities/isiOS.js'\n\n/**\n * Node views are used to customize the rendered DOM structure of a node.\n * @see https://tiptap.dev/guide/node-views\n */\nexport class NodeView<\n  Component,\n  NodeEditor extends CoreEditor = CoreEditor,\n  Options extends NodeViewRendererOptions = NodeViewRendererOptions,\n> implements ProseMirrorNodeView {\n  component: Component\n\n  editor: NodeEditor\n\n  options: Options\n\n  extension: NodeViewRendererProps['extension']\n\n  node: NodeViewRendererProps['node']\n\n  decorations: NodeViewRendererProps['decorations']\n\n  innerDecorations: NodeViewRendererProps['innerDecorations']\n\n  view: NodeViewRendererProps['view']\n\n  getPos: NodeViewRendererProps['getPos']\n\n  HTMLAttributes: NodeViewRendererProps['HTMLAttributes']\n\n  isDragging = false\n\n  constructor(component: Component, props: NodeViewRendererProps, options?: Partial<Options>) {\n    this.component = component\n    this.editor = props.editor as NodeEditor\n    this.options = {\n      stopEvent: null,\n      ignoreMutation: null,\n      ...options,\n    } as Options\n    this.extension = props.extension\n    this.node = props.node\n    this.decorations = props.decorations as DecorationWithType[]\n    this.innerDecorations = props.innerDecorations\n    this.view = props.view\n    this.HTMLAttributes = props.HTMLAttributes\n    this.getPos = props.getPos\n    this.mount()\n  }\n\n  mount() {\n    // eslint-disable-next-line\n    return\n  }\n\n  get dom(): HTMLElement {\n    return this.editor.view.dom as HTMLElement\n  }\n\n  get contentDOM(): HTMLElement | null {\n    return null\n  }\n\n  onDragStart(event: DragEvent) {\n    const { view } = this.editor\n    const target = event.target as HTMLElement\n\n    // get the drag handle element\n    // `closest` is not available for text nodes so we may have to use its parent\n    const dragHandle = target.nodeType === 3\n      ? target.parentElement?.closest('[data-drag-handle]')\n      : target.closest('[data-drag-handle]')\n\n    if (!this.dom || this.contentDOM?.contains(target) || !dragHandle) {\n      return\n    }\n\n    let x = 0\n    let y = 0\n\n    // calculate offset for drag element if we use a different drag handle element\n    if (this.dom !== dragHandle) {\n      const domBox = this.dom.getBoundingClientRect()\n      const handleBox = dragHandle.getBoundingClientRect()\n\n      // In React, we have to go through nativeEvent to reach offsetX/offsetY.\n      const offsetX = event.offsetX ?? (event as any).nativeEvent?.offsetX\n      const offsetY = event.offsetY ?? (event as any).nativeEvent?.offsetY\n\n      x = handleBox.x - domBox.x + offsetX\n      y = handleBox.y - domBox.y + offsetY\n    }\n\n    const clonedNode = this.dom.cloneNode(true) as HTMLElement\n\n    event.dataTransfer?.setDragImage(clonedNode, x, y)\n\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n    // we need to tell ProseMirror that we want to move the whole node\n    // so we create a NodeSelection\n    const selection = NodeSelection.create(view.state.doc, pos)\n    const transaction = view.state.tr.setSelection(selection)\n\n    view.dispatch(transaction)\n  }\n\n  stopEvent(event: Event) {\n    if (!this.dom) {\n      return false\n    }\n\n    if (typeof this.options.stopEvent === 'function') {\n      return this.options.stopEvent({ event })\n    }\n\n    const target = event.target as HTMLElement\n    const isInElement = this.dom.contains(target) && !this.contentDOM?.contains(target)\n\n    // any event from child nodes should be handled by ProseMirror\n    if (!isInElement) {\n      return false\n    }\n\n    const isDragEvent = event.type.startsWith('drag')\n    const isDropEvent = event.type === 'drop'\n    const isInput = ['INPUT', 'BUTTON', 'SELECT', 'TEXTAREA'].includes(target.tagName) || target.isContentEditable\n\n    // any input event within node views should be ignored by ProseMirror\n    if (isInput && !isDropEvent && !isDragEvent) {\n      return true\n    }\n\n    const { isEditable } = this.editor\n    const { isDragging } = this\n    const isDraggable = !!this.node.type.spec.draggable\n    const isSelectable = NodeSelection.isSelectable(this.node)\n    const isCopyEvent = event.type === 'copy'\n    const isPasteEvent = event.type === 'paste'\n    const isCutEvent = event.type === 'cut'\n    const isClickEvent = event.type === 'mousedown'\n\n    // ProseMirror tries to drag selectable nodes\n    // even if `draggable` is set to `false`\n    // this fix prevents that\n    if (!isDraggable && isSelectable && isDragEvent && event.target === this.dom) {\n      event.preventDefault()\n    }\n\n    if (isDraggable && isDragEvent && !isDragging && event.target === this.dom) {\n      event.preventDefault()\n      return false\n    }\n\n    // we have to store that dragging started\n    if (isDraggable && isEditable && !isDragging && isClickEvent) {\n      const dragHandle = target.closest('[data-drag-handle]')\n      const isValidDragHandle = dragHandle && (this.dom === dragHandle || this.dom.contains(dragHandle))\n\n      if (isValidDragHandle) {\n        this.isDragging = true\n\n        document.addEventListener(\n          'dragend',\n          () => {\n            this.isDragging = false\n          },\n          { once: true },\n        )\n\n        document.addEventListener(\n          'drop',\n          () => {\n            this.isDragging = false\n          },\n          { once: true },\n        )\n\n        document.addEventListener(\n          'mouseup',\n          () => {\n            this.isDragging = false\n          },\n          { once: true },\n        )\n      }\n    }\n\n    // these events are handled by prosemirror\n    if (\n      isDragging\n      || isDropEvent\n      || isCopyEvent\n      || isPasteEvent\n      || isCutEvent\n      || (isClickEvent && isSelectable)\n    ) {\n      return false\n    }\n\n    return true\n  }\n\n  /**\n   * Called when a DOM [mutation](https://developer.mozilla.org/en-US/docs/Web/API/MutationObserver) or a selection change happens within the view.\n   * @return `false` if the editor should re-read the selection or re-parse the range around the mutation\n   * @return `true` if it can safely be ignored.\n   */\n  ignoreMutation(mutation: ViewMutationRecord) {\n    if (!this.dom || !this.contentDOM) {\n      return true\n    }\n\n    if (typeof this.options.ignoreMutation === 'function') {\n      return this.options.ignoreMutation({ mutation })\n    }\n\n    // a leaf/atom node is like a black box for ProseMirror\n    // and should be fully handled by the node view\n    if (this.node.isLeaf || this.node.isAtom) {\n      return true\n    }\n\n    // ProseMirror should handle any selections\n    if (mutation.type === 'selection') {\n      return false\n    }\n\n    // try to prevent a bug on iOS and Android that will break node views on enter\n    // this is because ProseMirror can’t preventDispatch on enter\n    // this will lead to a re-render of the node view on enter\n    // see: https://github.com/ueberdosis/tiptap/issues/1214\n    // see: https://github.com/ueberdosis/tiptap/issues/2534\n    if (\n      this.dom.contains(mutation.target)\n      && mutation.type === 'childList'\n      && (isiOS() || isAndroid())\n      && this.editor.isFocused\n    ) {\n      const changedNodes = [\n        ...Array.from(mutation.addedNodes),\n        ...Array.from(mutation.removedNodes),\n      ] as HTMLElement[]\n\n      // we’ll check if every changed node is contentEditable\n      // to make sure it’s probably mutated by ProseMirror\n      if (changedNodes.every(node => node.isContentEditable)) {\n        return false\n      }\n    }\n\n    // we will allow mutation contentDOM with attributes\n    // so we can for example adding classes within our node view\n    if (this.contentDOM === mutation.target && mutation.type === 'attributes') {\n      return true\n    }\n\n    // ProseMirror should handle any changes within contentDOM\n    if (this.contentDOM.contains(mutation.target)) {\n      return false\n    }\n\n    return true\n  }\n\n  /**\n   * Update the attributes of the prosemirror node.\n   */\n  updateAttributes(attributes: Record<string, any>): void {\n    this.editor.commands.command(({ tr }) => {\n      const pos = this.getPos()\n\n      if (typeof pos !== 'number') {\n        return false\n      }\n\n      tr.setNodeMarkup(pos, undefined, {\n        ...this.node.attrs,\n        ...attributes,\n      })\n\n      return true\n    })\n  }\n\n  /**\n   * Delete the node.\n   */\n  deleteNode(): void {\n    const from = this.getPos()\n\n    if (typeof from !== 'number') {\n      return\n    }\n    const to = from + this.node.nodeSize\n\n    this.editor.commands.deleteRange({ from, to })\n  }\n}\n", "import { MarkType } from '@tiptap/pm/model'\n\nimport { getMarksBetween } from '../helpers/getMarksBetween.js'\nimport { PasteRule, PasteRuleFinder } from '../PasteRule.js'\nimport { ExtendedRegExpMatchArray } from '../types.js'\nimport { callOrReturn } from '../utilities/callOrReturn.js'\n\n/**\n * Build an paste rule that adds a mark when the\n * matched text is pasted into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport function markPasteRule(config: {\n  find: PasteRuleFinder\n  type: MarkType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray, event: ClipboardEvent) => Record<string, any>)\n    | false\n    | null\n}) {\n  return new PasteRule({\n    find: config.find,\n    handler: ({\n      state, range, match, pasteEvent,\n    }) => {\n      const attributes = callOrReturn(config.getAttributes, undefined, match, pasteEvent)\n\n      if (attributes === false || attributes === null) {\n        return null\n      }\n\n      const { tr } = state\n      const captureGroup = match[match.length - 1]\n      const fullMatch = match[0]\n      let markEnd = range.to\n\n      if (captureGroup) {\n        const startSpaces = fullMatch.search(/\\S/)\n        const textStart = range.from + fullMatch.indexOf(captureGroup)\n        const textEnd = textStart + captureGroup.length\n\n        const excludedMarks = getMarksBetween(range.from, range.to, state.doc)\n          .filter(item => {\n            // @ts-ignore\n            const excluded = item.mark.type.excluded as MarkType[]\n\n            return excluded.find(type => type === config.type && type !== item.mark.type)\n          })\n          .filter(item => item.to > textStart)\n\n        if (excludedMarks.length) {\n          return null\n        }\n\n        if (textEnd < range.to) {\n          tr.delete(textEnd, range.to)\n        }\n\n        if (textStart > range.from) {\n          tr.delete(range.from + startSpaces, textStart)\n        }\n\n        markEnd = range.from + startSpaces + captureGroup.length\n\n        tr.addMark(range.from + startSpaces, markEnd, config.type.create(attributes || {}))\n\n        tr.removeStoredMark(config.type)\n      }\n    },\n  })\n}\n", "// source: https://stackoverflow.com/a/6969486\nexport function escapeForRegEx(string: string): string {\n  return string.replace(/[-/\\\\^$*+?.()|[\\]{}]/g, '\\\\$&')\n}\n", "export function isString(value: any): value is string {\n  return typeof value === 'string'\n}\n", "import { NodeType } from '@tiptap/pm/model'\n\nimport { PasteRule, PasteRuleFinder } from '../PasteRule.js'\nimport { ExtendedRegExpMatchArray, JSONContent } from '../types.js'\nimport { callOrReturn } from '../utilities/index.js'\n\n/**\n * Build an paste rule that adds a node when the\n * matched text is pasted into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport function nodePasteRule(config: {\n  find: PasteRuleFinder\n  type: NodeType\n  getAttributes?:\n    | Record<string, any>\n    | ((match: ExtendedRegExpMatchArray, event: ClipboardEvent) => Record<string, any>)\n    | false\n    | null\n  getContent?:\n    | JSONContent[]\n    | ((attrs: Record<string, any>) => JSONContent[])\n    | false\n    | null\n}) {\n  return new PasteRule({\n    find: config.find,\n    handler({\n      match, chain, range, pasteEvent,\n    }) {\n      const attributes = callOrReturn(config.getAttributes, undefined, match, pasteEvent)\n      const content = callOrReturn(config.getContent, undefined, attributes)\n\n      if (attributes === false || attributes === null) {\n        return null\n      }\n\n      const node = { type: config.type.name, attrs: attributes } as JSONContent\n\n      if (content) {\n        node.content = content\n      }\n\n      if (match.input) {\n        chain().deleteRange(range).insertContentAt(range.from, node)\n      }\n    },\n  })\n}\n", "import { PasteRule, PasteRuleFinder } from '../PasteRule.js'\n\n/**\n * Build an paste rule that replaces text when the\n * matched text is pasted into it.\n * @see https://tiptap.dev/docs/editor/extensions/custom-extensions/extend-existing#paste-rules\n */\nexport function textPasteRule(config: {\n  find: PasteRuleFinder,\n  replace: string,\n}) {\n  return new PasteRule({\n    find: config.find,\n    handler: ({ state, range, match }) => {\n      let insert = config.replace\n      let start = range.from\n      const end = range.to\n\n      if (match[1]) {\n        const offset = match[0].lastIndexOf(match[1])\n\n        insert += match[0].slice(offset + match[1].length)\n        start += offset\n\n        const cutOff = start - end\n\n        if (cutOff > 0) {\n          insert = match[0].slice(offset - cutOff, offset) + insert\n          start = end\n        }\n      }\n\n      state.tr.insertText(insert, start, end)\n    },\n  })\n}\n", "import { Transaction } from '@tiptap/pm/state'\n\nexport interface TrackerResult {\n  position: number\n  deleted: boolean\n}\n\nexport class Tracker {\n  transaction: Transaction\n\n  currentStep: number\n\n  constructor(transaction: Transaction) {\n    this.transaction = transaction\n    this.currentStep = this.transaction.steps.length\n  }\n\n  map(position: number): TrackerResult {\n    let deleted = false\n\n    const mappedPosition = this.transaction.steps\n      .slice(this.currentStep)\n      .reduce((newPosition, step) => {\n        const mapResult = step.getMap().mapResult(newPosition)\n\n        if (mapResult.deleted) {\n          deleted = true\n        }\n\n        return mapResult.pos\n      }, position)\n\n    return {\n      position: mappedPosition,\n      deleted,\n    }\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACAA,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;;;ACOM,SAAU,qBAAqB,QAGpC;AACC,QAAM,EAAE,OAAO,YAAW,IAAK;AAC/B,MAAI,EAAE,UAAS,IAAK;AACpB,MAAI,EAAE,IAAG,IAAK;AACd,MAAI,EAAE,YAAW,IAAK;AAEtB,SAAO;IACL,GAAG;IACH,OAAO,MAAM,MAAM,KAAK,KAAK;IAC7B,kBAAkB,MAAM,iBAAiB,KAAK,KAAK;IACnD,SAAS,MAAM;IACf,QAAQ,MAAM;IACd,aAAa,MAAM,YAAY,KAAK,KAAK;IACzC,QAAQ,MAAM,OAAO,KAAK,KAAK;IAC/B,IAAI,cAAW;AACb,aAAO;;IAET,IAAI,YAAS;AACX,aAAO;;IAET,IAAI,MAAG;AACL,aAAO;;IAET,IAAI,KAAE;AACJ,kBAAY,YAAY;AACxB,YAAM,YAAY;AAClB,oBAAc,YAAY;AAE1B,aAAO;;;AAGb;ICjCa,uBAAc;EAOzB,YAAY,OAA8C;AACxD,SAAK,SAAS,MAAM;AACpB,SAAK,cAAc,KAAK,OAAO,iBAAiB;AAChD,SAAK,cAAc,MAAM;;EAG3B,IAAI,iBAAc;AAChB,WAAO,CAAC,CAAC,KAAK;;EAGhB,IAAI,QAAK;AACP,WAAO,KAAK,eAAe,KAAK,OAAO;;EAGzC,IAAI,WAAQ;AACV,UAAM,EAAE,aAAa,QAAQ,MAAK,IAAK;AACvC,UAAM,EAAE,KAAI,IAAK;AACjB,UAAM,EAAE,GAAE,IAAK;AACf,UAAM,QAAQ,KAAK,WAAW,EAAE;AAEhC,WAAO,OAAO,YACZ,OAAO,QAAQ,WAAW,EAAE,IAAI,CAAC,CAAC,MAAMC,QAAO,MAAK;AAClD,YAAM,SAAS,IAAI,SAAe;AAChC,cAAM,WAAWA,SAAQ,GAAG,IAAI,EAAE,KAAK;AAEvC,YAAI,CAAC,GAAG,QAAQ,iBAAiB,KAAK,CAAC,KAAK,gBAAgB;AAC1D,eAAK,SAAS,EAAE;;AAGlB,eAAO;MACT;AAEA,aAAO,CAAC,MAAM,MAAM;KACrB,CAAC;;EAIN,IAAI,QAAK;AACP,WAAO,MAAM,KAAK,YAAW;;EAG/B,IAAI,MAAG;AACL,WAAO,MAAM,KAAK,UAAS;;EAGtB,YAAY,SAAuB,iBAAiB,MAAI;AAC7D,UAAM,EAAE,aAAa,QAAQ,MAAK,IAAK;AACvC,UAAM,EAAE,KAAI,IAAK;AACjB,UAAM,YAAuB,CAAA;AAC7B,UAAM,sBAAsB,CAAC,CAAC;AAC9B,UAAM,KAAK,WAAW,MAAM;AAE5B,UAAMC,OAAM,MAAK;AACf,UACE,CAAC,uBACE,kBACA,CAAC,GAAG,QAAQ,iBAAiB,KAC7B,CAAC,KAAK,gBACT;AACA,aAAK,SAAS,EAAE;;AAGlB,aAAO,UAAU,MAAM,cAAY,aAAa,IAAI;IACtD;AAEA,UAAM,QAAQ;MACZ,GAAG,OAAO,YACR,OAAO,QAAQ,WAAW,EAAE,IAAI,CAAC,CAAC,MAAMD,QAAO,MAAK;AAClD,cAAM,iBAAiB,IAAI,SAAiB;AAC1C,gBAAM,QAAQ,KAAK,WAAW,IAAI,cAAc;AAChD,gBAAM,WAAWA,SAAQ,GAAG,IAAI,EAAE,KAAK;AAEvC,oBAAU,KAAK,QAAQ;AAEvB,iBAAO;QACT;AAEA,eAAO,CAAC,MAAM,cAAc;MAC9B,CAAC,CAAC;MAEJ,KAAAC;;AAGF,WAAO;;EAGF,UAAU,SAAqB;AACpC,UAAM,EAAE,aAAa,MAAK,IAAK;AAC/B,UAAM,WAAW;AACjB,UAAM,KAAK,WAAW,MAAM;AAC5B,UAAM,QAAQ,KAAK,WAAW,IAAI,QAAQ;AAC1C,UAAM,oBAAoB,OAAO,YAC/B,OAAO,QAAQ,WAAW,EAAE,IAAI,CAAC,CAAC,MAAMD,QAAO,MAAK;AAClD,aAAO,CAAC,MAAM,IAAI,SAAkBA,SAAQ,GAAG,IAAI,EAAE,EAAE,GAAG,OAAO,UAAU,OAAS,CAAE,CAAC;KACxF,CAAC;AAGJ,WAAO;MACL,GAAG;MACH,OAAO,MAAM,KAAK,YAAY,IAAI,QAAQ;;;EAIvC,WAAW,IAAiB,iBAAiB,MAAI;AACtD,UAAM,EAAE,aAAa,QAAQ,MAAK,IAAK;AACvC,UAAM,EAAE,KAAI,IAAK;AAEjB,UAAM,QAAsB;MAC1B;MACA;MACA;MACA,OAAO,qBAAqB;QAC1B;QACA,aAAa;OACd;MACD,UAAU,iBAAiB,MAAM,SAAY;MAC7C,OAAO,MAAM,KAAK,YAAY,IAAI,cAAc;MAChD,KAAK,MAAM,KAAK,UAAU,EAAE;MAC5B,IAAI,WAAQ;AACV,eAAO,OAAO,YACZ,OAAO,QAAQ,WAAW,EAAE,IAAI,CAAC,CAAC,MAAMA,QAAO,MAAK;AAClD,iBAAO,CAAC,MAAM,IAAI,SAAkBA,SAAQ,GAAG,IAAI,EAAE,KAAK,CAAC;SAC5D,CAAC;;;AAKR,WAAO;;AAEV;ICtIY,qBAAY;EAAzB,cAAA;AAEU,SAAS,YAAqD,CAAA;;EAE/D,GAAqC,OAAkB,IAAkC;AAC9F,QAAI,CAAC,KAAK,UAAU,KAAK,GAAG;AAC1B,WAAK,UAAU,KAAK,IAAI,CAAA;;AAG1B,SAAK,UAAU,KAAK,EAAE,KAAK,EAAE;AAE7B,WAAO;;EAGF,KAAuC,UAAqB,MAAgC;AACjG,UAAM,YAAY,KAAK,UAAU,KAAK;AAEtC,QAAI,WAAW;AACb,gBAAU,QAAQ,cAAY,SAAS,MAAM,MAAM,IAAI,CAAC;;AAG1D,WAAO;;EAGF,IAAsC,OAAkB,IAAmC;AAChG,UAAM,YAAY,KAAK,UAAU,KAAK;AAEtC,QAAI,WAAW;AACb,UAAI,IAAI;AACN,aAAK,UAAU,KAAK,IAAI,UAAU,OAAO,cAAY,aAAa,EAAE;aAC/D;AACL,eAAO,KAAK,UAAU,KAAK;;;AAI/B,WAAO;;EAGF,KAAuC,OAAkB,IAAkC;AAChG,UAAM,SAAS,IAAI,SAAoC;AACrD,WAAK,IAAI,OAAO,MAAM;AACtB,SAAG,MAAM,MAAM,IAAI;IACrB;AAEA,WAAO,KAAK,GAAG,OAAO,MAAM;;EAGvB,qBAAkB;AACvB,SAAK,YAAY,CAAA;;AAEpB;SCnDe,kBACd,WACA,OACA,SAAmD;AAGnD,MAAI,UAAU,OAAO,KAAK,MAAM,UAAa,UAAU,QAAQ;AAC7D,WAAO,kBAAkB,UAAU,QAAQ,OAAO,OAAO;;AAG3D,MAAI,OAAO,UAAU,OAAO,KAAK,MAAM,YAAY;AACjD,UAAM,QAAQ,UAAU,OAAO,KAAK,EAAE,KAAK;MACzC,GAAG;MACH,QAAQ,UAAU,SACd,kBAAkB,UAAU,QAAQ,OAAO,OAAO,IAClD;IACL,CAAA;AAED,WAAO;;AAGT,SAAO,UAAU,OAAO,KAAK;AAC/B;AC1BM,SAAU,gBAAgB,YAAsB;AACpD,QAAM,iBAAiB,WAAW,OAAO,eAAa,UAAU,SAAS,WAAW;AACpF,QAAM,iBAAiB,WAAW,OAAO,eAAa,UAAU,SAAS,MAAM;AAC/E,QAAM,iBAAiB,WAAW,OAAO,eAAa,UAAU,SAAS,MAAM;AAE/E,SAAO;IACL;IACA;IACA;;AAEJ;ACAM,SAAU,4BAA4B,YAAsB;AAChE,QAAM,sBAA4C,CAAA;AAClD,QAAM,EAAE,gBAAgB,eAAc,IAAK,gBAAgB,UAAU;AACrE,QAAM,wBAAwB,CAAC,GAAG,gBAAgB,GAAG,cAAc;AACnE,QAAM,mBAAwC;IAC5C,SAAS;IACT,UAAU;IACV,YAAY;IACZ,WAAW;IACX,aAAa;IACb,YAAY;;AAGd,aAAW,QAAQ,eAAY;AAC7B,UAAM,UAAU;MACd,MAAM,UAAU;MAChB,SAAS,UAAU;MACnB,SAAS,UAAU;MACnB,YAAY;;AAGd,UAAM,sBAAsB,kBAC1B,WACA,uBACA,OAAO;AAGT,QAAI,CAAC,qBAAqB;AACxB;;AAGF,UAAM,mBAAmB,oBAAmB;AAE5C,qBAAiB,QAAQ,qBAAkB;AACzC,sBAAgB,MAAM,QAAQ,UAAO;AACnC,eACG,QAAQ,gBAAgB,UAAU,EAClC,QAAQ,CAAC,CAAC,MAAM,SAAS,MAAK;AAC7B,8BAAoB,KAAK;YACvB;YACA;YACA,WAAW;cACT,GAAG;cACH,GAAG;YACJ;UACF,CAAA;QACH,CAAC;MACL,CAAC;IACH,CAAC;EACH,CAAC;AAED,wBAAsB,QAAQ,eAAY;AACxC,UAAM,UAAU;MACd,MAAM,UAAU;MAChB,SAAS,UAAU;MACnB,SAAS,UAAU;;AAGrB,UAAM,gBAAgB,kBACpB,WACA,iBACA,OAAO;AAGT,QAAI,CAAC,eAAe;AAClB;;AAIF,UAAM,aAAa,cAAa;AAEhC,WACG,QAAQ,UAAU,EAClB,QAAQ,CAAC,CAAC,MAAM,SAAS,MAAK;AAC7B,YAAM,aAAa;QACjB,GAAG;QACH,GAAG;;AAGL,UAAI,QAAO,eAAU,QAAV,eAAU,SAAA,SAAV,WAAY,aAAY,YAAY;AAC7C,mBAAW,UAAU,WAAW,QAAO;;AAGzC,WAAI,eAAA,QAAA,eAAU,SAAA,SAAV,WAAY,gBAAc,eAAA,QAAA,eAAU,SAAA,SAAV,WAAY,aAAY,QAAW;AAC/D,eAAO,WAAW;;AAGpB,0BAAoB,KAAK;QACvB,MAAM,UAAU;QAChB;QACA,WAAW;MACZ,CAAA;IACH,CAAC;EACL,CAAC;AAED,SAAO;AACT;AC7GgB,SAAA,YAAY,YAA+B,QAAc;AACvE,MAAI,OAAO,eAAe,UAAU;AAClC,QAAI,CAAC,OAAO,MAAM,UAAU,GAAG;AAC7B,YAAM,MACJ,gCAAgC,UAAU,2CAA2C;;AAIzF,WAAO,OAAO,MAAM,UAAU;;AAGhC,SAAO;AACT;ACdgB,SAAA,mBAAmB,SAA8B;AAC/D,SAAO,QACJ,OAAO,UAAQ,CAAC,CAAC,IAAI,EACrB,OAAO,CAAC,OAAO,SAAQ;AACtB,UAAM,mBAAmB,EAAE,GAAG,MAAK;AAEnC,WAAO,QAAQ,IAAI,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAK;AAC5C,YAAM,SAAS,iBAAiB,GAAG;AAEnC,UAAI,CAAC,QAAQ;AACX,yBAAiB,GAAG,IAAI;AAExB;;AAGF,UAAI,QAAQ,SAAS;AACnB,cAAM,eAAyB,QAAQ,OAAO,KAAK,EAAE,MAAM,GAAG,IAAI,CAAA;AAClE,cAAM,kBAA4B,iBAAiB,GAAG,IAAI,iBAAiB,GAAG,EAAE,MAAM,GAAG,IAAI,CAAA;AAE7F,cAAM,gBAAgB,aAAa,OACjC,gBAAc,CAAC,gBAAgB,SAAS,UAAU,CAAC;AAGrD,yBAAiB,GAAG,IAAI,CAAC,GAAG,iBAAiB,GAAG,aAAa,EAAE,KAAK,GAAG;iBAC9D,QAAQ,SAAS;AAC1B,cAAM,YAAsB,QAAQ,MAAM,MAAM,GAAG,EAAE,IAAI,CAACE,WAAkBA,OAAM,KAAI,CAAE,EAAE,OAAO,OAAO,IAAI,CAAA;AAC5G,cAAM,iBAA2B,iBAAiB,GAAG,IAAI,iBAAiB,GAAG,EAAE,MAAM,GAAG,EAAE,IAAI,CAACA,WAAkBA,OAAM,KAAI,CAAE,EAAE,OAAO,OAAO,IAAI,CAAA;AAEjJ,cAAM,WAAW,oBAAI,IAAG;AAExB,uBAAe,QAAQ,CAAAA,WAAQ;AAC7B,gBAAM,CAAC,UAAU,GAAG,IAAIA,OAAM,MAAM,GAAG,EAAE,IAAI,UAAQ,KAAK,KAAI,CAAE;AAEhE,mBAAS,IAAI,UAAU,GAAG;QAC5B,CAAC;AAED,kBAAU,QAAQ,CAAAA,WAAQ;AACxB,gBAAM,CAAC,UAAU,GAAG,IAAIA,OAAM,MAAM,GAAG,EAAE,IAAI,UAAQ,KAAK,KAAI,CAAE;AAEhE,mBAAS,IAAI,UAAU,GAAG;QAC5B,CAAC;AAED,yBAAiB,GAAG,IAAI,MAAM,KAAK,SAAS,QAAO,CAAE,EAAE,IAAI,CAAC,CAAC,UAAU,GAAG,MAAM,GAAG,QAAQ,KAAK,GAAG,EAAE,EAAE,KAAK,IAAI;aAC3G;AACL,yBAAiB,GAAG,IAAI;;IAE5B,CAAC;AAED,WAAO;KACN,CAAA,CAAE;AACT;AC7CgB,SAAA,sBACd,YACA,qBAAyC;AAEzC,SAAO,oBACJ,OACC,eAAa,UAAU,SAAS,WAAW,KAAK,IAAI,EAErD,OAAO,UAAQ,KAAK,UAAU,QAAQ,EACtC,IAAI,UAAO;AACV,QAAI,CAAC,KAAK,UAAU,YAAY;AAC9B,aAAO;QACL,CAAC,KAAK,IAAI,GAAG,WAAW,MAAM,KAAK,IAAI;;;AAI3C,WAAO,KAAK,UAAU,WAAW,WAAW,KAAK,KAAK,CAAA;EACxD,CAAC,EACA,OAAO,CAAC,YAAY,cAAc,gBAAgB,YAAY,SAAS,GAAG,CAAA,CAAE;AACjF;ACvBM,SAAU,WAAW,OAAU;AACnC,SAAO,OAAO,UAAU;AAC1B;ACOM,SAAU,aAAgB,OAAU,UAAe,WAAc,OAAY;AACjF,MAAI,WAAW,KAAK,GAAG;AACrB,QAAI,SAAS;AACX,aAAO,MAAM,KAAK,OAAO,EAAE,GAAG,KAAK;;AAGrC,WAAO,MAAM,GAAG,KAAK;;AAGvB,SAAO;AACT;ACpBgB,SAAA,cAAc,QAAQ,CAAA,GAAE;AACtC,SAAO,OAAO,KAAK,KAAK,EAAE,WAAW,KAAK,MAAM,gBAAgB;AAClE;ACFM,SAAU,WAAW,OAAU;AACnC,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;;AAGT,MAAI,MAAM,MAAM,sBAAsB,GAAG;AACvC,WAAO,OAAO,KAAK;;AAGrB,MAAI,UAAU,QAAQ;AACpB,WAAO;;AAGT,MAAI,UAAU,SAAS;AACrB,WAAO;;AAGT,SAAO;AACT;ACPgB,SAAA,qCACd,WACA,qBAAyC;AAEzC,MAAI,WAAW,WAAW;AACxB,WAAO;;AAGT,SAAO;IACL,GAAG;IACH,UAAU,CAAC,SAAqB;AAC9B,YAAM,gBAAgB,UAAU,WAAW,UAAU,SAAS,IAAI,IAAI,UAAU;AAEhF,UAAI,kBAAkB,OAAO;AAC3B,eAAO;;AAGT,YAAM,gBAAgB,oBAAoB,OAAO,CAAC,OAAO,SAAQ;AAC/D,cAAM,QAAQ,KAAK,UAAU,YACzB,KAAK,UAAU,UAAU,IAAI,IAC7B,WAAY,KAAM,aAAa,KAAK,IAAI,CAAC;AAE7C,YAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,iBAAO;;AAGT,eAAO;UACL,GAAG;UACH,CAAC,KAAK,IAAI,GAAG;;SAEd,CAAA,CAAE;AAEL,aAAO,EAAE,GAAG,eAAe,GAAG,cAAa;;;AAGjD;AChCA,SAAS,kBAAqB,MAAO;AACnC,SAAO,OAAO;;IAEZ,OAAO,QAAQ,IAAI,EAAE,OAAO,CAAC,CAAC,KAAK,KAAK,MAAK;AAC3C,UAAI,QAAQ,WAAW,cAAc,KAA2B,GAAG;AACjE,eAAO;;AAGT,aAAO,UAAU,QAAQ,UAAU;KACpC;EAAC;AAEN;AAQgB,SAAA,8BAA8B,YAAwB,QAAe;;AACnF,QAAM,gBAAgB,4BAA4B,UAAU;AAC5D,QAAM,EAAE,gBAAgB,eAAc,IAAK,gBAAgB,UAAU;AACrE,QAAM,WAAU,KAAA,eAAe,KAAK,eAAa,kBAAkB,WAAW,SAAS,CAAC,OAAC,QAAA,OAAA,SAAA,SAAA,GAAE;AAE3F,QAAM,QAAQ,OAAO,YACnB,eAAe,IAAI,eAAY;AAC7B,UAAM,sBAAsB,cAAc,OACxC,eAAa,UAAU,SAAS,UAAU,IAAI;AAEhD,UAAM,UAAU;MACd,MAAM,UAAU;MAChB,SAAS,UAAU;MACnB,SAAS,UAAU;MACnB;;AAGF,UAAM,kBAAkB,WAAW,OAAO,CAAC,QAAQ,MAAK;AACtD,YAAM,mBAAmB,kBACvB,GACA,oBACA,OAAO;AAGT,aAAO;QACL,GAAG;QACH,GAAI,mBAAmB,iBAAiB,SAAS,IAAI,CAAA;;OAEtD,CAAA,CAAE;AAEL,UAAM,SAAmB,kBAAkB;MACzC,GAAG;MACH,SAAS,aACP,kBAAyC,WAAW,WAAW,OAAO,CAAC;MAEzE,OAAO,aAAa,kBAAuC,WAAW,SAAS,OAAO,CAAC;MACvF,OAAO,aAAa,kBAAuC,WAAW,SAAS,OAAO,CAAC;MACvF,QAAQ,aAAa,kBAAwC,WAAW,UAAU,OAAO,CAAC;MAC1F,MAAM,aAAa,kBAAsC,WAAW,QAAQ,OAAO,CAAC;MACpF,YAAY,aACV,kBAA4C,WAAW,cAAc,OAAO,CAAC;MAE/E,WAAW,aACT,kBAA2C,WAAW,aAAa,OAAO,CAAC;MAE7E,MAAM,aAAa,kBAAsC,WAAW,QAAQ,OAAO,CAAC;MACpF,YAAY,aAAa,kBAA4C,WAAW,cAAc,OAAO,CAAC;MACtG,sBAAsB,aAAa,kBAAsD,WAAW,wBAAwB,OAAO,CAAC;MACpI,UAAU,aACR,kBAA0C,WAAW,YAAY,OAAO,CAAC;MAE3E,WAAW,aACT,kBAA2C,WAAW,aAAa,OAAO,CAAC;MAE7E,OAAO,OAAO,YACZ,oBAAoB,IAAI,wBAAqB;;AAC3C,eAAO,CAAC,mBAAmB,MAAM,EAAE,UAASC,MAAA,uBAAkB,QAAlB,uBAAkB,SAAA,SAAlB,mBAAoB,eAAS,QAAAA,QAAA,SAAA,SAAAA,IAAE,QAAO,CAAE;MACtF,CAAC,CAAC;IAEL,CAAA;AAED,UAAM,YAAY,aAChB,kBAA2C,WAAW,aAAa,OAAO,CAAC;AAG7E,QAAI,WAAW;AACb,aAAO,WAAW,UAAU,IAAI,eAAa,qCAAqC,WAAW,mBAAmB,CAAC;;AAGnH,UAAM,aAAa,kBACjB,WACA,cACA,OAAO;AAGT,QAAI,YAAY;AACd,aAAO,QAAQ,UAAQ,WAAW;QAChC;QACA,gBAAgB,sBAAsB,MAAM,mBAAmB;MAChE,CAAA;;AAGH,UAAM,aAAa,kBACjB,WACA,cACA,OAAO;AAGT,QAAI,YAAY;AACd,aAAO,SAAS;;AAGlB,WAAO,CAAC,UAAU,MAAM,MAAM;GAC/B,CAAC;AAGJ,QAAM,QAAQ,OAAO,YACnB,eAAe,IAAI,eAAY;AAC7B,UAAM,sBAAsB,cAAc,OACxC,eAAa,UAAU,SAAS,UAAU,IAAI;AAEhD,UAAM,UAAU;MACd,MAAM,UAAU;MAChB,SAAS,UAAU;MACnB,SAAS,UAAU;MACnB;;AAGF,UAAM,kBAAkB,WAAW,OAAO,CAAC,QAAQ,MAAK;AACtD,YAAM,mBAAmB,kBACvB,GACA,oBACA,OAAO;AAGT,aAAO;QACL,GAAG;QACH,GAAI,mBAAmB,iBAAiB,SAAgB,IAAI,CAAA;;OAE7D,CAAA,CAAE;AAEL,UAAM,SAAmB,kBAAkB;MACzC,GAAG;MACH,WAAW,aACT,kBAA2C,WAAW,aAAa,OAAO,CAAC;MAE7E,UAAU,aACR,kBAA0C,WAAW,YAAY,OAAO,CAAC;MAE3E,OAAO,aAAa,kBAAuC,WAAW,SAAS,OAAO,CAAC;MACvF,UAAU,aACR,kBAA0C,WAAW,YAAY,OAAO,CAAC;MAE3E,MAAM,aAAa,kBAAsC,WAAW,QAAQ,OAAO,CAAC;MACpF,OAAO,OAAO,YACZ,oBAAoB,IAAI,wBAAqB;;AAC3C,eAAO,CAAC,mBAAmB,MAAM,EAAE,UAASA,MAAA,uBAAkB,QAAlB,uBAAkB,SAAA,SAAlB,mBAAoB,eAAS,QAAAA,QAAA,SAAA,SAAAA,IAAE,QAAO,CAAE;MACtF,CAAC,CAAC;IAEL,CAAA;AAED,UAAM,YAAY,aAChB,kBAA2C,WAAW,aAAa,OAAO,CAAC;AAG7E,QAAI,WAAW;AACb,aAAO,WAAW,UAAU,IAAI,eAAa,qCAAqC,WAAW,mBAAmB,CAAC;;AAGnH,UAAM,aAAa,kBACjB,WACA,cACA,OAAO;AAGT,QAAI,YAAY;AACd,aAAO,QAAQ,UAAQ,WAAW;QAChC;QACA,gBAAgB,sBAAsB,MAAM,mBAAmB;MAChE,CAAA;;AAGH,WAAO,CAAC,UAAU,MAAM,MAAM;GAC/B,CAAC;AAGJ,SAAO,IAAI,OAAO;IAChB;IACA;IACA;EACD,CAAA;AACH;ACpMgB,SAAA,oBAAoB,MAAc,QAAc;AAC9D,SAAO,OAAO,MAAM,IAAI,KAAK,OAAO,MAAM,IAAI,KAAK;AACrD;ACRgB,SAAA,wBAAwB,WAAyB,SAAoB;AACnF,MAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,WAAO,QAAQ,KAAK,sBAAmB;AACrC,YAAM,OAAO,OAAO,qBAAqB,WACrC,mBACA,iBAAiB;AAErB,aAAO,SAAS,UAAU;IAC5B,CAAC;;AAGH,SAAO;AACT;ACZgB,SAAA,oBAAoB,UAAoB,QAAc;AACpE,QAAM,mBAAmB,cAAc,WAAW,MAAM,EAAE,kBAAkB,QAAQ;AAEpF,QAAM,oBAAoB,SAAS,eAAe,mBAAkB;AACpE,QAAM,YAAY,kBAAkB,cAAc,KAAK;AAEvD,YAAU,YAAY,gBAAgB;AAEtC,SAAO,UAAU;AACnB;ACHa,IAAA,0BAA0B,CAAC,OAAoB,WAAW,QAAO;AAC5E,MAAI,aAAa;AAEjB,QAAM,cAAc,MAAM;AAE1B,QAAM,OAAO,aACX,KAAK,IAAI,GAAG,cAAc,QAAQ,GAClC,aACA,CAAC,MAAM,KAAK,QAAQC,WAAS;;AAC3B,UAAM,UAAQ,MAAA,KAAA,KAAK,KAAK,MAAK,YAAS,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAA;MACpC;MACA;MACA;MACA,OAAAA;KACD,MACI,KAAK,eACL;AAEL,kBAAc,KAAK,UAAU,CAAC,KAAK,SAAS,QAAQ,MAAM,MAAM,GAAG,KAAK,IAAI,GAAG,cAAc,GAAG,CAAC;EACnG,CAAC;AAGH,SAAO;AACT;AC/BM,SAAU,SAAS,OAAU;AACjC,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AACnD;ICyBa,kBAAS;EAYpB,YAAY,QAUX;AACC,SAAK,OAAO,OAAO;AACnB,SAAK,UAAU,OAAO;;AAEzB;AAED,IAAM,0BAA0B,CAC9B,MACA,SACmC;AACnC,MAAI,SAAS,IAAI,GAAG;AAClB,WAAO,KAAK,KAAK,IAAI;;AAGvB,QAAM,iBAAiB,KAAK,IAAI;AAEhC,MAAI,CAAC,gBAAgB;AACnB,WAAO;;AAGT,QAAM,SAAmC,CAAC,eAAe,IAAI;AAE7D,SAAO,QAAQ,eAAe;AAC9B,SAAO,QAAQ;AACf,SAAO,OAAO,eAAe;AAE7B,MAAI,eAAe,aAAa;AAC9B,QAAI,CAAC,eAAe,KAAK,SAAS,eAAe,WAAW,GAAG;AAC7D,cAAQ,KACN,oFAAoF;;AAIxF,WAAO,KAAK,eAAe,WAAW;;AAGxC,SAAO;AACT;AAEA,SAASH,MAAI,QAOZ;;AACC,QAAM,EACJ,QAAQ,MAAM,IAAI,MAAM,OAAO,OAAM,IACnC;AACJ,QAAM,EAAE,KAAI,IAAK;AAEjB,MAAI,KAAK,WAAW;AAClB,WAAO;;AAGT,QAAM,QAAQ,KAAK,MAAM,IAAI,QAAQ,IAAI;AAEzC;;IAEE,MAAM,OAAO,KAAK,KAAK,QAEpB,CAAC,GAAC,KAAC,MAAM,cAAc,MAAM,eAAU,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM,KAAK,UAAQ,KAAK,KAAK,KAAK,IAAI;IAClF;AACA,WAAO;;AAGT,MAAI,UAAU;AAEd,QAAM,aAAa,wBAAwB,KAAK,IAAI;AAEpD,QAAM,QAAQ,UAAO;AACnB,QAAI,SAAS;AACX;;AAGF,UAAM,QAAQ,wBAAwB,YAAY,KAAK,IAAI;AAE3D,QAAI,CAAC,OAAO;AACV;;AAGF,UAAM,KAAK,KAAK,MAAM;AACtB,UAAM,QAAQ,qBAAqB;MACjC,OAAO,KAAK;MACZ,aAAa;IACd,CAAA;AACD,UAAM,QAAQ;MACZ,MAAM,QAAQ,MAAM,CAAC,EAAE,SAAS,KAAK;MACrC;;AAGF,UAAM,EAAE,UAAAI,WAAU,OAAO,IAAG,IAAK,IAAI,eAAe;MAClD;MACA;IACD,CAAA;AAED,UAAM,UAAU,KAAK,QAAQ;MAC3B;MACA;MACA;MACA,UAAAA;MACA;MACA;IACD,CAAA;AAGD,QAAI,YAAY,QAAQ,CAAC,GAAG,MAAM,QAAQ;AACxC;;AAKF,OAAG,QAAQ,QAAQ;MACjB,WAAW;MACX;MACA;MACA;IACD,CAAA;AAED,SAAK,SAAS,EAAE;AAChB,cAAU;EACZ,CAAC;AAED,SAAO;AACT;AAOM,SAAU,iBAAiB,OAA6C;AAC5E,QAAM,EAAE,QAAQ,MAAK,IAAK;AAC1B,QAAM,SAAS,IAAI,OAAO;IACxB,OAAO;MACL,OAAI;AACF,eAAO;;MAET,MAAM,IAAI,MAAM,OAAK;AACnB,cAAM,SAAS,GAAG,QAAQ,MAAM;AAEhC,YAAI,QAAQ;AACV,iBAAO;;AAIT,cAAM,qBAAqB,GAAG,QAAQ,iBAAiB;AAMvD,cAAM,mBAAmB,CAAC,CAAC;AAE3B,YAAI,kBAAkB;AACpB,qBAAW,MAAK;AACd,gBAAI,EAAE,KAAI,IAAK;AAEf,gBAAI,OAAO,SAAS,UAAU;AAC5B,qBAAO;mBACF;AACL,qBAAO,oBAAoB,SAAS,KAAK,IAAI,GAAG,MAAM,MAAM;;AAG9D,kBAAM,EAAE,KAAI,IAAK;AACjB,kBAAM,KAAK,OAAO,KAAK;AAEvBJ,kBAAI;cACF;cACA;cACA;cACA;cACA;cACA;YACD,CAAA;UACH,CAAC;;AAGH,eAAO,GAAG,gBAAgB,GAAG,aAAa,OAAO;;IAEpD;IAED,OAAO;MACL,gBAAgB,MAAM,MAAM,IAAI,MAAI;AAClC,eAAOA,MAAI;UACT;UACA;UACA;UACA;UACA;UACA;QACD,CAAA;;MAGH,iBAAiB;QACf,gBAAgB,UAAO;AACrB,qBAAW,MAAK;AACd,kBAAM,EAAE,QAAO,IAAK,KAAK,MAAM;AAE/B,gBAAI,SAAS;AACXA,oBAAI;gBACF;gBACA,MAAM,QAAQ;gBACd,IAAI,QAAQ;gBACZ,MAAM;gBACN;gBACA;cACD,CAAA;;UAEL,CAAC;AAED,iBAAO;;MAEV;;;MAID,cAAc,MAAM,OAAK;AACvB,YAAI,MAAM,QAAQ,SAAS;AACzB,iBAAO;;AAGT,cAAM,EAAE,QAAO,IAAK,KAAK,MAAM;AAE/B,YAAI,SAAS;AACX,iBAAOA,MAAI;YACT;YACA,MAAM,QAAQ;YACd,IAAI,QAAQ;YACZ,MAAM;YACN;YACA;UACD,CAAA;;AAGH,eAAO;;IAEV;;IAGD,cAAc;EACf,CAAA;AAED,SAAO;AACT;ACpSA,SAAS,QAAQ,OAAU;AACzB,SAAO,OAAO,UAAU,SAAS,KAAK,KAAK,EAAE,MAAM,GAAG,EAAE;AAC1D;AAEM,SAAU,cAAc,OAAU;AACtC,MAAI,QAAQ,KAAK,MAAM,UAAU;AAC/B,WAAO;;AAGT,SAAO,MAAM,gBAAgB,UAAU,OAAO,eAAe,KAAK,MAAM,OAAO;AACjF;ACVgB,SAAA,UAAU,QAA6B,QAA2B;AAChF,QAAM,SAAS,EAAE,GAAG,OAAM;AAE1B,MAAI,cAAc,MAAM,KAAK,cAAc,MAAM,GAAG;AAClD,WAAO,KAAK,MAAM,EAAE,QAAQ,SAAM;AAChC,UAAI,cAAc,OAAO,GAAG,CAAC,KAAK,cAAc,OAAO,GAAG,CAAC,GAAG;AAC5D,eAAO,GAAG,IAAI,UAAU,OAAO,GAAG,GAAG,OAAO,GAAG,CAAC;aAC3C;AACL,eAAO,GAAG,IAAI,OAAO,GAAG;;IAE5B,CAAC;;AAGH,SAAO;AACT;ICugBa,aAAA,MAAI;EAkBf,YAAY,SAAgD,CAAA,GAAE;AAjB9D,SAAI,OAAG;AAEP,SAAI,OAAG;AAEP,SAAM,SAAgB;AAEtB,SAAK,QAAgB;AAMrB,SAAA,SAAqB;MACnB,MAAM,KAAK;MACX,gBAAgB,CAAA;;AAIhB,SAAK,SAAS;MACZ,GAAG,KAAK;MACR,GAAG;;AAGL,SAAK,OAAO,KAAK,OAAO;AAExB,QAAI,OAAO,kBAAkB,OAAO,KAAK,OAAO,cAAc,EAAE,SAAS,GAAG;AAC1E,cAAQ,KACN,yHAAyH,KAAK,IAAI,IAAI;;AAK1I,SAAK,UAAU,KAAK,OAAO;AAE3B,QAAI,KAAK,OAAO,YAAY;AAC1B,WAAK,UAAU,aACb,kBAA2C,MAAM,cAAc;QAC7D,MAAM,KAAK;MACZ,CAAA,CAAC;;AAIN,SAAK,UAAU,aACb,kBAA2C,MAAM,cAAc;MAC7D,MAAM,KAAK;MACX,SAAS,KAAK;KACf,CAAC,KACC,CAAA;;EAGP,OAAO,OAAyB,SAAoC,CAAA,GAAE;AACpE,WAAO,IAAI,MAAW,MAAM;;EAG9B,UAAU,UAA4B,CAAA,GAAE;AAGtC,UAAM,YAAY,KAAK,OAAyB;MAC9C,GAAG,KAAK;MACR,YAAY,MAAK;AACf,eAAO,UAAU,KAAK,SAAgC,OAAO;;IAEhE,CAAA;AAGD,cAAU,OAAO,KAAK;AAEtB,cAAU,SAAS,KAAK;AAExB,WAAO;;EAGT,OACE,iBAAwE,CAAA,GAAE;AAE1E,UAAM,YAAY,IAAI,MAAuC,cAAc;AAE3E,cAAU,SAAS;AAEnB,SAAK,QAAQ;AAEb,cAAU,OAAO,eAAe,OAAO,eAAe,OAAO,UAAU,OAAO;AAE9E,QAAI,eAAe,kBAAkB,OAAO,KAAK,eAAe,cAAc,EAAE,SAAS,GAAG;AAC1F,cAAQ,KACN,yHAAyH,UAAU,IAAI,IAAI;;AAI/I,cAAU,UAAU,aAClB,kBAA2C,WAAW,cAAc;MAClE,MAAM,UAAU;IACjB,CAAA,CAAC;AAGJ,cAAU,UAAU,aAClB,kBAA2C,WAAW,cAAc;MAClE,MAAM,UAAU;MAChB,SAAS,UAAU;IACpB,CAAA,CAAC;AAGJ,WAAO;;EAGT,OAAO,WAAW,EAAE,QAAQ,KAAI,GAAkC;AAChE,UAAM,EAAE,GAAE,IAAK,OAAO;AACtB,UAAM,aAAa,OAAO,MAAM,UAAU;AAC1C,UAAM,UAAU,WAAW,QAAQ,WAAW,IAAG;AAEjD,QAAI,SAAS;AACX,YAAM,eAAe,WAAW,MAAK;AACrC,YAAM,WAAW,CAAC,CAAC,aAAa,KAAK,QAAK,MAAC,QAAD,MAAC,SAAA,SAAD,EAAG,KAAK,UAAS,KAAK,IAAI;AAEpE,UAAI,CAAC,UAAU;AACb,eAAO;;AAGT,YAAM,aAAa,aAAa,KAAK,QAAK,MAAC,QAAD,MAAC,SAAA,SAAD,EAAG,KAAK,UAAS,KAAK,IAAI;AAEpE,UAAI,YAAY;AACd,WAAG,iBAAiB,UAAU;;AAEhC,SAAG,WAAW,KAAK,WAAW,GAAG;AAEjC,aAAO,KAAK,SAAS,EAAE;AAEvB,aAAO;;AAGT,WAAO;;AAEV;AC5pBK,SAAU,SAAS,OAAU;AACjC,SAAO,OAAO,UAAU;AAC1B;IC+Ba,kBAAS;EAcpB,YAAY,QAYX;AACC,SAAK,OAAO,OAAO;AACnB,SAAK,UAAU,OAAO;;AAEzB;AAED,IAAM,0BAA0B,CAC9B,MACA,MACA,UAC8B;AAC9B,MAAI,SAAS,IAAI,GAAG;AAClB,WAAO,CAAC,GAAG,KAAK,SAAS,IAAI,CAAC;;AAGhC,QAAM,UAAU,KAAK,MAAM,KAAK;AAEhC,MAAI,CAAC,SAAS;AACZ,WAAO,CAAA;;AAGT,SAAO,QAAQ,IAAI,oBAAiB;AAClC,UAAM,SAAmC,CAAC,eAAe,IAAI;AAE7D,WAAO,QAAQ,eAAe;AAC9B,WAAO,QAAQ;AACf,WAAO,OAAO,eAAe;AAE7B,QAAI,eAAe,aAAa;AAC9B,UAAI,CAAC,eAAe,KAAK,SAAS,eAAe,WAAW,GAAG;AAC7D,gBAAQ,KACN,oFAAoF;;AAIxF,aAAO,KAAK,eAAe,WAAW;;AAGxC,WAAO;EACT,CAAC;AACH;AAEA,SAAS,IAAI,QAQZ;AACC,QAAM,EACJ,QAAQ,OAAO,MAAM,IAAI,MAAM,YAAY,UAAS,IAClD;AAEJ,QAAM,EAAE,UAAAI,WAAU,OAAO,IAAG,IAAK,IAAI,eAAe;IAClD;IACA;EACD,CAAA;AAED,QAAM,WAA4B,CAAA;AAElC,QAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAO;AAC7C,QAAI,CAAC,KAAK,eAAe,KAAK,KAAK,KAAK,MAAM;AAC5C;;AAGF,UAAM,eAAe,KAAK,IAAI,MAAM,GAAG;AACvC,UAAM,aAAa,KAAK,IAAI,IAAI,MAAM,KAAK,QAAQ,IAAI;AACvD,UAAM,cAAc,KAAK,YAAY,eAAe,KAAK,aAAa,KAAK,QAAW,GAAQ;AAE9F,UAAM,UAAU,wBAAwB,aAAa,KAAK,MAAM,UAAU;AAE1E,YAAQ,QAAQ,WAAQ;AACtB,UAAI,MAAM,UAAU,QAAW;AAC7B;;AAGF,YAAM,QAAQ,eAAe,MAAM,QAAQ;AAC3C,YAAM,MAAM,QAAQ,MAAM,CAAC,EAAE;AAC7B,YAAM,QAAQ;QACZ,MAAM,MAAM,GAAG,QAAQ,IAAI,KAAK;QAChC,IAAI,MAAM,GAAG,QAAQ,IAAI,GAAG;;AAG9B,YAAM,UAAU,KAAK,QAAQ;QAC3B;QACA;QACA;QACA,UAAAA;QACA;QACA;QACA;QACA;MACD,CAAA;AAED,eAAS,KAAK,OAAO;IACvB,CAAC;EACH,CAAC;AAED,QAAM,UAAU,SAAS,MAAM,aAAW,YAAY,IAAI;AAE1D,SAAO;AACT;AAGA,IAAI,4BAA2C;AAE/C,IAAM,4BAA4B,CAAC,SAAgB;;AACjD,QAAM,QAAQ,IAAI,eAAe,SAAS;IACxC,eAAe,IAAI,aAAY;EAChC,CAAA;AAED,GAAA,KAAA,MAAM,mBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,QAAQ,aAAa,IAAI;AAE9C,SAAO;AACT;AAOM,SAAU,iBAAiB,OAA6C;AAC5E,QAAM,EAAE,QAAQ,MAAK,IAAK;AAC1B,MAAI,oBAAoC;AACxC,MAAI,0BAA0B;AAC9B,MAAI,2BAA2B;AAC/B,MAAI,aAAa,OAAO,mBAAmB,cAAc,IAAI,eAAe,OAAO,IAAI;AACvF,MAAI;AAEJ,MAAI;AACF,gBAAY,OAAO,cAAc,cAAc,IAAI,UAAU,MAAM,IAAI;UACjE;AACN,gBAAY;;AAGd,QAAM,eAAe,CAAC,EACpB,OACA,MACA,IACA,MACA,SAAQ,MAOL;AACH,UAAM,KAAK,MAAM;AACjB,UAAM,iBAAiB,qBAAqB;MAC1C;MACA,aAAa;IACd,CAAA;AAED,UAAM,UAAU,IAAI;MAClB;MACA,OAAO;MACP,MAAM,KAAK,IAAI,OAAO,GAAG,CAAC;MAC1B,IAAI,GAAG,IAAI;MACX;MACA,YAAY;MACZ;IACD,CAAA;AAED,QAAI,CAAC,WAAW,CAAC,GAAG,MAAM,QAAQ;AAChC;;AAGF,QAAI;AACF,kBAAY,OAAO,cAAc,cAAc,IAAI,UAAU,MAAM,IAAI;YACjE;AACN,kBAAY;;AAEd,iBAAa,OAAO,mBAAmB,cAAc,IAAI,eAAe,OAAO,IAAI;AAEnF,WAAO;EACT;AAEA,QAAM,UAAU,MAAM,IAAI,UAAO;AAC/B,WAAO,IAAI,OAAO;;MAEhB,KAAK,MAAI;AACP,cAAM,kBAAkB,CAAC,UAAoB;;AAC3C,gCAAoB,KAAA,KAAK,IAAI,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS,MAAM,MAAiB,KACxE,KAAK,IAAI,gBACT;AAEJ,cAAI,mBAAmB;AACrB,wCAA4B;;QAEhC;AAEA,cAAM,gBAAgB,MAAK;AACzB,cAAI,2BAA2B;AAC7B,wCAA4B;;QAEhC;AAEA,eAAO,iBAAiB,aAAa,eAAe;AACpD,eAAO,iBAAiB,WAAW,aAAa;AAEhD,eAAO;UACL,UAAO;AACL,mBAAO,oBAAoB,aAAa,eAAe;AACvD,mBAAO,oBAAoB,WAAW,aAAa;;;;MAKzD,OAAO;QACL,iBAAiB;UACf,MAAM,CAAC,MAAM,UAAgB;AAC3B,uCAA2B,sBAAsB,KAAK,IAAI;AAC1D,wBAAY;AAEZ,gBAAI,CAAC,0BAA0B;AAC7B,oBAAM,sBAAsB;AAE5B,kBAAI,qBAAqB;AAEvB,2BAAW,MAAK;AACd,wBAAM,YAAY,oBAAoB,MAAM;AAE5C,sBAAI,WAAW;AACb,wCAAoB,SAAS,YAAY,EAAE,MAAM,UAAU,MAAM,IAAI,UAAU,GAAE,CAAE;;mBAEpF,EAAE;;;AAGT,mBAAO;;UAGT,OAAO,CAAC,OAAO,UAAgB;;AAC7B,kBAAM,QAAO,KAAC,MAAyB,mBAAa,QAAA,OAAA,SAAA,SAAA,GAAE,QAAQ,WAAW;AAEzE,yBAAa;AAEb,sCAA0B,CAAC,EAAC,SAAA,QAAA,SAAA,SAAA,SAAA,KAAM,SAAS,eAAe;AAE1D,mBAAO;;QAEV;MACF;MAED,mBAAmB,CAAC,cAAc,UAAU,UAAS;AACnD,cAAM,cAAc,aAAa,CAAC;AAClC,cAAM,UAAU,YAAY,QAAQ,SAAS,MAAM,WAAW,CAAC;AAC/D,cAAM,SAAS,YAAY,QAAQ,SAAS,MAAM,UAAU,CAAC;AAG7D,cAAM,qBAAqB,YAAY,QAAQ,iBAAiB;AAGhE,cAAM,mBAAmB,CAAC,CAAC;AAE3B,YAAI,CAAC,WAAW,CAAC,UAAU,CAAC,kBAAkB;AAC5C;;AAIF,YAAI,kBAAkB;AACpB,cAAI,EAAE,KAAI,IAAK;AAEf,cAAI,OAAO,SAAS,UAAU;AAC5B,mBAAO;iBACF;AACL,mBAAO,oBAAoB,SAAS,KAAK,IAAI,GAAG,MAAM,MAAM;;AAG9D,gBAAM,EAAE,MAAAC,MAAI,IAAK;AACjB,gBAAMC,MAAKD,QAAO,KAAK;AAEvB,gBAAM,WAAW,0BAA0B,IAAI;AAE/C,iBAAO,aAAa;YAClB;YACA;YACA,MAAAA;YACA,IAAI,EAAE,GAAGC,IAAE;YACX;UACD,CAAA;;AAIH,cAAM,OAAO,SAAS,IAAI,QAAQ,cAAc,MAAM,IAAI,OAAO;AACjE,cAAM,KAAK,SAAS,IAAI,QAAQ,YAAY,MAAM,IAAI,OAAO;AAG7D,YAAI,CAAC,SAAS,IAAI,KAAK,CAAC,MAAM,SAAS,GAAG,GAAG;AAC3C;;AAGF,eAAO,aAAa;UAClB;UACA;UACA;UACA;UACA,UAAU;QACX,CAAA;;IAEJ,CAAA;EACH,CAAC;AAED,SAAO;AACT;AC7WM,SAAU,eAAe,OAAY;AACzC,QAAM,WAAW,MAAM,OAAO,CAAC,IAAIH,WAAU,MAAM,QAAQ,EAAE,MAAMA,MAAK;AAExE,SAAO,MAAM,KAAK,IAAI,IAAI,QAAQ,CAAC;AACrC;ICkBa,yBAAA,kBAAgB;EAS3B,YAAY,YAAwB,QAAc;AAFlD,SAAe,kBAAa,CAAA;AAG1B,SAAK,SAAS;AACd,SAAK,aAAa,kBAAiB,QAAQ,UAAU;AACrD,SAAK,SAAS,8BAA8B,KAAK,YAAY,MAAM;AACnE,SAAK,gBAAe;;;;;;;;EAStB,OAAO,QAAQ,YAAsB;AACnC,UAAM,qBAAqB,kBAAiB,KAAK,kBAAiB,QAAQ,UAAU,CAAC;AACrF,UAAM,kBAAkB,eAAe,mBAAmB,IAAI,eAAa,UAAU,IAAI,CAAC;AAE1F,QAAI,gBAAgB,QAAQ;AAC1B,cAAQ,KACN,oDAAoD,gBACjD,IAAI,UAAQ,IAAI,IAAI,GAAG,EACvB,KAAK,IAAI,CAAC,6BAA6B;;AAI9C,WAAO;;;;;;;EAQT,OAAO,QAAQ,YAAsB;AACnC,WACE,WACG,IAAI,eAAY;AACf,YAAM,UAAU;QACd,MAAM,UAAU;QAChB,SAAS,UAAU;QACnB,SAAS,UAAU;;AAGrB,YAAM,gBAAgB,kBACpB,WACA,iBACA,OAAO;AAGT,UAAI,eAAe;AACjB,eAAO,CAAC,WAAW,GAAG,KAAK,QAAQ,cAAa,CAAE,CAAC;;AAGrD,aAAO;IACT,CAAC,EAEA,KAAK,EAAE;;;;;;;EASd,OAAO,KAAK,YAAsB;AAChC,UAAM,kBAAkB;AAExB,WAAO,WAAW,KAAK,CAAC,GAAG,MAAK;AAC9B,YAAM,YAAY,kBAAyC,GAAG,UAAU,KAAK;AAC7E,YAAM,YAAY,kBAAyC,GAAG,UAAU,KAAK;AAE7E,UAAI,YAAY,WAAW;AACzB,eAAO;;AAGT,UAAI,YAAY,WAAW;AACzB,eAAO;;AAGT,aAAO;IACT,CAAC;;;;;;EAOH,IAAI,WAAQ;AACV,WAAO,KAAK,WAAW,OAAO,CAACC,WAAU,cAAa;AACpD,YAAM,UAAU;QACd,MAAM,UAAU;QAChB,SAAS,UAAU;QACnB,SAAS,UAAU;QACnB,QAAQ,KAAK;QACb,MAAM,oBAAoB,UAAU,MAAM,KAAK,MAAM;;AAGvD,YAAM,cAAc,kBAClB,WACA,eACA,OAAO;AAGT,UAAI,CAAC,aAAa;AAChB,eAAOA;;AAGT,aAAO;QACL,GAAGA;QACH,GAAG,YAAW;;OAEf,CAAA,CAAiB;;;;;;EAOtB,IAAI,UAAO;AACT,UAAM,EAAE,OAAM,IAAK;AAOnB,UAAM,aAAa,kBAAiB,KAAK,CAAC,GAAG,KAAK,UAAU,EAAE,QAAO,CAAE;AAEvE,UAAM,aAA0B,CAAA;AAChC,UAAM,aAA0B,CAAA;AAEhC,UAAM,aAAa,WAChB,IAAI,eAAY;AACf,YAAM,UAAU;QACd,MAAM,UAAU;QAChB,SAAS,UAAU;QACnB,SAAS,UAAU;QACnB;QACA,MAAM,oBAAoB,UAAU,MAAM,KAAK,MAAM;;AAGvD,YAAM,UAAoB,CAAA;AAE1B,YAAM,uBAAuB,kBAC3B,WACA,wBACA,OAAO;AAGT,UAAI,kBAAiD,CAAA;AAGrD,UAAI,UAAU,SAAS,UAAU,kBAAyC,WAAW,YAAY,OAAO,GAAG;AACzG,wBAAgB,aAAa,MAAM,KAAK,WAAW,EAAE,QAAQ,MAAM,UAAiB,CAAE;;AAGxF,UAAI,sBAAsB;AACxB,cAAM,WAAW,OAAO,YACtB,OAAO,QAAQ,qBAAoB,CAAE,EAAE,IAAI,CAAC,CAAC,UAAU,MAAM,MAAK;AAChE,iBAAO,CAAC,UAAU,MAAM,OAAO,EAAE,OAAM,CAAE,CAAC;SAC3C,CAAC;AAGJ,0BAAkB,EAAE,GAAG,iBAAiB,GAAG,SAAQ;;AAGrD,YAAM,eAAe,OAAO,eAAe;AAE3C,cAAQ,KAAK,YAAY;AAEzB,YAAM,gBAAgB,kBACpB,WACA,iBACA,OAAO;AAGT,UAAI,wBAAwB,WAAW,OAAO,QAAQ,gBAAgB,KAAK,eAAe;AACxF,mBAAW,KAAK,GAAG,cAAa,CAAE;;AAGpC,YAAM,gBAAgB,kBACpB,WACA,iBACA,OAAO;AAGT,UAAI,wBAAwB,WAAW,OAAO,QAAQ,gBAAgB,KAAK,eAAe;AACxF,mBAAW,KAAK,GAAG,cAAa,CAAE;;AAGpC,YAAM,wBAAwB,kBAC5B,WACA,yBACA,OAAO;AAGT,UAAI,uBAAuB;AACzB,cAAM,qBAAqB,sBAAqB;AAEhD,gBAAQ,KAAK,GAAG,kBAAkB;;AAGpC,aAAO;IACT,CAAC,EACA,KAAI;AAEP,WAAO;MACL,iBAAiB;QACf;QACA,OAAO;OACR;MACD,GAAG,iBAAiB;QAClB;QACA,OAAO;OACR;MACD,GAAG;;;;;;;EAQP,IAAI,aAAU;AACZ,WAAO,4BAA4B,KAAK,UAAU;;;;;;EAOpD,IAAI,YAAS;AACX,UAAM,EAAE,OAAM,IAAK;AACnB,UAAM,EAAE,eAAc,IAAK,gBAAgB,KAAK,UAAU;AAE1D,WAAO,OAAO,YACZ,eACG,OAAO,eAAa,CAAC,CAAC,kBAAkB,WAAW,aAAa,CAAC,EACjE,IAAI,eAAY;AACf,YAAM,sBAAsB,KAAK,WAAW,OAC1C,eAAa,UAAU,SAAS,UAAU,IAAI;AAEhD,YAAM,UAAU;QACd,MAAM,UAAU;QAChB,SAAS,UAAU;QACnB,SAAS,UAAU;QACnB;QACA,MAAM,YAAY,UAAU,MAAM,KAAK,MAAM;;AAE/C,YAAM,cAAc,kBAClB,WACA,eACA,OAAO;AAGT,UAAI,CAAC,aAAa;AAChB,eAAO,CAAA;;AAGT,YAAM,WAAgC,CACpC,MACA,MACA,QACA,aACA,qBACE;AACF,cAAM,iBAAiB,sBAAsB,MAAM,mBAAmB;AAEtE,eAAO,YAAW,EAAG;;UAEnB;UACA;UACA;UACA;UACA;;UAEA;UACA;UACA;QACD,CAAA;MACH;AAEA,aAAO,CAAC,UAAU,MAAM,QAAQ;KACjC,CAAC;;;;;;EAQA,kBAAe;AACrB,SAAK,WAAW,QAAQ,eAAY;;AAElC,WAAK,OAAO,iBAAiB,UAAU,IAAI,IAAI,UAAU;AAEzD,YAAM,UAAU;QACd,MAAM,UAAU;QAChB,SAAS,UAAU;QACnB,SAAS,UAAU;QACnB,QAAQ,KAAK;QACb,MAAM,oBAAoB,UAAU,MAAM,KAAK,MAAM;;AAGvD,UAAI,UAAU,SAAS,QAAQ;AAC7B,cAAM,eAAc,KAAA,aAAa,kBAAkB,WAAW,eAAe,OAAO,CAAC,OAAC,QAAA,OAAA,SAAA,KAAI;AAE1F,YAAI,aAAa;AACf,eAAK,gBAAgB,KAAK,UAAU,IAAI;;;AAI5C,YAAM,iBAAiB,kBACrB,WACA,kBACA,OAAO;AAET,YAAM,WAAW,kBAAyC,WAAW,YAAY,OAAO;AACxF,YAAM,WAAW,kBAAyC,WAAW,YAAY,OAAO;AACxF,YAAM,oBAAoB,kBACxB,WACA,qBACA,OAAO;AAET,YAAM,gBAAgB,kBACpB,WACA,iBACA,OAAO;AAET,YAAM,UAAU,kBAAwC,WAAW,WAAW,OAAO;AACrF,YAAM,SAAS,kBAAuC,WAAW,UAAU,OAAO;AAClF,YAAM,YAAY,kBAA0C,WAAW,aAAa,OAAO;AAE3F,UAAI,gBAAgB;AAClB,aAAK,OAAO,GAAG,gBAAgB,cAAc;;AAG/C,UAAI,UAAU;AACZ,aAAK,OAAO,GAAG,UAAU,QAAQ;;AAGnC,UAAI,UAAU;AACZ,aAAK,OAAO,GAAG,UAAU,QAAQ;;AAGnC,UAAI,mBAAmB;AACrB,aAAK,OAAO,GAAG,mBAAmB,iBAAiB;;AAGrD,UAAI,eAAe;AACjB,aAAK,OAAO,GAAG,eAAe,aAAa;;AAG7C,UAAI,SAAS;AACX,aAAK,OAAO,GAAG,SAAS,OAAO;;AAGjC,UAAI,QAAQ;AACV,aAAK,OAAO,GAAG,QAAQ,MAAM;;AAG/B,UAAI,WAAW;AACb,aAAK,OAAO,GAAG,WAAW,SAAS;;IAEvC,CAAC;;AAEJ;ICIY,kBAAA,WAAS;EAkBpB,YAAY,SAAqD,CAAA,GAAE;AAjBnE,SAAI,OAAG;AAEP,SAAI,OAAG;AAEP,SAAM,SAAqB;AAE3B,SAAK,QAAqB;AAM1B,SAAA,SAA0B;MACxB,MAAM,KAAK;MACX,gBAAgB,CAAA;;AAIhB,SAAK,SAAS;MACZ,GAAG,KAAK;MACR,GAAG;;AAGL,SAAK,OAAO,KAAK,OAAO;AAExB,QAAI,OAAO,kBAAkB,OAAO,KAAK,OAAO,cAAc,EAAE,SAAS,GAAG;AAC1E,cAAQ,KACN,yHAAyH,KAAK,IAAI,IAAI;;AAK1I,SAAK,UAAU,KAAK,OAAO;AAE3B,QAAI,KAAK,OAAO,YAAY;AAC1B,WAAK,UAAU,aACb,kBAA2C,MAAM,cAAc;QAC7D,MAAM,KAAK;MACZ,CAAA,CAAC;;AAIN,SAAK,UAAU,aACb,kBAA2C,MAAM,cAAc;MAC7D,MAAM,KAAK;MACX,SAAS,KAAK;KACf,CAAC,KACC,CAAA;;EAGP,OAAO,OAAyB,SAAyC,CAAA,GAAE;AACzE,WAAO,IAAI,WAAgB,MAAM;;EAGnC,UAAU,UAA4B,CAAA,GAAE;AAGtC,UAAM,YAAY,KAAK,OAAyB;MAC9C,GAAG,KAAK;MACR,YAAY,MAAK;AACf,eAAO,UAAU,KAAK,SAAgC,OAAO;;IAEhE,CAAA;AAGD,cAAU,OAAO,KAAK;AAEtB,cAAU,SAAS,KAAK;AAExB,WAAO;;EAGT,OACE,iBAA6E,CAAA,GAAE;AAE/E,UAAM,YAAY,IAAI,WAA4C,EAAE,GAAG,KAAK,QAAQ,GAAG,eAAc,CAAE;AAEvG,cAAU,SAAS;AAEnB,SAAK,QAAQ;AAEb,cAAU,OAAO,eAAe,OAAO,eAAe,OAAO,UAAU,OAAO;AAE9E,QAAI,eAAe,kBAAkB,OAAO,KAAK,eAAe,cAAc,EAAE,SAAS,GAAG;AAC1F,cAAQ,KACN,yHAAyH,UAAU,IAAI,IAAI;;AAI/I,cAAU,UAAU,aAClB,kBAA2C,WAAW,cAAc;MAClE,MAAM,UAAU;IACjB,CAAA,CAAC;AAGJ,cAAU,UAAU,aAClB,kBAA2C,WAAW,cAAc;MAClE,MAAM,UAAU;MAChB,SAAS,UAAU;IACpB,CAAA,CAAC;AAGJ,WAAO;;AAEV;SC/ee,eACd,WACA,OACA,SAGC;AAED,QAAM,EAAE,MAAM,GAAE,IAAK;AACrB,QAAM,EAAE,iBAAiB,QAAQ,kBAAkB,CAAA,EAAE,IAAK,WAAW,CAAA;AACrE,MAAI,OAAO;AAEX,YAAU,aAAa,MAAM,IAAI,CAAC,MAAM,KAAK,QAAQD,WAAS;;AAC5D,QAAI,KAAK,WAAW,MAAM,MAAM;AAC9B,cAAQ;;AAGV,UAAM,iBAAiB,oBAAe,QAAf,oBAAA,SAAA,SAAA,gBAAkB,KAAK,KAAK,IAAI;AAEvD,QAAI,gBAAgB;AAClB,UAAI,QAAQ;AACV,gBAAQ,eAAe;UACrB;UACA;UACA;UACA,OAAAA;UACA;QACD,CAAA;;AAGH,aAAO;;AAGT,QAAI,KAAK,QAAQ;AACf,eAAQ,KAAA,SAAI,QAAJ,SAAI,SAAA,SAAJ,KAAM,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE,MAAM,KAAK,IAAI,MAAM,GAAG,IAAI,KAAK,KAAK,GAAG;;EAEjE,CAAC;AAED,SAAO;AACT;AC1CM,SAAU,6BAA6B,QAAc;AACzD,SAAO,OAAO,YACZ,OAAO,QAAQ,OAAO,KAAK,EACxB,OAAO,CAAC,CAAA,EAAG,IAAI,MAAM,KAAK,KAAK,MAAM,EACrC,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,CAAC;AAEtD;ACLO,IAAM,0BAA0B,UAAU,OAAuC;EACtF,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB;;;EAIpB,wBAAqB;AACnB,WAAO;MACL,IAAI,OAAO;QACT,KAAK,IAAI,UAAU,yBAAyB;QAC5C,OAAO;UACL,yBAAyB,MAAK;AAC5B,kBAAM,EAAE,OAAM,IAAK;AACnB,kBAAM,EAAE,OAAO,OAAM,IAAK;AAC1B,kBAAM,EAAE,KAAK,UAAS,IAAK;AAC3B,kBAAM,EAAE,OAAM,IAAK;AACnB,kBAAM,OAAO,KAAK,IAAI,GAAG,OAAO,IAAI,CAAAI,WAASA,OAAM,MAAM,GAAG,CAAC;AAC7D,kBAAM,KAAK,KAAK,IAAI,GAAG,OAAO,IAAI,CAAAA,WAASA,OAAM,IAAI,GAAG,CAAC;AACzD,kBAAM,kBAAkB,6BAA6B,MAAM;AAC3D,kBAAM,QAAQ,EAAE,MAAM,GAAE;AAExB,mBAAO,eAAe,KAAK,OAAO;cAChC,GAAI,KAAK,QAAQ,mBAAmB,SAChC,EAAE,gBAAgB,KAAK,QAAQ,eAAc,IAC7C,CAAA;cACJ;YACD,CAAA;;QAEJ;OACF;;;AAGN,CAAA;AC/BM,IAAM,OAA4B,MAAM,CAAC,EAAE,QAAQ,KAAI,MAAM;AAClE,wBAAsB,MAAK;;AACzB,QAAI,CAAC,OAAO,aAAa;AACtB,WAAK,IAAoB,KAAI;AAI9B,OAAA,KAAA,WAAA,QAAA,WAAA,SAAA,SAAA,OAAQ,aAAY,OAAE,QAAA,OAAA,SAAA,SAAA,GAAE,gBAAe;;EAE3C,CAAC;AAED,SAAO;AACT;ACXO,IAAM,eAA4C,CAAC,aAAa,UAAU,CAAC,EAAE,UAAAH,UAAQ,MAAM;AAChG,SAAOA,UAAS,WAAW,IAAI,UAAU;AAC3C;ACDO,IAAM,aAAwC,MAAM,CAAC,EAAE,OAAO,IAAI,SAAQ,MAAM;AACrF,QAAM,EAAE,UAAS,IAAK;AACtB,QAAM,EAAE,OAAM,IAAK;AAEnB,MAAI,CAAC,UAAU;AACb,WAAO;;AAGT,SAAO,QAAQ,CAAC,EAAE,OAAO,IAAG,MAAM;AAChC,UAAM,IAAI,aAAa,MAAM,KAAK,IAAI,KAAK,CAAC,MAAM,QAAO;AACvD,UAAI,KAAK,KAAK,QAAQ;AACpB;;AAGF,YAAM,EAAE,KAAK,QAAO,IAAK;AACzB,YAAM,cAAc,IAAI,QAAQ,QAAQ,IAAI,GAAG,CAAC;AAChD,YAAM,YAAY,IAAI,QAAQ,QAAQ,IAAI,MAAM,KAAK,QAAQ,CAAC;AAC9D,YAAM,YAAY,YAAY,WAAW,SAAS;AAElD,UAAI,CAAC,WAAW;AACd;;AAGF,YAAM,kBAAkB,WAAW,SAAS;AAE5C,UAAI,KAAK,KAAK,aAAa;AACzB,cAAM,EAAE,YAAW,IAAK,YAAY,OAAO,eAAe,YAAY,MAAK,CAAE;AAE7E,WAAG,cAAc,UAAU,OAAO,WAAW;;AAG/C,UAAI,mBAAmB,oBAAoB,GAAG;AAC5C,WAAG,KAAK,WAAW,eAAe;;IAEtC,CAAC;EACH,CAAC;AAED,SAAO;AACT;ACnCO,IAAM,UAAkC,QAAM,WAAQ;AAC3D,SAAO,GAAG,KAAK;AACjB;ACLO,IAAMI,uBAA0D,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AACnG,SAAOC,oBAA4B,OAAO,QAAQ;AACpD;ACEO,IAAM,MAA0B,CAAC,aAAa,cAAc,CAAC,EAAE,QAAQ,GAAE,MAAM;AACpF,QAAM,EAAE,MAAK,IAAK;AAElB,QAAM,eAAe,MAAM,IAAI,MAAM,YAAY,MAAM,YAAY,EAAE;AAErE,KAAG,YAAY,YAAY,MAAM,YAAY,EAAE;AAC/C,QAAM,SAAS,GAAG,QAAQ,IAAI,SAAS;AAEvC,KAAG,OAAO,QAAQ,aAAa,OAAO;AAEtC,KAAG,aAAa,IAAI,cAAc,GAAG,IAAI,QAAQ,SAAS,CAAC,CAAC,CAAC;AAE7D,SAAO;AACT;ACnBO,IAAM,oBAAsD,MAAM,CAAC,EAAE,IAAI,SAAQ,MAAM;AAC5F,QAAM,EAAE,UAAS,IAAK;AACtB,QAAM,cAAc,UAAU,QAAQ,KAAI;AAG1C,MAAI,YAAY,QAAQ,OAAO,GAAG;AAChC,WAAO;;AAGT,QAAM,OAAO,GAAG,UAAU;AAE1B,WAAS,QAAQ,KAAK,OAAO,QAAQ,GAAG,SAAS,GAAG;AAClD,UAAM,OAAO,KAAK,KAAK,KAAK;AAE5B,QAAI,KAAK,SAAS,YAAY,MAAM;AAClC,UAAI,UAAU;AACZ,cAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,cAAM,KAAK,KAAK,MAAM,KAAK;AAE3B,WAAG,OAAO,MAAM,EAAE,EAAE,eAAc;;AAGpC,aAAO;;;AAIX,SAAO;AACT;ACvBO,IAAM,aAAwC,gBAAc,CAAC,EAAE,IAAI,OAAO,SAAQ,MAAM;AAC7F,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,OAAO,GAAG,UAAU;AAE1B,WAAS,QAAQ,KAAK,OAAO,QAAQ,GAAG,SAAS,GAAG;AAClD,UAAM,OAAO,KAAK,KAAK,KAAK;AAE5B,QAAI,KAAK,SAAS,MAAM;AACtB,UAAI,UAAU;AACZ,cAAM,OAAO,KAAK,OAAO,KAAK;AAC9B,cAAM,KAAK,KAAK,MAAM,KAAK;AAE3B,WAAG,OAAO,MAAM,EAAE,EAAE,eAAc;;AAGpC,aAAO;;;AAIX,SAAO;AACT;ACvBO,IAAM,cAA0C,WAAS,CAAC,EAAE,IAAI,SAAQ,MAAM;AACnF,QAAM,EAAE,MAAM,GAAE,IAAK;AAErB,MAAI,UAAU;AACZ,OAAG,OAAO,MAAM,EAAE;;AAGpB,SAAO;AACT;ACPO,IAAMC,mBAAkD,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC3F,SAAOC,gBAAwB,OAAO,QAAQ;AAChD;ACJO,IAAM,QAA8B,MAAM,CAAC,EAAE,UAAAP,UAAQ,MAAM;AAChE,SAAOA,UAAS,iBAAiB,OAAO;AAC1C;ACAO,IAAMQ,YAAoC,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC7E,SAAOC,SAAiB,OAAO,QAAQ;AACzC;ACXgB,SAAA,eACd,SACA,SACA,UAA+B,EAAE,QAAQ,KAAI,GAAE;AAE/C,QAAM,OAAO,OAAO,KAAK,OAAO;AAEhC,MAAI,CAAC,KAAK,QAAQ;AAChB,WAAO;;AAGT,SAAO,KAAK,MAAM,SAAM;AACtB,QAAI,QAAQ,QAAQ;AAClB,aAAO,QAAQ,GAAG,MAAM,QAAQ,GAAG;;AAGrC,QAAI,SAAS,QAAQ,GAAG,CAAC,GAAG;AAC1B,aAAO,QAAQ,GAAG,EAAE,KAAK,QAAQ,GAAG,CAAC;;AAGvC,WAAO,QAAQ,GAAG,MAAM,QAAQ,GAAG;EACrC,CAAC;AACH;ACxBA,SAAS,cACP,OACA,MACA,aAAkC,CAAA,GAAE;AAEpC,SAAO,MAAM,KAAK,UAAO;AACvB,WACE,KAAK,SAAS,QACX;;MAED,OAAO,YAAY,OAAO,KAAK,UAAU,EAAE,IAAI,OAAK,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC,CAAC,CAAC;MACvE;IAAU;EAGhB,CAAC;AACH;AAEA,SAAS,YACP,OACA,MACA,aAAkC,CAAA,GAAE;AAEpC,SAAO,CAAC,CAAC,cAAc,OAAO,MAAM,UAAU;AAChD;SAKgB,aAId,MAIA,MAKA,YAAgC;;AAEhC,MAAI,CAAC,QAAQ,CAAC,MAAM;AAClB;;AAEF,MAAI,QAAQ,KAAK,OAAO,WAAW,KAAK,YAAY;AAGpD,MAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,KAAK,MAAM,KAAK,CAAAC,UAAQA,MAAK,SAAS,IAAI,GAAG;AACrE,YAAQ,KAAK,OAAO,YAAY,KAAK,YAAY;;AAInD,MAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,KAAK,MAAM,KAAK,CAAAA,UAAQA,MAAK,SAAS,IAAI,GAAG;AACrE;;AAIF,eAAa,gBAAc,KAAA,MAAM,KAAK,MAAM,CAAC,OAAG,QAAA,OAAA,SAAA,SAAA,GAAA;AAIhD,QAAM,OAAO,cAAc,CAAC,GAAG,MAAM,KAAK,KAAK,GAAG,MAAM,UAAU;AAElE,MAAI,CAAC,MAAM;AACT;;AAGF,MAAI,aAAa,MAAM;AACvB,MAAI,WAAW,KAAK,MAAK,IAAK,MAAM;AACpC,MAAI,WAAW,aAAa;AAC5B,MAAI,SAAS,WAAW,MAAM,KAAK;AAEnC,SACE,aAAa,KACV,YAAY,CAAC,GAAG,KAAK,OAAO,MAAM,aAAa,CAAC,EAAE,KAAK,GAAG,MAAM,UAAU,GAC7E;AACA,kBAAc;AACd,gBAAY,KAAK,OAAO,MAAM,UAAU,EAAE;;AAG5C,SACE,WAAW,KAAK,OAAO,cACpB,YAAY,CAAC,GAAG,KAAK,OAAO,MAAM,QAAQ,EAAE,KAAK,GAAG,MAAM,UAAU,GACvE;AACA,cAAU,KAAK,OAAO,MAAM,QAAQ,EAAE;AACtC,gBAAY;;AAGd,SAAO;IACL,MAAM;IACN,IAAI;;AAER;ACjGgB,SAAA,YAAY,YAA+B,QAAc;AACvE,MAAI,OAAO,eAAe,UAAU;AAClC,QAAI,CAAC,OAAO,MAAM,UAAU,GAAG;AAC7B,YAAM,MACJ,gCAAgC,UAAU,2CAA2C;;AAIzF,WAAO,OAAO,MAAM,UAAU;;AAGhC,SAAO;AACT;ACkBO,IAAM,kBAAkD,CAAC,YAAY,aAAa,CAAA,MAAO,CAAC,EAAE,IAAI,OAAO,SAAQ,MAAM;AAC1H,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,EAAE,KAAK,UAAS,IAAK;AAC3B,QAAM,EAAE,OAAO,MAAM,GAAE,IAAK;AAE5B,MAAI,UAAU;AACZ,UAAM,QAAQ,aAAa,OAAO,MAAM,UAAU;AAElD,QAAI,SAAS,MAAM,QAAQ,QAAQ,MAAM,MAAM,IAAI;AACjD,YAAM,eAAe,cAAc,OAAO,KAAK,MAAM,MAAM,MAAM,EAAE;AAEnE,SAAG,aAAa,YAAY;;;AAIhC,SAAO;AACT;ACjCO,IAAM,QAA8B,CAAAV,cAAY,WAAQ;AAC7D,QAAM,QAAQ,OAAOA,cAAa,aAC9BA,UAAS,KAAK,IACdA;AAEJ,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,QAAI,MAAM,CAAC,EAAE,KAAK,GAAG;AACnB,aAAO;;;AAIX,SAAO;AACT;ACzBM,SAAU,gBAAgB,OAAc;AAC5C,SAAO,iBAAiB;AAC1B;ACJgB,SAAA,OAAO,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAC;AAChD,SAAO,KAAK,IAAI,KAAK,IAAI,OAAO,GAAG,GAAG,GAAG;AAC3C;SCIgB,qBACd,KACA,WAA0B,MAAI;AAE9B,MAAI,CAAC,UAAU;AACb,WAAO;;AAGT,QAAM,mBAAmB,UAAU,QAAQ,GAAG;AAC9C,QAAM,iBAAiB,UAAU,MAAM,GAAG;AAE1C,MAAI,aAAa,WAAW,aAAa,MAAM;AAC7C,WAAO;;AAGT,MAAI,aAAa,OAAO;AACtB,WAAO;;AAGT,QAAM,SAAS,iBAAiB;AAChC,QAAM,SAAS,eAAe;AAE9B,MAAI,aAAa,OAAO;AACtB,WAAO,cAAc,OACnB,KACA,OAAO,GAAG,QAAQ,MAAM,GACxB,OAAO,IAAI,QAAQ,MAAM,QAAQ,MAAM,CAAC;;AAI5C,SAAO,cAAc,OACnB,KACA,OAAO,UAAU,QAAQ,MAAM,GAC/B,OAAO,UAAU,QAAQ,MAAM,CAAC;AAEpC;SCzCgB,YAAS;AACvB,SAAO,UAAU,aAAa,aAAa,WAAW,KAAK,UAAU,SAAS;AAChF;SCFgB,QAAK;AACnB,SAAO;IACL;IACA;IACA;IACA;IACA;IACA;EACD,EAAC,SAAS,UAAU,QAAQ,KAEzB,UAAU,UAAU,SAAS,KAAK,KAAK,gBAAgB;AAC7D;ACuBO,IAAM,QAA8B,CAAC,WAAW,MAAM,UAAU,CAAA,MAAO,CAAC,EAC7E,QACA,MACA,IACA,SAAQ,MACL;AACH,YAAU;IACR,gBAAgB;IAChB,GAAG;;AAGL,QAAM,eAAe,MAAK;AAGxB,QAAI,MAAK,KAAM,UAAS,GAAI;AACzB,WAAK,IAAoB,MAAK;;AAKjC,0BAAsB,MAAK;AACzB,UAAI,CAAC,OAAO,aAAa;AACvB,aAAK,MAAK;AAEV,YAAI,YAAO,QAAP,YAAO,SAAA,SAAP,QAAS,gBAAgB;AAC3B,iBAAO,SAAS,eAAc;;;IAGpC,CAAC;EACH;AAEA,MAAK,KAAK,SAAQ,KAAM,aAAa,QAAS,aAAa,OAAO;AAChE,WAAO;;AAIT,MAAI,YAAY,aAAa,QAAQ,CAAC,gBAAgB,OAAO,MAAM,SAAS,GAAG;AAC7E,iBAAY;AACZ,WAAO;;AAKT,QAAM,YAAY,qBAAqB,GAAG,KAAK,QAAQ,KAAK,OAAO,MAAM;AACzE,QAAM,kBAAkB,OAAO,MAAM,UAAU,GAAG,SAAS;AAE3D,MAAI,UAAU;AACZ,QAAI,CAAC,iBAAiB;AACpB,SAAG,aAAa,SAAS;;AAK3B,QAAI,mBAAmB,GAAG,aAAa;AACrC,SAAG,eAAe,GAAG,WAAW;;AAGlC,iBAAY;;AAGd,SAAO;AACT;AC1EO,IAAM,UAAkC,CAAC,OAAO,OAAO,WAAQ;AACpE,SAAO,MAAM,MAAM,CAAC,MAAMD,WAAU,GAAG,MAAM,EAAE,GAAG,OAAO,OAAAA,OAAK,CAAE,CAAC;AACnE;ACgBO,IAAM,gBAA8C,CAAC,OAAO,YAAY,CAAC,EAAE,IAAI,UAAAC,UAAQ,MAAM;AAClG,SAAOA,UAAS,gBACd,EAAE,MAAM,GAAG,UAAU,MAAM,IAAI,GAAG,UAAU,GAAE,GAC9C,OACA,OAAO;AAEX;AC7CA,IAAM,oBAAoB,CAAC,SAAqB;AAC9C,QAAM,WAAW,KAAK;AAEtB,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AAChD,UAAM,QAAQ,SAAS,CAAC;AAExB,QAAI,MAAM,aAAa,KAAK,MAAM,aAAa,gBAAgB,KAAK,MAAM,SAAS,GAAG;AACpF,WAAK,YAAY,KAAK;eACb,MAAM,aAAa,GAAG;AAC/B,wBAAkB,KAAoB;;;AAI1C,SAAO;AACT;AAEM,SAAU,kBAAkB,OAAa;AAE7C,QAAM,eAAe,SAAS,KAAK;AAEnC,QAAM,OAAO,IAAI,OAAO,UAAS,EAAG,gBAAgB,cAAc,WAAW,EAAE;AAE/E,SAAO,kBAAkB,IAAI;AAC/B;SCCgB,sBACd,SACA,QACA,SAAsC;AAEtC,MAAI,mBAAmBW,QAAmB,mBAAmB,UAAU;AACrE,WAAO;;AAET,YAAU;IACR,OAAO;IACP,cAAc,CAAA;IACd,GAAG;;AAGL,QAAM,gBAAgB,OAAO,YAAY,YAAY,YAAY;AACjE,QAAM,gBAAgB,OAAO,YAAY;AAEzC,MAAI,eAAe;AACjB,QAAI;AACF,YAAM,iBAAiB,MAAM,QAAQ,OAAO,KAAK,QAAQ,SAAS;AAGlE,UAAI,gBAAgB;AAClB,eAAO,SAAS,UAAU,QAAQ,IAAI,UAAQ,OAAO,aAAa,IAAI,CAAC,CAAC;;AAG1E,YAAM,OAAO,OAAO,aAAa,OAAO;AAExC,UAAI,QAAQ,uBAAuB;AACjC,aAAK,MAAK;;AAGZ,aAAO;aACA,OAAO;AACd,UAAI,QAAQ,uBAAuB;AACjC,cAAM,IAAI,MAAM,wCAAwC,EAAE,OAAO,MAAc,CAAE;;AAGnF,cAAQ,KAAK,mCAAmC,iBAAiB,SAAS,UAAU,KAAK;AAEzF,aAAO,sBAAsB,IAAI,QAAQ,OAAO;;;AAIpD,MAAI,eAAe;AAGjB,QAAI,QAAQ,uBAAuB;AACjC,UAAI,oBAAoB;AACxB,UAAI,iBAAiB;AAGrB,YAAM,qBAAqB,IAAI,OAAO;QACpC,SAAS,OAAO,KAAK;QACrB,OAAO,OAAO,KAAK;;;QAGnB,OAAO,OAAO,KAAK,MAAM,OAAO;UAC9B,8CAA8C;YAC5C,SAAS;YACT,OAAO;YACP,UAAU;cACR;gBACE,KAAK;gBACL,UAAU,OAAI;AAEZ,sCAAoB;AAEpB,mCAAiB,OAAO,MAAM,WAAW,IAAI,EAAE;AAC/C,yBAAO;;cAEV;YACF;UACF;SACF;MACF,CAAA;AAED,UAAI,QAAQ,OAAO;AACjB,kBAAU,WAAW,kBAAkB,EAAE,WAAW,kBAAkB,OAAO,GAAG,QAAQ,YAAY;aAC/F;AACL,kBAAU,WAAW,kBAAkB,EAAE,MAAM,kBAAkB,OAAO,GAAG,QAAQ,YAAY;;AAGjG,UAAI,QAAQ,yBAAyB,mBAAmB;AACtD,cAAM,IAAI,MAAM,wCAAwC,EAAE,OAAO,IAAI,MAAM,0BAA0B,cAAc,EAAE,EAAC,CAAE;;;AAI5H,UAAM,SAAS,UAAU,WAAW,MAAM;AAE1C,QAAI,QAAQ,OAAO;AACjB,aAAO,OAAO,WAAW,kBAAkB,OAAO,GAAG,QAAQ,YAAY,EAAE;;AAG7E,WAAO,OAAO,MAAM,kBAAkB,OAAO,GAAG,QAAQ,YAAY;;AAItE,SAAO,sBAAsB,IAAI,QAAQ,OAAO;AAClD;SCvHgB,wBAAwB,IAAiB,UAAkB,MAAY;AACrF,QAAM,OAAO,GAAG,MAAM,SAAS;AAE/B,MAAI,OAAO,UAAU;AACnB;;AAGF,QAAM,OAAO,GAAG,MAAM,IAAI;AAE1B,MAAI,EAAE,gBAAgB,eAAe,gBAAgB,oBAAoB;AACvE;;AAGF,QAAM,MAAM,GAAG,QAAQ,KAAK,IAAI;AAChC,MAAI,MAAM;AAEV,MAAI,QAAQ,CAAC,OAAO,KAAK,UAAU,UAAS;AAC1C,QAAI,QAAQ,GAAG;AACb,YAAM;;EAEV,CAAC;AAED,KAAG,aAAa,UAAU,KAAK,GAAG,IAAI,QAAQ,GAAG,GAAG,IAAI,CAAC;AAC3D;AC+BA,IAAM,aAAa,CAAC,mBAA0E;AAC5F,SAAO,EAAE,UAAU;AACrB;AAEO,IAAM,kBAAkD,CAAC,UAAU,OAAO,YAAY,CAAC,EAAE,IAAI,UAAU,OAAM,MAAM;;AACxH,MAAI,UAAU;AACZ,cAAU;MACR,cAAc,OAAO,QAAQ;MAC7B,iBAAiB;MACjB,iBAAiB;MACjB,iBAAiB;MACjB,GAAG;;AAGL,QAAI;AAEJ,QAAI;AACF,gBAAU,sBAAsB,OAAO,OAAO,QAAQ;QACpD,cAAc;UACZ,oBAAoB;UACpB,GAAG,QAAQ;QACZ;QACD,wBAAuB,KAAA,QAAQ,2BAAqB,QAAA,OAAA,SAAA,KAAI,OAAO,QAAQ;MACxE,CAAA;aACM,GAAG;AACV,aAAO,KAAK,gBAAgB;QAC1B;QACA,OAAO;QACP,sBAAsB,MAAK;AACzB,cAAI,OAAO,QAAQ,eAAe;AAChC,mBAAO,QAAQ,cAAc,aAAa;;;MAG/C,CAAA;AACD,aAAO;;AAGT,QAAI,EAAE,MAAM,GAAE,IAAK,OAAO,aAAa,WAAW,EAAE,MAAM,UAAU,IAAI,SAAQ,IAAK,EAAE,MAAM,SAAS,MAAM,IAAI,SAAS,GAAE;AAE3H,QAAI,oBAAoB;AACxB,QAAI,qBAAqB;AACzB,UAAM,QAAQ,WAAW,OAAO,IAAI,UAAU,CAAC,OAAO;AAEtD,UAAM,QAAQ,UAAO;AAEnB,WAAK,MAAK;AAEV,0BAAoB,oBAAoB,KAAK,UAAU,KAAK,MAAM,WAAW,IAAI;AAEjF,2BAAqB,qBAAqB,KAAK,UAAU;IAC3D,CAAC;AAOD,QAAI,SAAS,MAAM,oBAAoB;AACrC,YAAM,EAAE,OAAM,IAAK,GAAG,IAAI,QAAQ,IAAI;AACtC,YAAM,mBAAmB,OAAO,eAAe,CAAC,OAAO,KAAK,KAAK,QAAQ,CAAC,OAAO;AAEjF,UAAI,kBAAkB;AACpB,gBAAQ;AACR,cAAM;;;AAIV,QAAI;AAIJ,QAAI,mBAAmB;AAGrB,UAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,qBAAa,MAAM,IAAI,OAAK,EAAE,QAAQ,EAAE,EAAE,KAAK,EAAE;iBACxC,iBAAiB,UAAU;AACpC,YAAI,OAAO;AAEX,cAAM,QAAQ,UAAO;AACnB,cAAI,KAAK,MAAM;AACb,oBAAQ,KAAK;;QAEjB,CAAC;AAED,qBAAa;iBACJ,OAAO,UAAU,YAAY,CAAC,CAAC,SAAS,CAAC,CAAC,MAAM,MAAM;AAC/D,qBAAa,MAAM;aACd;AACL,qBAAa;;AAGf,SAAG,WAAW,YAAY,MAAM,EAAE;WAC7B;AACL,mBAAa;AAEb,SAAG,YAAY,MAAM,IAAI,UAAU;;AAIrC,QAAI,QAAQ,iBAAiB;AAC3B,8BAAwB,IAAI,GAAG,MAAM,SAAS,GAAG,EAAE;;AAGrD,QAAI,QAAQ,iBAAiB;AAC3B,SAAG,QAAQ,mBAAmB,EAAE,MAAM,MAAM,WAAU,CAAE;;AAG1D,QAAI,QAAQ,iBAAiB;AAC3B,SAAG,QAAQ,mBAAmB,EAAE,MAAM,MAAM,WAAU,CAAE;;;AAI5D,SAAO;AACT;AC9HO,IAAMC,UAAgC,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AACzE,SAAOC,OAAe,OAAO,QAAQ;AACvC;AAEO,IAAMC,YAAoC,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC7E,SAAOC,SAAiB,OAAO,QAAQ;AACzC;AAEO,IAAMC,gBAA4C,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AACrF,SAAOC,aAAqB,OAAO,QAAQ;AAC7C;AAEO,IAAMC,eAA0C,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AACnF,SAAOC,YAAoB,OAAO,QAAQ;AAC5C;AC5CO,IAAM,mBAAoD,MAAM,CAAC,EACtE,OACA,UACA,GAAE,MACC;AACH,MAAI;AACF,UAAM,QAAQ,UAAU,MAAM,KAAK,MAAM,UAAU,MAAM,KAAK,EAAE;AAEhE,QAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,aAAO;;AAGT,OAAG,KAAK,OAAO,CAAC;AAEhB,QAAI,UAAU;AACZ,eAAS,EAAE;;AAGb,WAAO;UACD;AACN,WAAO;;AAEX;ACtBO,IAAM,kBAAkD,MAAM,CAAC,EACpE,OACA,UACA,GAAE,MACC;AACH,MAAI;AACF,UAAM,QAAQ,UAAU,MAAM,KAAK,MAAM,UAAU,MAAM,KAAK,CAAE;AAEhE,QAAI,UAAU,QAAQ,UAAU,QAAW;AACzC,aAAO;;AAGT,OAAG,KAAK,OAAO,CAAC;AAEhB,QAAI,UAAU;AACZ,eAAS,EAAE;;AAGb,WAAO;UACD;AACN,WAAO;;AAEX;ACvBO,IAAMC,yBAA8D,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AACvG,SAAOC,sBAAgB,OAAO,QAAQ;AACxC;ACFO,IAAMC,wBAA4D,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AACrG,SAAOD,qBAAgB,OAAO,QAAQ;AACxC;SCjBgB,UAAO;AACrB,SAAO,OAAO,cAAc,cACxB,MAAM,KAAK,UAAU,QAAQ,IAC7B;AACN;ACAA,SAAS,iBAAiB,MAAY;AACpC,QAAM,QAAQ,KAAK,MAAM,QAAQ;AACjC,MAAI,SAAS,MAAM,MAAM,SAAS,CAAC;AAEnC,MAAI,WAAW,SAAS;AACtB,aAAS;;AAGX,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,WAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK,GAAG;AAC5C,UAAM,MAAM,MAAM,CAAC;AAEnB,QAAI,kBAAkB,KAAK,GAAG,GAAG;AAC/B,aAAO;eACE,YAAY,KAAK,GAAG,GAAG;AAChC,YAAM;eACG,sBAAsB,KAAK,GAAG,GAAG;AAC1C,aAAO;eACE,cAAc,KAAK,GAAG,GAAG;AAClC,cAAQ;eACC,SAAS,KAAK,GAAG,GAAG;AAC7B,UAAI,MAAK,KAAM,QAAO,GAAI;AACxB,eAAO;aACF;AACL,eAAO;;WAEJ;AACL,YAAM,IAAI,MAAM,+BAA+B,GAAG,EAAE;;;AAIxD,MAAI,KAAK;AACP,aAAS,OAAO,MAAM;;AAGxB,MAAI,MAAM;AACR,aAAS,QAAQ,MAAM;;AAGzB,MAAI,MAAM;AACR,aAAS,QAAQ,MAAM;;AAGzB,MAAI,OAAO;AACT,aAAS,SAAS,MAAM;;AAG1B,SAAO;AACT;AAeO,IAAM,mBAAoD,UAAQ,CAAC,EACxE,QACA,MACA,IACA,SAAQ,MACL;AACH,QAAM,OAAO,iBAAiB,IAAI,EAAE,MAAM,QAAQ;AAClD,QAAM,MAAM,KAAK,KAAK,UAAQ,CAAC,CAAC,OAAO,QAAQ,QAAQ,OAAO,EAAE,SAAS,IAAI,CAAC;AAC9E,QAAM,QAAQ,IAAI,cAAc,WAAW;IACzC,KAAK,QAAQ,UACT,MACA;IACJ,QAAQ,KAAK,SAAS,KAAK;IAC3B,SAAS,KAAK,SAAS,MAAM;IAC7B,SAAS,KAAK,SAAS,MAAM;IAC7B,UAAU,KAAK,SAAS,OAAO;IAC/B,SAAS;IACT,YAAY;EACb,CAAA;AAED,QAAM,sBAAsB,OAAO,mBAAmB,MAAK;AACzD,SAAK,SAAS,iBAAiB,OAAK,EAAE,MAAM,KAAK,CAAC;EACpD,CAAC;AAED,0BAAmB,QAAnB,wBAAmB,SAAA,SAAnB,oBAAqB,MAAM,QAAQ,UAAO;AACxC,UAAM,UAAU,KAAK,IAAI,GAAG,OAAO;AAEnC,QAAI,WAAW,UAAU;AACvB,SAAG,UAAU,OAAO;;EAExB,CAAC;AAED,SAAO;AACT;ACjGM,SAAU,aACd,OACA,YACA,aAAkC,CAAA,GAAE;AAEpC,QAAM,EAAE,MAAM,IAAI,MAAK,IAAK,MAAM;AAClC,QAAM,OAAO,aAAa,YAAY,YAAY,MAAM,MAAM,IAAI;AAElE,QAAM,aAA0B,CAAA;AAEhC,QAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAO;AAC7C,QAAI,KAAK,QAAQ;AACf;;AAGF,UAAM,eAAe,KAAK,IAAI,MAAM,GAAG;AACvC,UAAM,aAAa,KAAK,IAAI,IAAI,MAAM,KAAK,QAAQ;AAEnD,eAAW,KAAK;MACd;MACA,MAAM;MACN,IAAI;IACL,CAAA;EACH,CAAC;AAED,QAAM,iBAAiB,KAAK;AAC5B,QAAM,oBAAoB,WACvB,OAAO,eAAY;AAClB,QAAI,CAAC,MAAM;AACT,aAAO;;AAGT,WAAO,KAAK,SAAS,UAAU,KAAK,KAAK;EAC3C,CAAC,EACA,OAAO,eAAa,eAAe,UAAU,KAAK,OAAO,YAAY,EAAE,QAAQ,MAAK,CAAE,CAAC;AAE1F,MAAI,OAAO;AACT,WAAO,CAAC,CAAC,kBAAkB;;AAG7B,QAAM,QAAQ,kBAAkB,OAAO,CAAC,KAAK,cAAc,MAAM,UAAU,KAAK,UAAU,MAAM,CAAC;AAEjG,SAAO,SAAS;AAClB;AC5BO,IAAME,QAA4B,CAAC,YAAY,aAAa,CAAA,MAAO,CAAC,EAAE,OAAO,SAAQ,MAAM;AAChG,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAMC,YAAW,aAAa,OAAO,MAAM,UAAU;AAErD,MAAI,CAACA,WAAU;AACb,WAAO;;AAGT,SAAOC,KAAa,OAAO,QAAQ;AACrC;ACfO,IAAMC,kBAAgD,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AACzF,SAAOC,eAAuB,OAAO,QAAQ;AAC/C;ACCO,IAAMC,gBAA4C,gBAAc,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC7F,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AAEjD,SAAOC,aAAqB,IAAI,EAAE,OAAO,QAAQ;AACnD;ACPO,IAAMC,iBAA8C,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AACvF,SAAOC,cAAsB,OAAO,QAAQ;AAC9C;ACVgB,SAAA,wBAAwB,MAAc,QAAc;AAClE,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,WAAO;;AAGT,MAAI,OAAO,MAAM,IAAI,GAAG;AACtB,WAAO;;AAGT,SAAO;AACT;ACbgB,SAAA,YAAY,KAA0B,aAA8B;AAClF,QAAM,QAAQ,OAAO,gBAAgB,WACjC,CAAC,WAAW,IACZ;AAEJ,SAAO,OACJ,KAAK,GAAG,EACR,OAAO,CAAC,QAA6B,SAAQ;AAC5C,QAAI,CAAC,MAAM,SAAS,IAAI,GAAG;AACzB,aAAO,IAAI,IAAI,IAAI,IAAI;;AAGzB,WAAO;KACN,CAAA,CAAE;AACT;ACMO,IAAM,kBAAkD,CAAC,YAAY,eAAe,CAAC,EAAE,IAAI,OAAO,SAAQ,MAAM;AACrH,MAAI,WAA4B;AAChC,MAAI,WAA4B;AAEhC,QAAM,aAAa,wBACjB,OAAO,eAAe,WAAW,aAAa,WAAW,MACzD,MAAM,MAAM;AAGd,MAAI,CAAC,YAAY;AACf,WAAO;;AAGT,MAAI,eAAe,QAAQ;AACzB,eAAW,YAAY,YAAwB,MAAM,MAAM;;AAG7D,MAAI,eAAe,QAAQ;AACzB,eAAW,YAAY,YAAwB,MAAM,MAAM;;AAG7D,MAAI,UAAU;AACZ,OAAG,UAAU,OAAO,QAAQ,WAAQ;AAClC,YAAM,IAAI,aAAa,MAAM,MAAM,KAAK,MAAM,IAAI,KAAK,CAAC,MAAM,QAAO;AACnE,YAAI,YAAY,aAAa,KAAK,MAAM;AACtC,aAAG,cAAc,KAAK,QAAW,YAAY,KAAK,OAAO,UAAU,CAAC;;AAGtE,YAAI,YAAY,KAAK,MAAM,QAAQ;AACjC,eAAK,MAAM,QAAQ,UAAO;AACxB,gBAAI,aAAa,KAAK,MAAM;AAC1B,iBAAG,QACD,KACA,MAAM,KAAK,UACX,SAAS,OAAO,YAAY,KAAK,OAAO,UAAU,CAAC,CAAC;;UAG1D,CAAC;;MAEL,CAAC;IACH,CAAC;;AAGH,SAAO;AACT;ACvDO,IAAM,iBAAgD,MAAM,CAAC,EAAE,IAAI,SAAQ,MAAM;AACtF,MAAI,UAAU;AACZ,OAAG,eAAc;;AAGnB,SAAO;AACT;ACJO,IAAM,YAAsC,MAAM,CAAC,EAAE,IAAI,SAAQ,MAAM;AAC5E,MAAI,UAAU;AACZ,UAAM,YAAY,IAAI,aAAa,GAAG,GAAG;AAEzC,OAAG,aAAa,SAAS;;AAG3B,SAAO;AACT;ACRO,IAAMC,sBAAwD,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AACjG,SAAOC,mBAA2B,OAAO,QAAQ;AACnD;ACFO,IAAMC,qBAAsD,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC/F,SAAOC,kBAA0B,OAAO,QAAQ;AAClD;ACFO,IAAMC,oBAAoD,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC7F,SAAOC,iBAAyB,OAAO,QAAQ;AACjD;ACAO,IAAMC,sBAAwD,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AACjG,SAAOC,mBAA2B,OAAO,QAAQ;AACnD;ACFO,IAAMC,wBAA4D,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AACrG,SAAOC,qBAA6B,OAAO,QAAQ;AACrD;ACNM,SAAU,eACd,SACA,QACA,eAA6B,CAAA,GAC7B,UAA+C,CAAA,GAAE;AAEjD,SAAO,sBAAsB,SAAS,QAAQ;IAC5C,OAAO;IACP;IACA,uBAAuB,QAAQ;EAChC,CAAA;AACH;ACqBO,IAAM,aAAwC,CAAC,SAAS,aAAa,OAAO,eAAe,CAAA,GAAI,UAAU,CAAA,MAAO,CAAC,EACtH,QAAQ,IAAI,UAAU,UAAAzC,UAAQ,MAC3B;;AACH,QAAM,EAAE,IAAG,IAAK;AAIhB,MAAI,aAAa,uBAAuB,QAAQ;AAC9C,UAAM0C,YAAW,eAAe,SAAS,OAAO,QAAQ,cAAc;MACpE,wBAAuB,KAAA,QAAQ,2BAAqB,QAAA,OAAA,SAAA,KAAI,OAAO,QAAQ;IACxE,CAAA;AAED,QAAI,UAAU;AACZ,SAAG,YAAY,GAAG,IAAI,QAAQ,MAAMA,SAAQ,EAAE,QAAQ,iBAAiB,CAAC,UAAU;;AAEpF,WAAO;;AAGT,MAAI,UAAU;AACZ,OAAG,QAAQ,iBAAiB,CAAC,UAAU;;AAGzC,SAAO1C,UAAS,gBAAgB,EAAE,MAAM,GAAG,IAAI,IAAI,QAAQ,KAAI,GAAI,SAAS;IAC1E;IACA,wBAAuB,KAAA,QAAQ,2BAAqB,QAAA,OAAA,SAAA,KAAI,OAAO,QAAQ;EACxE,CAAA;AACH;ACnEgB,SAAA,kBACd,OACA,YAA6B;AAE7B,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,EAAE,MAAM,IAAI,MAAK,IAAK,MAAM;AAClC,QAAM,QAAgB,CAAA;AAEtB,MAAI,OAAO;AACT,QAAI,MAAM,aAAa;AACrB,YAAM,KAAK,GAAG,MAAM,WAAW;;AAGjC,UAAM,KAAK,GAAG,MAAM,UAAU,MAAM,MAAK,CAAE;SACtC;AACL,UAAM,IAAI,aAAa,MAAM,IAAI,UAAO;AACtC,YAAM,KAAK,GAAG,KAAK,KAAK;IAC1B,CAAC;;AAGH,QAAM,OAAO,MAAM,KAAK,cAAY,SAAS,KAAK,SAAS,KAAK,IAAI;AAEpE,MAAI,CAAC,MAAM;AACT,WAAO,CAAA;;AAGT,SAAO,EAAE,GAAG,KAAK,MAAK;AACxB;ACtBgB,SAAA,wBACd,QACA,cAA2B;AAE3B,QAAM,YAAY,IAAI,UAAU,MAAM;AAEtC,eAAa,QAAQ,iBAAc;AACjC,gBAAY,MAAM,QAAQ,UAAO;AAC/B,gBAAU,KAAK,IAAI;IACrB,CAAC;EACH,CAAC;AAED,SAAO;AACT;AChBM,SAAU,eAAe,OAAmB;AAChD,WAAS,IAAI,GAAG,IAAI,MAAM,WAAW,KAAK,GAAG;AAC3C,UAAM,EAAE,KAAI,IAAK,MAAM,KAAK,CAAC;AAE7B,QAAI,KAAK,eAAe,CAAC,KAAK,iBAAgB,GAAI;AAChD,aAAO;;;AAIX,SAAO;AACT;ACPgB,SAAA,aAAa,MAAuB,WAAoB;AACtE,QAAM,eAA8B,CAAA;AAEpC,OAAK,YAAY,CAAC,OAAO,QAAO;AAC9B,QAAI,UAAU,KAAK,GAAG;AACpB,mBAAa,KAAK;QAChB,MAAM;QACN;MACD,CAAA;;EAEL,CAAC;AAED,SAAO;AACT;SCZgB,oBACd,MACA,OACA,WAAoB;AAEpB,QAAM,eAA8B,CAAA;AAapC,OAAK,aAAa,MAAM,MAAM,MAAM,IAAI,CAAC,OAAO,QAAO;AACrD,QAAI,UAAU,KAAK,GAAG;AACpB,mBAAa,KAAK;QAChB,MAAM;QACN;MACD,CAAA;;EAEL,CAAC;AAED,SAAO;AACT;AC1BgB,SAAA,2BACd,MACA,WAAoB;AASpB,WAAS,IAAI,KAAK,OAAO,IAAI,GAAG,KAAK,GAAG;AACtC,UAAM,OAAO,KAAK,KAAK,CAAC;AAExB,QAAI,UAAU,IAAI,GAAG;AACnB,aAAO;QACL,KAAK,IAAI,IAAI,KAAK,OAAO,CAAC,IAAI;QAC9B,OAAO,KAAK,MAAM,CAAC;QACnB,OAAO;QACP;;;;AAIR;ACvBM,SAAU,eAAe,WAAoB;AACjD,SAAO,CAAC,cAAyB,2BAA2B,UAAU,OAAO,SAAS;AACxF;ACRgB,SAAA,UAAU,YAAwB,QAAe;AAC/D,QAAM,qBAAqB,iBAAiB,QAAQ,UAAU;AAE9D,SAAO,8BAA8B,oBAAoB,MAAM;AACjE;ACCgB,SAAA,aAAa,KAAkB,YAAsB;AACnE,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,cAAc2C,KAAK,SAAS,QAAQ,GAAG;AAE7C,SAAO,oBAAoB,YAAY,SAAS,MAAM;AACxD;ACLgB,SAAA,aAAa,MAAc,YAAsB;AAC/D,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,MAAM,kBAAkB,IAAI;AAElC,SAAO,UAAU,WAAW,MAAM,EAAE,MAAM,GAAG,EAAE,OAAM;AACvD;ACHgB,SAAA,QACd,MACA,SAGC;AAED,QAAM,QAAQ;IACZ,MAAM;IACN,IAAI,KAAK,QAAQ;;AAGnB,SAAO,eAAe,MAAM,OAAO,OAAO;AAC5C;SCbgB,aACd,KACA,YACA,SAGC;AAED,QAAM,EAAE,iBAAiB,QAAQ,kBAAkB,CAAA,EAAE,IAAK,WAAW,CAAA;AACrE,QAAM,SAAS,UAAU,UAAU;AACnC,QAAM,cAAcA,KAAK,SAAS,QAAQ,GAAG;AAE7C,SAAO,QAAQ,aAAa;IAC1B;IACA,iBAAiB;MACf,GAAG,6BAA6B,MAAM;MACtC,GAAG;IACJ;EACF,CAAA;AACH;AC5BgB,SAAA,kBACd,OACA,YAA6B;AAE7B,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,EAAE,MAAM,GAAE,IAAK,MAAM;AAC3B,QAAM,QAAgB,CAAA;AAEtB,QAAM,IAAI,aAAa,MAAM,IAAI,CAAAC,UAAO;AACtC,UAAM,KAAKA,KAAI;EACjB,CAAC;AAED,QAAM,OAAO,MAAM,QAAO,EAAG,KAAK,cAAY,SAAS,KAAK,SAAS,KAAK,IAAI;AAE9E,MAAI,CAAC,MAAM;AACT,WAAO,CAAA;;AAGT,SAAO,EAAE,GAAG,KAAK,MAAK;AACxB;ACXgB,SAAA,cACd,OACA,YAAwC;AAExC,QAAM,aAAa,wBACjB,OAAO,eAAe,WAAW,aAAa,WAAW,MACzD,MAAM,MAAM;AAGd,MAAI,eAAe,QAAQ;AACzB,WAAO,kBAAkB,OAAO,UAAsB;;AAGxD,MAAI,eAAe,QAAQ;AACzB,WAAO,kBAAkB,OAAO,UAAsB;;AAGxD,SAAO,CAAA;AACT;AC3BM,SAAU,iBAAoB,OAAY,KAAK,KAAK,WAAS;AACjE,QAAM,OAAyB,CAAA;AAE/B,SAAO,MAAM,OAAO,UAAO;AACzB,UAAM,MAAM,GAAG,IAAI;AAEnB,WAAO,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,IACjD,QACC,KAAK,GAAG,IAAI;EACnB,CAAC;AACH;ACAA,SAAS,sBAAsB,SAAuB;AACpD,QAAM,gBAAgB,iBAAiB,OAAO;AAE9C,SAAO,cAAc,WAAW,IAC5B,gBACA,cAAc,OAAO,CAAC,QAAQ7C,WAAS;AACvC,UAAM,OAAO,cAAc,OAAO,CAAC,GAAG,MAAM,MAAMA,MAAK;AAEvD,WAAO,CAAC,KAAK,KAAK,iBAAc;AAC9B,aAAO,OAAO,SAAS,QAAQ,YAAY,SAAS,QAC/C,OAAO,SAAS,MAAM,YAAY,SAAS,MAC3C,OAAO,SAAS,QAAQ,YAAY,SAAS,QAC7C,OAAO,SAAS,MAAM,YAAY,SAAS;IAClD,CAAC;EACH,CAAC;AACL;AAMM,SAAU,iBAAiB,WAAoB;AACnD,QAAM,EAAE,SAAS,MAAK,IAAK;AAC3B,QAAM,UAA0B,CAAA;AAEhC,UAAQ,KAAK,QAAQ,CAAC,SAASA,WAAS;AACtC,UAAM,SAAkB,CAAA;AAKxB,QAAI,CAAC,QAAQ,OAAO,QAAQ;AAC1B,YAAM,EAAE,MAAM,GAAE,IAAK,MAAMA,MAAK;AAKhC,UAAI,SAAS,UAAa,OAAO,QAAW;AAC1C;;AAGF,aAAO,KAAK,EAAE,MAAM,GAAE,CAAE;WACnB;AACL,cAAQ,QAAQ,CAAC,MAAM,OAAM;AAC3B,eAAO,KAAK,EAAE,MAAM,GAAE,CAAE;MAC1B,CAAC;;AAGH,WAAO,QAAQ,CAAC,EAAE,MAAM,GAAE,MAAM;AAC9B,YAAM,WAAW,QAAQ,MAAMA,MAAK,EAAE,IAAI,MAAM,EAAE;AAClD,YAAM,SAAS,QAAQ,MAAMA,MAAK,EAAE,IAAI,EAAE;AAC1C,YAAM,WAAW,QAAQ,OAAM,EAAG,IAAI,UAAU,EAAE;AAClD,YAAM,SAAS,QAAQ,OAAM,EAAG,IAAI,MAAM;AAE1C,cAAQ,KAAK;QACX,UAAU;UACR,MAAM;UACN,IAAI;QACL;QACD,UAAU;UACR,MAAM;UACN,IAAI;QACL;MACF,CAAA;IACH,CAAC;EACH,CAAC;AAED,SAAO,sBAAsB,OAAO;AACtC;SCzEgB,aAAa,MAAuB,cAAc,GAAC;AACjE,QAAM,YAAY,KAAK,SAAS,KAAK,KAAK,OAAO;AACjD,QAAM,YAAY,YAAY,IAAI;AAClC,QAAM,OAAO;AACb,QAAM,KAAK,OAAO,KAAK;AACvB,QAAM,QAAQ,KAAK,MAAM,IAAI,UAAO;AAClC,UAAM8C,UAAwD;MAC5D,MAAM,KAAK,KAAK;;AAGlB,QAAI,OAAO,KAAK,KAAK,KAAK,EAAE,QAAQ;AAClC,MAAAA,QAAO,QAAQ,EAAE,GAAG,KAAK,MAAK;;AAGhC,WAAOA;EACT,CAAC;AACD,QAAM,QAAQ,EAAE,GAAG,KAAK,MAAK;AAC7B,QAAM,SAA2B;IAC/B,MAAM,KAAK,KAAK;IAChB;IACA;;AAGF,MAAI,OAAO,KAAK,KAAK,EAAE,QAAQ;AAC7B,WAAO,QAAQ;;AAGjB,MAAI,MAAM,QAAQ;AAChB,WAAO,QAAQ;;AAGjB,MAAI,KAAK,QAAQ,YAAY;AAC3B,WAAO,UAAU,CAAA;AAEjB,SAAK,QAAQ,CAAC,OAAO,WAAU;;AAC7B,OAAA,KAAA,OAAO,aAAS,QAAA,OAAA,SAAA,SAAA,GAAA,KAAK,aAAa,OAAO,cAAc,SAAS,SAAS,CAAC;IAC5E,CAAC;;AAGH,MAAI,KAAK,MAAM;AACb,WAAO,OAAO,KAAK;;AAGrB,SAAO;AACT;SChDgB,gBAAgB,MAAc,IAAY,KAAoB;AAC5E,QAAM,QAAqB,CAAA;AAG3B,MAAI,SAAS,IAAI;AACf,QACG,QAAQ,IAAI,EACZ,MAAK,EACL,QAAQ,UAAO;AACd,YAAM,OAAO,IAAI,QAAQ,IAAI;AAC7B,YAAM,QAAQ,aAAa,MAAM,KAAK,IAAI;AAE1C,UAAI,CAAC,OAAO;AACV;;AAGF,YAAM,KAAK;QACT;QACA,GAAG;MACJ,CAAA;IACH,CAAC;SACE;AACL,QAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAO;AACvC,UAAI,CAAC,SAAQ,SAAA,QAAA,SAAA,SAAA,SAAA,KAAM,cAAa,QAAW;AACzC;;AAGF,YAAM,KACJ,GAAG,KAAK,MAAM,IAAI,WAAS;QACzB,MAAM;QACN,IAAI,MAAM,KAAK;QACf;QACA,CAAC;IAEP,CAAC;;AAGH,SAAO;AACT;AChCO,IAAM,oBAAoB,CAAC,OAAoB,YAA+B,KAAa,WAAW,OAAM;AACjH,QAAM,OAAO,MAAM,IAAI,QAAQ,GAAG;AAElC,MAAI,eAAe;AACnB,MAAI,OAAoB;AAExB,SAAO,eAAe,KAAK,SAAS,MAAM;AACxC,UAAM,cAAc,KAAK,KAAK,YAAY;AAE1C,SAAI,gBAAA,QAAA,gBAAA,SAAA,SAAA,YAAa,KAAK,UAAS,YAAY;AACzC,aAAO;WACF;AACL,sBAAgB;;;AAIpB,SAAO,CAAC,MAAM,YAAY;AAC5B;SCnBgB,sBACd,qBACA,UACA,YAA+B;AAE/B,SAAO,OAAO,YAAY,OACvB,QAAQ,UAAU,EAClB,OAAO,CAAC,CAAC,IAAI,MAAK;AACjB,UAAM,qBAAqB,oBAAoB,KAAK,UAAO;AACzD,aAAO,KAAK,SAAS,YAAY,KAAK,SAAS;IACjD,CAAC;AAED,QAAI,CAAC,oBAAoB;AACvB,aAAO;;AAGT,WAAO,mBAAmB,UAAU;GACrC,CAAC;AACN;ACpBM,SAAU,aACd,OACA,YACA,aAAkC,CAAA,GAAE;AAEpC,QAAM,EAAE,OAAO,OAAM,IAAK,MAAM;AAChC,QAAM,OAAO,aAAa,YAAY,YAAY,MAAM,MAAM,IAAI;AAElE,MAAI,OAAO;AACT,WAAO,CAAC,EAAE,MAAM,eAAe,MAAM,UAAU,MAAM,MAAK,GACvD,OAAO,UAAO;AACb,UAAI,CAAC,MAAM;AACT,eAAO;;AAGT,aAAO,KAAK,SAAS,KAAK,KAAK;IACjC,CAAC,EACA,KAAK,UAAQ,eAAe,KAAK,OAAO,YAAY,EAAE,QAAQ,MAAK,CAAE,CAAC;;AAG3E,MAAI,iBAAiB;AACrB,QAAM,aAA0B,CAAA;AAEhC,SAAO,QAAQ,CAAC,EAAE,OAAO,IAAG,MAAM;AAChC,UAAM,OAAO,MAAM;AACnB,UAAM,KAAK,IAAI;AAEf,UAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAO;AAC7C,UAAI,CAAC,KAAK,UAAU,CAAC,KAAK,MAAM,QAAQ;AACtC;;AAGF,YAAM,eAAe,KAAK,IAAI,MAAM,GAAG;AACvC,YAAM,aAAa,KAAK,IAAI,IAAI,MAAM,KAAK,QAAQ;AACnD,YAAM1C,SAAQ,aAAa;AAE3B,wBAAkBA;AAElB,iBAAW,KACT,GAAG,KAAK,MAAM,IAAI,WAAS;QACzB;QACA,MAAM;QACN,IAAI;QACJ,CAAC;IAEP,CAAC;EACH,CAAC;AAED,MAAI,mBAAmB,GAAG;AACxB,WAAO;;AAIT,QAAM,eAAe,WAClB,OAAO,eAAY;AAClB,QAAI,CAAC,MAAM;AACT,aAAO;;AAGT,WAAO,KAAK,SAAS,UAAU,KAAK,KAAK;EAC3C,CAAC,EACA,OAAO,eAAa,eAAe,UAAU,KAAK,OAAO,YAAY,EAAE,QAAQ,MAAK,CAAE,CAAC,EACvF,OAAO,CAAC,KAAK,cAAc,MAAM,UAAU,KAAK,UAAU,MAAM,CAAC;AAIpE,QAAM,gBAAgB,WACnB,OAAO,eAAY;AAClB,QAAI,CAAC,MAAM;AACT,aAAO;;AAGT,WAAO,UAAU,KAAK,SAAS,QAAQ,UAAU,KAAK,KAAK,SAAS,IAAI;EAC1E,CAAC,EACA,OAAO,CAAC,KAAK,cAAc,MAAM,UAAU,KAAK,UAAU,MAAM,CAAC;AAIpE,QAAM,QAAQ,eAAe,IAAI,eAAe,gBAAgB;AAEhE,SAAO,SAAS;AAClB;AClFM,SAAU,SACd,OACA,MACA,aAAkC,CAAA,GAAE;AAEpC,MAAI,CAAC,MAAM;AACT,WAAO,aAAa,OAAO,MAAM,UAAU,KAAK,aAAa,OAAO,MAAM,UAAU;;AAGtF,QAAM,aAAa,wBAAwB,MAAM,MAAM,MAAM;AAE7D,MAAI,eAAe,QAAQ;AACzB,WAAO,aAAa,OAAO,MAAM,UAAU;;AAG7C,MAAI,eAAe,QAAQ;AACzB,WAAO,aAAa,OAAO,MAAM,UAAU;;AAG7C,SAAO;AACT;ICtBa,gBAAgB,CAAC,OAAoB,aAAqB;AACrE,QAAM,EAAE,OAAO,KAAK,QAAO,IAAK,MAAM;AAEtC,MAAI,UAAU;AACZ,UAAM,aAAa,eAAe,UAAQ,KAAK,KAAK,SAAS,QAAQ,EAAE,MAAM,SAAS;AAEtF,QAAI,CAAC,YAAY;AACf,aAAO;;AAGT,UAAM,aAAa,MAAM,IAAI,QAAQ,WAAW,MAAM,CAAC;AAEvD,QAAI,QAAQ,MAAM,MAAM,WAAW,IAAG,GAAI;AACxC,aAAO;;AAGT,WAAO;;AAGT,MAAI,IAAI,eAAe,IAAI,OAAO,WAAW,KAAK,MAAM,QAAQ,IAAI,KAAK;AACvE,WAAO;;AAGT,SAAO;AACT;AC1Ba,IAAA,kBAAkB,CAAC,UAAsB;AACpD,QAAM,EAAE,OAAO,IAAG,IAAK,MAAM;AAE7B,MAAI,MAAM,eAAe,KAAK,MAAM,QAAQ,IAAI,KAAK;AACnD,WAAO;;AAGT,SAAO;AACT;ACJgB,SAAA,OAAO,MAAc,YAAsB;AACzD,QAAM,EAAE,eAAc,IAAK,gBAAgB,UAAU;AACrD,QAAM,YAAY,eAAe,KAAK,UAAQ,KAAK,SAAS,IAAI;AAEhE,MAAI,CAAC,WAAW;AACd,WAAO;;AAGT,QAAM,UAAU;IACd,MAAM,UAAU;IAChB,SAAS,UAAU;IACnB,SAAS,UAAU;;AAErB,QAAM,QAAQ,aAAa,kBAAuC,WAAW,SAAS,OAAO,CAAC;AAE9F,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO;;AAGT,SAAO,MAAM,MAAM,GAAG,EAAE,SAAS,MAAM;AACzC;ACrBgB,SAAA,YACd,MACA,EACE,gBAAgB,MAChB,mBAAmB,MAAK,IAUtB,CAAA,GAAE;;AAEN,MAAI,kBAAkB;AACpB,QAAI,KAAK,KAAK,SAAS,aAAa;AAElC,aAAO;;AAET,QAAI,KAAK,QAAQ;AACf,aAAO,SAAS,MAAK,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,KAAI,EAAE;;;AAIxC,MAAI,KAAK,QAAQ;AACf,WAAO,CAAC,KAAK;;AAGf,MAAI,KAAK,UAAU,KAAK,QAAQ;AAC9B,WAAO;;AAGT,MAAI,KAAK,QAAQ,eAAe,GAAG;AACjC,WAAO;;AAGT,MAAI,eAAe;AACjB,QAAI,iBAAiB;AAErB,SAAK,QAAQ,QAAQ,eAAY;AAC/B,UAAI,mBAAmB,OAAO;AAE5B;;AAGF,UAAI,CAAC,YAAY,WAAW,EAAE,kBAAkB,cAAa,CAAE,GAAG;AAChE,yBAAiB;;IAErB,CAAC;AAED,WAAO;;AAGT,SAAO;AACT;AC3DM,SAAU,gBAAgB,OAAc;AAC5C,SAAO,iBAAiB;AAC1B;SCAgB,aAAa,MAAkB,MAAc,IAAU;AACrE,QAAM,SAAS;AACf,QAAM,SAAS,KAAK,MAAM,IAAI,QAAQ;AACtC,QAAM,eAAe,OAAO,MAAM,QAAQ,MAAM;AAChD,QAAM,cAAc,OAAO,IAAI,QAAQ,MAAM;AAC7C,QAAM,QAAQ,KAAK,YAAY,YAAY;AAC3C,QAAM,MAAM,KAAK,YAAY,aAAa,EAAE;AAC5C,QAAM,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,GAAG;AACvC,QAAM,SAAS,KAAK,IAAI,MAAM,QAAQ,IAAI,MAAM;AAChD,QAAM,OAAO,KAAK,IAAI,MAAM,MAAM,IAAI,IAAI;AAC1C,QAAM,QAAQ,KAAK,IAAI,MAAM,OAAO,IAAI,KAAK;AAC7C,QAAM,QAAQ,QAAQ;AACtB,QAAM,SAAS,SAAS;AACxB,QAAM,IAAI;AACV,QAAM,IAAI;AACV,QAAM,OAAO;IACX;IACA;IACA;IACA;IACA;IACA;IACA;IACA;;AAGF,SAAO;IACL,GAAG;IACH,QAAQ,MAAM;;AAElB;ACRA,SAAS,2BAA2B,EAClC,MACA,YACA,YACA,SACA,mBAAmB,CAAA,EAAE,GAOtB;AAUC,MAAI,KAAK,SAAS,MAAM,QAAQ,KAAK,KAAK,GAAG;AAC3C,SAAK,QAAQ,KAAK,MAAM,OAAO,UAAO;AACpC,YAAM,OAAO,OAAO,SAAS,WAAW,OAAO,KAAK;AAEpD,UAAI,WAAW,IAAI,IAAI,GAAG;AACxB,eAAO;;AAGT,uBAAiB,KAAK;QACpB,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC;QACzC,aAAa;MACd,CAAA;AAED,aAAO;IACT,CAAC;;AAGH,MAAI,KAAK,WAAW,MAAM,QAAQ,KAAK,OAAO,GAAG;AAC/C,SAAK,UAAU,KAAK,QACjB,IACC,WAAS,2BAA2B;MAClC,MAAM;MACN;MACA;MACA;MACA;KACD,EAAE,IAAI,EAER,OAAO,OAAK,MAAM,QAAQ,MAAM,MAAS;;AAG9C,MAAI,KAAK,QAAQ,CAAC,WAAW,IAAI,KAAK,IAAI,GAAG;AAC3C,qBAAiB,KAAK;MACpB,UAAU,KAAK,MAAM,KAAK,UAAU,IAAI,CAAC;MACzC,aAAa,KAAK;IACnB,CAAA;AAED,QAAI,KAAK,WAAW,MAAM,QAAQ,KAAK,OAAO,MAAM,YAAA,QAAA,YAAA,SAAA,SAAA,QAAS,yBAAwB,OAAQ;AAE3F,WAAK,OAAO;AAEZ,aAAO;QACL;QACA;;;AAKJ,WAAO;MACL,MAAM;MACN;;;AAIJ,SAAO,EAAE,MAAM,iBAAgB;AACjC;SAMgB,sBAId,MAIA,QAIA,SAAsC;AAoBtC,SAAO,2BAA2B;IAChC;IACA,YAAY,IAAI,IAAI,OAAO,KAAK,OAAO,KAAK,CAAC;IAC7C,YAAY,IAAI,IAAI,OAAO,KAAK,OAAO,KAAK,CAAC;IAC7C;EACD,CAAA;AACH;AC9HA,SAAS,WAAW,OAAoB,IAAiB,aAAqB;;AAC5E,QAAM,EAAE,UAAS,IAAK;AACtB,MAAI,SAA6B;AAEjC,MAAI,gBAAgB,SAAS,GAAG;AAC9B,aAAS,UAAU;;AAGrB,MAAI,QAAQ;AACV,UAAM,gBAAe,KAAA,MAAM,iBAAW,QAAA,OAAA,SAAA,KAAI,OAAO,MAAK;AAGtD,WACE,CAAC,CAAC,YAAY,QAAQ,YAAY,KAC/B,CAAC,aAAa,KAAK,UAAQ,KAAK,KAAK,SAAS,WAAW,CAAC;;AAIjE,QAAM,EAAE,OAAM,IAAK;AAEnB,SAAO,OAAO,KAAK,CAAC,EAAE,OAAO,IAAG,MAAM;AACpC,QAAI,uBAAuB,MAAM,UAAU,IACvC,MAAM,IAAI,iBAAiB,MAAM,IAAI,KAAK,eAAe,WAAW,IACpE;AAEJ,UAAM,IAAI,aAAa,MAAM,KAAK,IAAI,KAAK,CAAC,MAAM,MAAM,WAAU;AAEhE,UAAI,sBAAsB;AACxB,eAAO;;AAGT,UAAI,KAAK,UAAU;AACjB,cAAM,uBAAuB,CAAC,UAAU,OAAO,KAAK,eAAe,WAAW;AAC9E,cAAM,4BAA4B,CAAC,CAAC,YAAY,QAAQ,KAAK,KAAK,KAC7D,CAAC,KAAK,MAAM,KAAK,eAAa,UAAU,KAAK,SAAS,WAAW,CAAC;AAEvE,+BAAuB,wBAAwB;;AAEjD,aAAO,CAAC;IACV,CAAC;AAED,WAAO;EACT,CAAC;AACH;AACO,IAAM,UAAkC,CAAC,YAAY,aAAa,CAAA,MAAO,CAAC,EAAE,IAAI,OAAO,SAAQ,MAAM;AAC1G,QAAM,EAAE,UAAS,IAAK;AACtB,QAAM,EAAE,OAAO,OAAM,IAAK;AAC1B,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AAEjD,MAAI,UAAU;AACZ,QAAI,OAAO;AACT,YAAM,gBAAgB,kBAAkB,OAAO,IAAI;AAEnD,SAAG,cACD,KAAK,OAAO;QACV,GAAG;QACH,GAAG;MACJ,CAAA,CAAC;WAEC;AACL,aAAO,QAAQ,WAAQ;AACrB,cAAM,OAAO,MAAM,MAAM;AACzB,cAAM,KAAK,MAAM,IAAI;AAErB,cAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAO;AAC7C,gBAAM,cAAc,KAAK,IAAI,KAAK,IAAI;AACtC,gBAAM,YAAY,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AAClD,gBAAM,cAAc,KAAK,MAAM,KAAK,UAAQ,KAAK,SAAS,IAAI;AAK9D,cAAI,aAAa;AACf,iBAAK,MAAM,QAAQ,UAAO;AACxB,kBAAI,SAAS,KAAK,MAAM;AACtB,mBAAG,QACD,aACA,WACA,KAAK,OAAO;kBACV,GAAG,KAAK;kBACR,GAAG;gBACJ,CAAA,CAAC;;YAGR,CAAC;iBACI;AACL,eAAG,QAAQ,aAAa,WAAW,KAAK,OAAO,UAAU,CAAC;;QAE9D,CAAC;MACH,CAAC;;;AAIL,SAAO,WAAW,OAAO,IAAI,IAAI;AACnC;ACjGO,IAAM,UAAkC,CAAC,KAAK,UAAU,CAAC,EAAE,GAAE,MAAM;AACxE,KAAG,QAAQ,KAAK,KAAK;AAErB,SAAO;AACT;ACFO,IAAM,UAAkC,CAAC,YAAY,aAAa,CAAA,MAAO,CAAC,EAAE,OAAO,UAAU,MAAK,MAAM;AAC7G,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AAEjD,MAAI;AAEJ,MAAI,MAAM,UAAU,QAAQ,WAAW,MAAM,UAAU,KAAK,GAAG;AAE7D,uBAAmB,MAAM,UAAU,QAAQ,OAAO;;AAIpD,MAAI,CAAC,KAAK,aAAa;AACrB,YAAQ,KAAK,sEAAsE;AAEnF,WAAO;;AAGT,SACE,MAAK,EAEF,QAAQ,CAAC,EAAE,UAAAH,UAAQ,MAAM;AACxB,UAAM,cAAc,aAAa,MAAM,EAAE,GAAG,kBAAkB,GAAG,WAAU,CAAE,EAAE,KAAK;AAEpF,QAAI,aAAa;AACf,aAAO;;AAGT,WAAOA,UAAS,WAAU;EAC5B,CAAC,EACA,QAAQ,CAAC,EAAE,OAAO,aAAY,MAAM;AACnC,WAAO,aAAa,MAAM,EAAE,GAAG,kBAAkB,GAAG,WAAU,CAAE,EAAE,cAAc,QAAQ;EAC1F,CAAC,EACA,IAAG;AAEV;ACpCO,IAAM,mBAAoD,cAAY,CAAC,EAAE,IAAI,SAAQ,MAAM;AAChG,MAAI,UAAU;AACZ,UAAM,EAAE,IAAG,IAAK;AAChB,UAAM,OAAO,OAAO,UAAU,GAAG,IAAI,QAAQ,IAAI;AACjD,UAAM,YAAY,cAAc,OAAO,KAAK,IAAI;AAEhD,OAAG,aAAa,SAAS;;AAG3B,SAAO;AACT;ACVO,IAAM,mBAAoD,cAAY,CAAC,EAAE,IAAI,SAAQ,MAAM;AAChG,MAAI,UAAU;AACZ,UAAM,EAAE,IAAG,IAAK;AAChB,UAAM,EAAE,MAAM,GAAE,IAAK,OAAO,aAAa,WAAW,EAAE,MAAM,UAAU,IAAI,SAAQ,IAAK;AACvF,UAAM,SAAS,cAAc,QAAQ,GAAG,EAAE;AAC1C,UAAM,SAAS,cAAc,MAAM,GAAG,EAAE;AACxC,UAAM,eAAe,OAAO,MAAM,QAAQ,MAAM;AAChD,UAAM,cAAc,OAAO,IAAI,QAAQ,MAAM;AAC7C,UAAM,YAAY,cAAc,OAAO,KAAK,cAAc,WAAW;AAErE,OAAG,aAAa,SAAS;;AAG3B,SAAO;AACT;ACbO,IAAM8C,gBAA4C,gBAAc,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC7F,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AAEjD,SAAOC,aAAqB,IAAI,EAAE,OAAO,QAAQ;AACnD;AChBA,SAAS,YAAY,OAAoB,iBAA0B;AACjE,QAAM,QAAQ,MAAM,eAAgB,MAAM,UAAU,IAAI,gBAAgB,MAAM,UAAU,MAAM,MAAK;AAEnG,MAAI,OAAO;AACT,UAAM,gBAAgB,MAAM,OAAO,UAAQ,oBAAe,QAAf,oBAAA,SAAA,SAAA,gBAAiB,SAAS,KAAK,KAAK,IAAI,CAAC;AAEpF,UAAM,GAAG,YAAY,aAAa;;AAEtC;AAgBO,IAAM,aAAwC,CAAC,EAAE,YAAY,KAAI,IAAK,CAAA,MAAO,CAAC,EACnF,IAAI,OAAO,UAAU,OAAM,MACxB;AACH,QAAM,EAAE,WAAW,IAAG,IAAK;AAC3B,QAAM,EAAE,OAAO,IAAG,IAAK;AACvB,QAAM,sBAAsB,OAAO,iBAAiB;AACpD,QAAM,gBAAgB,sBACpB,qBACA,MAAM,KAAI,EAAG,KAAK,MAClB,MAAM,KAAI,EAAG,KAAK;AAGpB,MAAI,qBAAqB,iBAAiB,UAAU,KAAK,SAAS;AAChE,QAAI,CAAC,MAAM,gBAAgB,CAAC,SAAS,KAAK,MAAM,GAAG,GAAG;AACpD,aAAO;;AAGT,QAAI,UAAU;AACZ,UAAI,WAAW;AACb,oBAAY,OAAO,OAAO,iBAAiB,eAAe;;AAG5D,SAAG,MAAM,MAAM,GAAG,EAAE,eAAc;;AAGpC,WAAO;;AAGT,MAAI,CAAC,MAAM,OAAO,SAAS;AACzB,WAAO;;AAGT,QAAM,QAAQ,IAAI,iBAAiB,IAAI,OAAO,QAAQ;AAEtD,QAAM,QAAQ,MAAM,UAAU,IAC1B,SACA,eAAe,MAAM,KAAK,EAAE,EAAE,eAAe,MAAM,WAAW,EAAE,CAAC,CAAC;AAEtE,MAAI,QAAQ,SAAS,QACjB;IACA;MACE,MAAM;MACN,OAAO;IACR;EACF,IACC;AAEJ,MAAI,MAAM,SAAS,GAAG,KAAK,GAAG,QAAQ,IAAI,MAAM,GAAG,GAAG,GAAG,KAAK;AAE9D,MACE,CAAC,SACI,CAAC,OACD,SAAS,GAAG,KAAK,GAAG,QAAQ,IAAI,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC,EAAE,MAAM,MAAK,CAAE,IAAI,MAAS,GACzF;AACA,UAAM;AACN,YAAQ,QACJ;MACA;QACE,MAAM;QACN,OAAO;MACR;IACF,IACC;;AAGN,MAAI,UAAU;AACZ,QAAI,KAAK;AACP,UAAI,qBAAqB,eAAe;AACtC,WAAG,gBAAe;;AAGpB,SAAG,MAAM,GAAG,QAAQ,IAAI,MAAM,GAAG,GAAG,GAAG,KAAK;AAE5C,UAAI,SAAS,CAAC,SAAS,CAAC,MAAM,gBAAgB,MAAM,OAAO,SAAS,OAAO;AACzE,cAAMC,SAAQ,GAAG,QAAQ,IAAI,MAAM,OAAM,CAAE;AAC3C,cAAM,SAAS,GAAG,IAAI,QAAQA,MAAK;AAEnC,YAAI,MAAM,KAAK,EAAE,EAAE,eAAe,OAAO,MAAK,GAAI,OAAO,MAAK,IAAK,GAAG,KAAK,GAAG;AAC5E,aAAG,cAAc,GAAG,QAAQ,IAAI,MAAM,OAAM,CAAE,GAAG,KAAK;;;;AAK5D,QAAI,WAAW;AACb,kBAAY,OAAO,OAAO,iBAAiB,eAAe;;AAG5D,OAAG,eAAc;;AAGnB,SAAO;AACT;AClGO,IAAM,gBAA8C,CAAC,YAAY,gBAAgB,CAAA,MAAO,CAAC,EAC9F,IAAI,OAAO,UAAU,OAAM,MACxB;;AACH,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,EAAE,OAAO,IAAG,IAAK,MAAM;AAI3B,QAAM,OAAwB,MAAM,UAAU;AAEhD,MAAK,QAAQ,KAAK,WAAY,MAAM,QAAQ,KAAK,CAAC,MAAM,WAAW,GAAG,GAAG;AACvE,WAAO;;AAGT,QAAM,cAAc,MAAM,KAAK,EAAE;AAEjC,MAAI,YAAY,SAAS,MAAM;AAC7B,WAAO;;AAGT,QAAM,sBAAsB,OAAO,iBAAiB;AAEpD,MAAI,MAAM,OAAO,QAAQ,SAAS,KAAK,MAAM,KAAK,EAAE,EAAE,eAAe,MAAM,WAAW,EAAE,GAAG;AAIzF,QACE,MAAM,UAAU,KACX,MAAM,KAAK,EAAE,EAAE,SAAS,QACxB,MAAM,MAAM,EAAE,MAAM,MAAM,KAAK,EAAE,EAAE,aAAa,GACrD;AACA,aAAO;;AAGT,QAAI,UAAU;AACZ,UAAI,OAAO,SAAS;AAElB,YAAM,cAAc,MAAM,MAAM,EAAE,IAAI,IAAI,MAAM,MAAM,EAAE,IAAI,IAAI;AAIlE,eAAS,IAAI,MAAM,QAAQ,aAAa,KAAK,MAAM,QAAQ,GAAG,KAAK,GAAG;AACpE,eAAO,SAAS,KAAK,MAAM,KAAK,CAAC,EAAE,KAAK,IAAI,CAAC;;AAI7C,YAAM,aAAa,MAAM,WAAW,EAAE,IAAI,MAAM,KAAK,EAAE,EAAE,aAAa,IAAI,MAAM,WAAW,EAAE,IAAI,MAAM,KAAK,EAAE,EAAE,aAAa,IAAI;AAGnI,YAAMC,yBAAwB;QAC5B,GAAG,sBACD,qBACA,MAAM,KAAI,EAAG,KAAK,MAClB,MAAM,KAAI,EAAG,KAAK;QAEpB,GAAG;;AAEL,YAAMC,cAAW,KAAA,KAAK,aAAa,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAE,cAAcD,sBAAqB,MAAK;AAExF,aAAO,KAAK,OAAO,SAAS,KAAK,KAAK,cAAc,MAAMC,SAAQ,KAAK,MAAS,CAAC;AAEjF,YAAM,QAAQ,MAAM,OAAO,MAAM,SAAS,cAAc,EAAE;AAE1D,SAAG,QAAQ,OAAO,MAAM,MAAM,CAAC,UAAU,GAAG,IAAI,MAAM,MAAM,IAAI,aAAa,CAAC,CAAC;AAE/E,UAAI,MAAM;AAEV,SAAG,IAAI,aAAa,OAAO,GAAG,IAAI,QAAQ,MAAM,CAAC,GAAG,QAAO;AACzD,YAAI,MAAM,IAAI;AACZ,iBAAO;;AAGT,YAAI,EAAE,eAAe,EAAE,QAAQ,SAAS,GAAG;AACzC,gBAAM,MAAM;;MAEhB,CAAC;AAED,UAAI,MAAM,IAAI;AACZ,WAAG,aAAa,cAAc,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAC;;AAGzD,SAAG,eAAc;;AAGnB,WAAO;;AAGT,QAAM,WAAW,IAAI,QAAQ,MAAM,IAAG,IAAK,YAAY,eAAe,CAAC,EAAE,cAAc;AAEvF,QAAM,oBAAoB;IACxB,GAAG,sBACD,qBACA,YAAY,KAAK,MACjB,YAAY,KAAK;IAEnB,GAAG;;AAEL,QAAM,wBAAwB;IAC5B,GAAG,sBACD,qBACA,MAAM,KAAI,EAAG,KAAK,MAClB,MAAM,KAAI,EAAG,KAAK;IAEpB,GAAG;;AAGL,KAAG,OAAO,MAAM,KAAK,IAAI,GAAG;AAE5B,QAAM,QAAQ,WACV;IACA,EAAE,MAAM,OAAO,kBAAiB;IAChC,EAAE,MAAM,UAAU,OAAO,sBAAqB;EAC/C,IACC,CAAC,EAAE,MAAM,OAAO,kBAAiB,CAAE;AAEvC,MAAI,CAAC,SAAS,GAAG,KAAK,MAAM,KAAK,CAAC,GAAG;AACnC,WAAO;;AAGT,MAAI,UAAU;AACZ,UAAM,EAAE,WAAW,YAAW,IAAK;AACnC,UAAM,EAAE,gBAAe,IAAK,OAAO;AACnC,UAAM,QAAQ,eAAgB,UAAU,IAAI,gBAAgB,UAAU,MAAM,MAAK;AAEjF,OAAG,MAAM,MAAM,KAAK,GAAG,KAAK,EAAE,eAAc;AAE5C,QAAI,CAAC,SAAS,CAAC,UAAU;AACvB,aAAO;;AAGT,UAAM,gBAAgB,MAAM,OAAO,UAAQ,gBAAgB,SAAS,KAAK,KAAK,IAAI,CAAC;AAEnF,OAAG,YAAY,aAAa;;AAG9B,SAAO;AACT;ACvJA,IAAM,oBAAoB,CAAC,IAAiB,aAA+B;AACzE,QAAM,OAAO,eAAe,UAAQ,KAAK,SAAS,QAAQ,EAAE,GAAG,SAAS;AAExE,MAAI,CAAC,MAAM;AACT,WAAO;;AAGT,QAAM,SAAS,GAAG,IAAI,QAAQ,KAAK,IAAI,GAAG,KAAK,MAAM,CAAC,CAAC,EAAE,OAAO,KAAK,KAAK;AAE1E,MAAI,WAAW,QAAW;AACxB,WAAO;;AAGT,QAAM,aAAa,GAAG,IAAI,OAAO,MAAM;AACvC,QAAM,mBAAmB,KAAK,KAAK,UAAS,eAAA,QAAA,eAAA,SAAA,SAAA,WAAY,SAAQ,QAAQ,GAAG,KAAK,KAAK,GAAG;AAExF,MAAI,CAAC,kBAAkB;AACrB,WAAO;;AAGT,KAAG,KAAK,KAAK,GAAG;AAEhB,SAAO;AACT;AAEA,IAAM,mBAAmB,CAAC,IAAiB,aAA+B;AACxE,QAAM,OAAO,eAAe,UAAQ,KAAK,SAAS,QAAQ,EAAE,GAAG,SAAS;AAExE,MAAI,CAAC,MAAM;AACT,WAAO;;AAGT,QAAM,QAAQ,GAAG,IAAI,QAAQ,KAAK,KAAK,EAAE,MAAM,KAAK,KAAK;AAEzD,MAAI,UAAU,QAAW;AACvB,WAAO;;AAGT,QAAM,YAAY,GAAG,IAAI,OAAO,KAAK;AACrC,QAAM,kBAAkB,KAAK,KAAK,UAAS,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,SAAQ,QAAQ,GAAG,KAAK,KAAK;AAEnF,MAAI,CAAC,iBAAiB;AACpB,WAAO;;AAGT,KAAG,KAAK,KAAK;AAEb,SAAO;AACT;AAkBO,IAAM,aAAwC,CAAC,gBAAgB,gBAAgB,WAAW,aAAa,CAAA,MAAO,CAAC,EACpH,QAAQ,IAAI,OAAO,UAAU,OAAO,UAAAlD,WAAU,IAAG,MAC9C;AACH,QAAM,EAAE,YAAY,gBAAe,IAAK,OAAO;AAC/C,QAAM,WAAW,YAAY,gBAAgB,MAAM,MAAM;AACzD,QAAM,WAAW,YAAY,gBAAgB,MAAM,MAAM;AACzD,QAAM,EAAE,WAAW,YAAW,IAAK;AACnC,QAAM,EAAE,OAAO,IAAG,IAAK;AACvB,QAAM,QAAQ,MAAM,WAAW,GAAG;AAElC,QAAM,QAAQ,eAAgB,UAAU,IAAI,gBAAgB,UAAU,MAAM,MAAK;AAEjF,MAAI,CAAC,OAAO;AACV,WAAO;;AAGT,QAAM,aAAa,eAAe,UAAQ,OAAO,KAAK,KAAK,MAAM,UAAU,CAAC,EAAE,SAAS;AAEvF,MAAI,MAAM,SAAS,KAAK,cAAc,MAAM,QAAQ,WAAW,SAAS,GAAG;AAEzE,QAAI,WAAW,KAAK,SAAS,UAAU;AACrC,aAAOA,UAAS,aAAa,QAAQ;;AAIvC,QACE,OAAO,WAAW,KAAK,KAAK,MAAM,UAAU,KACvC,SAAS,aAAa,WAAW,KAAK,OAAO,KAC7C,UACL;AACA,aAAO,MAAK,EACT,QAAQ,MAAK;AACZ,WAAG,cAAc,WAAW,KAAK,QAAQ;AAEzC,eAAO;MACT,CAAC,EACA,QAAQ,MAAM,kBAAkB,IAAI,QAAQ,CAAC,EAC7C,QAAQ,MAAM,iBAAiB,IAAI,QAAQ,CAAC,EAC5C,IAAG;;;AAGV,MAAI,CAAC,aAAa,CAAC,SAAS,CAAC,UAAU;AAErC,WAAO,MAAK,EAET,QAAQ,MAAK;AACZ,YAAM,gBAAgB,IAAG,EAAG,WAAW,UAAU,UAAU;AAE3D,UAAI,eAAe;AACjB,eAAO;;AAGT,aAAOA,UAAS,WAAU;IAC5B,CAAC,EACA,WAAW,UAAU,UAAU,EAC/B,QAAQ,MAAM,kBAAkB,IAAI,QAAQ,CAAC,EAC7C,QAAQ,MAAM,iBAAiB,IAAI,QAAQ,CAAC,EAC5C,IAAG;;AAGR,SACE,MAAK,EAEF,QAAQ,MAAK;AACZ,UAAM,gBAAgB,IAAG,EAAG,WAAW,UAAU,UAAU;AAE3D,UAAM,gBAAgB,MAAM,OAAO,UAAQ,gBAAgB,SAAS,KAAK,KAAK,IAAI,CAAC;AAEnF,OAAG,YAAY,aAAa;AAE5B,QAAI,eAAe;AACjB,aAAO;;AAGT,WAAOA,UAAS,WAAU;EAC5B,CAAC,EACA,WAAW,UAAU,UAAU,EAC/B,QAAQ,MAAM,kBAAkB,IAAI,QAAQ,CAAC,EAC7C,QAAQ,MAAM,iBAAiB,IAAI,QAAQ,CAAC,EAC5C,IAAG;AAEV;ACtHO,IAAM,aAAwC,CAAC,YAAY,aAAa,CAAA,GAAI,UAAU,CAAA,MAAO,CAAC,EAAE,OAAO,UAAAA,UAAQ,MAAM;AAC1H,QAAM,EAAE,uBAAuB,MAAK,IAAK;AACzC,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAMwB,YAAW,aAAa,OAAO,MAAM,UAAU;AAErD,MAAIA,WAAU;AACZ,WAAOxB,UAAS,UAAU,MAAM,EAAE,qBAAoB,CAAE;;AAG1D,SAAOA,UAAS,QAAQ,MAAM,UAAU;AAC1C;ACvBO,IAAM,aAAwC,CAAC,YAAY,kBAAkB,aAAa,CAAA,MAAO,CAAC,EAAE,OAAO,UAAAA,UAAQ,MAAM;AAC9H,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,aAAa,YAAY,kBAAkB,MAAM,MAAM;AAC7D,QAAMwB,YAAW,aAAa,OAAO,MAAM,UAAU;AAErD,MAAI;AAEJ,MAAI,MAAM,UAAU,QAAQ,WAAW,MAAM,UAAU,KAAK,GAAG;AAE7D,uBAAmB,MAAM,UAAU,QAAQ,OAAO;;AAGpD,MAAIA,WAAU;AACZ,WAAOxB,UAAS,QAAQ,YAAY,gBAAgB;;AAKtD,SAAOA,UAAS,QAAQ,MAAM,EAAE,GAAG,kBAAkB,GAAG,WAAU,CAAE;AACtE;ACxBO,IAAM,aAAwC,CAAC,YAAY,aAAa,CAAA,MAAO,CAAC,EAAE,OAAO,UAAAA,UAAQ,MAAM;AAC5G,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAMwB,YAAW,aAAa,OAAO,MAAM,UAAU;AAErD,MAAIA,WAAU;AACZ,WAAOxB,UAAS,KAAK,IAAI;;AAG3B,SAAOA,UAAS,OAAO,MAAM,UAAU;AACzC;ACfO,IAAM,gBAA8C,MAAM,CAAC,EAAE,OAAO,SAAQ,MAAM;AACvF,QAAM,UAAU,MAAM;AAEtB,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK,GAAG;AAC1C,UAAM,SAAS,QAAQ,CAAC;AACxB,QAAI;AAIJ,QAAI,OAAO,KAAK,iBAAiB,WAAW,OAAO,SAAS,KAAK,IAAI;AACnE,UAAI,UAAU;AACZ,cAAM,KAAK,MAAM;AACjB,cAAM,SAAS,SAAS;AAExB,iBAAS,IAAI,OAAO,MAAM,SAAS,GAAG,KAAK,GAAG,KAAK,GAAG;AACpD,aAAG,KAAK,OAAO,MAAM,CAAC,EAAE,OAAO,OAAO,KAAK,CAAC,CAAC,CAAC;;AAGhD,YAAI,SAAS,MAAM;AACjB,gBAAM,QAAQ,GAAG,IAAI,QAAQ,SAAS,IAAI,EAAE,MAAK;AAEjD,aAAG,YAAY,SAAS,MAAM,SAAS,IAAI,MAAM,OAAO,KAAK,SAAS,MAAM,KAAK,CAAC;eAC7E;AACL,aAAG,OAAO,SAAS,MAAM,SAAS,EAAE;;;AAIxC,aAAO;;;AAIX,SAAO;AACT;AChCO,IAAM,gBAA8C,MAAM,CAAC,EAAE,IAAI,SAAQ,MAAM;AACpF,QAAM,EAAE,UAAS,IAAK;AACtB,QAAM,EAAE,OAAO,OAAM,IAAK;AAE1B,MAAI,OAAO;AACT,WAAO;;AAGT,MAAI,UAAU;AACZ,WAAO,QAAQ,WAAQ;AACrB,SAAG,WAAW,MAAM,MAAM,KAAK,MAAM,IAAI,GAAG;IAC9C,CAAC;;AAGH,SAAO;AACT;ACGO,IAAM,YAAsC,CAAC,YAAY,UAAU,CAAA,MAAO,CAAC,EAAE,IAAI,OAAO,SAAQ,MAAM;;AAC3G,QAAM,EAAE,uBAAuB,MAAK,IAAK;AACzC,QAAM,EAAE,UAAS,IAAK;AACtB,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AACjD,QAAM,EAAE,OAAO,OAAO,OAAM,IAAK;AAEjC,MAAI,CAAC,UAAU;AACb,WAAO;;AAGT,MAAI,SAAS,sBAAsB;AACjC,QAAI,EAAE,MAAM,GAAE,IAAK;AACnB,UAAM,SAAQ,KAAA,MAAM,MAAK,EAAG,KAAK,UAAQ,KAAK,SAAS,IAAI,OAAG,QAAA,OAAA,SAAA,SAAA,GAAA;AAC9D,UAAM,QAAQ,aAAa,OAAO,MAAM,KAAK;AAE7C,QAAI,OAAO;AACT,aAAO,MAAM;AACb,WAAK,MAAM;;AAGb,OAAG,WAAW,MAAM,IAAI,IAAI;SACvB;AACL,WAAO,QAAQ,WAAQ;AACrB,SAAG,WAAW,MAAM,MAAM,KAAK,MAAM,IAAI,KAAK,IAAI;IACpD,CAAC;;AAGH,KAAG,iBAAiB,IAAI;AAExB,SAAO;AACT;AC5BO,IAAM,mBAAoD,CAAC,YAAY,aAAa,CAAA,MAAO,CAAC,EAAE,IAAI,OAAO,SAAQ,MAAM;AAE5H,MAAI,WAA4B;AAChC,MAAI,WAA4B;AAEhC,QAAM,aAAa,wBACjB,OAAO,eAAe,WAAW,aAAa,WAAW,MACzD,MAAM,MAAM;AAGd,MAAI,CAAC,YAAY;AACf,WAAO;;AAGT,MAAI,eAAe,QAAQ;AACzB,eAAW,YAAY,YAAwB,MAAM,MAAM;;AAG7D,MAAI,eAAe,QAAQ;AACzB,eAAW,YAAY,YAAwB,MAAM,MAAM;;AAG7D,MAAI,UAAU;AACZ,OAAG,UAAU,OAAO,QAAQ,CAAC,UAAyB;AAEpD,YAAM,OAAO,MAAM,MAAM;AACzB,YAAM,KAAK,MAAM,IAAI;AAErB,UAAI;AACJ,UAAI;AACJ,UAAI;AACJ,UAAI;AAEJ,UAAI,GAAG,UAAU,OAAO;AACtB,cAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAY,QAAe;AAE3D,cAAI,YAAY,aAAa,KAAK,MAAM;AACtC,0BAAc,KAAK,IAAI,KAAK,IAAI;AAChC,wBAAY,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AAC5C,sBAAU;AACV,uBAAW;;QAEf,CAAC;aACI;AACL,cAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAY,QAAe;AAE3D,cAAI,MAAM,QAAQ,YAAY,aAAa,KAAK,MAAM;AACpD,0BAAc,KAAK,IAAI,KAAK,IAAI;AAChC,wBAAY,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AAC5C,sBAAU;AACV,uBAAW;;AAGb,cAAI,OAAO,QAAQ,OAAO,IAAI;AAE5B,gBAAI,YAAY,aAAa,KAAK,MAAM;AACtC,iBAAG,cAAc,KAAK,QAAW;gBAC/B,GAAG,KAAK;gBACR,GAAG;cACJ,CAAA;;AAGH,gBAAI,YAAY,KAAK,MAAM,QAAQ;AACjC,mBAAK,MAAM,QAAQ,CAAC,SAAc;AAEhC,oBAAI,aAAa,KAAK,MAAM;AAC1B,wBAAM,eAAe,KAAK,IAAI,KAAK,IAAI;AACvC,wBAAM,aAAa,KAAK,IAAI,MAAM,KAAK,UAAU,EAAE;AAEnD,qBAAG,QACD,cACA,YACA,SAAS,OAAO;oBACd,GAAG,KAAK;oBACR,GAAG;kBACJ,CAAA,CAAC;;cAGR,CAAC;;;QAGP,CAAC;;AAGH,UAAI,UAAU;AAEZ,YAAI,YAAY,QAAW;AACzB,aAAG,cAAc,SAAS,QAAW;YACnC,GAAG,SAAS;YACZ,GAAG;UACJ,CAAA;;AAGH,YAAI,YAAY,SAAS,MAAM,QAAQ;AACrC,mBAAS,MAAM,QAAQ,CAAC,SAAc;AAEpC,gBAAI,aAAa,KAAK,MAAM;AAC1B,iBAAG,QACD,aACA,WACA,SAAS,OAAO;gBACd,GAAG,KAAK;gBACR,GAAG;cACJ,CAAA,CAAC;;UAGR,CAAC;;;IAGP,CAAC;;AAGH,SAAO;AACT;AC/HO,IAAMmD,UAAgC,CAAC,YAAY,aAAa,CAAA,MAAO,CAAC,EAAE,OAAO,SAAQ,MAAM;AACpG,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AAEjD,SAAOC,OAAe,MAAM,UAAU,EAAE,OAAO,QAAQ;AACzD;ACJO,IAAMC,cAAwC,CAAC,YAAY,aAAa,CAAA,MAAO,CAAC,EAAE,OAAO,SAAQ,MAAM;AAC5G,QAAM,OAAO,YAAY,YAAY,MAAM,MAAM;AAEjD,SAAOC,WAAmB,MAAM,UAAU,EAAE,OAAO,QAAQ;AAC7D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnBO,IAAM,WAAW,UAAU,OAAO;EACvC,MAAM;EAEN,cAAW;AACT,WAAO;MACL,GAAG;;;AAGR,CAAA;ACTM,IAAM,OAAO,UAAU,OAAO;EACnC,MAAM;EAEN,wBAAqB;AACnB,WAAO;MACL,IAAI,OAAO;QACT,KAAK,IAAI,UAAU,YAAY;QAE/B,OAAO;UACL,YAAY,CAAC,GAAG,GAAG,OAAO,UAAS;AACjC,iBAAK,OAAO,KAAK,QAAQ;cACvB,QAAQ,KAAK;cACb,OAAO;cACP;cACA;YACD,CAAA;;QAEJ;OACF;;;AAGN,CAAA;ACrBM,IAAM,WAAW,UAAU,OAAO;EACvC,MAAM;EAEN,wBAAqB;AACnB,WAAO;MACL,IAAI,OAAO;QACT,KAAK,IAAI,UAAU,UAAU;QAC7B,OAAO;UACL,UAAU,MAAM,KAAK,OAAO,QAAQ;QACrC;OACF;;;AAGN,CAAA;ACbM,IAAM,uBAAuB,IAAI,UAAU,aAAa;AAExD,IAAM,cAAc,UAAU,OAAO;EAC1C,MAAM;EAEN,wBAAqB;AACnB,UAAM,EAAE,OAAM,IAAK;AAEnB,WAAO;MACL,IAAI,OAAO;QACT,KAAK;QACL,OAAO;UACL,iBAAiB;YACf,OAAO,CAAC,MAAM,UAAgB;AAC5B,qBAAO,YAAY;AAEnB,oBAAM,cAAc,OAAO,MAAM,GAC9B,QAAQ,SAAS,EAAE,MAAK,CAAE,EAC1B,QAAQ,gBAAgB,KAAK;AAEhC,mBAAK,SAAS,WAAW;AAEzB,qBAAO;;YAET,MAAM,CAAC,MAAM,UAAgB;AAC3B,qBAAO,YAAY;AAEnB,oBAAM,cAAc,OAAO,MAAM,GAC9B,QAAQ,QAAQ,EAAE,MAAK,CAAE,EACzB,QAAQ,gBAAgB,KAAK;AAEhC,mBAAK,SAAS,WAAW;AAEzB,qBAAO;;UAEV;QACF;OACF;;;AAGN,CAAA;ACnCM,IAAM,SAAS,UAAU,OAAO;EACrC,MAAM;EAEN,uBAAoB;AAClB,UAAM,kBAAkB,MAAM,KAAK,OAAO,SAAS,MAAM,CAAC,EAAE,UAAAtD,UAAQ,MAAO;MACzE,MAAMA,UAAS,cAAa;;MAG5B,MAAMA,UAAS,QAAQ,CAAC,EAAE,GAAE,MAAM;AAChC,cAAM,EAAE,WAAW,IAAG,IAAK;AAC3B,cAAM,EAAE,OAAO,QAAO,IAAK;AAC3B,cAAM,EAAE,KAAK,OAAM,IAAK;AACxB,cAAM,aAAa,QAAQ,OAAO,eAAe,MAAM,IAAI,GAAG,IAAI,QAAQ,MAAM,CAAC,IAAI;AACrF,cAAM,oBAAoB,WAAW,OAAO,KAAK,KAAK;AAEtD,cAAM,YAAY,QAAQ,MAAM,QAAQ;AAExC,cAAM,YAAa,qBAAqB,WAAW,OAAO,eAAe,IACrE,cAAc,QAAQ,MACtB,UAAU,QAAQ,GAAG,EAAE,SAAS;AAEpC,YACE,CAAC,SACE,CAAC,OAAO,KAAK,eACb,OAAO,YAAY,UACnB,CAAC,aACA,aAAa,QAAQ,OAAO,KAAK,SAAS,aAC9C;AACA,iBAAO;;AAGT,eAAOA,UAAS,WAAU;MAC5B,CAAC;MAED,MAAMA,UAAS,gBAAe;MAC9B,MAAMA,UAAS,aAAY;MAC3B,MAAMA,UAAS,mBAAkB;IAClC,CAAA;AAED,UAAM,eAAe,MAAM,KAAK,OAAO,SAAS,MAAM,CAAC,EAAE,UAAAA,UAAQ,MAAO;MACtE,MAAMA,UAAS,gBAAe;MAC9B,MAAMA,UAAS,kBAAiB;MAChC,MAAMA,UAAS,YAAW;MAC1B,MAAMA,UAAS,kBAAiB;IACjC,CAAA;AAED,UAAM,cAAc,MAAM,KAAK,OAAO,SAAS,MAAM,CAAC,EAAE,UAAAA,UAAQ,MAAO;MACrE,MAAMA,UAAS,cAAa;MAC5B,MAAMA,UAAS,oBAAmB;MAClC,MAAMA,UAAS,eAAc;MAC7B,MAAMA,UAAS,WAAU;IAC1B,CAAA;AAED,UAAM,aAAa;MACjB,OAAO;MACP,aAAa,MAAM,KAAK,OAAO,SAAS,SAAQ;MAChD,WAAW;MACX,iBAAiB;MACjB,mBAAmB;MACnB,QAAQ;MACR,cAAc;MACd,SAAS,MAAM,KAAK,OAAO,SAAS,UAAS;;AAG/C,UAAM,WAAW;MACf,GAAG;;AAGL,UAAM,YAAY;MAChB,GAAG;MACH,UAAU;MACV,iBAAiB;MACjB,UAAU;MACV,sBAAsB;MACtB,cAAc;MACd,SAAS;MACT,UAAU,MAAM,KAAK,OAAO,SAAS,qBAAoB;MACzD,UAAU,MAAM,KAAK,OAAO,SAAS,mBAAkB;;AAGzD,QAAI,MAAK,KAAM,QAAO,GAAI;AACxB,aAAO;;AAGT,WAAO;;EAGT,wBAAqB;AACnB,WAAO;;;;;;MAML,IAAI,OAAO;QACT,KAAK,IAAI,UAAU,eAAe;QAClC,mBAAmB,CAAC,cAAc,UAAU,aAAY;AACtD,cAAI,aAAa,KAAK,CAAAuD,QAAMA,IAAG,QAAQ,aAAa,CAAC,GAAG;AACtD;;AAGF,gBAAM,aAAa,aAAa,KAAK,iBAAe,YAAY,UAAU,KACrE,CAAC,SAAS,IAAI,GAAG,SAAS,GAAG;AAElC,gBAAM,WAAW,aAAa,KAAK,iBAAe,YAAY,QAAQ,sBAAsB,CAAC;AAE7F,cAAI,CAAC,cAAc,UAAU;AAC3B;;AAGF,gBAAM,EAAE,OAAO,MAAM,GAAE,IAAK,SAAS;AACrC,gBAAM,UAAU,UAAU,QAAQ,SAAS,GAAG,EAAE;AAChD,gBAAM,SAAS,UAAU,MAAM,SAAS,GAAG,EAAE;AAC7C,gBAAM,iBAAiB,SAAS,WAAW,OAAO;AAElD,cAAI,SAAS,CAAC,gBAAgB;AAC5B;;AAGF,gBAAM,UAAU,YAAY,SAAS,GAAG;AAExC,cAAI,CAAC,SAAS;AACZ;;AAGF,gBAAM,KAAK,SAAS;AACpB,gBAAM,QAAQ,qBAAqB;YACjC,OAAO;YACP,aAAa;UACd,CAAA;AACD,gBAAM,EAAE,UAAAvD,UAAQ,IAAK,IAAI,eAAe;YACtC,QAAQ,KAAK;YACb;UACD,CAAA;AAED,UAAAA,UAAS,WAAU;AAEnB,cAAI,CAAC,GAAG,MAAM,QAAQ;AACpB;;AAGF,iBAAO;;OAEV;;;AAGN,CAAA;ACvJM,IAAM,QAAQ,UAAU,OAAO;EACpC,MAAM;EAEN,wBAAqB;AAEnB,WAAO;MACL,IAAI,OAAO;QACT,KAAK,IAAI,UAAU,aAAa;QAEhC,OAAO;UACL,aAAa,CAAC,OAAO,GAAG,UAAS;AAC/B,iBAAK,OAAO,KAAK,SAAS;cACxB,QAAQ,KAAK;cACb,OAAO;cACP;YACD,CAAA;;QAEJ;OACF;;;AAGN,CAAA;ACrBM,IAAM,WAAW,UAAU,OAAO;EACvC,MAAM;EAEN,wBAAqB;AACnB,WAAO;MACL,IAAI,OAAO;QACT,KAAK,IAAI,UAAU,UAAU;QAC7B,OAAO;UACL,YAAY,MAAoC,KAAK,OAAO,aAAa,EAAE,UAAU,IAAG,IAAK,CAAA;QAC9F;OACF;;;AAGN,CAAA;;;;;;;;;;;;;ICVY,gBAAA,SAAO;EAOlB,IAAY,OAAI;AACd,WAAO,KAAK,KAAK,KAAK;;EAGxB,YAAY,KAAkB,QAAgB,UAAU,OAAO,OAAoB,MAAI;AAO/E,SAAW,cAAgB;AAU5B,SAAW,cAAkB;AAhBlC,SAAK,UAAU;AACf,SAAK,cAAc;AACnB,SAAK,SAAS;AACd,SAAK,cAAc;;EAKrB,IAAI,OAAI;AACN,WAAO,KAAK,eAAe,KAAK,YAAY,KAAI;;EAGlD,IAAI,UAAO;AACT,WAAO,KAAK,OAAO,KAAK,SAAS,KAAK,GAAG,EAAE;;EAK7C,IAAI,QAAK;;AACP,YAAO,KAAA,KAAK,iBAAW,QAAA,OAAA,SAAA,KAAI,KAAK,YAAY;;EAG9C,IAAI,MAAG;AACL,WAAO,KAAK,YAAY;;EAG1B,IAAI,UAAO;AACT,WAAO,KAAK,KAAK;;EAGnB,IAAI,QAAQ,SAAgB;AAC1B,QAAI,OAAO,KAAK;AAChB,QAAI,KAAK,KAAK;AAEd,QAAI,KAAK,SAAS;AAChB,UAAI,KAAK,QAAQ,SAAS,GAAG;AAC3B,gBAAQ,MAAM,kEAAkE,KAAK,IAAI,OAAO,KAAK,GAAG,EAAE;AAC1G;;AAGF,aAAO,KAAK,OAAO;AACnB,WAAK,KAAK,KAAK;;AAGjB,SAAK,OAAO,SAAS,gBAAgB,EAAE,MAAM,GAAE,GAAI,OAAO;;EAG5D,IAAI,aAAU;AACZ,WAAO,KAAK,KAAK;;EAGnB,IAAI,cAAW;AACb,WAAO,KAAK,KAAK;;EAGnB,IAAI,OAAI;AACN,WAAO,KAAK,KAAK;;EAGnB,IAAI,OAAI;AACN,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK;;AAGd,WAAO,KAAK,YAAY,MAAM,KAAK,YAAY,KAAK;;EAGtD,IAAI,QAAK;AACP,WAAO;MACL,MAAM,KAAK;MACX,IAAI,KAAK;;;EAIb,IAAI,KAAE;AACJ,QAAI,KAAK,SAAS;AAChB,aAAO,KAAK,MAAM,KAAK;;AAGzB,WAAO,KAAK,YAAY,IAAI,KAAK,YAAY,KAAK,KAAK,KAAK,KAAK,SAAS,IAAI;;EAGhF,IAAI,SAAM;AACR,QAAI,KAAK,UAAU,GAAG;AACpB,aAAO;;AAGT,UAAM,YAAY,KAAK,YAAY,MAAM,KAAK,YAAY,QAAQ,CAAC;AACnE,UAAM,OAAO,KAAK,YAAY,IAAI,QAAQ,SAAS;AAEnD,WAAO,IAAI,SAAQ,MAAM,KAAK,MAAM;;EAGtC,IAAI,SAAM;AACR,QAAI,OAAO,KAAK,YAAY,IAAI,QAAQ,KAAK,QAAQ,KAAK,UAAU,IAAI,EAAE;AAE1E,QAAI,KAAK,UAAU,KAAK,OAAO;AAC7B,aAAO,KAAK,YAAY,IAAI,QAAQ,KAAK,OAAO,CAAC;;AAGnD,WAAO,IAAI,SAAQ,MAAM,KAAK,MAAM;;EAGtC,IAAI,QAAK;AACP,QAAI,OAAO,KAAK,YAAY,IAAI,QAAQ,KAAK,MAAM,KAAK,UAAU,IAAI,EAAE;AAExE,QAAI,KAAK,UAAU,KAAK,OAAO;AAC7B,aAAO,KAAK,YAAY,IAAI,QAAQ,KAAK,KAAK,CAAC;;AAGjD,WAAO,IAAI,SAAQ,MAAM,KAAK,MAAM;;EAGtC,IAAI,WAAQ;AACV,UAAM,WAAsB,CAAA;AAE5B,SAAK,KAAK,QAAQ,QAAQ,CAAC,MAAM,WAAU;AACzC,YAAM,UAAU,KAAK,WAAW,CAAC,KAAK;AACtC,YAAM,gBAAgB,KAAK,UAAU,CAAC,KAAK;AAE3C,YAAM,YAAY,KAAK,MAAM,UAAU,gBAAgB,IAAI;AAC3D,YAAM,OAAO,KAAK,YAAY,IAAI,QAAQ,SAAS;AAEnD,UAAI,CAAC,WAAW,KAAK,SAAS,KAAK,OAAO;AACxC;;AAGF,YAAM,eAAe,IAAI,SAAQ,MAAM,KAAK,QAAQ,SAAS,UAAU,OAAO,IAAI;AAElF,UAAI,SAAS;AACX,qBAAa,cAAc,KAAK,QAAQ;;AAG1C,eAAS,KAAK,IAAI,SAAQ,MAAM,KAAK,QAAQ,SAAS,UAAU,OAAO,IAAI,CAAC;IAC9E,CAAC;AAED,WAAO;;EAGT,IAAI,aAAU;AACZ,WAAO,KAAK,SAAS,CAAC,KAAK;;EAG7B,IAAI,YAAS;AACX,UAAM,WAAW,KAAK;AAEtB,WAAO,SAAS,SAAS,SAAS,CAAC,KAAK;;EAG1C,QAAQ,UAAkB,aAAqC,CAAA,GAAE;AAC/D,QAAI,OAAuB;AAC3B,QAAI,cAAc,KAAK;AAEvB,WAAO,eAAe,CAAC,MAAM;AAC3B,UAAI,YAAY,KAAK,KAAK,SAAS,UAAU;AAC3C,YAAI,OAAO,KAAK,UAAU,EAAE,SAAS,GAAG;AACtC,gBAAM,iBAAiB,YAAY,KAAK;AACxC,gBAAM,WAAW,OAAO,KAAK,UAAU;AAEvC,mBAASD,SAAQ,GAAGA,SAAQ,SAAS,QAAQA,UAAS,GAAG;AACvD,kBAAM,MAAM,SAASA,MAAK;AAE1B,gBAAI,eAAe,GAAG,MAAM,WAAW,GAAG,GAAG;AAC3C;;;eAGC;AACL,iBAAO;;;AAIX,oBAAc,YAAY;;AAG5B,WAAO;;EAGT,cAAc,UAAkB,aAAqC,CAAA,GAAE;AACrE,WAAO,KAAK,iBAAiB,UAAU,YAAY,IAAI,EAAE,CAAC,KAAK;;EAGjE,iBAAiB,UAAkB,aAAqC,CAAA,GAAI,gBAAgB,OAAK;AAC/F,QAAI,QAAmB,CAAA;AAEvB,QAAI,CAAC,KAAK,YAAY,KAAK,SAAS,WAAW,GAAG;AAChD,aAAO;;AAET,UAAM,WAAW,OAAO,KAAK,UAAU;AAMvC,SAAK,SAAS,QAAQ,cAAW;AAE/B,UAAI,iBAAiB,MAAM,SAAS,GAAG;AACrC;;AAGF,UAAI,SAAS,KAAK,KAAK,SAAS,UAAU;AACxC,cAAM,yBAAyB,SAAS,MAAM,SAAO,WAAW,GAAG,MAAM,SAAS,KAAK,MAAM,GAAG,CAAC;AAEjG,YAAI,wBAAwB;AAC1B,gBAAM,KAAK,QAAQ;;;AAKvB,UAAI,iBAAiB,MAAM,SAAS,GAAG;AACrC;;AAGF,cAAQ,MAAM,OAAO,SAAS,iBAAiB,UAAU,YAAY,aAAa,CAAC;IACrF,CAAC;AAED,WAAO;;EAGT,aAAa,YAAkC;AAC7C,UAAM,EAAE,GAAE,IAAK,KAAK,OAAO;AAE3B,OAAG,cAAc,KAAK,MAAM,QAAW;MACrC,GAAG,KAAK,KAAK;MACb,GAAG;IACJ,CAAA;AAED,SAAK,OAAO,KAAK,SAAS,EAAE;;AAE/B;ACvPM,IAAM,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;SCAL,eAAeF,QAAe,OAAgB,QAAe;AAC3E,QAAM,iBAAoC,SAAS,cAAc,0BAA0B,SAAS,IAAI,MAAM,KAAK,EAAE,GAAG;AAExH,MAAI,mBAAmB,MAAM;AAC3B,WAAO;;AAGT,QAAM,YAAY,SAAS,cAAc,OAAO;AAEhD,MAAI,OAAO;AACT,cAAU,aAAa,SAAS,KAAK;;AAGvC,YAAU,aAAa,oBAAoB,SAAS,IAAI,MAAM,KAAK,EAAE,IAAI,EAAE;AAC3E,YAAU,YAAYA;AACtB,WAAS,qBAAqB,MAAM,EAAE,CAAC,EAAE,YAAY,SAAS;AAE9D,SAAO;AACT;AC8BM,IAAO,SAAP,cAAsB,aAA0B;EAgDpD,YAAY,UAAkC,CAAA,GAAE;AAC9C,UAAK;AAtCA,SAAS,YAAG;AAKZ,SAAa,gBAAG;AAEhB,SAAgB,mBAAwB,CAAA;AAExC,SAAA,UAAyB;MAC9B,SAAS,SAAS,cAAc,KAAK;MACrC,SAAS;MACT,WAAW;MACX,aAAa;MACb,YAAY,CAAA;MACZ,WAAW;MACX,UAAU;MACV,aAAa,CAAA;MACb,cAAc,CAAA;MACd,sBAAsB,CAAA;MACtB,kBAAkB;MAClB,kBAAkB;MAClB,sBAAsB;MACtB,oBAAoB;MACpB,gBAAgB,MAAM;MACtB,UAAU,MAAM;MAChB,UAAU,MAAM;MAChB,mBAAmB,MAAM;MACzB,eAAe,MAAM;MACrB,SAAS,MAAM;MACf,QAAQ,MAAM;MACd,WAAW,MAAM;MACjB,gBAAgB,CAAC,EAAE,MAAK,MAAM;AAAG,cAAM;MAAK;MAC5C,SAAS,MAAM;MACf,QAAQ,MAAM;;AAiUT,SAAsB,yBAAG;AAExB,SAAmB,sBAAuB;AA9ThD,SAAK,WAAW,OAAO;AACvB,SAAK,uBAAsB;AAC3B,SAAK,qBAAoB;AACzB,SAAK,aAAY;AACjB,SAAK,GAAG,gBAAgB,KAAK,QAAQ,cAAc;AACnD,SAAK,KAAK,gBAAgB,EAAE,QAAQ,KAAI,CAAE;AAC1C,SAAK,GAAG,gBAAgB,KAAK,QAAQ,cAAc;AACnD,SAAK,WAAU;AACf,SAAK,UAAS;AACd,SAAK,GAAG,UAAU,KAAK,QAAQ,QAAQ;AACvC,SAAK,GAAG,UAAU,KAAK,QAAQ,QAAQ;AACvC,SAAK,GAAG,mBAAmB,KAAK,QAAQ,iBAAiB;AACzD,SAAK,GAAG,eAAe,KAAK,QAAQ,aAAa;AACjD,SAAK,GAAG,SAAS,KAAK,QAAQ,OAAO;AACrC,SAAK,GAAG,QAAQ,KAAK,QAAQ,MAAM;AACnC,SAAK,GAAG,WAAW,KAAK,QAAQ,SAAS;AACzC,SAAK,GAAG,QAAQ,CAAC,EAAE,OAAO,OAAO,MAAK,MAAO,KAAK,QAAQ,OAAO,OAAO,OAAO,KAAK,CAAC;AACrF,SAAK,GAAG,SAAS,CAAC,EAAE,OAAO,MAAK,MAAO,KAAK,QAAQ,QAAQ,OAAO,KAAK,CAAC;AAEzE,WAAO,WAAW,MAAK;AACrB,UAAI,KAAK,aAAa;AACpB;;AAGF,WAAK,SAAS,MAAM,KAAK,QAAQ,SAAS;AAC1C,WAAK,KAAK,UAAU,EAAE,QAAQ,KAAI,CAAE;AACpC,WAAK,gBAAgB;OACpB,CAAC;;;;;EAMN,IAAW,UAAO;AAChB,WAAO,KAAK;;;;;EAMd,IAAW,WAAQ;AACjB,WAAO,KAAK,eAAe;;;;;EAMtB,QAAK;AACV,WAAO,KAAK,eAAe,MAAK;;;;;EAM3B,MAAG;AACR,WAAO,KAAK,eAAe,IAAG;;;;;EAMxB,YAAS;AACf,QAAI,KAAK,QAAQ,aAAa,UAAU;AACtC,WAAK,MAAM,eAAe,OAAO,KAAK,QAAQ,WAAW;;;;;;;;EAStD,WAAW,UAAkC,CAAA,GAAE;AACpD,SAAK,UAAU;MACb,GAAG,KAAK;MACR,GAAG;;AAGL,QAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,SAAS,KAAK,aAAa;AACjD;;AAGF,QAAI,KAAK,QAAQ,aAAa;AAC5B,WAAK,KAAK,SAAS,KAAK,QAAQ,WAAW;;AAG7C,SAAK,KAAK,YAAY,KAAK,KAAK;;;;;EAM3B,YAAY,UAAmB,aAAa,MAAI;AACrD,SAAK,WAAW,EAAE,SAAQ,CAAE;AAE5B,QAAI,YAAY;AACd,WAAK,KAAK,UAAU,EAAE,QAAQ,MAAM,aAAa,KAAK,MAAM,GAAE,CAAE;;;;;;EAOpE,IAAW,aAAU;AAInB,WAAO,KAAK,QAAQ,YAAY,KAAK,QAAQ,KAAK,KAAK;;;;;EAMzD,IAAW,QAAK;AACd,WAAO,KAAK,KAAK;;;;;;;;;EAUZ,eACL,QACA,eAAkE;AAElE,UAAM,UAAU,WAAW,aAAa,IACpC,cAAc,QAAQ,CAAC,GAAG,KAAK,MAAM,OAAO,CAAC,IAC7C,CAAC,GAAG,KAAK,MAAM,SAAS,MAAM;AAElC,UAAM,QAAQ,KAAK,MAAM,YAAY,EAAE,QAAO,CAAE;AAEhD,SAAK,KAAK,YAAY,KAAK;AAE3B,WAAO;;;;;;;;EASF,iBAAiB,yBAAoE;AAC1F,QAAI,KAAK,aAAa;AACpB,aAAO;;AAGT,UAAM,cAAc,KAAK,MAAM;AAC/B,QAAI,UAAU;AAEb,KAAA,EAA8B,OAAO,uBAAuB,EAAE,QAAQ,qBAAkB;AAEvF,YAAM,OAAO,OAAO,oBAAoB,WAAW,GAAG,eAAe,MAAM,gBAAgB;AAG3F,gBAAU,QAAQ,OAAO,YAAU,CAAC,OAAO,IAAI,WAAW,IAAI,CAAC;IACjE,CAAC;AAED,QAAI,YAAY,WAAW,QAAQ,QAAQ;AAEzC,aAAO;;AAGT,UAAM,QAAQ,KAAK,MAAM,YAAY;MACnC;IACD,CAAA;AAED,SAAK,KAAK,YAAY,KAAK;AAE3B,WAAO;;;;;EAMD,yBAAsB;;AAE5B,UAAM,iBAAiB,KAAK,QAAQ,uBAAuB;MACzD;MACA,wBAAwB,UAAU;QAChC,iBAAgB,MAAA,KAAA,KAAK,QAAQ,0BAAoB,QAAA,OAAA,SAAA,SAAA,GAAE,6BAAuB,QAAA,OAAA,SAAA,SAAA,GAAE;OAC7E;MACD;MACA;MACA;MACA;MACA;MACA;IACD,EAAC,OAAO,SAAM;AACb,UAAI,OAAO,KAAK,QAAQ,yBAAyB,UAAU;AACzD,eAAO,KAAK,QAAQ,qBAAqB,IAAI,IAAsD,MAAM;;AAE3G,aAAO;IACT,CAAC,IAAI,CAAA;AACL,UAAM,gBAAgB,CAAC,GAAG,gBAAgB,GAAG,KAAK,QAAQ,UAAU,EAAE,OAAO,eAAY;AACvF,aAAO,CAAC,aAAa,QAAQ,MAAM,EAAE,SAAS,cAAS,QAAT,cAAS,SAAA,SAAT,UAAW,IAAI;IAC/D,CAAC;AAED,SAAK,mBAAmB,IAAI,iBAAiB,eAAe,IAAI;;;;;EAM1D,uBAAoB;AAC1B,SAAK,iBAAiB,IAAI,eAAe;MACvC,QAAQ;IACT,CAAA;;;;;EAMK,eAAY;AAClB,SAAK,SAAS,KAAK,iBAAiB;;;;;EAM9B,aAAU;;AAChB,QAAI;AAEJ,QAAI;AACF,YAAM,eACJ,KAAK,QAAQ,SACb,KAAK,QACL,KAAK,QAAQ,cACb,EAAE,uBAAuB,KAAK,QAAQ,mBAAkB,CAAE;aAErD,GAAG;AACV,UAAI,EAAE,aAAa,UAAU,CAAC,CAAC,wCAAwC,sCAAsC,EAAE,SAAS,EAAE,OAAO,GAAG;AAElI,cAAM;;AAER,WAAK,KAAK,gBAAgB;QACxB,QAAQ;QACR,OAAO;QACP,sBAAsB,MAAK;AACzB,cAAI,KAAK,QAAQ,eAAe;AAC9B,iBAAK,QAAQ,cAAc,aAAa;;AAG1C,eAAK,QAAQ,aAAa,KAAK,QAAQ,WAAW,OAAO,eAAa,UAAU,SAAS,eAAe;AAGxG,eAAK,uBAAsB;;MAE9B,CAAA;AAGD,YAAM,eACJ,KAAK,QAAQ,SACb,KAAK,QACL,KAAK,QAAQ,cACb,EAAE,uBAAuB,MAAK,CAAE;;AAGpC,UAAM,YAAY,qBAAqB,KAAK,KAAK,QAAQ,SAAS;AAElE,SAAK,OAAO,IAAI,WAAW,KAAK,QAAQ,SAAS;MAC/C,GAAG,KAAK,QAAQ;MAChB,YAAY;;QAEV,MAAM;QACN,IAAG,KAAA,KAAK,QAAQ,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAE;MAC9B;MACD,qBAAqB,KAAK,oBAAoB,KAAK,IAAI;MACvD,OAAO,YAAY,OAAO;QACxB;QACA,WAAW,aAAa;OACzB;IACF,CAAA;AAID,UAAM,WAAW,KAAK,MAAM,YAAY;MACtC,SAAS,KAAK,iBAAiB;IAChC,CAAA;AAED,SAAK,KAAK,YAAY,QAAQ;AAE9B,SAAK,gBAAe;AACpB,SAAK,aAAY;AAKjB,UAAM,MAAM,KAAK,KAAK;AAEtB,QAAI,SAAS;;;;;EAMR,kBAAe;AACpB,QAAI,KAAK,KAAK,aAAa;AACzB;;AAGF,SAAK,KAAK,SAAS;MACjB,WAAW,KAAK,iBAAiB;IAClC,CAAA;;;;;EAMI,eAAY;AACjB,SAAK,KAAK,IAAI,YAAY,UAAU,KAAK,KAAK,IAAI,SAAS;;EAOtD,mBAAmB,IAAc;AACtC,SAAK,yBAAyB;AAC9B,OAAE;AACF,SAAK,yBAAyB;AAE9B,UAAM,KAAK,KAAK;AAEhB,SAAK,sBAAsB;AAE3B,WAAO;;;;;;;EAQD,oBAAoB,aAAwB;AAGlD,QAAI,KAAK,KAAK,aAAa;AACzB;;AAGF,QAAI,KAAK,wBAAwB;AAC/B,UAAI,CAAC,KAAK,qBAAqB;AAC7B,aAAK,sBAAsB;AAE3B;;AAGF,kBAAY,MAAM,QAAQ,UAAO;AAAA,YAAA;AAAC,gBAAA,KAAA,KAAK,yBAAmB,QAAA,OAAA,SAAA,SAAA,GAAE,KAAK,IAAI;MAAC,CAAA;AAEtE;;AAGF,UAAM,QAAQ,KAAK,MAAM,MAAM,WAAW;AAC1C,UAAM,sBAAsB,CAAC,KAAK,MAAM,UAAU,GAAG,MAAM,SAAS;AAEpE,SAAK,KAAK,qBAAqB;MAC7B,QAAQ;MACR;MACA,WAAW;IACZ,CAAA;AACD,SAAK,KAAK,YAAY,KAAK;AAC3B,SAAK,KAAK,eAAe;MACvB,QAAQ;MACR;IACD,CAAA;AAED,QAAI,qBAAqB;AACvB,WAAK,KAAK,mBAAmB;QAC3B,QAAQ;QACR;MACD,CAAA;;AAGH,UAAM2D,SAAQ,YAAY,QAAQ,OAAO;AACzC,UAAMC,QAAO,YAAY,QAAQ,MAAM;AAEvC,QAAID,QAAO;AACT,WAAK,KAAK,SAAS;QACjB,QAAQ;QACR,OAAOA,OAAM;QACb;MACD,CAAA;;AAGH,QAAIC,OAAM;AACR,WAAK,KAAK,QAAQ;QAChB,QAAQ;QACR,OAAOA,MAAK;QACZ;MACD,CAAA;;AAGH,QAAI,CAAC,YAAY,cAAc,YAAY,QAAQ,eAAe,GAAG;AACnE;;AAGF,SAAK,KAAK,UAAU;MAClB,QAAQ;MACR;IACD,CAAA;;;;;EAMI,cAAc,YAAwC;AAC3D,WAAO,cAAc,KAAK,OAAO,UAAU;;EAWtC,SAAS,kBAA0B,uBAA0B;AAClE,UAAM,OAAO,OAAO,qBAAqB,WAAW,mBAAmB;AAEvE,UAAM,aAAa,OAAO,qBAAqB,WAAW,wBAAwB;AAElF,WAAO,SAAS,KAAK,OAAO,MAAM,UAAU;;;;;EAMvC,UAAO;AACZ,WAAO,KAAK,MAAM,IAAI,OAAM;;;;;EAMvB,UAAO;AACZ,WAAO,oBAAoB,KAAK,MAAM,IAAI,SAAS,KAAK,MAAM;;;;;EAMzD,QAAQ,SAGd;AACC,UAAM,EAAE,iBAAiB,QAAQ,kBAAkB,CAAA,EAAE,IAAK,WAAW,CAAA;AAErE,WAAO,QAAQ,KAAK,MAAM,KAAK;MAC7B;MACA,iBAAiB;QACf,GAAG,6BAA6B,KAAK,MAAM;QAC3C,GAAG;MACJ;IACF,CAAA;;;;;EAMH,IAAW,UAAO;AAChB,WAAO,YAAY,KAAK,MAAM,GAAG;;;;;;;EAQ5B,oBAAiB;AACtB,YAAQ,KACN,6HAA6H;AAG/H,WAAO,KAAK,MAAM,IAAI,QAAQ,OAAO;;;;;EAMhC,UAAO;AACZ,SAAK,KAAK,SAAS;AAEnB,QAAI,KAAK,MAAM;AAGb,YAAM,MAAM,KAAK,KAAK;AAEtB,UAAI,OAAO,IAAI,QAAQ;AACrB,eAAO,IAAI;;AAEb,WAAK,KAAK,QAAO;;AAGnB,SAAK,mBAAkB;;;;;EAMzB,IAAW,cAAW;;AAEpB,WAAO,GAAC,KAAA,KAAK,UAAI,QAAA,OAAA,SAAA,SAAA,GAAE;;EAGd,MAAM,UAAkB,YAAmC;;AAChE,aAAO,KAAA,KAAK,UAAM,QAAA,OAAA,SAAA,SAAA,GAAA,cAAc,UAAU,UAAU,MAAK;;EAGpD,OAAO,UAAkB,YAAmC;;AACjE,aAAO,KAAA,KAAK,UAAM,QAAA,OAAA,SAAA,SAAA,GAAA,iBAAiB,UAAU,UAAU,MAAK;;EAGvD,KAAK,KAAW;AACrB,UAAM,OAAO,KAAK,MAAM,IAAI,QAAQ,GAAG;AAEvC,WAAO,IAAI,QAAQ,MAAM,IAAI;;EAG/B,IAAI,OAAI;AACN,WAAO,KAAK,KAAK,CAAC;;AAErB;ACpmBK,SAAU,cAAc,QAQ7B;AACC,SAAO,IAAI,UAAU;IACnB,MAAM,OAAO;IACb,SAAS,CAAC,EAAE,OAAO,OAAO,MAAK,MAAM;AACnC,YAAM,aAAa,aAAa,OAAO,eAAe,QAAW,KAAK;AAEtE,UAAI,eAAe,SAAS,eAAe,MAAM;AAC/C,eAAO;;AAGT,YAAM,EAAE,GAAE,IAAK;AACf,YAAM,eAAe,MAAM,MAAM,SAAS,CAAC;AAC3C,YAAM,YAAY,MAAM,CAAC;AAEzB,UAAI,cAAc;AAChB,cAAM,cAAc,UAAU,OAAO,IAAI;AACzC,cAAM,YAAY,MAAM,OAAO,UAAU,QAAQ,YAAY;AAC7D,cAAM,UAAU,YAAY,aAAa;AAEzC,cAAM,gBAAgB,gBAAgB,MAAM,MAAM,MAAM,IAAI,MAAM,GAAG,EAClE,OAAO,UAAO;AAEb,gBAAM,WAAW,KAAK,KAAK,KAAK;AAEhC,iBAAO,SAAS,KAAK,UAAQ,SAAS,OAAO,QAAQ,SAAS,KAAK,KAAK,IAAI;QAC9E,CAAC,EACA,OAAO,UAAQ,KAAK,KAAK,SAAS;AAErC,YAAI,cAAc,QAAQ;AACxB,iBAAO;;AAGT,YAAI,UAAU,MAAM,IAAI;AACtB,aAAG,OAAO,SAAS,MAAM,EAAE;;AAG7B,YAAI,YAAY,MAAM,MAAM;AAC1B,aAAG,OAAO,MAAM,OAAO,aAAa,SAAS;;AAG/C,cAAM,UAAU,MAAM,OAAO,cAAc,aAAa;AAExD,WAAG,QAAQ,MAAM,OAAO,aAAa,SAAS,OAAO,KAAK,OAAO,cAAc,CAAA,CAAE,CAAC;AAElF,WAAG,iBAAiB,OAAO,IAAI;;;EAGpC,CAAA;AACH;ACzDM,SAAU,cAAc,QAoB7B;AACC,SAAO,IAAI,UAAU;IACnB,MAAM,OAAO;IACb,SAAS,CAAC,EAAE,OAAO,OAAO,MAAK,MAAM;AACnC,YAAM,aAAa,aAAa,OAAO,eAAe,QAAW,KAAK,KAAK,CAAA;AAC3E,YAAM,EAAE,GAAE,IAAK;AACf,YAAM,QAAQ,MAAM;AACpB,UAAI,MAAM,MAAM;AAEhB,YAAM,UAAU,OAAO,KAAK,OAAO,UAAU;AAE7C,UAAI,MAAM,CAAC,GAAG;AACZ,cAAM,SAAS,MAAM,CAAC,EAAE,YAAY,MAAM,CAAC,CAAC;AAC5C,YAAI,aAAa,QAAQ;AAEzB,YAAI,aAAa,KAAK;AACpB,uBAAa;eACR;AACL,gBAAM,aAAa,MAAM,CAAC,EAAE;;AAI9B,cAAM,WAAW,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,SAAS,CAAC;AAE7C,WAAG,WAAW,UAAU,QAAQ,MAAM,CAAC,EAAE,SAAS,CAAC;AAGnD,WAAG,YAAY,YAAY,KAAK,OAAO;iBAC9B,MAAM,CAAC,GAAG;AACnB,cAAM,iBAAiB,OAAO,KAAK,WAAW,QAAQ,QAAQ;AAE9D,WAAG,OAAO,gBAAgB,OAAO,KAAK,OAAO,UAAU,CAAC,EAAE,OACxD,GAAG,QAAQ,IAAI,KAAK,GACpB,GAAG,QAAQ,IAAI,GAAG,CAAC;;AAIvB,SAAG,eAAc;;EAEpB,CAAA;AACH;AC1DM,SAAU,uBAAuB,QAQtC;AACC,SAAO,IAAI,UAAU;IACnB,MAAM,OAAO;IACb,SAAS,CAAC,EAAE,OAAO,OAAO,MAAK,MAAM;AACnC,YAAM,SAAS,MAAM,IAAI,QAAQ,MAAM,IAAI;AAC3C,YAAM,aAAa,aAAa,OAAO,eAAe,QAAW,KAAK,KAAK,CAAA;AAE3E,UAAI,CAAC,OAAO,KAAK,EAAE,EAAE,eAAe,OAAO,MAAM,EAAE,GAAG,OAAO,WAAW,EAAE,GAAG,OAAO,IAAI,GAAG;AACzF,eAAO;;AAGT,YAAM,GACH,OAAO,MAAM,MAAM,MAAM,EAAE,EAC3B,aAAa,MAAM,MAAM,MAAM,MAAM,OAAO,MAAM,UAAU;;EAElE,CAAA;AACH;AC9BM,SAAU,cAAc,QAG7B;AACC,SAAO,IAAI,UAAU;IACnB,MAAM,OAAO;IACb,SAAS,CAAC,EAAE,OAAO,OAAO,MAAK,MAAM;AACnC,UAAI,SAAS,OAAO;AACpB,UAAI,QAAQ,MAAM;AAClB,YAAM,MAAM,MAAM;AAElB,UAAI,MAAM,CAAC,GAAG;AACZ,cAAM,SAAS,MAAM,CAAC,EAAE,YAAY,MAAM,CAAC,CAAC;AAE5C,kBAAU,MAAM,CAAC,EAAE,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM;AACjD,iBAAS;AAET,cAAM,SAAS,QAAQ;AAEvB,YAAI,SAAS,GAAG;AACd,mBAAS,MAAM,CAAC,EAAE,MAAM,SAAS,QAAQ,MAAM,IAAI;AACnD,kBAAQ;;;AAIZ,YAAM,GAAG,WAAW,QAAQ,OAAO,GAAG;;EAEzC,CAAA;AACH;ACZM,SAAU,kBAAkB,QAajC;AACC,SAAO,IAAI,UAAU;IACnB,MAAM,OAAO;IACb,SAAS,CAAC,EACR,OAAO,OAAO,OAAO,MAAK,MACvB;AACH,YAAM,aAAa,aAAa,OAAO,eAAe,QAAW,KAAK,KAAK,CAAA;AAC3E,YAAM,KAAK,MAAM,GAAG,OAAO,MAAM,MAAM,MAAM,EAAE;AAC/C,YAAM,SAAS,GAAG,IAAI,QAAQ,MAAM,IAAI;AACxC,YAAM,aAAa,OAAO,WAAU;AACpC,YAAM,WAAW,cAAc,aAAa,YAAY,OAAO,MAAM,UAAU;AAE/E,UAAI,CAAC,UAAU;AACb,eAAO;;AAGT,SAAG,KAAK,YAAY,QAAQ;AAE5B,UAAI,OAAO,aAAa,OAAO,QAAQ;AACrC,cAAM,EAAE,WAAW,YAAW,IAAK;AACnC,cAAM,EAAE,gBAAe,IAAK,OAAO,OAAO;AAC1C,cAAM,QAAQ,eAAgB,UAAU,IAAI,gBAAgB,UAAU,MAAM,MAAK;AAEjF,YAAI,OAAO;AACT,gBAAM,gBAAgB,MAAM,OAAO,UAAQ,gBAAgB,SAAS,KAAK,KAAK,IAAI,CAAC;AAEnF,aAAG,YAAY,aAAa;;;AAGhC,UAAI,OAAO,gBAAgB;AAEzB,cAAM,WAAW,OAAO,KAAK,SAAS,gBAAgB,OAAO,KAAK,SAAS,gBAAgB,aAAa;AAExG,cAAK,EAAG,iBAAiB,UAAU,UAAU,EAAE,IAAG;;AAGpD,YAAM,SAAS,GAAG,IAAI,QAAQ,MAAM,OAAO,CAAC,EAAE;AAE9C,UACE,UACG,OAAO,SAAS,OAAO,QACvB,QAAQ,GAAG,KAAK,MAAM,OAAO,CAAC,MAC7B,CAAC,OAAO,iBAAiB,OAAO,cAAc,OAAO,MAAM,IAC/D;AACA,WAAG,KAAK,MAAM,OAAO,CAAC;;;EAG3B,CAAA;AACH;ICqpBad,cAAA,MAAI;EAkBf,YAAY,SAAgD,CAAA,GAAE;AAjB9D,SAAI,OAAG;AAEP,SAAI,OAAG;AAEP,SAAM,SAAgB;AAEtB,SAAK,QAAgB;AAMrB,SAAA,SAAqB;MACnB,MAAM,KAAK;MACX,gBAAgB,CAAA;;AAIhB,SAAK,SAAS;MACZ,GAAG,KAAK;MACR,GAAG;;AAGL,SAAK,OAAO,KAAK,OAAO;AAExB,QAAI,OAAO,kBAAkB,OAAO,KAAK,OAAO,cAAc,EAAE,SAAS,GAAG;AAC1E,cAAQ,KACN,yHAAyH,KAAK,IAAI,IAAI;;AAK1I,SAAK,UAAU,KAAK,OAAO;AAE3B,QAAI,KAAK,OAAO,YAAY;AAC1B,WAAK,UAAU,aACb,kBAA2C,MAAM,cAAc;QAC7D,MAAM,KAAK;MACZ,CAAA,CAAC;;AAIN,SAAK,UAAU,aACb,kBAA2C,MAAM,cAAc;MAC7D,MAAM,KAAK;MACX,SAAS,KAAK;KACf,CAAC,KACC,CAAA;;EAGP,OAAO,OAAyB,SAAoC,CAAA,GAAE;AACpE,WAAO,IAAI,MAAW,MAAM;;EAG9B,UAAU,UAA4B,CAAA,GAAE;AAGtC,UAAM,YAAY,KAAK,OAAyB;MAC9C,GAAG,KAAK;MACR,YAAY,MAAK;AACf,eAAO,UAAU,KAAK,SAAgC,OAAO;;IAEhE,CAAA;AAGD,cAAU,OAAO,KAAK;AAEtB,cAAU,SAAS,KAAK;AAExB,WAAO;;EAGT,OACE,iBAAwE,CAAA,GAAE;AAE1E,UAAM,YAAY,IAAI,MAAuC,cAAc;AAE3E,cAAU,SAAS;AAEnB,SAAK,QAAQ;AAEb,cAAU,OAAO,eAAe,OAAO,eAAe,OAAO,UAAU,OAAO;AAE9E,QAAI,eAAe,kBAAkB,OAAO,KAAK,eAAe,cAAc,EAAE,SAAS,GAAG;AAC1F,cAAQ,KACN,yHAAyH,UAAU,IAAI,IAAI;;AAI/I,cAAU,UAAU,aAClB,kBAA2C,WAAW,cAAc;MAClE,MAAM,UAAU;IACjB,CAAA,CAAC;AAGJ,cAAU,UAAU,aAClB,kBAA2C,WAAW,cAAc;MAClE,MAAM,UAAU;MAChB,SAAS,UAAU;IACpB,CAAA,CAAC;AAGJ,WAAO;;AAEV;ICt0BY,iBAAQ;EA2BnB,YAAY,WAAsB,OAA8B,SAA0B;AAF1F,SAAU,aAAG;AAGX,SAAK,YAAY;AACjB,SAAK,SAAS,MAAM;AACpB,SAAK,UAAU;MACb,WAAW;MACX,gBAAgB;MAChB,GAAG;;AAEL,SAAK,YAAY,MAAM;AACvB,SAAK,OAAO,MAAM;AAClB,SAAK,cAAc,MAAM;AACzB,SAAK,mBAAmB,MAAM;AAC9B,SAAK,OAAO,MAAM;AAClB,SAAK,iBAAiB,MAAM;AAC5B,SAAK,SAAS,MAAM;AACpB,SAAK,MAAK;;EAGZ,QAAK;AAEH;;EAGF,IAAI,MAAG;AACL,WAAO,KAAK,OAAO,KAAK;;EAG1B,IAAI,aAAU;AACZ,WAAO;;EAGT,YAAY,OAAgB;;AAC1B,UAAM,EAAE,KAAI,IAAK,KAAK;AACtB,UAAM,SAAS,MAAM;AAIrB,UAAM,aAAa,OAAO,aAAa,KACnC,KAAA,OAAO,mBAAe,QAAA,OAAA,SAAA,SAAA,GAAA,QAAQ,oBAAoB,IAClD,OAAO,QAAQ,oBAAoB;AAEvC,QAAI,CAAC,KAAK,SAAO,KAAA,KAAK,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS,MAAM,MAAK,CAAC,YAAY;AACjE;;AAGF,QAAI,IAAI;AACR,QAAI,IAAI;AAGR,QAAI,KAAK,QAAQ,YAAY;AAC3B,YAAM,SAAS,KAAK,IAAI,sBAAqB;AAC7C,YAAM,YAAY,WAAW,sBAAqB;AAGlD,YAAM,WAAU,KAAA,MAAM,aAAW,QAAA,OAAA,SAAA,MAAA,KAAC,MAAc,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAE;AAC7D,YAAM,WAAU,KAAA,MAAM,aAAW,QAAA,OAAA,SAAA,MAAA,KAAC,MAAc,iBAAW,QAAA,OAAA,SAAA,SAAA,GAAE;AAE7D,UAAI,UAAU,IAAI,OAAO,IAAI;AAC7B,UAAI,UAAU,IAAI,OAAO,IAAI;;AAG/B,UAAM,aAAa,KAAK,IAAI,UAAU,IAAI;AAE1C,KAAA,KAAA,MAAM,kBAAY,QAAA,OAAA,SAAA,SAAA,GAAE,aAAa,YAAY,GAAG,CAAC;AAEjD,UAAM,MAAM,KAAK,OAAM;AAEvB,QAAI,OAAO,QAAQ,UAAU;AAC3B;;AAIF,UAAM,YAAY,cAAc,OAAO,KAAK,MAAM,KAAK,GAAG;AAC1D,UAAM,cAAc,KAAK,MAAM,GAAG,aAAa,SAAS;AAExD,SAAK,SAAS,WAAW;;EAG3B,UAAU,OAAY;;AACpB,QAAI,CAAC,KAAK,KAAK;AACb,aAAO;;AAGT,QAAI,OAAO,KAAK,QAAQ,cAAc,YAAY;AAChD,aAAO,KAAK,QAAQ,UAAU,EAAE,MAAK,CAAE;;AAGzC,UAAM,SAAS,MAAM;AACrB,UAAM,cAAc,KAAK,IAAI,SAAS,MAAM,KAAK,GAAC,KAAA,KAAK,gBAAU,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS,MAAM;AAGlF,QAAI,CAAC,aAAa;AAChB,aAAO;;AAGT,UAAM,cAAc,MAAM,KAAK,WAAW,MAAM;AAChD,UAAM,cAAc,MAAM,SAAS;AACnC,UAAM,UAAU,CAAC,SAAS,UAAU,UAAU,UAAU,EAAE,SAAS,OAAO,OAAO,KAAK,OAAO;AAG7F,QAAI,WAAW,CAAC,eAAe,CAAC,aAAa;AAC3C,aAAO;;AAGT,UAAM,EAAE,WAAU,IAAK,KAAK;AAC5B,UAAM,EAAE,WAAU,IAAK;AACvB,UAAM,cAAc,CAAC,CAAC,KAAK,KAAK,KAAK,KAAK;AAC1C,UAAM,eAAe,cAAc,aAAa,KAAK,IAAI;AACzD,UAAM,cAAc,MAAM,SAAS;AACnC,UAAM,eAAe,MAAM,SAAS;AACpC,UAAM,aAAa,MAAM,SAAS;AAClC,UAAM,eAAe,MAAM,SAAS;AAKpC,QAAI,CAAC,eAAe,gBAAgB,eAAe,MAAM,WAAW,KAAK,KAAK;AAC5E,YAAM,eAAc;;AAGtB,QAAI,eAAe,eAAe,CAAC,cAAc,MAAM,WAAW,KAAK,KAAK;AAC1E,YAAM,eAAc;AACpB,aAAO;;AAIT,QAAI,eAAe,cAAc,CAAC,cAAc,cAAc;AAC5D,YAAM,aAAa,OAAO,QAAQ,oBAAoB;AACtD,YAAM,oBAAoB,eAAe,KAAK,QAAQ,cAAc,KAAK,IAAI,SAAS,UAAU;AAEhG,UAAI,mBAAmB;AACrB,aAAK,aAAa;AAElB,iBAAS,iBACP,WACA,MAAK;AACH,eAAK,aAAa;QACpB,GACA,EAAE,MAAM,KAAI,CAAE;AAGhB,iBAAS,iBACP,QACA,MAAK;AACH,eAAK,aAAa;QACpB,GACA,EAAE,MAAM,KAAI,CAAE;AAGhB,iBAAS,iBACP,WACA,MAAK;AACH,eAAK,aAAa;QACpB,GACA,EAAE,MAAM,KAAI,CAAE;;;AAMpB,QACE,cACG,eACA,eACA,gBACA,cACC,gBAAgB,cACpB;AACA,aAAO;;AAGT,WAAO;;;;;;;EAQT,eAAe,UAA4B;AACzC,QAAI,CAAC,KAAK,OAAO,CAAC,KAAK,YAAY;AACjC,aAAO;;AAGT,QAAI,OAAO,KAAK,QAAQ,mBAAmB,YAAY;AACrD,aAAO,KAAK,QAAQ,eAAe,EAAE,SAAQ,CAAE;;AAKjD,QAAI,KAAK,KAAK,UAAU,KAAK,KAAK,QAAQ;AACxC,aAAO;;AAIT,QAAI,SAAS,SAAS,aAAa;AACjC,aAAO;;AAQT,QACE,KAAK,IAAI,SAAS,SAAS,MAAM,KAC9B,SAAS,SAAS,gBACjB,MAAK,KAAM,UAAS,MACrB,KAAK,OAAO,WACf;AACA,YAAM,eAAe;QACnB,GAAG,MAAM,KAAK,SAAS,UAAU;QACjC,GAAG,MAAM,KAAK,SAAS,YAAY;;AAKrC,UAAI,aAAa,MAAM,UAAQ,KAAK,iBAAiB,GAAG;AACtD,eAAO;;;AAMX,QAAI,KAAK,eAAe,SAAS,UAAU,SAAS,SAAS,cAAc;AACzE,aAAO;;AAIT,QAAI,KAAK,WAAW,SAAS,SAAS,MAAM,GAAG;AAC7C,aAAO;;AAGT,WAAO;;;;;EAMT,iBAAiB,YAA+B;AAC9C,SAAK,OAAO,SAAS,QAAQ,CAAC,EAAE,GAAE,MAAM;AACtC,YAAM,MAAM,KAAK,OAAM;AAEvB,UAAI,OAAO,QAAQ,UAAU;AAC3B,eAAO;;AAGT,SAAG,cAAc,KAAK,QAAW;QAC/B,GAAG,KAAK,KAAK;QACb,GAAG;MACJ,CAAA;AAED,aAAO;IACT,CAAC;;;;;EAMH,aAAU;AACR,UAAM,OAAO,KAAK,OAAM;AAExB,QAAI,OAAO,SAAS,UAAU;AAC5B;;AAEF,UAAM,KAAK,OAAO,KAAK,KAAK;AAE5B,SAAK,OAAO,SAAS,YAAY,EAAE,MAAM,GAAE,CAAE;;AAEhD;ACxSK,SAAU,cAAc,QAQ7B;AACC,SAAO,IAAI,UAAU;IACnB,MAAM,OAAO;IACb,SAAS,CAAC,EACR,OAAO,OAAO,OAAO,WAAU,MAC5B;AACH,YAAM,aAAa,aAAa,OAAO,eAAe,QAAW,OAAO,UAAU;AAElF,UAAI,eAAe,SAAS,eAAe,MAAM;AAC/C,eAAO;;AAGT,YAAM,EAAE,GAAE,IAAK;AACf,YAAM,eAAe,MAAM,MAAM,SAAS,CAAC;AAC3C,YAAM,YAAY,MAAM,CAAC;AACzB,UAAI,UAAU,MAAM;AAEpB,UAAI,cAAc;AAChB,cAAM,cAAc,UAAU,OAAO,IAAI;AACzC,cAAM,YAAY,MAAM,OAAO,UAAU,QAAQ,YAAY;AAC7D,cAAM,UAAU,YAAY,aAAa;AAEzC,cAAM,gBAAgB,gBAAgB,MAAM,MAAM,MAAM,IAAI,MAAM,GAAG,EAClE,OAAO,UAAO;AAEb,gBAAM,WAAW,KAAK,KAAK,KAAK;AAEhC,iBAAO,SAAS,KAAK,UAAQ,SAAS,OAAO,QAAQ,SAAS,KAAK,KAAK,IAAI;QAC9E,CAAC,EACA,OAAO,UAAQ,KAAK,KAAK,SAAS;AAErC,YAAI,cAAc,QAAQ;AACxB,iBAAO;;AAGT,YAAI,UAAU,MAAM,IAAI;AACtB,aAAG,OAAO,SAAS,MAAM,EAAE;;AAG7B,YAAI,YAAY,MAAM,MAAM;AAC1B,aAAG,OAAO,MAAM,OAAO,aAAa,SAAS;;AAG/C,kBAAU,MAAM,OAAO,cAAc,aAAa;AAElD,WAAG,QAAQ,MAAM,OAAO,aAAa,SAAS,OAAO,KAAK,OAAO,cAAc,CAAA,CAAE,CAAC;AAElF,WAAG,iBAAiB,OAAO,IAAI;;;EAGpC,CAAA;AACH;ACtEM,SAAU,eAAe,QAAc;AAC3C,SAAO,OAAO,QAAQ,yBAAyB,MAAM;AACvD;ACHM,SAAU,SAAS,OAAU;AACjC,SAAO,OAAO,UAAU;AAC1B;ACSM,SAAU,cAAc,QAa7B;AACC,SAAO,IAAI,UAAU;IACnB,MAAM,OAAO;IACb,QAAQ,EACN,OAAO,OAAO,OAAO,WAAU,GAChC;AACC,YAAM,aAAa,aAAa,OAAO,eAAe,QAAW,OAAO,UAAU;AAClF,YAAM,UAAU,aAAa,OAAO,YAAY,QAAW,UAAU;AAErE,UAAI,eAAe,SAAS,eAAe,MAAM;AAC/C,eAAO;;AAGT,YAAM,OAAO,EAAE,MAAM,OAAO,KAAK,MAAM,OAAO,WAAU;AAExD,UAAI,SAAS;AACX,aAAK,UAAU;;AAGjB,UAAI,MAAM,OAAO;AACf,cAAK,EAAG,YAAY,KAAK,EAAE,gBAAgB,MAAM,MAAM,IAAI;;;EAGhE,CAAA;AACH;ACzCM,SAAU,cAAc,QAG7B;AACC,SAAO,IAAI,UAAU;IACnB,MAAM,OAAO;IACb,SAAS,CAAC,EAAE,OAAO,OAAO,MAAK,MAAM;AACnC,UAAI,SAAS,OAAO;AACpB,UAAI,QAAQ,MAAM;AAClB,YAAM,MAAM,MAAM;AAElB,UAAI,MAAM,CAAC,GAAG;AACZ,cAAM,SAAS,MAAM,CAAC,EAAE,YAAY,MAAM,CAAC,CAAC;AAE5C,kBAAU,MAAM,CAAC,EAAE,MAAM,SAAS,MAAM,CAAC,EAAE,MAAM;AACjD,iBAAS;AAET,cAAM,SAAS,QAAQ;AAEvB,YAAI,SAAS,GAAG;AACd,mBAAS,MAAM,CAAC,EAAE,MAAM,SAAS,QAAQ,MAAM,IAAI;AACnD,kBAAQ;;;AAIZ,YAAM,GAAG,WAAW,QAAQ,OAAO,GAAG;;EAEzC,CAAA;AACH;IC5Ba,gBAAO;EAKlB,YAAY,aAAwB;AAClC,SAAK,cAAc;AACnB,SAAK,cAAc,KAAK,YAAY,MAAM;;EAG5C,IAAI,UAAgB;AAClB,QAAI,UAAU;AAEd,UAAM,iBAAiB,KAAK,YAAY,MACrC,MAAM,KAAK,WAAW,EACtB,OAAO,CAAC,aAAa,SAAQ;AAC5B,YAAM,YAAY,KAAK,OAAM,EAAG,UAAU,WAAW;AAErD,UAAI,UAAU,SAAS;AACrB,kBAAU;;AAGZ,aAAO,UAAU;OAChB,QAAQ;AAEb,WAAO;MACL,UAAU;MACV;;;AAGL;", "names": ["import_dist", "import_dist", "import_dist", "import_dist", "command", "run", "style", "_a", "index", "commands", "from", "to", "range", "createParagraphNear", "originalCreateParagraphNear", "deleteSelection", "originalDeleteSelection", "exitCode", "originalExitCode", "mark", "ProseMirrorNode", "joinUp", "originalJoinUp", "joinDown", "originalJoinDown", "joinBackward", "originalJoinBackward", "joinForward", "originalJoin<PERSON>orward", "joinTextblockBackward", "originalCommand", "joinTextblockForward", "lift", "isActive", "originalLift", "liftEmptyBlock", "originalLiftEmptyBlock", "liftListItem", "originalLiftListItem", "newlineInCode", "originalNewlineInCode", "selectNodeBackward", "originalSelectNodeBackward", "selectNodeForward", "originalSelectNodeForward", "selectParentNode", "originalSelectParentNode", "selectTextblockEnd", "originalSelectTextblockEnd", "selectTextblockStart", "originalSelectTextblockStart", "document", "Node", "node", "output", "sinkListItem", "originalSinkListItem", "first", "newNextTypeAttributes", "nextType", "wrapIn", "originalWrapIn", "wrapInList", "originalWrapInList", "tr", "focus", "blur"]}