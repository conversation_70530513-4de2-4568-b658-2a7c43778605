{"version": 3, "sources": ["../src/index.ts", "../src/text.ts"], "sourcesContent": ["import { Text } from './text.js'\n\nexport * from './text.js'\n\nexport default Text\n", "import { Node } from '@tiptap/core'\n\n/**\n * This extension allows you to create text nodes.\n * @see https://www.tiptap.dev/api/nodes/text\n */\nexport const Text = Node.create({\n  name: 'text',\n  group: 'inline',\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,kBAAqB;AAMd,IAAM,OAAO,iBAAK,OAAO;AAAA,EAC9B,MAAM;AAAA,EACN,OAAO;AACT,CAAC;;;ADLD,IAAO,gBAAQ;", "names": []}