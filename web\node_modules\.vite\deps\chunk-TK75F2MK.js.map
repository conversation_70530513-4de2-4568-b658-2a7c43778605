{"version": 3, "sources": ["../../linkifyjs/dist/linkify.mjs"], "sourcesContent": ["// THIS FILE IS AUTOMATICALLY GENERATED DO NOT EDIT DIRECTLY\n// See update-tlds.js for encoding/decoding format\n// https://data.iana.org/TLD/tlds-alpha-by-domain.txt\nconst encodedTlds = 'aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5mögensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2';\n// Internationalized domain names containing non-ASCII\nconst encodedUtlds = 'ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2';\n\n/**\n * @template A\n * @template B\n * @param {A} target\n * @param {B} properties\n * @return {A & B}\n */\nconst assign = (target, properties) => {\n  for (const key in properties) {\n    target[key] = properties[key];\n  }\n  return target;\n};\n\n/**\n * Finite State Machine generation utilities\n */\n\n/**\n * @template T\n * @typedef {{ [group: string]: T[] }} Collections\n */\n\n/**\n * @typedef {{ [group: string]: true }} Flags\n */\n\n// Keys in scanner Collections instances\nconst numeric = 'numeric';\nconst ascii = 'ascii';\nconst alpha = 'alpha';\nconst asciinumeric = 'asciinumeric';\nconst alphanumeric = 'alphanumeric';\nconst domain = 'domain';\nconst emoji = 'emoji';\nconst scheme = 'scheme';\nconst slashscheme = 'slashscheme';\nconst whitespace = 'whitespace';\n\n/**\n * @template T\n * @param {string} name\n * @param {Collections<T>} groups to register in\n * @returns {T[]} Current list of tokens in the given collection\n */\nfunction registerGroup(name, groups) {\n  if (!(name in groups)) {\n    groups[name] = [];\n  }\n  return groups[name];\n}\n\n/**\n * @template T\n * @param {T} t token to add\n * @param {Collections<T>} groups\n * @param {Flags} flags\n */\nfunction addToGroups(t, flags, groups) {\n  if (flags[numeric]) {\n    flags[asciinumeric] = true;\n    flags[alphanumeric] = true;\n  }\n  if (flags[ascii]) {\n    flags[asciinumeric] = true;\n    flags[alpha] = true;\n  }\n  if (flags[asciinumeric]) {\n    flags[alphanumeric] = true;\n  }\n  if (flags[alpha]) {\n    flags[alphanumeric] = true;\n  }\n  if (flags[alphanumeric]) {\n    flags[domain] = true;\n  }\n  if (flags[emoji]) {\n    flags[domain] = true;\n  }\n  for (const k in flags) {\n    const group = registerGroup(k, groups);\n    if (group.indexOf(t) < 0) {\n      group.push(t);\n    }\n  }\n}\n\n/**\n * @template T\n * @param {T} t token to check\n * @param {Collections<T>} groups\n * @returns {Flags} group flags that contain this token\n */\nfunction flagsForToken(t, groups) {\n  const result = {};\n  for (const c in groups) {\n    if (groups[c].indexOf(t) >= 0) {\n      result[c] = true;\n    }\n  }\n  return result;\n}\n\n/**\n * @template T\n * @typedef {null | T } Transition\n */\n\n/**\n * Define a basic state machine state. j is the list of character transitions,\n * jr is the list of regex-match transitions, jd is the default state to\n * transition to t is the accepting token type, if any. If this is the terminal\n * state, then it does not emit a token.\n *\n * The template type T represents the type of the token this state accepts. This\n * should be a string (such as of the token exports in `text.js`) or a\n * MultiToken subclass (from `multi.js`)\n *\n * @template T\n * @param {T} [token] Token that this state emits\n */\nfunction State(token = null) {\n  // this.n = null; // DEBUG: State name\n  /** @type {{ [input: string]: State<T> }} j */\n  this.j = {}; // IMPLEMENTATION 1\n  // this.j = []; // IMPLEMENTATION 2\n  /** @type {[RegExp, State<T>][]} jr */\n  this.jr = [];\n  /** @type {?State<T>} jd */\n  this.jd = null;\n  /** @type {?T} t */\n  this.t = token;\n}\n\n/**\n * Scanner token groups\n * @type Collections<string>\n */\nState.groups = {};\nState.prototype = {\n  accepts() {\n    return !!this.t;\n  },\n  /**\n   * Follow an existing transition from the given input to the next state.\n   * Does not mutate.\n   * @param {string} input character or token type to transition on\n   * @returns {?State<T>} the next state, if any\n   */\n  go(input) {\n    const state = this;\n    const nextState = state.j[input];\n    if (nextState) {\n      return nextState;\n    }\n    for (let i = 0; i < state.jr.length; i++) {\n      const regex = state.jr[i][0];\n      const nextState = state.jr[i][1]; // note: might be empty to prevent default jump\n      if (nextState && regex.test(input)) {\n        return nextState;\n      }\n    }\n    // Nowhere left to jump! Return default, if any\n    return state.jd;\n  },\n  /**\n   * Whether the state has a transition for the given input. Set the second\n   * argument to true to only look for an exact match (and not a default or\n   * regular-expression-based transition)\n   * @param {string} input\n   * @param {boolean} exactOnly\n   */\n  has(input, exactOnly = false) {\n    return exactOnly ? input in this.j : !!this.go(input);\n  },\n  /**\n   * Short for \"transition all\"; create a transition from the array of items\n   * in the given list to the same final resulting state.\n   * @param {string | string[]} inputs Group of inputs to transition on\n   * @param {Transition<T> | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   */\n  ta(inputs, next, flags, groups) {\n    for (let i = 0; i < inputs.length; i++) {\n      this.tt(inputs[i], next, flags, groups);\n    }\n  },\n  /**\n   * Short for \"take regexp transition\"; defines a transition for this state\n   * when it encounters a token which matches the given regular expression\n   * @param {RegExp} regexp Regular expression transition (populate first)\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   * @returns {State<T>} taken after the given input\n   */\n  tr(regexp, next, flags, groups) {\n    groups = groups || State.groups;\n    let nextState;\n    if (next && next.j) {\n      nextState = next;\n    } else {\n      // Token with maybe token groups\n      nextState = new State(next);\n      if (flags && groups) {\n        addToGroups(next, flags, groups);\n      }\n    }\n    this.jr.push([regexp, nextState]);\n    return nextState;\n  },\n  /**\n   * Short for \"take transitions\", will take as many sequential transitions as\n   * the length of the given input and returns the\n   * resulting final state.\n   * @param {string | string[]} input\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of token groups\n   * @returns {State<T>} taken after the given input\n   */\n  ts(input, next, flags, groups) {\n    let state = this;\n    const len = input.length;\n    if (!len) {\n      return state;\n    }\n    for (let i = 0; i < len - 1; i++) {\n      state = state.tt(input[i]);\n    }\n    return state.tt(input[len - 1], next, flags, groups);\n  },\n  /**\n   * Short for \"take transition\", this is a method for building/working with\n   * state machines.\n   *\n   * If a state already exists for the given input, returns it.\n   *\n   * If a token is specified, that state will emit that token when reached by\n   * the linkify engine.\n   *\n   * If no state exists, it will be initialized with some default transitions\n   * that resemble existing default transitions.\n   *\n   * If a state is given for the second argument, that state will be\n   * transitioned to on the given input regardless of what that input\n   * previously did.\n   *\n   * Specify a token group flags to define groups that this token belongs to.\n   * The token will be added to corresponding entires in the given groups\n   * object.\n   *\n   * @param {string} input character, token type to transition on\n   * @param {T | State<T>} [next] Transition options\n   * @param {Flags} [flags] Collections flags to add token to\n   * @param {Collections<T>} [groups] Master list of groups\n   * @returns {State<T>} taken after the given input\n   */\n  tt(input, next, flags, groups) {\n    groups = groups || State.groups;\n    const state = this;\n\n    // Check if existing state given, just a basic transition\n    if (next && next.j) {\n      state.j[input] = next;\n      return next;\n    }\n    const t = next;\n\n    // Take the transition with the usual default mechanisms and use that as\n    // a template for creating the next state\n    let nextState,\n      templateState = state.go(input);\n    if (templateState) {\n      nextState = new State();\n      assign(nextState.j, templateState.j);\n      nextState.jr.push.apply(nextState.jr, templateState.jr);\n      nextState.jd = templateState.jd;\n      nextState.t = templateState.t;\n    } else {\n      nextState = new State();\n    }\n    if (t) {\n      // Ensure newly token is in the same groups as the old token\n      if (groups) {\n        if (nextState.t && typeof nextState.t === 'string') {\n          const allFlags = assign(flagsForToken(nextState.t, groups), flags);\n          addToGroups(t, allFlags, groups);\n        } else if (flags) {\n          addToGroups(t, flags, groups);\n        }\n      }\n      nextState.t = t; // overwrite anything that was previously there\n    }\n    state.j[input] = nextState;\n    return nextState;\n  }\n};\n\n// Helper functions to improve minification (not exported outside linkifyjs module)\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string | string[]} input\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst ta = (state, input, next, flags, groups) => state.ta(input, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {RegExp} regexp\n * @param {T | State<T>} [next]\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst tr = (state, regexp, next, flags, groups) => state.tr(regexp, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string | string[]} input\n * @param {T | State<T>} [next]\n * @param {Flags} [flags]\n * @param {Collections<T>} [groups]\n */\nconst ts = (state, input, next, flags, groups) => state.ts(input, next, flags, groups);\n\n/**\n * @template T\n * @param {State<T>} state\n * @param {string} input\n * @param {T | State<T>} [next]\n * @param {Collections<T>} [groups]\n * @param {Flags} [flags]\n */\nconst tt = (state, input, next, flags, groups) => state.tt(input, next, flags, groups);\n\n/******************************************************************************\nText Tokens\nIdentifiers for token outputs from the regexp scanner\n******************************************************************************/\n\n// A valid web domain token\nconst WORD = 'WORD'; // only contains a-z\nconst UWORD = 'UWORD'; // contains letters other than a-z, used for IDN\nconst ASCIINUMERICAL = 'ASCIINUMERICAL'; // contains a-z, 0-9\nconst ALPHANUMERICAL = 'ALPHANUMERICAL'; // contains numbers and letters other than a-z, used for IDN\n\n// Special case of word\nconst LOCALHOST = 'LOCALHOST';\n\n// Valid top-level domain, special case of WORD (see tlds.js)\nconst TLD = 'TLD';\n\n// Valid IDN TLD, special case of UWORD (see tlds.js)\nconst UTLD = 'UTLD';\n\n// The scheme portion of a web URI protocol. Supported types include: `mailto`,\n// `file`, and user-defined custom protocols. Limited to schemes that contain\n// only letters\nconst SCHEME = 'SCHEME';\n\n// Similar to SCHEME, except makes distinction for schemes that must always be\n// followed by `://`, not just `:`. Supported types include `http`, `https`,\n// `ftp`, `ftps`\nconst SLASH_SCHEME = 'SLASH_SCHEME';\n\n// Any sequence of digits 0-9\nconst NUM = 'NUM';\n\n// Any number of consecutive whitespace characters that are not newline\nconst WS = 'WS';\n\n// New line (unix style)\nconst NL = 'NL'; // \\n\n\n// Opening/closing bracket classes\n// TODO: Rename OPEN -> LEFT and CLOSE -> RIGHT in v5 to fit with Unicode names\n// Also rename angle brackes to LESSTHAN and GREATER THAN\nconst OPENBRACE = 'OPENBRACE'; // {\nconst CLOSEBRACE = 'CLOSEBRACE'; // }\nconst OPENBRACKET = 'OPENBRACKET'; // [\nconst CLOSEBRACKET = 'CLOSEBRACKET'; // ]\nconst OPENPAREN = 'OPENPAREN'; // (\nconst CLOSEPAREN = 'CLOSEPAREN'; // )\nconst OPENANGLEBRACKET = 'OPENANGLEBRACKET'; // <\nconst CLOSEANGLEBRACKET = 'CLOSEANGLEBRACKET'; // >\nconst FULLWIDTHLEFTPAREN = 'FULLWIDTHLEFTPAREN'; // （\nconst FULLWIDTHRIGHTPAREN = 'FULLWIDTHRIGHTPAREN'; // ）\nconst LEFTCORNERBRACKET = 'LEFTCORNERBRACKET'; // 「\nconst RIGHTCORNERBRACKET = 'RIGHTCORNERBRACKET'; // 」\nconst LEFTWHITECORNERBRACKET = 'LEFTWHITECORNERBRACKET'; // 『\nconst RIGHTWHITECORNERBRACKET = 'RIGHTWHITECORNERBRACKET'; // 』\nconst FULLWIDTHLESSTHAN = 'FULLWIDTHLESSTHAN'; // ＜\nconst FULLWIDTHGREATERTHAN = 'FULLWIDTHGREATERTHAN'; // ＞\n\n// Various symbols\nconst AMPERSAND = 'AMPERSAND'; // &\nconst APOSTROPHE = 'APOSTROPHE'; // '\nconst ASTERISK = 'ASTERISK'; // *\nconst AT = 'AT'; // @\nconst BACKSLASH = 'BACKSLASH'; // \\\nconst BACKTICK = 'BACKTICK'; // `\nconst CARET = 'CARET'; // ^\nconst COLON = 'COLON'; // :\nconst COMMA = 'COMMA'; // ,\nconst DOLLAR = 'DOLLAR'; // $\nconst DOT = 'DOT'; // .\nconst EQUALS = 'EQUALS'; // =\nconst EXCLAMATION = 'EXCLAMATION'; // !\nconst HYPHEN = 'HYPHEN'; // -\nconst PERCENT = 'PERCENT'; // %\nconst PIPE = 'PIPE'; // |\nconst PLUS = 'PLUS'; // +\nconst POUND = 'POUND'; // #\nconst QUERY = 'QUERY'; // ?\nconst QUOTE = 'QUOTE'; // \"\nconst FULLWIDTHMIDDLEDOT = 'FULLWIDTHMIDDLEDOT'; // ・\n\nconst SEMI = 'SEMI'; // ;\nconst SLASH = 'SLASH'; // /\nconst TILDE = 'TILDE'; // ~\nconst UNDERSCORE = 'UNDERSCORE'; // _\n\n// Emoji symbol\nconst EMOJI$1 = 'EMOJI';\n\n// Default token - anything that is not one of the above\nconst SYM = 'SYM';\n\nvar tk = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tALPHANUMERICAL: ALPHANUMERICAL,\n\tAMPERSAND: AMPERSAND,\n\tAPOSTROPHE: APOSTROPHE,\n\tASCIINUMERICAL: ASCIINUMERICAL,\n\tASTERISK: ASTERISK,\n\tAT: AT,\n\tBACKSLASH: BACKSLASH,\n\tBACKTICK: BACKTICK,\n\tCARET: CARET,\n\tCLOSEANGLEBRACKET: CLOSEANGLEBRACKET,\n\tCLOSEBRACE: CLOSEBRACE,\n\tCLOSEBRACKET: CLOSEBRACKET,\n\tCLOSEPAREN: CLOSEPAREN,\n\tCOLON: COLON,\n\tCOMMA: COMMA,\n\tDOLLAR: DOLLAR,\n\tDOT: DOT,\n\tEMOJI: EMOJI$1,\n\tEQUALS: EQUALS,\n\tEXCLAMATION: EXCLAMATION,\n\tFULLWIDTHGREATERTHAN: FULLWIDTHGREATERTHAN,\n\tFULLWIDTHLEFTPAREN: FULLWIDTHLEFTPAREN,\n\tFULLWIDTHLESSTHAN: FULLWIDTHLESSTHAN,\n\tFULLWIDTHMIDDLEDOT: FULLWIDTHMIDDLEDOT,\n\tFULLWIDTHRIGHTPAREN: FULLWIDTHRIGHTPAREN,\n\tHYPHEN: HYPHEN,\n\tLEFTCORNERBRACKET: LEFTCORNERBRACKET,\n\tLEFTWHITECORNERBRACKET: LEFTWHITECORNERBRACKET,\n\tLOCALHOST: LOCALHOST,\n\tNL: NL,\n\tNUM: NUM,\n\tOPENANGLEBRACKET: OPENANGLEBRACKET,\n\tOPENBRACE: OPENBRACE,\n\tOPENBRACKET: OPENBRACKET,\n\tOPENPAREN: OPENPAREN,\n\tPERCENT: PERCENT,\n\tPIPE: PIPE,\n\tPLUS: PLUS,\n\tPOUND: POUND,\n\tQUERY: QUERY,\n\tQUOTE: QUOTE,\n\tRIGHTCORNERBRACKET: RIGHTCORNERBRACKET,\n\tRIGHTWHITECORNERBRACKET: RIGHTWHITECORNERBRACKET,\n\tSCHEME: SCHEME,\n\tSEMI: SEMI,\n\tSLASH: SLASH,\n\tSLASH_SCHEME: SLASH_SCHEME,\n\tSYM: SYM,\n\tTILDE: TILDE,\n\tTLD: TLD,\n\tUNDERSCORE: UNDERSCORE,\n\tUTLD: UTLD,\n\tUWORD: UWORD,\n\tWORD: WORD,\n\tWS: WS\n});\n\n// Note that these two Unicode ones expand into a really big one with Babel\nconst ASCII_LETTER = /[a-z]/;\nconst LETTER = /\\p{L}/u; // Any Unicode character with letter data type\nconst EMOJI = /\\p{Emoji}/u; // Any Unicode emoji character\nconst EMOJI_VARIATION$1 = /\\ufe0f/;\nconst DIGIT = /\\d/;\nconst SPACE = /\\s/;\n\nvar regexp = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tASCII_LETTER: ASCII_LETTER,\n\tDIGIT: DIGIT,\n\tEMOJI: EMOJI,\n\tEMOJI_VARIATION: EMOJI_VARIATION$1,\n\tLETTER: LETTER,\n\tSPACE: SPACE\n});\n\n/**\n\tThe scanner provides an interface that takes a string of text as input, and\n\toutputs an array of tokens instances that can be used for easy URL parsing.\n*/\n\nconst CR = '\\r'; // carriage-return character\nconst LF = '\\n'; // line-feed character\nconst EMOJI_VARIATION = '\\ufe0f'; // Variation selector, follows heart and others\nconst EMOJI_JOINER = '\\u200d'; // zero-width joiner\nconst OBJECT_REPLACEMENT = '\\ufffc'; // whitespace placeholder that sometimes appears in rich text editors\n\nlet tlds = null,\n  utlds = null; // don't change so only have to be computed once\n\n/**\n * Scanner output token:\n * - `t` is the token name (e.g., 'NUM', 'EMOJI', 'TLD')\n * - `v` is the value of the token (e.g., '123', '❤️', 'com')\n * - `s` is the start index of the token in the original string\n * - `e` is the end index of the token in the original string\n * @typedef {{t: string, v: string, s: number, e: number}} Token\n */\n\n/**\n * @template T\n * @typedef {{ [collection: string]: T[] }} Collections\n */\n\n/**\n * Initialize the scanner character-based state machine for the given start\n * state\n * @param {[string, boolean][]} customSchemes List of custom schemes, where each\n * item is a length-2 tuple with the first element set to the string scheme, and\n * the second element set to `true` if the `://` after the scheme is optional\n */\nfunction init$2(customSchemes = []) {\n  // Frequently used states (name argument removed during minification)\n  /** @type Collections<string> */\n  const groups = {}; // of tokens\n  State.groups = groups;\n  /** @type State<string> */\n  const Start = new State();\n  if (tlds == null) {\n    tlds = decodeTlds(encodedTlds);\n  }\n  if (utlds == null) {\n    utlds = decodeTlds(encodedUtlds);\n  }\n\n  // States for special URL symbols that accept immediately after start\n  tt(Start, \"'\", APOSTROPHE);\n  tt(Start, '{', OPENBRACE);\n  tt(Start, '}', CLOSEBRACE);\n  tt(Start, '[', OPENBRACKET);\n  tt(Start, ']', CLOSEBRACKET);\n  tt(Start, '(', OPENPAREN);\n  tt(Start, ')', CLOSEPAREN);\n  tt(Start, '<', OPENANGLEBRACKET);\n  tt(Start, '>', CLOSEANGLEBRACKET);\n  tt(Start, '（', FULLWIDTHLEFTPAREN);\n  tt(Start, '）', FULLWIDTHRIGHTPAREN);\n  tt(Start, '「', LEFTCORNERBRACKET);\n  tt(Start, '」', RIGHTCORNERBRACKET);\n  tt(Start, '『', LEFTWHITECORNERBRACKET);\n  tt(Start, '』', RIGHTWHITECORNERBRACKET);\n  tt(Start, '＜', FULLWIDTHLESSTHAN);\n  tt(Start, '＞', FULLWIDTHGREATERTHAN);\n  tt(Start, '&', AMPERSAND);\n  tt(Start, '*', ASTERISK);\n  tt(Start, '@', AT);\n  tt(Start, '`', BACKTICK);\n  tt(Start, '^', CARET);\n  tt(Start, ':', COLON);\n  tt(Start, ',', COMMA);\n  tt(Start, '$', DOLLAR);\n  tt(Start, '.', DOT);\n  tt(Start, '=', EQUALS);\n  tt(Start, '!', EXCLAMATION);\n  tt(Start, '-', HYPHEN);\n  tt(Start, '%', PERCENT);\n  tt(Start, '|', PIPE);\n  tt(Start, '+', PLUS);\n  tt(Start, '#', POUND);\n  tt(Start, '?', QUERY);\n  tt(Start, '\"', QUOTE);\n  tt(Start, '/', SLASH);\n  tt(Start, ';', SEMI);\n  tt(Start, '~', TILDE);\n  tt(Start, '_', UNDERSCORE);\n  tt(Start, '\\\\', BACKSLASH);\n  tt(Start, '・', FULLWIDTHMIDDLEDOT);\n  const Num = tr(Start, DIGIT, NUM, {\n    [numeric]: true\n  });\n  tr(Num, DIGIT, Num);\n  const Asciinumeric = tr(Num, ASCII_LETTER, ASCIINUMERICAL, {\n    [asciinumeric]: true\n  });\n  const Alphanumeric = tr(Num, LETTER, ALPHANUMERICAL, {\n    [alphanumeric]: true\n  });\n\n  // State which emits a word token\n  const Word = tr(Start, ASCII_LETTER, WORD, {\n    [ascii]: true\n  });\n  tr(Word, DIGIT, Asciinumeric);\n  tr(Word, ASCII_LETTER, Word);\n  tr(Asciinumeric, DIGIT, Asciinumeric);\n  tr(Asciinumeric, ASCII_LETTER, Asciinumeric);\n\n  // Same as previous, but specific to non-fsm.ascii alphabet words\n  const UWord = tr(Start, LETTER, UWORD, {\n    [alpha]: true\n  });\n  tr(UWord, ASCII_LETTER); // Non-accepting\n  tr(UWord, DIGIT, Alphanumeric);\n  tr(UWord, LETTER, UWord);\n  tr(Alphanumeric, DIGIT, Alphanumeric);\n  tr(Alphanumeric, ASCII_LETTER); // Non-accepting\n  tr(Alphanumeric, LETTER, Alphanumeric); // Non-accepting\n\n  // Whitespace jumps\n  // Tokens of only non-newline whitespace are arbitrarily long\n  // If any whitespace except newline, more whitespace!\n  const Nl = tt(Start, LF, NL, {\n    [whitespace]: true\n  });\n  const Cr = tt(Start, CR, WS, {\n    [whitespace]: true\n  });\n  const Ws = tr(Start, SPACE, WS, {\n    [whitespace]: true\n  });\n  tt(Start, OBJECT_REPLACEMENT, Ws);\n  tt(Cr, LF, Nl); // \\r\\n\n  tt(Cr, OBJECT_REPLACEMENT, Ws);\n  tr(Cr, SPACE, Ws);\n  tt(Ws, CR); // non-accepting state to avoid mixing whitespaces\n  tt(Ws, LF); // non-accepting state to avoid mixing whitespaces\n  tr(Ws, SPACE, Ws);\n  tt(Ws, OBJECT_REPLACEMENT, Ws);\n\n  // Emoji tokens. They are not grouped by the scanner except in cases where a\n  // zero-width joiner is present\n  const Emoji = tr(Start, EMOJI, EMOJI$1, {\n    [emoji]: true\n  });\n  tt(Emoji, '#'); // no transition, emoji regex seems to match #\n  tr(Emoji, EMOJI, Emoji);\n  tt(Emoji, EMOJI_VARIATION, Emoji);\n  // tt(Start, EMOJI_VARIATION, Emoji); // This one is sketchy\n\n  const EmojiJoiner = tt(Emoji, EMOJI_JOINER);\n  tt(EmojiJoiner, '#');\n  tr(EmojiJoiner, EMOJI, Emoji);\n  // tt(EmojiJoiner, EMOJI_VARIATION, Emoji); // also sketchy\n\n  // Generates states for top-level domains\n  // Note that this is most accurate when tlds are in alphabetical order\n  const wordjr = [[ASCII_LETTER, Word], [DIGIT, Asciinumeric]];\n  const uwordjr = [[ASCII_LETTER, null], [LETTER, UWord], [DIGIT, Alphanumeric]];\n  for (let i = 0; i < tlds.length; i++) {\n    fastts(Start, tlds[i], TLD, WORD, wordjr);\n  }\n  for (let i = 0; i < utlds.length; i++) {\n    fastts(Start, utlds[i], UTLD, UWORD, uwordjr);\n  }\n  addToGroups(TLD, {\n    tld: true,\n    ascii: true\n  }, groups);\n  addToGroups(UTLD, {\n    utld: true,\n    alpha: true\n  }, groups);\n\n  // Collect the states generated by different protocols. NOTE: If any new TLDs\n  // get added that are also protocols, set the token to be the same as the\n  // protocol to ensure parsing works as expected.\n  fastts(Start, 'file', SCHEME, WORD, wordjr);\n  fastts(Start, 'mailto', SCHEME, WORD, wordjr);\n  fastts(Start, 'http', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'https', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'ftp', SLASH_SCHEME, WORD, wordjr);\n  fastts(Start, 'ftps', SLASH_SCHEME, WORD, wordjr);\n  addToGroups(SCHEME, {\n    scheme: true,\n    ascii: true\n  }, groups);\n  addToGroups(SLASH_SCHEME, {\n    slashscheme: true,\n    ascii: true\n  }, groups);\n\n  // Register custom schemes. Assumes each scheme is asciinumeric with hyphens\n  customSchemes = customSchemes.sort((a, b) => a[0] > b[0] ? 1 : -1);\n  for (let i = 0; i < customSchemes.length; i++) {\n    const sch = customSchemes[i][0];\n    const optionalSlashSlash = customSchemes[i][1];\n    const flags = optionalSlashSlash ? {\n      [scheme]: true\n    } : {\n      [slashscheme]: true\n    };\n    if (sch.indexOf('-') >= 0) {\n      flags[domain] = true;\n    } else if (!ASCII_LETTER.test(sch)) {\n      flags[numeric] = true; // numbers only\n    } else if (DIGIT.test(sch)) {\n      flags[asciinumeric] = true;\n    } else {\n      flags[ascii] = true;\n    }\n    ts(Start, sch, sch, flags);\n  }\n\n  // Localhost token\n  ts(Start, 'localhost', LOCALHOST, {\n    ascii: true\n  });\n\n  // Set default transition for start state (some symbol)\n  Start.jd = new State(SYM);\n  return {\n    start: Start,\n    tokens: assign({\n      groups\n    }, tk)\n  };\n}\n\n/**\n\tGiven a string, returns an array of TOKEN instances representing the\n\tcomposition of that string.\n\n\t@method run\n\t@param {State<string>} start scanner starting state\n\t@param {string} str input string to scan\n\t@return {Token[]} list of tokens, each with a type and value\n*/\nfunction run$1(start, str) {\n  // State machine is not case sensitive, so input is tokenized in lowercased\n  // form (still returns regular case). Uses selective `toLowerCase` because\n  // lowercasing the entire string causes the length and character position to\n  // vary in some non-English strings with V8-based runtimes.\n  const iterable = stringToArray(str.replace(/[A-Z]/g, c => c.toLowerCase()));\n  const charCount = iterable.length; // <= len if there are emojis, etc\n  const tokens = []; // return value\n\n  // cursor through the string itself, accounting for characters that have\n  // width with length 2 such as emojis\n  let cursor = 0;\n\n  // Cursor through the array-representation of the string\n  let charCursor = 0;\n\n  // Tokenize the string\n  while (charCursor < charCount) {\n    let state = start;\n    let nextState = null;\n    let tokenLength = 0;\n    let latestAccepting = null;\n    let sinceAccepts = -1;\n    let charsSinceAccepts = -1;\n    while (charCursor < charCount && (nextState = state.go(iterable[charCursor]))) {\n      state = nextState;\n\n      // Keep track of the latest accepting state\n      if (state.accepts()) {\n        sinceAccepts = 0;\n        charsSinceAccepts = 0;\n        latestAccepting = state;\n      } else if (sinceAccepts >= 0) {\n        sinceAccepts += iterable[charCursor].length;\n        charsSinceAccepts++;\n      }\n      tokenLength += iterable[charCursor].length;\n      cursor += iterable[charCursor].length;\n      charCursor++;\n    }\n\n    // Roll back to the latest accepting state\n    cursor -= sinceAccepts;\n    charCursor -= charsSinceAccepts;\n    tokenLength -= sinceAccepts;\n\n    // No more jumps, just make a new token from the last accepting one\n    tokens.push({\n      t: latestAccepting.t,\n      // token type/name\n      v: str.slice(cursor - tokenLength, cursor),\n      // string value\n      s: cursor - tokenLength,\n      // start index\n      e: cursor // end index (excluding)\n    });\n  }\n  return tokens;\n}\n\n/**\n * Convert a String to an Array of characters, taking into account that some\n * characters like emojis take up two string indexes.\n *\n * Adapted from core-js (MIT license)\n * https://github.com/zloirock/core-js/blob/2d69cf5f99ab3ea3463c395df81e5a15b68f49d9/packages/core-js/internals/string-multibyte.js\n *\n * @function stringToArray\n * @param {string} str\n * @returns {string[]}\n */\nfunction stringToArray(str) {\n  const result = [];\n  const len = str.length;\n  let index = 0;\n  while (index < len) {\n    let first = str.charCodeAt(index);\n    let second;\n    let char = first < 0xd800 || first > 0xdbff || index + 1 === len || (second = str.charCodeAt(index + 1)) < 0xdc00 || second > 0xdfff ? str[index] // single character\n    : str.slice(index, index + 2); // two-index characters\n    result.push(char);\n    index += char.length;\n  }\n  return result;\n}\n\n/**\n * Fast version of ts function for when transition defaults are well known\n * @param {State<string>} state\n * @param {string} input\n * @param {string} t\n * @param {string} defaultt\n * @param {[RegExp, State<string>][]} jr\n * @returns {State<string>}\n */\nfunction fastts(state, input, t, defaultt, jr) {\n  let next;\n  const len = input.length;\n  for (let i = 0; i < len - 1; i++) {\n    const char = input[i];\n    if (state.j[char]) {\n      next = state.j[char];\n    } else {\n      next = new State(defaultt);\n      next.jr = jr.slice();\n      state.j[char] = next;\n    }\n    state = next;\n  }\n  next = new State(t);\n  next.jr = jr.slice();\n  state.j[input[len - 1]] = next;\n  return next;\n}\n\n/**\n * Converts a string of Top-Level Domain names encoded in update-tlds.js back\n * into a list of strings.\n * @param {str} encoded encoded TLDs string\n * @returns {str[]} original TLDs list\n */\nfunction decodeTlds(encoded) {\n  const words = [];\n  const stack = [];\n  let i = 0;\n  let digits = '0123456789';\n  while (i < encoded.length) {\n    let popDigitCount = 0;\n    while (digits.indexOf(encoded[i + popDigitCount]) >= 0) {\n      popDigitCount++; // encountered some digits, have to pop to go one level up trie\n    }\n    if (popDigitCount > 0) {\n      words.push(stack.join('')); // whatever preceded the pop digits must be a word\n      for (let popCount = parseInt(encoded.substring(i, i + popDigitCount), 10); popCount > 0; popCount--) {\n        stack.pop();\n      }\n      i += popDigitCount;\n    } else {\n      stack.push(encoded[i]); // drop down a level into the trie\n      i++;\n    }\n  }\n  return words;\n}\n\n/**\n * An object where each key is a valid DOM Event Name such as `click` or `focus`\n * and each value is an event handler function.\n *\n * https://developer.mozilla.org/en-US/docs/Web/API/Element#events\n * @typedef {?{ [event: string]: Function }} EventListeners\n */\n\n/**\n * All formatted properties required to render a link, including `tagName`,\n * `attributes`, `content` and `eventListeners`.\n * @typedef {{ tagName: any, attributes: {[attr: string]: any}, content: string,\n * eventListeners: EventListeners }} IntermediateRepresentation\n */\n\n/**\n * Specify either an object described by the template type `O` or a function.\n *\n * The function takes a string value (usually the link's href attribute), the\n * link type (`'url'`, `'hashtag`', etc.) and an internal token representation\n * of the link. It should return an object of the template type `O`\n * @template O\n * @typedef {O | ((value: string, type: string, token: MultiToken) => O)} OptObj\n */\n\n/**\n * Specify either a function described by template type `F` or an object.\n *\n * Each key in the object should be a link type (`'url'`, `'hashtag`', etc.). Each\n * value should be a function with template type `F` that is called when the\n * corresponding link type is encountered.\n * @template F\n * @typedef {F | { [type: string]: F}} OptFn\n */\n\n/**\n * Specify either a value with template type `V`, a function that returns `V` or\n * an object where each value resolves to `V`.\n *\n * The function takes a string value (usually the link's href attribute), the\n * link type (`'url'`, `'hashtag`', etc.) and an internal token representation\n * of the link. It should return an object of the template type `V`\n *\n * For the object, each key should be a link type (`'url'`, `'hashtag`', etc.).\n * Each value should either have type `V` or a function that returns V. This\n * function similarly takes a string value and a token.\n *\n * Example valid types for `Opt<string>`:\n *\n * ```js\n * 'hello'\n * (value, type, token) => 'world'\n * { url: 'hello', email: (value, token) => 'world'}\n * ```\n * @template V\n * @typedef {V | ((value: string, type: string, token: MultiToken) => V) | { [type: string]: V | ((value: string, token: MultiToken) => V) }} Opt\n */\n\n/**\n * See available options: https://linkify.js.org/docs/options.html\n * @typedef {{\n * \tdefaultProtocol?: string,\n *  events?: OptObj<EventListeners>,\n * \tformat?: Opt<string>,\n * \tformatHref?: Opt<string>,\n * \tnl2br?: boolean,\n * \ttagName?: Opt<any>,\n * \ttarget?: Opt<string>,\n * \trel?: Opt<string>,\n * \tvalidate?: Opt<boolean>,\n * \ttruncate?: Opt<number>,\n * \tclassName?: Opt<string>,\n * \tattributes?: OptObj<({ [attr: string]: any })>,\n *  ignoreTags?: string[],\n * \trender?: OptFn<((ir: IntermediateRepresentation) => any)>\n * }} Opts\n */\n\n/**\n * @type Required<Opts>\n */\nconst defaults = {\n  defaultProtocol: 'http',\n  events: null,\n  format: noop,\n  formatHref: noop,\n  nl2br: false,\n  tagName: 'a',\n  target: null,\n  rel: null,\n  validate: true,\n  truncate: Infinity,\n  className: null,\n  attributes: null,\n  ignoreTags: [],\n  render: null\n};\n\n/**\n * Utility class for linkify interfaces to apply specified\n * {@link Opts formatting and rendering options}.\n *\n * @param {Opts | Options} [opts] Option value overrides.\n * @param {(ir: IntermediateRepresentation) => any} [defaultRender] (For\n *   internal use) default render function that determines how to generate an\n *   HTML element based on a link token's derived tagName, attributes and HTML.\n *   Similar to render option\n */\nfunction Options(opts, defaultRender = null) {\n  let o = assign({}, defaults);\n  if (opts) {\n    o = assign(o, opts instanceof Options ? opts.o : opts);\n  }\n\n  // Ensure all ignored tags are uppercase\n  const ignoredTags = o.ignoreTags;\n  const uppercaseIgnoredTags = [];\n  for (let i = 0; i < ignoredTags.length; i++) {\n    uppercaseIgnoredTags.push(ignoredTags[i].toUpperCase());\n  }\n  /** @protected */\n  this.o = o;\n  if (defaultRender) {\n    this.defaultRender = defaultRender;\n  }\n  this.ignoreTags = uppercaseIgnoredTags;\n}\nOptions.prototype = {\n  o: defaults,\n  /**\n   * @type string[]\n   */\n  ignoreTags: [],\n  /**\n   * @param {IntermediateRepresentation} ir\n   * @returns {any}\n   */\n  defaultRender(ir) {\n    return ir;\n  },\n  /**\n   * Returns true or false based on whether a token should be displayed as a\n   * link based on the user options.\n   * @param {MultiToken} token\n   * @returns {boolean}\n   */\n  check(token) {\n    return this.get('validate', token.toString(), token);\n  },\n  // Private methods\n\n  /**\n   * Resolve an option's value based on the value of the option and the given\n   * params. If operator and token are specified and the target option is\n   * callable, automatically calls the function with the given argument.\n   * @template {keyof Opts} K\n   * @param {K} key Name of option to use\n   * @param {string} [operator] will be passed to the target option if it's a\n   * function. If not specified, RAW function value gets returned\n   * @param {MultiToken} [token] The token from linkify.tokenize\n   * @returns {Opts[K] | any}\n   */\n  get(key, operator, token) {\n    const isCallable = operator != null;\n    let option = this.o[key];\n    if (!option) {\n      return option;\n    }\n    if (typeof option === 'object') {\n      option = token.t in option ? option[token.t] : defaults[key];\n      if (typeof option === 'function' && isCallable) {\n        option = option(operator, token);\n      }\n    } else if (typeof option === 'function' && isCallable) {\n      option = option(operator, token.t, token);\n    }\n    return option;\n  },\n  /**\n   * @template {keyof Opts} L\n   * @param {L} key Name of options object to use\n   * @param {string} [operator]\n   * @param {MultiToken} [token]\n   * @returns {Opts[L] | any}\n   */\n  getObj(key, operator, token) {\n    let obj = this.o[key];\n    if (typeof obj === 'function' && operator != null) {\n      obj = obj(operator, token.t, token);\n    }\n    return obj;\n  },\n  /**\n   * Convert the given token to a rendered element that may be added to the\n   * calling-interface's DOM\n   * @param {MultiToken} token Token to render to an HTML element\n   * @returns {any} Render result; e.g., HTML string, DOM element, React\n   *   Component, etc.\n   */\n  render(token) {\n    const ir = token.render(this); // intermediate representation\n    const renderFn = this.get('render', null, token) || this.defaultRender;\n    return renderFn(ir, token.t, token);\n  }\n};\nfunction noop(val) {\n  return val;\n}\n\nvar options = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tOptions: Options,\n\tassign: assign,\n\tdefaults: defaults\n});\n\n/******************************************************************************\n\tMulti-Tokens\n\tTokens composed of arrays of TextTokens\n******************************************************************************/\n\n/**\n * @param {string} value\n * @param {Token[]} tokens\n */\nfunction MultiToken(value, tokens) {\n  this.t = 'token';\n  this.v = value;\n  this.tk = tokens;\n}\n\n/**\n * Abstract class used for manufacturing tokens of text tokens. That is rather\n * than the value for a token being a small string of text, it's value an array\n * of text tokens.\n *\n * Used for grouping together URLs, emails, hashtags, and other potential\n * creations.\n * @class MultiToken\n * @property {string} t\n * @property {string} v\n * @property {Token[]} tk\n * @abstract\n */\nMultiToken.prototype = {\n  isLink: false,\n  /**\n   * Return the string this token represents.\n   * @return {string}\n   */\n  toString() {\n    return this.v;\n  },\n  /**\n   * What should the value for this token be in the `href` HTML attribute?\n   * Returns the `.toString` value by default.\n   * @param {string} [scheme]\n   * @return {string}\n   */\n  toHref(scheme) {\n    return this.toString();\n  },\n  /**\n   * @param {Options} options Formatting options\n   * @returns {string}\n   */\n  toFormattedString(options) {\n    const val = this.toString();\n    const truncate = options.get('truncate', val, this);\n    const formatted = options.get('format', val, this);\n    return truncate && formatted.length > truncate ? formatted.substring(0, truncate) + '…' : formatted;\n  },\n  /**\n   *\n   * @param {Options} options\n   * @returns {string}\n   */\n  toFormattedHref(options) {\n    return options.get('formatHref', this.toHref(options.get('defaultProtocol')), this);\n  },\n  /**\n   * The start index of this token in the original input string\n   * @returns {number}\n   */\n  startIndex() {\n    return this.tk[0].s;\n  },\n  /**\n   * The end index of this token in the original input string (up to this\n   * index but not including it)\n   * @returns {number}\n   */\n  endIndex() {\n    return this.tk[this.tk.length - 1].e;\n  },\n  /**\n  \tReturns an object  of relevant values for this token, which includes keys\n  \t* type - Kind of token ('url', 'email', etc.)\n  \t* value - Original text\n  \t* href - The value that should be added to the anchor tag's href\n  \t\tattribute\n  \t\t@method toObject\n  \t@param {string} [protocol] `'http'` by default\n  */\n  toObject(protocol = defaults.defaultProtocol) {\n    return {\n      type: this.t,\n      value: this.toString(),\n      isLink: this.isLink,\n      href: this.toHref(protocol),\n      start: this.startIndex(),\n      end: this.endIndex()\n    };\n  },\n  /**\n   *\n   * @param {Options} options Formatting option\n   */\n  toFormattedObject(options) {\n    return {\n      type: this.t,\n      value: this.toFormattedString(options),\n      isLink: this.isLink,\n      href: this.toFormattedHref(options),\n      start: this.startIndex(),\n      end: this.endIndex()\n    };\n  },\n  /**\n   * Whether this token should be rendered as a link according to the given options\n   * @param {Options} options\n   * @returns {boolean}\n   */\n  validate(options) {\n    return options.get('validate', this.toString(), this);\n  },\n  /**\n   * Return an object that represents how this link should be rendered.\n   * @param {Options} options Formattinng options\n   */\n  render(options) {\n    const token = this;\n    const href = this.toHref(options.get('defaultProtocol'));\n    const formattedHref = options.get('formatHref', href, this);\n    const tagName = options.get('tagName', href, token);\n    const content = this.toFormattedString(options);\n    const attributes = {};\n    const className = options.get('className', href, token);\n    const target = options.get('target', href, token);\n    const rel = options.get('rel', href, token);\n    const attrs = options.getObj('attributes', href, token);\n    const eventListeners = options.getObj('events', href, token);\n    attributes.href = formattedHref;\n    if (className) {\n      attributes.class = className;\n    }\n    if (target) {\n      attributes.target = target;\n    }\n    if (rel) {\n      attributes.rel = rel;\n    }\n    if (attrs) {\n      assign(attributes, attrs);\n    }\n    return {\n      tagName,\n      attributes,\n      content,\n      eventListeners\n    };\n  }\n};\n\n/**\n * Create a new token that can be emitted by the parser state machine\n * @param {string} type readable type of the token\n * @param {object} props properties to assign or override, including isLink = true or false\n * @returns {new (value: string, tokens: Token[]) => MultiToken} new token class\n */\nfunction createTokenClass(type, props) {\n  class Token extends MultiToken {\n    constructor(value, tokens) {\n      super(value, tokens);\n      this.t = type;\n    }\n  }\n  for (const p in props) {\n    Token.prototype[p] = props[p];\n  }\n  Token.t = type;\n  return Token;\n}\n\n/**\n\tRepresents a list of tokens making up a valid email address\n*/\nconst Email = createTokenClass('email', {\n  isLink: true,\n  toHref() {\n    return 'mailto:' + this.toString();\n  }\n});\n\n/**\n\tRepresents some plain text\n*/\nconst Text = createTokenClass('text');\n\n/**\n\tMulti-linebreak token - represents a line break\n\t@class Nl\n*/\nconst Nl = createTokenClass('nl');\n\n/**\n\tRepresents a list of text tokens making up a valid URL\n\t@class Url\n*/\nconst Url = createTokenClass('url', {\n  isLink: true,\n  /**\n  \tLowercases relevant parts of the domain and adds the protocol if\n  \trequired. Note that this will not escape unsafe HTML characters in the\n  \tURL.\n  \t\t@param {string} [scheme] default scheme (e.g., 'https')\n  \t@return {string} the full href\n  */\n  toHref(scheme = defaults.defaultProtocol) {\n    // Check if already has a prefix scheme\n    return this.hasProtocol() ? this.v : `${scheme}://${this.v}`;\n  },\n  /**\n   * Check whether this URL token has a protocol\n   * @return {boolean}\n   */\n  hasProtocol() {\n    const tokens = this.tk;\n    return tokens.length >= 2 && tokens[0].t !== LOCALHOST && tokens[1].t === COLON;\n  }\n});\n\nvar multi = /*#__PURE__*/Object.freeze({\n\t__proto__: null,\n\tBase: MultiToken,\n\tEmail: Email,\n\tMultiToken: MultiToken,\n\tNl: Nl,\n\tText: Text,\n\tUrl: Url,\n\tcreateTokenClass: createTokenClass\n});\n\n/**\n\tNot exactly parser, more like the second-stage scanner (although we can\n\ttheoretically hotswap the code here with a real parser in the future... but\n\tfor a little URL-finding utility abstract syntax trees may be a little\n\toverkill).\n\n\tURL format: http://en.wikipedia.org/wiki/URI_scheme\n\tEmail format: http://en.wikipedia.org/wiki/EmailAddress (links to RFC in\n\treference)\n\n\t@module linkify\n\t@submodule parser\n\t@main run\n*/\n\nconst makeState = arg => new State(arg);\n\n/**\n * Generate the parser multi token-based state machine\n * @param {{ groups: Collections<string> }} tokens\n */\nfunction init$1({\n  groups\n}) {\n  // Types of characters the URL can definitely end in\n  const qsAccepting = groups.domain.concat([AMPERSAND, ASTERISK, AT, BACKSLASH, BACKTICK, CARET, DOLLAR, EQUALS, HYPHEN, NUM, PERCENT, PIPE, PLUS, POUND, SLASH, SYM, TILDE, UNDERSCORE]);\n\n  // Types of tokens that can follow a URL and be part of the query string\n  // but cannot be the very last characters\n  // Characters that cannot appear in the URL at all should be excluded\n  const qsNonAccepting = [APOSTROPHE, COLON, COMMA, DOT, EXCLAMATION, PERCENT, QUERY, QUOTE, SEMI, OPENANGLEBRACKET, CLOSEANGLEBRACKET, OPENBRACE, CLOSEBRACE, CLOSEBRACKET, OPENBRACKET, OPENPAREN, CLOSEPAREN, FULLWIDTHLEFTPAREN, FULLWIDTHRIGHTPAREN, LEFTCORNERBRACKET, RIGHTCORNERBRACKET, LEFTWHITECORNERBRACKET, RIGHTWHITECORNERBRACKET, FULLWIDTHLESSTHAN, FULLWIDTHGREATERTHAN];\n\n  // For addresses without the mailto prefix\n  // Tokens allowed in the localpart of the email\n  const localpartAccepting = [AMPERSAND, APOSTROPHE, ASTERISK, BACKSLASH, BACKTICK, CARET, DOLLAR, EQUALS, HYPHEN, OPENBRACE, CLOSEBRACE, PERCENT, PIPE, PLUS, POUND, QUERY, SLASH, SYM, TILDE, UNDERSCORE];\n\n  // The universal starting state.\n  /**\n   * @type State<Token>\n   */\n  const Start = makeState();\n  const Localpart = tt(Start, TILDE); // Local part of the email address\n  ta(Localpart, localpartAccepting, Localpart);\n  ta(Localpart, groups.domain, Localpart);\n  const Domain = makeState(),\n    Scheme = makeState(),\n    SlashScheme = makeState();\n  ta(Start, groups.domain, Domain); // parsed string ends with a potential domain name (A)\n  ta(Start, groups.scheme, Scheme); // e.g., 'mailto'\n  ta(Start, groups.slashscheme, SlashScheme); // e.g., 'http'\n\n  ta(Domain, localpartAccepting, Localpart);\n  ta(Domain, groups.domain, Domain);\n  const LocalpartAt = tt(Domain, AT); // Local part of the email address plus @\n\n  tt(Localpart, AT, LocalpartAt); // close to an email address now\n\n  // Local part of an email address can be e.g. 'http' or 'mailto'\n  tt(Scheme, AT, LocalpartAt);\n  tt(SlashScheme, AT, LocalpartAt);\n  const LocalpartDot = tt(Localpart, DOT); // Local part of the email address plus '.' (localpart cannot end in .)\n  ta(LocalpartDot, localpartAccepting, Localpart);\n  ta(LocalpartDot, groups.domain, Localpart);\n  const EmailDomain = makeState();\n  ta(LocalpartAt, groups.domain, EmailDomain); // parsed string starts with local email info + @ with a potential domain name\n  ta(EmailDomain, groups.domain, EmailDomain);\n  const EmailDomainDot = tt(EmailDomain, DOT); // domain followed by DOT\n  ta(EmailDomainDot, groups.domain, EmailDomain);\n  const Email$1 = makeState(Email); // Possible email address (could have more tlds)\n  ta(EmailDomainDot, groups.tld, Email$1);\n  ta(EmailDomainDot, groups.utld, Email$1);\n  tt(LocalpartAt, LOCALHOST, Email$1);\n\n  // Hyphen can jump back to a domain name\n  const EmailDomainHyphen = tt(EmailDomain, HYPHEN); // parsed string starts with local email info + @ with a potential domain name\n  tt(EmailDomainHyphen, HYPHEN, EmailDomainHyphen);\n  ta(EmailDomainHyphen, groups.domain, EmailDomain);\n  ta(Email$1, groups.domain, EmailDomain);\n  tt(Email$1, DOT, EmailDomainDot);\n  tt(Email$1, HYPHEN, EmailDomainHyphen);\n\n  // Final possible email states\n  const EmailColon = tt(Email$1, COLON); // URL followed by colon (potential port number here)\n  /*const EmailColonPort = */\n  ta(EmailColon, groups.numeric, Email); // URL followed by colon and port number\n\n  // Account for dots and hyphens. Hyphens are usually parts of domain names\n  // (but not TLDs)\n  const DomainHyphen = tt(Domain, HYPHEN); // domain followed by hyphen\n  const DomainDot = tt(Domain, DOT); // domain followed by DOT\n  tt(DomainHyphen, HYPHEN, DomainHyphen);\n  ta(DomainHyphen, groups.domain, Domain);\n  ta(DomainDot, localpartAccepting, Localpart);\n  ta(DomainDot, groups.domain, Domain);\n  const DomainDotTld = makeState(Url); // Simplest possible URL with no query string\n  ta(DomainDot, groups.tld, DomainDotTld);\n  ta(DomainDot, groups.utld, DomainDotTld);\n  ta(DomainDotTld, groups.domain, Domain);\n  ta(DomainDotTld, localpartAccepting, Localpart);\n  tt(DomainDotTld, DOT, DomainDot);\n  tt(DomainDotTld, HYPHEN, DomainHyphen);\n  tt(DomainDotTld, AT, LocalpartAt);\n  const DomainDotTldColon = tt(DomainDotTld, COLON); // URL followed by colon (potential port number here)\n  const DomainDotTldColonPort = makeState(Url); // TLD followed by a port number\n  ta(DomainDotTldColon, groups.numeric, DomainDotTldColonPort);\n\n  // Long URL with optional port and maybe query string\n  const Url$1 = makeState(Url);\n\n  // URL with extra symbols at the end, followed by an opening bracket\n  const UrlNonaccept = makeState(); // URL followed by some symbols (will not be part of the final URL)\n\n  // Query strings\n  ta(Url$1, qsAccepting, Url$1);\n  ta(Url$1, qsNonAccepting, UrlNonaccept);\n  ta(UrlNonaccept, qsAccepting, Url$1);\n  ta(UrlNonaccept, qsNonAccepting, UrlNonaccept);\n\n  // Become real URLs after `SLASH` or `COLON NUM SLASH`\n  // Here works with or without scheme:// prefix\n  tt(DomainDotTld, SLASH, Url$1);\n  tt(DomainDotTldColonPort, SLASH, Url$1);\n\n  // Note that domains that begin with schemes are treated slighly differently\n  const SchemeColon = tt(Scheme, COLON); // e.g., 'mailto:'\n  const SlashSchemeColon = tt(SlashScheme, COLON); // e.g., 'http:'\n  const SlashSchemeColonSlash = tt(SlashSchemeColon, SLASH); // e.g., 'http:/'\n\n  const UriPrefix = tt(SlashSchemeColonSlash, SLASH); // e.g., 'http://'\n\n  // Scheme states can transition to domain states\n  ta(Scheme, groups.domain, Domain);\n  tt(Scheme, DOT, DomainDot);\n  tt(Scheme, HYPHEN, DomainHyphen);\n  ta(SlashScheme, groups.domain, Domain);\n  tt(SlashScheme, DOT, DomainDot);\n  tt(SlashScheme, HYPHEN, DomainHyphen);\n\n  // Force URL with scheme prefix followed by anything sane\n  ta(SchemeColon, groups.domain, Url$1);\n  tt(SchemeColon, SLASH, Url$1);\n  tt(SchemeColon, QUERY, Url$1);\n  ta(UriPrefix, groups.domain, Url$1);\n  ta(UriPrefix, qsAccepting, Url$1);\n  tt(UriPrefix, SLASH, Url$1);\n  const bracketPairs = [[OPENBRACE, CLOSEBRACE],\n  // {}\n  [OPENBRACKET, CLOSEBRACKET],\n  // []\n  [OPENPAREN, CLOSEPAREN],\n  // ()\n  [OPENANGLEBRACKET, CLOSEANGLEBRACKET],\n  // <>\n  [FULLWIDTHLEFTPAREN, FULLWIDTHRIGHTPAREN],\n  // （）\n  [LEFTCORNERBRACKET, RIGHTCORNERBRACKET],\n  // 「」\n  [LEFTWHITECORNERBRACKET, RIGHTWHITECORNERBRACKET],\n  // 『』\n  [FULLWIDTHLESSTHAN, FULLWIDTHGREATERTHAN] // ＜＞\n  ];\n  for (let i = 0; i < bracketPairs.length; i++) {\n    const [OPEN, CLOSE] = bracketPairs[i];\n    const UrlOpen = tt(Url$1, OPEN); // URL followed by open bracket\n\n    // Continue not accepting for open brackets\n    tt(UrlNonaccept, OPEN, UrlOpen);\n\n    // Closing bracket component. This character WILL be included in the URL\n    tt(UrlOpen, CLOSE, Url$1);\n\n    // URL that beings with an opening bracket, followed by a symbols.\n    // Note that the final state can still be `UrlOpen` (if the URL has a\n    // single opening bracket for some reason).\n    const UrlOpenQ = makeState(Url);\n    ta(UrlOpen, qsAccepting, UrlOpenQ);\n    const UrlOpenSyms = makeState(); // UrlOpen followed by some symbols it cannot end it\n    ta(UrlOpen, qsNonAccepting);\n\n    // URL that begins with an opening bracket, followed by some symbols\n    ta(UrlOpenQ, qsAccepting, UrlOpenQ);\n    ta(UrlOpenQ, qsNonAccepting, UrlOpenSyms);\n    ta(UrlOpenSyms, qsAccepting, UrlOpenQ);\n    ta(UrlOpenSyms, qsNonAccepting, UrlOpenSyms);\n\n    // Close brace/bracket to become regular URL\n    tt(UrlOpenQ, CLOSE, Url$1);\n    tt(UrlOpenSyms, CLOSE, Url$1);\n  }\n  tt(Start, LOCALHOST, DomainDotTld); // localhost is a valid URL state\n  tt(Start, NL, Nl); // single new line\n\n  return {\n    start: Start,\n    tokens: tk\n  };\n}\n\n/**\n * Run the parser state machine on a list of scanned string-based tokens to\n * create a list of multi tokens, each of which represents a URL, email address,\n * plain text, etc.\n *\n * @param {State<MultiToken>} start parser start state\n * @param {string} input the original input used to generate the given tokens\n * @param {Token[]} tokens list of scanned tokens\n * @returns {MultiToken[]}\n */\nfunction run(start, input, tokens) {\n  let len = tokens.length;\n  let cursor = 0;\n  let multis = [];\n  let textTokens = [];\n  while (cursor < len) {\n    let state = start;\n    let secondState = null;\n    let nextState = null;\n    let multiLength = 0;\n    let latestAccepting = null;\n    let sinceAccepts = -1;\n    while (cursor < len && !(secondState = state.go(tokens[cursor].t))) {\n      // Starting tokens with nowhere to jump to.\n      // Consider these to be just plain text\n      textTokens.push(tokens[cursor++]);\n    }\n    while (cursor < len && (nextState = secondState || state.go(tokens[cursor].t))) {\n      // Get the next state\n      secondState = null;\n      state = nextState;\n\n      // Keep track of the latest accepting state\n      if (state.accepts()) {\n        sinceAccepts = 0;\n        latestAccepting = state;\n      } else if (sinceAccepts >= 0) {\n        sinceAccepts++;\n      }\n      cursor++;\n      multiLength++;\n    }\n    if (sinceAccepts < 0) {\n      // No accepting state was found, part of a regular text token add\n      // the first text token to the text tokens array and try again from\n      // the next\n      cursor -= multiLength;\n      if (cursor < len) {\n        textTokens.push(tokens[cursor]);\n        cursor++;\n      }\n    } else {\n      // Accepting state!\n      // First close off the textTokens (if available)\n      if (textTokens.length > 0) {\n        multis.push(initMultiToken(Text, input, textTokens));\n        textTokens = [];\n      }\n\n      // Roll back to the latest accepting state\n      cursor -= sinceAccepts;\n      multiLength -= sinceAccepts;\n\n      // Create a new multitoken\n      const Multi = latestAccepting.t;\n      const subtokens = tokens.slice(cursor - multiLength, cursor);\n      multis.push(initMultiToken(Multi, input, subtokens));\n    }\n  }\n\n  // Finally close off the textTokens (if available)\n  if (textTokens.length > 0) {\n    multis.push(initMultiToken(Text, input, textTokens));\n  }\n  return multis;\n}\n\n/**\n * Utility function for instantiating a new multitoken with all the relevant\n * fields during parsing.\n * @param {new (value: string, tokens: Token[]) => MultiToken} Multi class to instantiate\n * @param {string} input original input string\n * @param {Token[]} tokens consecutive tokens scanned from input string\n * @returns {MultiToken}\n */\nfunction initMultiToken(Multi, input, tokens) {\n  const startIdx = tokens[0].s;\n  const endIdx = tokens[tokens.length - 1].e;\n  const value = input.slice(startIdx, endIdx);\n  return new Multi(value, tokens);\n}\n\nconst warn = typeof console !== 'undefined' && console && console.warn || (() => {});\nconst warnAdvice = 'until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.';\n\n// Side-effect initialization state\nconst INIT = {\n  scanner: null,\n  parser: null,\n  tokenQueue: [],\n  pluginQueue: [],\n  customSchemes: [],\n  initialized: false\n};\n\n/**\n * @typedef {{\n * \tstart: State<string>,\n * \ttokens: { groups: Collections<string> } & typeof tk\n * }} ScannerInit\n */\n\n/**\n * @typedef {{\n * \tstart: State<MultiToken>,\n * \ttokens: typeof multi\n * }} ParserInit\n */\n\n/**\n * @typedef {(arg: { scanner: ScannerInit }) => void} TokenPlugin\n */\n\n/**\n * @typedef {(arg: { scanner: ScannerInit, parser: ParserInit }) => void} Plugin\n */\n\n/**\n * De-register all plugins and reset the internal state-machine. Used for\n * testing; not required in practice.\n * @private\n */\nfunction reset() {\n  State.groups = {};\n  INIT.scanner = null;\n  INIT.parser = null;\n  INIT.tokenQueue = [];\n  INIT.pluginQueue = [];\n  INIT.customSchemes = [];\n  INIT.initialized = false;\n  return INIT;\n}\n\n/**\n * Register a token plugin to allow the scanner to recognize additional token\n * types before the parser state machine is constructed from the results.\n * @param {string} name of plugin to register\n * @param {TokenPlugin} plugin function that accepts the scanner state machine\n * and available scanner tokens and collections and extends the state machine to\n * recognize additional tokens or groups.\n */\nfunction registerTokenPlugin(name, plugin) {\n  if (typeof plugin !== 'function') {\n    throw new Error(`linkifyjs: Invalid token plugin ${plugin} (expects function)`);\n  }\n  for (let i = 0; i < INIT.tokenQueue.length; i++) {\n    if (name === INIT.tokenQueue[i][0]) {\n      warn(`linkifyjs: token plugin \"${name}\" already registered - will be overwritten`);\n      INIT.tokenQueue[i] = [name, plugin];\n      return;\n    }\n  }\n  INIT.tokenQueue.push([name, plugin]);\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register token plugin \"${name}\" ${warnAdvice}`);\n  }\n}\n\n/**\n * Register a linkify plugin\n * @param {string} name of plugin to register\n * @param {Plugin} plugin function that accepts the parser state machine and\n * extends the parser to recognize additional link types\n */\nfunction registerPlugin(name, plugin) {\n  if (typeof plugin !== 'function') {\n    throw new Error(`linkifyjs: Invalid plugin ${plugin} (expects function)`);\n  }\n  for (let i = 0; i < INIT.pluginQueue.length; i++) {\n    if (name === INIT.pluginQueue[i][0]) {\n      warn(`linkifyjs: plugin \"${name}\" already registered - will be overwritten`);\n      INIT.pluginQueue[i] = [name, plugin];\n      return;\n    }\n  }\n  INIT.pluginQueue.push([name, plugin]);\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register plugin \"${name}\" ${warnAdvice}`);\n  }\n}\n\n/**\n * Detect URLs with the following additional protocol. Anything with format\n * \"protocol://...\" will be considered a link. If `optionalSlashSlash` is set to\n * `true`, anything with format \"protocol:...\" will be considered a link.\n * @param {string} scheme\n * @param {boolean} [optionalSlashSlash]\n */\nfunction registerCustomProtocol(scheme, optionalSlashSlash = false) {\n  if (INIT.initialized) {\n    warn(`linkifyjs: already initialized - will not register custom scheme \"${scheme}\" ${warnAdvice}`);\n  }\n  if (!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(scheme)) {\n    throw new Error(`linkifyjs: incorrect scheme format.\n1. Must only contain digits, lowercase ASCII letters or \"-\"\n2. Cannot start or end with \"-\"\n3. \"-\" cannot repeat`);\n  }\n  INIT.customSchemes.push([scheme, optionalSlashSlash]);\n}\n\n/**\n * Initialize the linkify state machine. Called automatically the first time\n * linkify is called on a string, but may be called manually as well.\n */\nfunction init() {\n  // Initialize scanner state machine and plugins\n  INIT.scanner = init$2(INIT.customSchemes);\n  for (let i = 0; i < INIT.tokenQueue.length; i++) {\n    INIT.tokenQueue[i][1]({\n      scanner: INIT.scanner\n    });\n  }\n\n  // Initialize parser state machine and plugins\n  INIT.parser = init$1(INIT.scanner.tokens);\n  for (let i = 0; i < INIT.pluginQueue.length; i++) {\n    INIT.pluginQueue[i][1]({\n      scanner: INIT.scanner,\n      parser: INIT.parser\n    });\n  }\n  INIT.initialized = true;\n  return INIT;\n}\n\n/**\n * Parse a string into tokens that represent linkable and non-linkable sub-components\n * @param {string} str\n * @return {MultiToken[]} tokens\n */\nfunction tokenize(str) {\n  if (!INIT.initialized) {\n    init();\n  }\n  return run(INIT.parser.start, str, run$1(INIT.scanner.start, str));\n}\ntokenize.scan = run$1; // for testing\n\n/**\n * Find a list of linkable items in the given string.\n * @param {string} str string to find links in\n * @param {string | Opts} [type] either formatting options or specific type of\n * links to find, e.g., 'url' or 'email'\n * @param {Opts} [opts] formatting options for final output. Cannot be specified\n * if opts already provided in `type` argument\n */\nfunction find(str, type = null, opts = null) {\n  if (type && typeof type === 'object') {\n    if (opts) {\n      throw Error(`linkifyjs: Invalid link type ${type}; must be a string`);\n    }\n    opts = type;\n    type = null;\n  }\n  const options = new Options(opts);\n  const tokens = tokenize(str);\n  const filtered = [];\n  for (let i = 0; i < tokens.length; i++) {\n    const token = tokens[i];\n    if (token.isLink && (!type || token.t === type) && options.check(token)) {\n      filtered.push(token.toFormattedObject(options));\n    }\n  }\n  return filtered;\n}\n\n/**\n * Is the given string valid linkable text of some sort. Note that this does not\n * trim the text for you.\n *\n * Optionally pass in a second `type` param, which is the type of link to test\n * for.\n *\n * For example,\n *\n *     linkify.test(str, 'email');\n *\n * Returns `true` if str is a valid email.\n * @param {string} str string to test for links\n * @param {string} [type] optional specific link type to look for\n * @returns boolean true/false\n */\nfunction test(str, type = null) {\n  const tokens = tokenize(str);\n  return tokens.length === 1 && tokens[0].isLink && (!type || tokens[0].t === type);\n}\n\nexport { MultiToken, Options, State, createTokenClass, find, init, multi, options, regexp, registerCustomProtocol, registerPlugin, registerTokenPlugin, reset, stringToArray, test, multi as text, tokenize };\n"], "mappings": ";;;;;;;;;;;;;;;AAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAGA,IAAM,cAAc;AAEpB,IAAM,eAAe;AASrB,IAAM,SAAS,CAAC,QAAQ,eAAe;AACrC,aAAW,OAAO,YAAY;AAC5B,WAAO,GAAG,IAAI,WAAW,GAAG;AAAA,EAC9B;AACA,SAAO;AACT;AAgBA,IAAM,UAAU;AAChB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,eAAe;AACrB,IAAM,eAAe;AACrB,IAAM,SAAS;AACf,IAAM,QAAQ;AACd,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,aAAa;AAQnB,SAAS,cAAc,MAAM,QAAQ;AACnC,MAAI,EAAE,QAAQ,SAAS;AACrB,WAAO,IAAI,IAAI,CAAC;AAAA,EAClB;AACA,SAAO,OAAO,IAAI;AACpB;AAQA,SAAS,YAAY,GAAG,OAAO,QAAQ;AACrC,MAAI,MAAM,OAAO,GAAG;AAClB,UAAM,YAAY,IAAI;AACtB,UAAM,YAAY,IAAI;AAAA,EACxB;AACA,MAAI,MAAM,KAAK,GAAG;AAChB,UAAM,YAAY,IAAI;AACtB,UAAM,KAAK,IAAI;AAAA,EACjB;AACA,MAAI,MAAM,YAAY,GAAG;AACvB,UAAM,YAAY,IAAI;AAAA,EACxB;AACA,MAAI,MAAM,KAAK,GAAG;AAChB,UAAM,YAAY,IAAI;AAAA,EACxB;AACA,MAAI,MAAM,YAAY,GAAG;AACvB,UAAM,MAAM,IAAI;AAAA,EAClB;AACA,MAAI,MAAM,KAAK,GAAG;AAChB,UAAM,MAAM,IAAI;AAAA,EAClB;AACA,aAAW,KAAK,OAAO;AACrB,UAAM,QAAQ,cAAc,GAAG,MAAM;AACrC,QAAI,MAAM,QAAQ,CAAC,IAAI,GAAG;AACxB,YAAM,KAAK,CAAC;AAAA,IACd;AAAA,EACF;AACF;AAQA,SAAS,cAAc,GAAG,QAAQ;AAChC,QAAM,SAAS,CAAC;AAChB,aAAW,KAAK,QAAQ;AACtB,QAAI,OAAO,CAAC,EAAE,QAAQ,CAAC,KAAK,GAAG;AAC7B,aAAO,CAAC,IAAI;AAAA,IACd;AAAA,EACF;AACA,SAAO;AACT;AAoBA,SAAS,MAAM,QAAQ,MAAM;AAG3B,OAAK,IAAI,CAAC;AAGV,OAAK,KAAK,CAAC;AAEX,OAAK,KAAK;AAEV,OAAK,IAAI;AACX;AAMA,MAAM,SAAS,CAAC;AAChB,MAAM,YAAY;AAAA,EAChB,UAAU;AACR,WAAO,CAAC,CAAC,KAAK;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,GAAG,OAAO;AACR,UAAM,QAAQ;AACd,UAAM,YAAY,MAAM,EAAE,KAAK;AAC/B,QAAI,WAAW;AACb,aAAO;AAAA,IACT;AACA,aAAS,IAAI,GAAG,IAAI,MAAM,GAAG,QAAQ,KAAK;AACxC,YAAM,QAAQ,MAAM,GAAG,CAAC,EAAE,CAAC;AAC3B,YAAMC,aAAY,MAAM,GAAG,CAAC,EAAE,CAAC;AAC/B,UAAIA,cAAa,MAAM,KAAK,KAAK,GAAG;AAClC,eAAOA;AAAA,MACT;AAAA,IACF;AAEA,WAAO,MAAM;AAAA,EACf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,IAAI,OAAO,YAAY,OAAO;AAC5B,WAAO,YAAY,SAAS,KAAK,IAAI,CAAC,CAAC,KAAK,GAAG,KAAK;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,GAAG,QAAQ,MAAM,OAAO,QAAQ;AAC9B,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,WAAK,GAAG,OAAO,CAAC,GAAG,MAAM,OAAO,MAAM;AAAA,IACxC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,GAAGC,SAAQ,MAAM,OAAO,QAAQ;AAC9B,aAAS,UAAU,MAAM;AACzB,QAAI;AACJ,QAAI,QAAQ,KAAK,GAAG;AAClB,kBAAY;AAAA,IACd,OAAO;AAEL,kBAAY,IAAI,MAAM,IAAI;AAC1B,UAAI,SAAS,QAAQ;AACnB,oBAAY,MAAM,OAAO,MAAM;AAAA,MACjC;AAAA,IACF;AACA,SAAK,GAAG,KAAK,CAACA,SAAQ,SAAS,CAAC;AAChC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,GAAG,OAAO,MAAM,OAAO,QAAQ;AAC7B,QAAI,QAAQ;AACZ,UAAM,MAAM,MAAM;AAClB,QAAI,CAAC,KAAK;AACR,aAAO;AAAA,IACT;AACA,aAAS,IAAI,GAAG,IAAI,MAAM,GAAG,KAAK;AAChC,cAAQ,MAAM,GAAG,MAAM,CAAC,CAAC;AAAA,IAC3B;AACA,WAAO,MAAM,GAAG,MAAM,MAAM,CAAC,GAAG,MAAM,OAAO,MAAM;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BA,GAAG,OAAO,MAAM,OAAO,QAAQ;AAC7B,aAAS,UAAU,MAAM;AACzB,UAAM,QAAQ;AAGd,QAAI,QAAQ,KAAK,GAAG;AAClB,YAAM,EAAE,KAAK,IAAI;AACjB,aAAO;AAAA,IACT;AACA,UAAM,IAAI;AAIV,QAAI,WACF,gBAAgB,MAAM,GAAG,KAAK;AAChC,QAAI,eAAe;AACjB,kBAAY,IAAI,MAAM;AACtB,aAAO,UAAU,GAAG,cAAc,CAAC;AACnC,gBAAU,GAAG,KAAK,MAAM,UAAU,IAAI,cAAc,EAAE;AACtD,gBAAU,KAAK,cAAc;AAC7B,gBAAU,IAAI,cAAc;AAAA,IAC9B,OAAO;AACL,kBAAY,IAAI,MAAM;AAAA,IACxB;AACA,QAAI,GAAG;AAEL,UAAI,QAAQ;AACV,YAAI,UAAU,KAAK,OAAO,UAAU,MAAM,UAAU;AAClD,gBAAM,WAAW,OAAO,cAAc,UAAU,GAAG,MAAM,GAAG,KAAK;AACjE,sBAAY,GAAG,UAAU,MAAM;AAAA,QACjC,WAAW,OAAO;AAChB,sBAAY,GAAG,OAAO,MAAM;AAAA,QAC9B;AAAA,MACF;AACA,gBAAU,IAAI;AAAA,IAChB;AACA,UAAM,EAAE,KAAK,IAAI;AACjB,WAAO;AAAA,EACT;AACF;AAWA,IAAM,KAAK,CAAC,OAAO,OAAO,MAAM,OAAO,WAAW,MAAM,GAAG,OAAO,MAAM,OAAO,MAAM;AAUrF,IAAM,KAAK,CAAC,OAAOA,SAAQ,MAAM,OAAO,WAAW,MAAM,GAAGA,SAAQ,MAAM,OAAO,MAAM;AAUvF,IAAM,KAAK,CAAC,OAAO,OAAO,MAAM,OAAO,WAAW,MAAM,GAAG,OAAO,MAAM,OAAO,MAAM;AAUrF,IAAM,KAAK,CAAC,OAAO,OAAO,MAAM,OAAO,WAAW,MAAM,GAAG,OAAO,MAAM,OAAO,MAAM;AAQrF,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,iBAAiB;AACvB,IAAM,iBAAiB;AAGvB,IAAM,YAAY;AAGlB,IAAM,MAAM;AAGZ,IAAM,OAAO;AAKb,IAAM,SAAS;AAKf,IAAM,eAAe;AAGrB,IAAM,MAAM;AAGZ,IAAM,KAAK;AAGX,IAAM,KAAK;AAKX,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,cAAc;AACpB,IAAM,eAAe;AACrB,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,mBAAmB;AACzB,IAAM,oBAAoB;AAC1B,IAAM,qBAAqB;AAC3B,IAAM,sBAAsB;AAC5B,IAAM,oBAAoB;AAC1B,IAAM,qBAAqB;AAC3B,IAAM,yBAAyB;AAC/B,IAAM,0BAA0B;AAChC,IAAM,oBAAoB;AAC1B,IAAM,uBAAuB;AAG7B,IAAM,YAAY;AAClB,IAAM,aAAa;AACnB,IAAM,WAAW;AACjB,IAAM,KAAK;AACX,IAAM,YAAY;AAClB,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,SAAS;AACf,IAAM,MAAM;AACZ,IAAM,SAAS;AACf,IAAM,cAAc;AACpB,IAAM,SAAS;AACf,IAAM,UAAU;AAChB,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,qBAAqB;AAE3B,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,QAAQ;AACd,IAAM,aAAa;AAGnB,IAAM,UAAU;AAGhB,IAAM,MAAM;AAEZ,IAAI,KAAkB,OAAO,OAAO;AAAA,EACnC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,OAAO;AAAA,EACP;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAGD,IAAM,eAAe;AACrB,IAAM,SAAS,WAAC,UAAM,GAAC;AACvB,IAAM,QAAQ,WAAC,cAAU,GAAC;AAC1B,IAAM,oBAAoB;AAC1B,IAAM,QAAQ;AACd,IAAM,QAAQ;AAEd,IAAI,SAAsB,OAAO,OAAO;AAAA,EACvC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA,iBAAiB;AAAA,EACjB;AAAA,EACA;AACD,CAAC;AAOD,IAAM,KAAK;AACX,IAAM,KAAK;AACX,IAAM,kBAAkB;AACxB,IAAM,eAAe;AACrB,IAAM,qBAAqB;AAE3B,IAAI,OAAO;AAAX,IACE,QAAQ;AAuBV,SAAS,OAAO,gBAAgB,CAAC,GAAG;AAGlC,QAAM,SAAS,CAAC;AAChB,QAAM,SAAS;AAEf,QAAM,QAAQ,IAAI,MAAM;AACxB,MAAI,QAAQ,MAAM;AAChB,WAAO,WAAW,WAAW;AAAA,EAC/B;AACA,MAAI,SAAS,MAAM;AACjB,YAAQ,WAAW,YAAY;AAAA,EACjC;AAGA,KAAG,OAAO,KAAK,UAAU;AACzB,KAAG,OAAO,KAAK,SAAS;AACxB,KAAG,OAAO,KAAK,UAAU;AACzB,KAAG,OAAO,KAAK,WAAW;AAC1B,KAAG,OAAO,KAAK,YAAY;AAC3B,KAAG,OAAO,KAAK,SAAS;AACxB,KAAG,OAAO,KAAK,UAAU;AACzB,KAAG,OAAO,KAAK,gBAAgB;AAC/B,KAAG,OAAO,KAAK,iBAAiB;AAChC,KAAG,OAAO,KAAK,kBAAkB;AACjC,KAAG,OAAO,KAAK,mBAAmB;AAClC,KAAG,OAAO,KAAK,iBAAiB;AAChC,KAAG,OAAO,KAAK,kBAAkB;AACjC,KAAG,OAAO,KAAK,sBAAsB;AACrC,KAAG,OAAO,KAAK,uBAAuB;AACtC,KAAG,OAAO,KAAK,iBAAiB;AAChC,KAAG,OAAO,KAAK,oBAAoB;AACnC,KAAG,OAAO,KAAK,SAAS;AACxB,KAAG,OAAO,KAAK,QAAQ;AACvB,KAAG,OAAO,KAAK,EAAE;AACjB,KAAG,OAAO,KAAK,QAAQ;AACvB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,MAAM;AACrB,KAAG,OAAO,KAAK,GAAG;AAClB,KAAG,OAAO,KAAK,MAAM;AACrB,KAAG,OAAO,KAAK,WAAW;AAC1B,KAAG,OAAO,KAAK,MAAM;AACrB,KAAG,OAAO,KAAK,OAAO;AACtB,KAAG,OAAO,KAAK,IAAI;AACnB,KAAG,OAAO,KAAK,IAAI;AACnB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,IAAI;AACnB,KAAG,OAAO,KAAK,KAAK;AACpB,KAAG,OAAO,KAAK,UAAU;AACzB,KAAG,OAAO,MAAM,SAAS;AACzB,KAAG,OAAO,KAAK,kBAAkB;AACjC,QAAM,MAAM,GAAG,OAAO,OAAO,KAAK;AAAA,IAChC,CAAC,OAAO,GAAG;AAAA,EACb,CAAC;AACD,KAAG,KAAK,OAAO,GAAG;AAClB,QAAM,eAAe,GAAG,KAAK,cAAc,gBAAgB;AAAA,IACzD,CAAC,YAAY,GAAG;AAAA,EAClB,CAAC;AACD,QAAM,eAAe,GAAG,KAAK,QAAQ,gBAAgB;AAAA,IACnD,CAAC,YAAY,GAAG;AAAA,EAClB,CAAC;AAGD,QAAM,OAAO,GAAG,OAAO,cAAc,MAAM;AAAA,IACzC,CAAC,KAAK,GAAG;AAAA,EACX,CAAC;AACD,KAAG,MAAM,OAAO,YAAY;AAC5B,KAAG,MAAM,cAAc,IAAI;AAC3B,KAAG,cAAc,OAAO,YAAY;AACpC,KAAG,cAAc,cAAc,YAAY;AAG3C,QAAM,QAAQ,GAAG,OAAO,QAAQ,OAAO;AAAA,IACrC,CAAC,KAAK,GAAG;AAAA,EACX,CAAC;AACD,KAAG,OAAO,YAAY;AACtB,KAAG,OAAO,OAAO,YAAY;AAC7B,KAAG,OAAO,QAAQ,KAAK;AACvB,KAAG,cAAc,OAAO,YAAY;AACpC,KAAG,cAAc,YAAY;AAC7B,KAAG,cAAc,QAAQ,YAAY;AAKrC,QAAMC,MAAK,GAAG,OAAO,IAAI,IAAI;AAAA,IAC3B,CAAC,UAAU,GAAG;AAAA,EAChB,CAAC;AACD,QAAM,KAAK,GAAG,OAAO,IAAI,IAAI;AAAA,IAC3B,CAAC,UAAU,GAAG;AAAA,EAChB,CAAC;AACD,QAAM,KAAK,GAAG,OAAO,OAAO,IAAI;AAAA,IAC9B,CAAC,UAAU,GAAG;AAAA,EAChB,CAAC;AACD,KAAG,OAAO,oBAAoB,EAAE;AAChC,KAAG,IAAI,IAAIA,GAAE;AACb,KAAG,IAAI,oBAAoB,EAAE;AAC7B,KAAG,IAAI,OAAO,EAAE;AAChB,KAAG,IAAI,EAAE;AACT,KAAG,IAAI,EAAE;AACT,KAAG,IAAI,OAAO,EAAE;AAChB,KAAG,IAAI,oBAAoB,EAAE;AAI7B,QAAM,QAAQ,GAAG,OAAO,OAAO,SAAS;AAAA,IACtC,CAAC,KAAK,GAAG;AAAA,EACX,CAAC;AACD,KAAG,OAAO,GAAG;AACb,KAAG,OAAO,OAAO,KAAK;AACtB,KAAG,OAAO,iBAAiB,KAAK;AAGhC,QAAM,cAAc,GAAG,OAAO,YAAY;AAC1C,KAAG,aAAa,GAAG;AACnB,KAAG,aAAa,OAAO,KAAK;AAK5B,QAAM,SAAS,CAAC,CAAC,cAAc,IAAI,GAAG,CAAC,OAAO,YAAY,CAAC;AAC3D,QAAM,UAAU,CAAC,CAAC,cAAc,IAAI,GAAG,CAAC,QAAQ,KAAK,GAAG,CAAC,OAAO,YAAY,CAAC;AAC7E,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACpC,WAAO,OAAO,KAAK,CAAC,GAAG,KAAK,MAAM,MAAM;AAAA,EAC1C;AACA,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,WAAO,OAAO,MAAM,CAAC,GAAG,MAAM,OAAO,OAAO;AAAA,EAC9C;AACA,cAAY,KAAK;AAAA,IACf,KAAK;AAAA,IACL,OAAO;AAAA,EACT,GAAG,MAAM;AACT,cAAY,MAAM;AAAA,IAChB,MAAM;AAAA,IACN,OAAO;AAAA,EACT,GAAG,MAAM;AAKT,SAAO,OAAO,QAAQ,QAAQ,MAAM,MAAM;AAC1C,SAAO,OAAO,UAAU,QAAQ,MAAM,MAAM;AAC5C,SAAO,OAAO,QAAQ,cAAc,MAAM,MAAM;AAChD,SAAO,OAAO,SAAS,cAAc,MAAM,MAAM;AACjD,SAAO,OAAO,OAAO,cAAc,MAAM,MAAM;AAC/C,SAAO,OAAO,QAAQ,cAAc,MAAM,MAAM;AAChD,cAAY,QAAQ;AAAA,IAClB,QAAQ;AAAA,IACR,OAAO;AAAA,EACT,GAAG,MAAM;AACT,cAAY,cAAc;AAAA,IACxB,aAAa;AAAA,IACb,OAAO;AAAA,EACT,GAAG,MAAM;AAGT,kBAAgB,cAAc,KAAK,CAAC,GAAG,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,IAAI,EAAE;AACjE,WAAS,IAAI,GAAG,IAAI,cAAc,QAAQ,KAAK;AAC7C,UAAM,MAAM,cAAc,CAAC,EAAE,CAAC;AAC9B,UAAM,qBAAqB,cAAc,CAAC,EAAE,CAAC;AAC7C,UAAM,QAAQ,qBAAqB;AAAA,MACjC,CAAC,MAAM,GAAG;AAAA,IACZ,IAAI;AAAA,MACF,CAAC,WAAW,GAAG;AAAA,IACjB;AACA,QAAI,IAAI,QAAQ,GAAG,KAAK,GAAG;AACzB,YAAM,MAAM,IAAI;AAAA,IAClB,WAAW,CAAC,aAAa,KAAK,GAAG,GAAG;AAClC,YAAM,OAAO,IAAI;AAAA,IACnB,WAAW,MAAM,KAAK,GAAG,GAAG;AAC1B,YAAM,YAAY,IAAI;AAAA,IACxB,OAAO;AACL,YAAM,KAAK,IAAI;AAAA,IACjB;AACA,OAAG,OAAO,KAAK,KAAK,KAAK;AAAA,EAC3B;AAGA,KAAG,OAAO,aAAa,WAAW;AAAA,IAChC,OAAO;AAAA,EACT,CAAC;AAGD,QAAM,KAAK,IAAI,MAAM,GAAG;AACxB,SAAO;AAAA,IACL,OAAO;AAAA,IACP,QAAQ,OAAO;AAAA,MACb;AAAA,IACF,GAAG,EAAE;AAAA,EACP;AACF;AAWA,SAAS,MAAM,OAAO,KAAK;AAKzB,QAAM,WAAW,cAAc,IAAI,QAAQ,UAAU,OAAK,EAAE,YAAY,CAAC,CAAC;AAC1E,QAAM,YAAY,SAAS;AAC3B,QAAM,SAAS,CAAC;AAIhB,MAAI,SAAS;AAGb,MAAI,aAAa;AAGjB,SAAO,aAAa,WAAW;AAC7B,QAAI,QAAQ;AACZ,QAAI,YAAY;AAChB,QAAI,cAAc;AAClB,QAAI,kBAAkB;AACtB,QAAI,eAAe;AACnB,QAAI,oBAAoB;AACxB,WAAO,aAAa,cAAc,YAAY,MAAM,GAAG,SAAS,UAAU,CAAC,IAAI;AAC7E,cAAQ;AAGR,UAAI,MAAM,QAAQ,GAAG;AACnB,uBAAe;AACf,4BAAoB;AACpB,0BAAkB;AAAA,MACpB,WAAW,gBAAgB,GAAG;AAC5B,wBAAgB,SAAS,UAAU,EAAE;AACrC;AAAA,MACF;AACA,qBAAe,SAAS,UAAU,EAAE;AACpC,gBAAU,SAAS,UAAU,EAAE;AAC/B;AAAA,IACF;AAGA,cAAU;AACV,kBAAc;AACd,mBAAe;AAGf,WAAO,KAAK;AAAA,MACV,GAAG,gBAAgB;AAAA;AAAA,MAEnB,GAAG,IAAI,MAAM,SAAS,aAAa,MAAM;AAAA;AAAA,MAEzC,GAAG,SAAS;AAAA;AAAA,MAEZ,GAAG;AAAA;AAAA,IACL,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAaA,SAAS,cAAc,KAAK;AAC1B,QAAM,SAAS,CAAC;AAChB,QAAM,MAAM,IAAI;AAChB,MAAI,QAAQ;AACZ,SAAO,QAAQ,KAAK;AAClB,QAAI,QAAQ,IAAI,WAAW,KAAK;AAChC,QAAI;AACJ,QAAI,OAAO,QAAQ,SAAU,QAAQ,SAAU,QAAQ,MAAM,QAAQ,SAAS,IAAI,WAAW,QAAQ,CAAC,KAAK,SAAU,SAAS,QAAS,IAAI,KAAK,IAC9I,IAAI,MAAM,OAAO,QAAQ,CAAC;AAC5B,WAAO,KAAK,IAAI;AAChB,aAAS,KAAK;AAAA,EAChB;AACA,SAAO;AACT;AAWA,SAAS,OAAO,OAAO,OAAO,GAAG,UAAU,IAAI;AAC7C,MAAI;AACJ,QAAM,MAAM,MAAM;AAClB,WAAS,IAAI,GAAG,IAAI,MAAM,GAAG,KAAK;AAChC,UAAM,OAAO,MAAM,CAAC;AACpB,QAAI,MAAM,EAAE,IAAI,GAAG;AACjB,aAAO,MAAM,EAAE,IAAI;AAAA,IACrB,OAAO;AACL,aAAO,IAAI,MAAM,QAAQ;AACzB,WAAK,KAAK,GAAG,MAAM;AACnB,YAAM,EAAE,IAAI,IAAI;AAAA,IAClB;AACA,YAAQ;AAAA,EACV;AACA,SAAO,IAAI,MAAM,CAAC;AAClB,OAAK,KAAK,GAAG,MAAM;AACnB,QAAM,EAAE,MAAM,MAAM,CAAC,CAAC,IAAI;AAC1B,SAAO;AACT;AAQA,SAAS,WAAW,SAAS;AAC3B,QAAM,QAAQ,CAAC;AACf,QAAM,QAAQ,CAAC;AACf,MAAI,IAAI;AACR,MAAI,SAAS;AACb,SAAO,IAAI,QAAQ,QAAQ;AACzB,QAAI,gBAAgB;AACpB,WAAO,OAAO,QAAQ,QAAQ,IAAI,aAAa,CAAC,KAAK,GAAG;AACtD;AAAA,IACF;AACA,QAAI,gBAAgB,GAAG;AACrB,YAAM,KAAK,MAAM,KAAK,EAAE,CAAC;AACzB,eAAS,WAAW,SAAS,QAAQ,UAAU,GAAG,IAAI,aAAa,GAAG,EAAE,GAAG,WAAW,GAAG,YAAY;AACnG,cAAM,IAAI;AAAA,MACZ;AACA,WAAK;AAAA,IACP,OAAO;AACL,YAAM,KAAK,QAAQ,CAAC,CAAC;AACrB;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAmFA,IAAM,WAAW;AAAA,EACf,iBAAiB;AAAA,EACjB,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,YAAY;AAAA,EACZ,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,UAAU;AAAA,EACV,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AAAA,EACZ,YAAY,CAAC;AAAA,EACb,QAAQ;AACV;AAYA,SAAS,QAAQ,MAAM,gBAAgB,MAAM;AAC3C,MAAI,IAAI,OAAO,CAAC,GAAG,QAAQ;AAC3B,MAAI,MAAM;AACR,QAAI,OAAO,GAAG,gBAAgB,UAAU,KAAK,IAAI,IAAI;AAAA,EACvD;AAGA,QAAM,cAAc,EAAE;AACtB,QAAM,uBAAuB,CAAC;AAC9B,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,yBAAqB,KAAK,YAAY,CAAC,EAAE,YAAY,CAAC;AAAA,EACxD;AAEA,OAAK,IAAI;AACT,MAAI,eAAe;AACjB,SAAK,gBAAgB;AAAA,EACvB;AACA,OAAK,aAAa;AACpB;AACA,QAAQ,YAAY;AAAA,EAClB,GAAG;AAAA;AAAA;AAAA;AAAA,EAIH,YAAY,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA,EAKb,cAAc,IAAI;AAChB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO;AACX,WAAO,KAAK,IAAI,YAAY,MAAM,SAAS,GAAG,KAAK;AAAA,EACrD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,IAAI,KAAK,UAAU,OAAO;AACxB,UAAM,aAAa,YAAY;AAC/B,QAAI,SAAS,KAAK,EAAE,GAAG;AACvB,QAAI,CAAC,QAAQ;AACX,aAAO;AAAA,IACT;AACA,QAAI,OAAO,WAAW,UAAU;AAC9B,eAAS,MAAM,KAAK,SAAS,OAAO,MAAM,CAAC,IAAI,SAAS,GAAG;AAC3D,UAAI,OAAO,WAAW,cAAc,YAAY;AAC9C,iBAAS,OAAO,UAAU,KAAK;AAAA,MACjC;AAAA,IACF,WAAW,OAAO,WAAW,cAAc,YAAY;AACrD,eAAS,OAAO,UAAU,MAAM,GAAG,KAAK;AAAA,IAC1C;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,KAAK,UAAU,OAAO;AAC3B,QAAI,MAAM,KAAK,EAAE,GAAG;AACpB,QAAI,OAAO,QAAQ,cAAc,YAAY,MAAM;AACjD,YAAM,IAAI,UAAU,MAAM,GAAG,KAAK;AAAA,IACpC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,OAAO,OAAO;AACZ,UAAM,KAAK,MAAM,OAAO,IAAI;AAC5B,UAAM,WAAW,KAAK,IAAI,UAAU,MAAM,KAAK,KAAK,KAAK;AACzD,WAAO,SAAS,IAAI,MAAM,GAAG,KAAK;AAAA,EACpC;AACF;AACA,SAAS,KAAK,KAAK;AACjB,SAAO;AACT;AAEA,IAAI,UAAuB,OAAO,OAAO;AAAA,EACxC,WAAW;AAAA,EACX;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAWD,SAAS,WAAW,OAAO,QAAQ;AACjC,OAAK,IAAI;AACT,OAAK,IAAI;AACT,OAAK,KAAK;AACZ;AAeA,WAAW,YAAY;AAAA,EACrB,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,EAKR,WAAW;AACT,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAOC,SAAQ;AACb,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkBC,UAAS;AACzB,UAAM,MAAM,KAAK,SAAS;AAC1B,UAAM,WAAWA,SAAQ,IAAI,YAAY,KAAK,IAAI;AAClD,UAAM,YAAYA,SAAQ,IAAI,UAAU,KAAK,IAAI;AACjD,WAAO,YAAY,UAAU,SAAS,WAAW,UAAU,UAAU,GAAG,QAAQ,IAAI,MAAM;AAAA,EAC5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,gBAAgBA,UAAS;AACvB,WAAOA,SAAQ,IAAI,cAAc,KAAK,OAAOA,SAAQ,IAAI,iBAAiB,CAAC,GAAG,IAAI;AAAA,EACpF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,aAAa;AACX,WAAO,KAAK,GAAG,CAAC,EAAE;AAAA,EACpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAW;AACT,WAAO,KAAK,GAAG,KAAK,GAAG,SAAS,CAAC,EAAE;AAAA,EACrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,SAAS,WAAW,SAAS,iBAAiB;AAC5C,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,OAAO,KAAK,SAAS;AAAA,MACrB,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK,OAAO,QAAQ;AAAA,MAC1B,OAAO,KAAK,WAAW;AAAA,MACvB,KAAK,KAAK,SAAS;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,kBAAkBA,UAAS;AACzB,WAAO;AAAA,MACL,MAAM,KAAK;AAAA,MACX,OAAO,KAAK,kBAAkBA,QAAO;AAAA,MACrC,QAAQ,KAAK;AAAA,MACb,MAAM,KAAK,gBAAgBA,QAAO;AAAA,MAClC,OAAO,KAAK,WAAW;AAAA,MACvB,KAAK,KAAK,SAAS;AAAA,IACrB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAASA,UAAS;AAChB,WAAOA,SAAQ,IAAI,YAAY,KAAK,SAAS,GAAG,IAAI;AAAA,EACtD;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,OAAOA,UAAS;AACd,UAAM,QAAQ;AACd,UAAM,OAAO,KAAK,OAAOA,SAAQ,IAAI,iBAAiB,CAAC;AACvD,UAAM,gBAAgBA,SAAQ,IAAI,cAAc,MAAM,IAAI;AAC1D,UAAM,UAAUA,SAAQ,IAAI,WAAW,MAAM,KAAK;AAClD,UAAM,UAAU,KAAK,kBAAkBA,QAAO;AAC9C,UAAM,aAAa,CAAC;AACpB,UAAM,YAAYA,SAAQ,IAAI,aAAa,MAAM,KAAK;AACtD,UAAM,SAASA,SAAQ,IAAI,UAAU,MAAM,KAAK;AAChD,UAAM,MAAMA,SAAQ,IAAI,OAAO,MAAM,KAAK;AAC1C,UAAM,QAAQA,SAAQ,OAAO,cAAc,MAAM,KAAK;AACtD,UAAM,iBAAiBA,SAAQ,OAAO,UAAU,MAAM,KAAK;AAC3D,eAAW,OAAO;AAClB,QAAI,WAAW;AACb,iBAAW,QAAQ;AAAA,IACrB;AACA,QAAI,QAAQ;AACV,iBAAW,SAAS;AAAA,IACtB;AACA,QAAI,KAAK;AACP,iBAAW,MAAM;AAAA,IACnB;AACA,QAAI,OAAO;AACT,aAAO,YAAY,KAAK;AAAA,IAC1B;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACF;AAQA,SAAS,iBAAiB,MAAM,OAAO;AAAA,EACrC,MAAM,cAAc,WAAW;AAAA,IAC7B,YAAY,OAAO,QAAQ;AACzB,YAAM,OAAO,MAAM;AACnB,WAAK,IAAI;AAAA,IACX;AAAA,EACF;AACA,aAAW,KAAK,OAAO;AACrB,UAAM,UAAU,CAAC,IAAI,MAAM,CAAC;AAAA,EAC9B;AACA,QAAM,IAAI;AACV,SAAO;AACT;AAKA,IAAM,QAAQ,iBAAiB,SAAS;AAAA,EACtC,QAAQ;AAAA,EACR,SAAS;AACP,WAAO,YAAY,KAAK,SAAS;AAAA,EACnC;AACF,CAAC;AAKD,IAAM,OAAO,iBAAiB,MAAM;AAMpC,IAAM,KAAK,iBAAiB,IAAI;AAMhC,IAAM,MAAM,iBAAiB,OAAO;AAAA,EAClC,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQR,OAAOD,UAAS,SAAS,iBAAiB;AAExC,WAAO,KAAK,YAAY,IAAI,KAAK,IAAI,GAAGA,OAAM,MAAM,KAAK,CAAC;AAAA,EAC5D;AAAA;AAAA;AAAA;AAAA;AAAA,EAKA,cAAc;AACZ,UAAM,SAAS,KAAK;AACpB,WAAO,OAAO,UAAU,KAAK,OAAO,CAAC,EAAE,MAAM,aAAa,OAAO,CAAC,EAAE,MAAM;AAAA,EAC5E;AACF,CAAC;AAED,IAAI,QAAqB,OAAO,OAAO;AAAA,EACtC,WAAW;AAAA,EACX,MAAM;AAAA,EACN;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAiBD,IAAM,YAAY,SAAO,IAAI,MAAM,GAAG;AAMtC,SAAS,OAAO;AAAA,EACd;AACF,GAAG;AAED,QAAM,cAAc,OAAO,OAAO,OAAO,CAAC,WAAW,UAAU,IAAI,WAAW,UAAU,OAAO,QAAQ,QAAQ,QAAQ,KAAK,SAAS,MAAM,MAAM,OAAO,OAAO,KAAK,OAAO,UAAU,CAAC;AAKtL,QAAM,iBAAiB,CAAC,YAAY,OAAO,OAAO,KAAK,aAAa,SAAS,OAAO,OAAO,MAAM,kBAAkB,mBAAmB,WAAW,YAAY,cAAc,aAAa,WAAW,YAAY,oBAAoB,qBAAqB,mBAAmB,oBAAoB,wBAAwB,yBAAyB,mBAAmB,oBAAoB;AAIvX,QAAM,qBAAqB,CAAC,WAAW,YAAY,UAAU,WAAW,UAAU,OAAO,QAAQ,QAAQ,QAAQ,WAAW,YAAY,SAAS,MAAM,MAAM,OAAO,OAAO,OAAO,KAAK,OAAO,UAAU;AAMxM,QAAM,QAAQ,UAAU;AACxB,QAAM,YAAY,GAAG,OAAO,KAAK;AACjC,KAAG,WAAW,oBAAoB,SAAS;AAC3C,KAAG,WAAW,OAAO,QAAQ,SAAS;AACtC,QAAM,SAAS,UAAU,GACvB,SAAS,UAAU,GACnB,cAAc,UAAU;AAC1B,KAAG,OAAO,OAAO,QAAQ,MAAM;AAC/B,KAAG,OAAO,OAAO,QAAQ,MAAM;AAC/B,KAAG,OAAO,OAAO,aAAa,WAAW;AAEzC,KAAG,QAAQ,oBAAoB,SAAS;AACxC,KAAG,QAAQ,OAAO,QAAQ,MAAM;AAChC,QAAM,cAAc,GAAG,QAAQ,EAAE;AAEjC,KAAG,WAAW,IAAI,WAAW;AAG7B,KAAG,QAAQ,IAAI,WAAW;AAC1B,KAAG,aAAa,IAAI,WAAW;AAC/B,QAAM,eAAe,GAAG,WAAW,GAAG;AACtC,KAAG,cAAc,oBAAoB,SAAS;AAC9C,KAAG,cAAc,OAAO,QAAQ,SAAS;AACzC,QAAM,cAAc,UAAU;AAC9B,KAAG,aAAa,OAAO,QAAQ,WAAW;AAC1C,KAAG,aAAa,OAAO,QAAQ,WAAW;AAC1C,QAAM,iBAAiB,GAAG,aAAa,GAAG;AAC1C,KAAG,gBAAgB,OAAO,QAAQ,WAAW;AAC7C,QAAM,UAAU,UAAU,KAAK;AAC/B,KAAG,gBAAgB,OAAO,KAAK,OAAO;AACtC,KAAG,gBAAgB,OAAO,MAAM,OAAO;AACvC,KAAG,aAAa,WAAW,OAAO;AAGlC,QAAM,oBAAoB,GAAG,aAAa,MAAM;AAChD,KAAG,mBAAmB,QAAQ,iBAAiB;AAC/C,KAAG,mBAAmB,OAAO,QAAQ,WAAW;AAChD,KAAG,SAAS,OAAO,QAAQ,WAAW;AACtC,KAAG,SAAS,KAAK,cAAc;AAC/B,KAAG,SAAS,QAAQ,iBAAiB;AAGrC,QAAM,aAAa,GAAG,SAAS,KAAK;AAEpC,KAAG,YAAY,OAAO,SAAS,KAAK;AAIpC,QAAM,eAAe,GAAG,QAAQ,MAAM;AACtC,QAAM,YAAY,GAAG,QAAQ,GAAG;AAChC,KAAG,cAAc,QAAQ,YAAY;AACrC,KAAG,cAAc,OAAO,QAAQ,MAAM;AACtC,KAAG,WAAW,oBAAoB,SAAS;AAC3C,KAAG,WAAW,OAAO,QAAQ,MAAM;AACnC,QAAM,eAAe,UAAU,GAAG;AAClC,KAAG,WAAW,OAAO,KAAK,YAAY;AACtC,KAAG,WAAW,OAAO,MAAM,YAAY;AACvC,KAAG,cAAc,OAAO,QAAQ,MAAM;AACtC,KAAG,cAAc,oBAAoB,SAAS;AAC9C,KAAG,cAAc,KAAK,SAAS;AAC/B,KAAG,cAAc,QAAQ,YAAY;AACrC,KAAG,cAAc,IAAI,WAAW;AAChC,QAAM,oBAAoB,GAAG,cAAc,KAAK;AAChD,QAAM,wBAAwB,UAAU,GAAG;AAC3C,KAAG,mBAAmB,OAAO,SAAS,qBAAqB;AAG3D,QAAM,QAAQ,UAAU,GAAG;AAG3B,QAAM,eAAe,UAAU;AAG/B,KAAG,OAAO,aAAa,KAAK;AAC5B,KAAG,OAAO,gBAAgB,YAAY;AACtC,KAAG,cAAc,aAAa,KAAK;AACnC,KAAG,cAAc,gBAAgB,YAAY;AAI7C,KAAG,cAAc,OAAO,KAAK;AAC7B,KAAG,uBAAuB,OAAO,KAAK;AAGtC,QAAM,cAAc,GAAG,QAAQ,KAAK;AACpC,QAAM,mBAAmB,GAAG,aAAa,KAAK;AAC9C,QAAM,wBAAwB,GAAG,kBAAkB,KAAK;AAExD,QAAM,YAAY,GAAG,uBAAuB,KAAK;AAGjD,KAAG,QAAQ,OAAO,QAAQ,MAAM;AAChC,KAAG,QAAQ,KAAK,SAAS;AACzB,KAAG,QAAQ,QAAQ,YAAY;AAC/B,KAAG,aAAa,OAAO,QAAQ,MAAM;AACrC,KAAG,aAAa,KAAK,SAAS;AAC9B,KAAG,aAAa,QAAQ,YAAY;AAGpC,KAAG,aAAa,OAAO,QAAQ,KAAK;AACpC,KAAG,aAAa,OAAO,KAAK;AAC5B,KAAG,aAAa,OAAO,KAAK;AAC5B,KAAG,WAAW,OAAO,QAAQ,KAAK;AAClC,KAAG,WAAW,aAAa,KAAK;AAChC,KAAG,WAAW,OAAO,KAAK;AAC1B,QAAM,eAAe;AAAA,IAAC,CAAC,WAAW,UAAU;AAAA;AAAA,IAE5C,CAAC,aAAa,YAAY;AAAA;AAAA,IAE1B,CAAC,WAAW,UAAU;AAAA;AAAA,IAEtB,CAAC,kBAAkB,iBAAiB;AAAA;AAAA,IAEpC,CAAC,oBAAoB,mBAAmB;AAAA;AAAA,IAExC,CAAC,mBAAmB,kBAAkB;AAAA;AAAA,IAEtC,CAAC,wBAAwB,uBAAuB;AAAA;AAAA,IAEhD,CAAC,mBAAmB,oBAAoB;AAAA;AAAA,EACxC;AACA,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC5C,UAAM,CAAC,MAAM,KAAK,IAAI,aAAa,CAAC;AACpC,UAAM,UAAU,GAAG,OAAO,IAAI;AAG9B,OAAG,cAAc,MAAM,OAAO;AAG9B,OAAG,SAAS,OAAO,KAAK;AAKxB,UAAM,WAAW,UAAU,GAAG;AAC9B,OAAG,SAAS,aAAa,QAAQ;AACjC,UAAM,cAAc,UAAU;AAC9B,OAAG,SAAS,cAAc;AAG1B,OAAG,UAAU,aAAa,QAAQ;AAClC,OAAG,UAAU,gBAAgB,WAAW;AACxC,OAAG,aAAa,aAAa,QAAQ;AACrC,OAAG,aAAa,gBAAgB,WAAW;AAG3C,OAAG,UAAU,OAAO,KAAK;AACzB,OAAG,aAAa,OAAO,KAAK;AAAA,EAC9B;AACA,KAAG,OAAO,WAAW,YAAY;AACjC,KAAG,OAAO,IAAI,EAAE;AAEhB,SAAO;AAAA,IACL,OAAO;AAAA,IACP,QAAQ;AAAA,EACV;AACF;AAYA,SAAS,IAAI,OAAO,OAAO,QAAQ;AACjC,MAAI,MAAM,OAAO;AACjB,MAAI,SAAS;AACb,MAAI,SAAS,CAAC;AACd,MAAI,aAAa,CAAC;AAClB,SAAO,SAAS,KAAK;AACnB,QAAI,QAAQ;AACZ,QAAI,cAAc;AAClB,QAAI,YAAY;AAChB,QAAI,cAAc;AAClB,QAAI,kBAAkB;AACtB,QAAI,eAAe;AACnB,WAAO,SAAS,OAAO,EAAE,cAAc,MAAM,GAAG,OAAO,MAAM,EAAE,CAAC,IAAI;AAGlE,iBAAW,KAAK,OAAO,QAAQ,CAAC;AAAA,IAClC;AACA,WAAO,SAAS,QAAQ,YAAY,eAAe,MAAM,GAAG,OAAO,MAAM,EAAE,CAAC,IAAI;AAE9E,oBAAc;AACd,cAAQ;AAGR,UAAI,MAAM,QAAQ,GAAG;AACnB,uBAAe;AACf,0BAAkB;AAAA,MACpB,WAAW,gBAAgB,GAAG;AAC5B;AAAA,MACF;AACA;AACA;AAAA,IACF;AACA,QAAI,eAAe,GAAG;AAIpB,gBAAU;AACV,UAAI,SAAS,KAAK;AAChB,mBAAW,KAAK,OAAO,MAAM,CAAC;AAC9B;AAAA,MACF;AAAA,IACF,OAAO;AAGL,UAAI,WAAW,SAAS,GAAG;AACzB,eAAO,KAAK,eAAe,MAAM,OAAO,UAAU,CAAC;AACnD,qBAAa,CAAC;AAAA,MAChB;AAGA,gBAAU;AACV,qBAAe;AAGf,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,YAAY,OAAO,MAAM,SAAS,aAAa,MAAM;AAC3D,aAAO,KAAK,eAAe,OAAO,OAAO,SAAS,CAAC;AAAA,IACrD;AAAA,EACF;AAGA,MAAI,WAAW,SAAS,GAAG;AACzB,WAAO,KAAK,eAAe,MAAM,OAAO,UAAU,CAAC;AAAA,EACrD;AACA,SAAO;AACT;AAUA,SAAS,eAAe,OAAO,OAAO,QAAQ;AAC5C,QAAM,WAAW,OAAO,CAAC,EAAE;AAC3B,QAAM,SAAS,OAAO,OAAO,SAAS,CAAC,EAAE;AACzC,QAAM,QAAQ,MAAM,MAAM,UAAU,MAAM;AAC1C,SAAO,IAAI,MAAM,OAAO,MAAM;AAChC;AAEA,IAAM,OAAO,OAAO,YAAY,eAAe,WAAW,QAAQ,SAAS,MAAM;AAAC;AAClF,IAAM,aAAa;AAGnB,IAAM,OAAO;AAAA,EACX,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,YAAY,CAAC;AAAA,EACb,aAAa,CAAC;AAAA,EACd,eAAe,CAAC;AAAA,EAChB,aAAa;AACf;AA6BA,SAAS,QAAQ;AACf,QAAM,SAAS,CAAC;AAChB,OAAK,UAAU;AACf,OAAK,SAAS;AACd,OAAK,aAAa,CAAC;AACnB,OAAK,cAAc,CAAC;AACpB,OAAK,gBAAgB,CAAC;AACtB,OAAK,cAAc;AACnB,SAAO;AACT;AAyDA,SAAS,uBAAuBE,SAAQ,qBAAqB,OAAO;AAClE,MAAI,KAAK,aAAa;AACpB,SAAK,qEAAqEA,OAAM,KAAK,UAAU,EAAE;AAAA,EACnG;AACA,MAAI,CAAC,2BAA2B,KAAKA,OAAM,GAAG;AAC5C,UAAM,IAAI,MAAM;AAAA;AAAA;AAAA,qBAGC;AAAA,EACnB;AACA,OAAK,cAAc,KAAK,CAACA,SAAQ,kBAAkB,CAAC;AACtD;AAMA,SAAS,OAAO;AAEd,OAAK,UAAU,OAAO,KAAK,aAAa;AACxC,WAAS,IAAI,GAAG,IAAI,KAAK,WAAW,QAAQ,KAAK;AAC/C,SAAK,WAAW,CAAC,EAAE,CAAC,EAAE;AAAA,MACpB,SAAS,KAAK;AAAA,IAChB,CAAC;AAAA,EACH;AAGA,OAAK,SAAS,OAAO,KAAK,QAAQ,MAAM;AACxC,WAAS,IAAI,GAAG,IAAI,KAAK,YAAY,QAAQ,KAAK;AAChD,SAAK,YAAY,CAAC,EAAE,CAAC,EAAE;AAAA,MACrB,SAAS,KAAK;AAAA,MACd,QAAQ,KAAK;AAAA,IACf,CAAC;AAAA,EACH;AACA,OAAK,cAAc;AACnB,SAAO;AACT;AAOA,SAAS,SAAS,KAAK;AACrB,MAAI,CAAC,KAAK,aAAa;AACrB,SAAK;AAAA,EACP;AACA,SAAO,IAAI,KAAK,OAAO,OAAO,KAAK,MAAM,KAAK,QAAQ,OAAO,GAAG,CAAC;AACnE;AACA,SAAS,OAAO;AAUhB,SAAS,KAAK,KAAK,OAAO,MAAM,OAAO,MAAM;AAC3C,MAAI,QAAQ,OAAO,SAAS,UAAU;AACpC,QAAI,MAAM;AACR,YAAM,MAAM,gCAAgC,IAAI,oBAAoB;AAAA,IACtE;AACA,WAAO;AACP,WAAO;AAAA,EACT;AACA,QAAMC,WAAU,IAAI,QAAQ,IAAI;AAChC,QAAM,SAAS,SAAS,GAAG;AAC3B,QAAM,WAAW,CAAC;AAClB,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,QAAQ,OAAO,CAAC;AACtB,QAAI,MAAM,WAAW,CAAC,QAAQ,MAAM,MAAM,SAASA,SAAQ,MAAM,KAAK,GAAG;AACvE,eAAS,KAAK,MAAM,kBAAkBA,QAAO,CAAC;AAAA,IAChD;AAAA,EACF;AACA,SAAO;AACT;", "names": ["import_dist", "nextState", "regexp", "Nl", "scheme", "options", "scheme", "options"]}