{"version": 3, "sources": ["../../@tiptap/extension-typography/src/typography.ts"], "sourcesContent": ["import { Extension, textInputRule } from '@tiptap/core'\n\nexport interface TypographyOptions {\n  /**\n   * The em dash character.\n   * @default '—'\n   */\n  emDash: false | string,\n\n  /**\n   * The ellipsis character.\n   * @default '…'\n   */\n  ellipsis: false | string,\n\n  /**\n   * The open double quote character.\n   * @default '“'\n   */\n  openDoubleQuote: false | string,\n\n  /**\n   * The close double quote character.\n   * @default '”'\n   */\n  closeDoubleQuote: false | string,\n\n  /**\n   * The open single quote character.\n   * @default '‘'\n   */\n  openSingleQuote: false | string,\n\n  /**\n   * The close single quote character.\n   * @default '’'\n   */\n  closeSingleQuote: false | string,\n\n  /**\n   * The left arrow character.\n   * @default '←'\n   */\n  leftArrow: false | string,\n\n  /**\n   * The right arrow character.\n   * @default '→'\n   */\n  rightArrow: false | string,\n\n  /**\n   * The copyright character.\n   * @default '©'\n   */\n  copyright: false | string,\n\n  /**\n   * The trademark character.\n   * @default '™'\n   */\n  trademark: false | string,\n\n  /**\n   * The servicemark character.\n   * @default '℠'\n   */\n  servicemark: false | string,\n\n  /**\n   * The registered trademark character.\n   * @default '®'\n   */\n  registeredTrademark: false | string,\n\n  /**\n   * The one half character.\n   * @default '½'\n   */\n  oneHalf: false | string,\n\n  /**\n   * The plus minus character.\n   * @default '±'\n   */\n  plusMinus: false | string,\n\n  /**\n   * The not equal character.\n   * @default '≠'\n   */\n  notEqual: false | string,\n\n  /**\n   * The laquo character.\n   * @default '«'\n   */\n  laquo: false | string,\n\n  /**\n   * The raquo character.\n   * @default '»'\n   */\n  raquo: false | string,\n\n  /**\n   * The multiplication character.\n   * @default '×'\n   */\n  multiplication: false | string,\n\n  /**\n   * The superscript two character.\n   * @default '²'\n   */\n  superscriptTwo: false | string,\n\n  /**\n   * The superscript three character.\n   * @default '³'\n   */\n  superscriptThree: false | string,\n\n  /**\n   * The one quarter character.\n   * @default '¼'\n   */\n  oneQuarter: false | string,\n\n  /**\n   * The three quarters character.\n   * @default '¾'\n   */\n  threeQuarters: false | string,\n}\n\nexport const emDash = (override?: string) => textInputRule({\n  find: /--$/,\n  replace: override ?? '—',\n})\n\nexport const ellipsis = (override?: string) => textInputRule({\n  find: /\\.\\.\\.$/,\n  replace: override ?? '…',\n})\n\nexport const openDoubleQuote = (override?: string) => textInputRule({\n  find: /(?:^|[\\s{[(<'\"\\u2018\\u201C])(\")$/,\n  replace: override ?? '“',\n})\n\nexport const closeDoubleQuote = (override?: string) => textInputRule({\n  find: /\"$/,\n  replace: override ?? '”',\n})\n\nexport const openSingleQuote = (override?: string) => textInputRule({\n  find: /(?:^|[\\s{[(<'\"\\u2018\\u201C])(')$/,\n  replace: override ?? '‘',\n})\n\nexport const closeSingleQuote = (override?: string) => textInputRule({\n  find: /'$/,\n  replace: override ?? '’',\n})\n\nexport const leftArrow = (override?: string) => textInputRule({\n  find: /<-$/,\n  replace: override ?? '←',\n})\n\nexport const rightArrow = (override?: string) => textInputRule({\n  find: /->$/,\n  replace: override ?? '→',\n})\n\nexport const copyright = (override?: string) => textInputRule({\n  find: /\\(c\\)$/,\n  replace: override ?? '©',\n})\n\nexport const trademark = (override?: string) => textInputRule({\n  find: /\\(tm\\)$/,\n  replace: override ?? '™',\n})\n\nexport const servicemark = (override?: string) => textInputRule({\n  find: /\\(sm\\)$/,\n  replace: override ?? '℠',\n})\n\nexport const registeredTrademark = (override?: string) => textInputRule({\n  find: /\\(r\\)$/,\n  replace: override ?? '®',\n})\n\nexport const oneHalf = (override?: string) => textInputRule({\n  find: /(?:^|\\s)(1\\/2)\\s$/,\n  replace: override ?? '½',\n})\n\nexport const plusMinus = (override?: string) => textInputRule({\n  find: /\\+\\/-$/,\n  replace: override ?? '±',\n})\n\nexport const notEqual = (override?: string) => textInputRule({\n  find: /!=$/,\n  replace: override ?? '≠',\n})\n\nexport const laquo = (override?: string) => textInputRule({\n  find: /<<$/,\n  replace: override ?? '«',\n})\n\nexport const raquo = (override?: string) => textInputRule({\n  find: />>$/,\n  replace: override ?? '»',\n})\n\nexport const multiplication = (override?: string) => textInputRule({\n  find: /\\d+\\s?([*x])\\s?\\d+$/,\n  replace: override ?? '×',\n})\n\nexport const superscriptTwo = (override?: string) => textInputRule({\n  find: /\\^2$/,\n  replace: override ?? '²',\n})\n\nexport const superscriptThree = (override?: string) => textInputRule({\n  find: /\\^3$/,\n  replace: override ?? '³',\n})\n\nexport const oneQuarter = (override?: string) => textInputRule({\n  find: /(?:^|\\s)(1\\/4)\\s$/,\n  replace: override ?? '¼',\n})\n\nexport const threeQuarters = (override?: string) => textInputRule({\n  find: /(?:^|\\s)(3\\/4)\\s$/,\n  replace: override ?? '¾',\n})\n\n/**\n * This extension allows you to add typography replacements for specific characters.\n * @see https://www.tiptap.dev/api/extensions/typography\n */\nexport const Typography = Extension.create<TypographyOptions>({\n  name: 'typography',\n\n  addOptions() {\n    return {\n      closeDoubleQuote: '”',\n      closeSingleQuote: '’',\n      copyright: '©',\n      ellipsis: '…',\n      emDash: '—',\n      laquo: '«',\n      leftArrow: '←',\n      multiplication: '×',\n      notEqual: '≠',\n      oneHalf: '½',\n      oneQuarter: '¼',\n      openDoubleQuote: '“',\n      openSingleQuote: '‘',\n      plusMinus: '±',\n      raquo: '»',\n      registeredTrademark: '®',\n      rightArrow: '→',\n      servicemark: '℠',\n      superscriptThree: '³',\n      superscriptTwo: '²',\n      threeQuarters: '¾',\n      trademark: '™',\n    }\n  },\n\n  addInputRules() {\n    const rules = []\n\n    if (this.options.emDash !== false) {\n      rules.push(emDash(this.options.emDash))\n    }\n\n    if (this.options.ellipsis !== false) {\n      rules.push(ellipsis(this.options.ellipsis))\n    }\n\n    if (this.options.openDoubleQuote !== false) {\n      rules.push(openDoubleQuote(this.options.openDoubleQuote))\n    }\n\n    if (this.options.closeDoubleQuote !== false) {\n      rules.push(closeDoubleQuote(this.options.closeDoubleQuote))\n    }\n\n    if (this.options.openSingleQuote !== false) {\n      rules.push(openSingleQuote(this.options.openSingleQuote))\n    }\n\n    if (this.options.closeSingleQuote !== false) {\n      rules.push(closeSingleQuote(this.options.closeSingleQuote))\n    }\n\n    if (this.options.leftArrow !== false) {\n      rules.push(leftArrow(this.options.leftArrow))\n    }\n\n    if (this.options.rightArrow !== false) {\n      rules.push(rightArrow(this.options.rightArrow))\n    }\n\n    if (this.options.copyright !== false) {\n      rules.push(copyright(this.options.copyright))\n    }\n\n    if (this.options.trademark !== false) {\n      rules.push(trademark(this.options.trademark))\n    }\n\n    if (this.options.servicemark !== false) {\n      rules.push(servicemark(this.options.servicemark))\n    }\n\n    if (this.options.registeredTrademark !== false) {\n      rules.push(registeredTrademark(this.options.registeredTrademark))\n    }\n\n    if (this.options.oneHalf !== false) {\n      rules.push(oneHalf(this.options.oneHalf))\n    }\n\n    if (this.options.plusMinus !== false) {\n      rules.push(plusMinus(this.options.plusMinus))\n    }\n\n    if (this.options.notEqual !== false) {\n      rules.push(notEqual(this.options.notEqual))\n    }\n\n    if (this.options.laquo !== false) {\n      rules.push(laquo(this.options.laquo))\n    }\n\n    if (this.options.raquo !== false) {\n      rules.push(raquo(this.options.raquo))\n    }\n\n    if (this.options.multiplication !== false) {\n      rules.push(multiplication(this.options.multiplication))\n    }\n\n    if (this.options.superscriptTwo !== false) {\n      rules.push(superscriptTwo(this.options.superscriptTwo))\n    }\n\n    if (this.options.superscriptThree !== false) {\n      rules.push(superscriptThree(this.options.superscriptThree))\n    }\n\n    if (this.options.oneQuarter !== false) {\n      rules.push(oneQuarter(this.options.oneQuarter))\n    }\n\n    if (this.options.threeQuarters !== false) {\n      rules.push(threeQuarters(this.options.threeQuarters))\n    }\n\n    return rules\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwIa,IAAA,SAAS,CAAC,aAAsB,cAAc;EACzD,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,WAAW,CAAC,aAAsB,cAAc;EAC3D,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,kBAAkB,CAAC,aAAsB,cAAc;EAClE,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,mBAAmB,CAAC,aAAsB,cAAc;EACnE,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,kBAAkB,CAAC,aAAsB,cAAc;EAClE,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,mBAAmB,CAAC,aAAsB,cAAc;EACnE,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,YAAY,CAAC,aAAsB,cAAc;EAC5D,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,aAAa,CAAC,aAAsB,cAAc;EAC7D,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,YAAY,CAAC,aAAsB,cAAc;EAC5D,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,YAAY,CAAC,aAAsB,cAAc;EAC5D,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,cAAc,CAAC,aAAsB,cAAc;EAC9D,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,sBAAsB,CAAC,aAAsB,cAAc;EACtE,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,UAAU,CAAC,aAAsB,cAAc;EAC1D,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,YAAY,CAAC,aAAsB,cAAc;EAC5D,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,WAAW,CAAC,aAAsB,cAAc;EAC3D,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,QAAQ,CAAC,aAAsB,cAAc;EACxD,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,QAAQ,CAAC,aAAsB,cAAc;EACxD,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,iBAAiB,CAAC,aAAsB,cAAc;EACjE,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,iBAAiB,CAAC,aAAsB,cAAc;EACjE,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,mBAAmB,CAAC,aAAsB,cAAc;EACnE,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,aAAa,CAAC,aAAsB,cAAc;EAC7D,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAEY,IAAA,gBAAgB,CAAC,aAAsB,cAAc;EAChE,MAAM;EACN,SAAS,aAAQ,QAAR,aAAA,SAAA,WAAY;AACtB,CAAA;AAMY,IAAA,aAAa,UAAU,OAA0B;EAC5D,MAAM;EAEN,aAAU;AACR,WAAO;MACL,kBAAkB;MAClB,kBAAkB;MAClB,WAAW;MACX,UAAU;MACV,QAAQ;MACR,OAAO;MACP,WAAW;MACX,gBAAgB;MAChB,UAAU;MACV,SAAS;MACT,YAAY;MACZ,iBAAiB;MACjB,iBAAiB;MACjB,WAAW;MACX,OAAO;MACP,qBAAqB;MACrB,YAAY;MACZ,aAAa;MACb,kBAAkB;MAClB,gBAAgB;MAChB,eAAe;MACf,WAAW;;;EAIf,gBAAa;AACX,UAAM,QAAQ,CAAA;AAEd,QAAI,KAAK,QAAQ,WAAW,OAAO;AACjC,YAAM,KAAK,OAAO,KAAK,QAAQ,MAAM,CAAC;;AAGxC,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,YAAM,KAAK,SAAS,KAAK,QAAQ,QAAQ,CAAC;;AAG5C,QAAI,KAAK,QAAQ,oBAAoB,OAAO;AAC1C,YAAM,KAAK,gBAAgB,KAAK,QAAQ,eAAe,CAAC;;AAG1D,QAAI,KAAK,QAAQ,qBAAqB,OAAO;AAC3C,YAAM,KAAK,iBAAiB,KAAK,QAAQ,gBAAgB,CAAC;;AAG5D,QAAI,KAAK,QAAQ,oBAAoB,OAAO;AAC1C,YAAM,KAAK,gBAAgB,KAAK,QAAQ,eAAe,CAAC;;AAG1D,QAAI,KAAK,QAAQ,qBAAqB,OAAO;AAC3C,YAAM,KAAK,iBAAiB,KAAK,QAAQ,gBAAgB,CAAC;;AAG5D,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,YAAM,KAAK,UAAU,KAAK,QAAQ,SAAS,CAAC;;AAG9C,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,YAAM,KAAK,WAAW,KAAK,QAAQ,UAAU,CAAC;;AAGhD,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,YAAM,KAAK,UAAU,KAAK,QAAQ,SAAS,CAAC;;AAG9C,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,YAAM,KAAK,UAAU,KAAK,QAAQ,SAAS,CAAC;;AAG9C,QAAI,KAAK,QAAQ,gBAAgB,OAAO;AACtC,YAAM,KAAK,YAAY,KAAK,QAAQ,WAAW,CAAC;;AAGlD,QAAI,KAAK,QAAQ,wBAAwB,OAAO;AAC9C,YAAM,KAAK,oBAAoB,KAAK,QAAQ,mBAAmB,CAAC;;AAGlE,QAAI,KAAK,QAAQ,YAAY,OAAO;AAClC,YAAM,KAAK,QAAQ,KAAK,QAAQ,OAAO,CAAC;;AAG1C,QAAI,KAAK,QAAQ,cAAc,OAAO;AACpC,YAAM,KAAK,UAAU,KAAK,QAAQ,SAAS,CAAC;;AAG9C,QAAI,KAAK,QAAQ,aAAa,OAAO;AACnC,YAAM,KAAK,SAAS,KAAK,QAAQ,QAAQ,CAAC;;AAG5C,QAAI,KAAK,QAAQ,UAAU,OAAO;AAChC,YAAM,KAAK,MAAM,KAAK,QAAQ,KAAK,CAAC;;AAGtC,QAAI,KAAK,QAAQ,UAAU,OAAO;AAChC,YAAM,KAAK,MAAM,KAAK,QAAQ,KAAK,CAAC;;AAGtC,QAAI,KAAK,QAAQ,mBAAmB,OAAO;AACzC,YAAM,KAAK,eAAe,KAAK,QAAQ,cAAc,CAAC;;AAGxD,QAAI,KAAK,QAAQ,mBAAmB,OAAO;AACzC,YAAM,KAAK,eAAe,KAAK,QAAQ,cAAc,CAAC;;AAGxD,QAAI,KAAK,QAAQ,qBAAqB,OAAO;AAC3C,YAAM,KAAK,iBAAiB,KAAK,QAAQ,gBAAgB,CAAC;;AAG5D,QAAI,KAAK,QAAQ,eAAe,OAAO;AACrC,YAAM,KAAK,WAAW,KAAK,QAAQ,UAAU,CAAC;;AAGhD,QAAI,KAAK,QAAQ,kBAAkB,OAAO;AACxC,YAAM,KAAK,cAAc,KAAK,QAAQ,aAAa,CAAC;;AAGtD,WAAO;;AAEV,CAAA;", "names": []}