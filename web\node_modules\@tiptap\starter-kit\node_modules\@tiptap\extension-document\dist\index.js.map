{"version": 3, "sources": ["../src/document.ts", "../src/index.ts"], "sourcesContent": ["import { Node } from '@tiptap/core'\n\n/**\n * The default document node which represents the top level node of the editor.\n * @see https://tiptap.dev/api/nodes/document\n */\nexport const Document = Node.create({\n  name: 'doc',\n  topNode: true,\n  content: 'block+',\n})\n", "import { Document } from './document.js'\n\nexport * from './document.js'\n\nexport default Document\n"], "mappings": ";AAAA,SAAS,YAAY;AAMd,IAAM,WAAW,KAAK,OAAO;AAAA,EAClC,MAAM;AAAA,EACN,SAAS;AAAA,EACT,SAAS;AACX,CAAC;;;ACND,IAAO,gBAAQ;", "names": []}