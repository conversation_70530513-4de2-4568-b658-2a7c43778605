{"version": 3, "sources": ["../../@tiptap/extension-bullet-list/src/bullet-list.ts"], "sourcesContent": ["import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nconst ListItemName = 'listItem'\nconst TextStyleName = 'textStyle'\n\nexport interface BulletListOptions {\n  /**\n   * The node name for the list items\n   * @default 'listItem'\n   * @example 'paragraph'\n   */\n  itemTypeName: string,\n\n  /**\n   * HTML attributes to add to the bullet list element\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n\n  /**\n   * Keep the marks when splitting the list\n   * @default false\n   * @example true\n   */\n  keepMarks: boolean,\n\n  /**\n   * Keep the attributes when splitting the list\n   * @default false\n   * @example true\n   */\n  keepAttributes: boolean,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bulletList: {\n      /**\n       * Toggle a bullet list\n       */\n      toggleBulletList: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Matches a bullet list to a dash or asterisk.\n */\nexport const inputRegex = /^\\s*([-+*])\\s$/\n\n/**\n * This extension allows you to create bullet lists.\n * This requires the ListItem extension\n * @see https://tiptap.dev/api/nodes/bullet-list\n * @see https://tiptap.dev/api/nodes/list-item.\n */\nexport const BulletList = Node.create<BulletListOptions>({\n  name: 'bulletList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'listItem',\n      HTMLAttributes: {},\n      keepMarks: false,\n      keepAttributes: false,\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  parseHTML() {\n    return [\n      { tag: 'ul' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleBulletList: () => ({ commands, chain }) => {\n        if (this.options.keepAttributes) {\n          return chain().toggleList(this.name, this.options.itemTypeName, this.options.keepMarks).updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName)).run()\n        }\n        return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-8': () => this.editor.commands.toggleBulletList(),\n    }\n  },\n\n  addInputRules() {\n    let inputRule = wrappingInputRule({\n      find: inputRegex,\n      type: this.type,\n    })\n\n    if (this.options.keepMarks || this.options.keepAttributes) {\n      inputRule = wrappingInputRule({\n        find: inputRegex,\n        type: this.type,\n        keepMarks: this.options.keepMarks,\n        keepAttributes: this.options.keepAttributes,\n        getAttributes: () => { return this.editor.getAttributes(TextStyleName) },\n        editor: this.editor,\n      })\n    }\n    return [\n      inputRule,\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,eAAe;AACrB,IAAM,gBAAgB;AA8Cf,IAAM,aAAa;AAQb,IAAA,aAAa,KAAK,OAA0B;EACvD,MAAM;EAEN,aAAU;AACR,WAAO;MACL,cAAc;MACd,gBAAgB,CAAA;MAChB,WAAW;MACX,gBAAgB;;;EAIpB,OAAO;EAEP,UAAO;AACL,WAAO,GAAG,KAAK,QAAQ,YAAY;;EAGrC,YAAS;AACP,WAAO;MACL,EAAE,KAAK,KAAI;;;EAIf,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAG/E,cAAW;AACT,WAAO;MACL,kBAAkB,MAAM,CAAC,EAAE,UAAU,MAAK,MAAM;AAC9C,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,iBAAO,MAAK,EAAG,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS,EAAE,iBAAiB,cAAc,KAAK,OAAO,cAAc,aAAa,CAAC,EAAE,IAAG;;AAEtK,eAAO,SAAS,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS;;;;EAK7F,uBAAoB;AAClB,WAAO;MACL,eAAe,MAAM,KAAK,OAAO,SAAS,iBAAgB;;;EAI9D,gBAAa;AACX,QAAI,YAAY,kBAAkB;MAChC,MAAM;MACN,MAAM,KAAK;IACZ,CAAA;AAED,QAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,gBAAgB;AACzD,kBAAY,kBAAkB;QAC5B,MAAM;QACN,MAAM,KAAK;QACX,WAAW,KAAK,QAAQ;QACxB,gBAAgB,KAAK,QAAQ;QAC7B,eAAe,MAAK;AAAG,iBAAO,KAAK,OAAO,cAAc,aAAa;QAAC;QACtE,QAAQ,KAAK;MACd,CAAA;;AAEH,WAAO;MACL;;;AAGL,CAAA;", "names": []}