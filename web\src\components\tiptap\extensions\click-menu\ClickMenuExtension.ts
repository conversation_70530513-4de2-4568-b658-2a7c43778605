import { Extension } from '@tiptap/core'
import { Plugin, PluginKey } from '@tiptap/pm/state'

import type { ClickMenuOptions } from './types'

export const ClickMenuExtension = Extension.create<ClickMenuOptions>({
  name: 'clickMenu',

  addOptions() {
    return {
      showDragHandle: true,
      showAddButton: true,
      onAddClick: undefined,
      onDragStart: undefined,
      onDragEnd: undefined,
    }
  },

  addProseMirrorPlugins() {
    const pluginKey = new PluginKey(`${this.name}-plugin`)
    const extension = this

    return [
      new Plugin({
        key: pluginKey,
        view: (editorView) => {
          let menuContainer: HTMLElement | null = null
          let currentMenu: HTMLElement | null = null
          let isMouseOverMenu = false

          const createMenuContainer = () => {
            if (menuContainer) {
              console.log('ClickMenu: 菜单容器已存在，跳过创建')
              return
            }

            menuContainer = document.createElement('div')
            menuContainer.className = 'click-menu-container'
            menuContainer.style.cssText = `
              position: absolute;
              left: -50px;
              top: 0;
              width: 40px;
              height: 100%;
              pointer-events: none;
              z-index: 100;
              background: rgba(0, 255, 0, 0.1);
            `

            // 直接将容器添加到编辑器DOM元素
            const editorElement = editorView.dom
            if (editorElement && editorElement.parentElement) {
              // 确保编辑器父容器有相对定位
              editorElement.parentElement.style.position = 'relative'
              editorElement.parentElement.appendChild(menuContainer)
              console.log('ClickMenu: 菜单容器已创建并添加到编辑器父容器', {
                container: menuContainer,
                editorDom: editorElement,
                parent: editorElement.parentElement
              })
            } else {
              console.error('ClickMenu: 无法找到编辑器或其父元素')
            }
          }

          const showMenu = (blockElement: Element) => {
            if (!menuContainer || !extension.editor.isEditable) {
              console.log('ClickMenu: 无法显示菜单', {
                hasContainer: !!menuContainer,
                isEditable: extension.editor.isEditable
              })
              return
            }

            // 清除现有菜单
            hideMenu()

            const menu = document.createElement('div')
            menu.className = 'click-menu'
            menu.style.cssText = `
              position: absolute;
              left: 8px;
              display: flex;
              gap: 4px;
              pointer-events: all;
              background: rgba(255, 255, 255, 0.9);
              border: 1px solid #ccc;
              border-radius: 4px;
              padding: 2px;
            `

            // 计算位置 - 使用编辑器DOM作为基准
            const blockRect = blockElement.getBoundingClientRect()
            const editorRect = editorView.dom.getBoundingClientRect()
            const top = blockRect.top - editorRect.top

            menu.style.top = `${top}px`

            console.log('ClickMenu: 显示菜单', {
              blockElement: blockElement.tagName,
              blockRect,
              editorRect,
              top,
              menu
            })

            // 添加按钮
            if (extension.options.showAddButton) {
              const addButton = document.createElement('button')
              addButton.className = 'click-menu-add'
              addButton.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M5 12h14m-7-7v14"/></svg>`
              addButton.addEventListener('click', (e) => {
                e.preventDefault()
                e.stopPropagation()
                const pos = editorView.posAtDOM(blockElement, 0)
                extension.options.onAddClick?.(extension.editor, pos)
              })
              menu.appendChild(addButton)
            }

            // 拖拽手柄
            if (extension.options.showDragHandle) {
              const dragHandle = document.createElement('button')
              dragHandle.className = 'click-menu-drag'
              dragHandle.innerHTML = `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="currentColor"><path d="M8.5 7a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m0 6.5a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m1.5 5a1.5 1.5 0 1 1-3 0a1.5 1.5 0 0 1 3 0M15.5 7a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m1.5 5a1.5 1.5 0 1 1-3 0a1.5 1.5 0 0 1 3 0m-1.5 8a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3"/></svg>`
              dragHandle.draggable = true

              dragHandle.addEventListener('dragstart', (e) => {
                const pos = editorView.posAtDOM(blockElement, 0)
                extension.options.onDragStart?.(extension.editor, pos)

                if (e.dataTransfer) {
                  e.dataTransfer.effectAllowed = 'move'
                  e.dataTransfer.setData('text/plain', pos.toString())
                }
                blockElement.classList.add('dragging')
              })

              dragHandle.addEventListener('dragend', () => {
                const pos = editorView.posAtDOM(blockElement, 0)
                extension.options.onDragEnd?.(extension.editor, pos)
                blockElement.classList.remove('dragging')
              })

              menu.appendChild(dragHandle)
            }

            // 菜单悬停事件
            menu.addEventListener('mouseenter', () => {
              isMouseOverMenu = true
            })

            menu.addEventListener('mouseleave', () => {
              isMouseOverMenu = false
              setTimeout(() => {
                if (!isMouseOverMenu) {
                  hideMenu()
                }
              }, 100)
            })

            menuContainer.appendChild(menu)
            currentMenu = menu
          }

          const hideMenu = () => {
            if (currentMenu) {
              currentMenu.remove()
              currentMenu = null
            }
          }

          const handleMouseOver = (e: MouseEvent) => {
            if (!extension.editor.isEditable || isMouseOverMenu) {
              console.log('ClickMenu: 跳过鼠标悬停 - 编辑器不可编辑或鼠标在菜单上', {
                isEditable: extension.editor.isEditable,
                isMouseOverMenu
              })
              return
            }

            const target = e.target as Element
            const blockElement = target.closest('p, h1, h2, h3, h4, h5, h6, ul, ol, li, blockquote, pre, div[data-type]')

            console.log('ClickMenu: 鼠标悬停检测', {
              target: target.tagName,
              blockElement: blockElement?.tagName,
              isInEditor: blockElement ? editorView.dom.contains(blockElement) : false
            })

            if (blockElement && editorView.dom.contains(blockElement)) {
              showMenu(blockElement)
            }
          }

          const handleMouseOut = (e: MouseEvent) => {
            if (isMouseOverMenu) {
              return
            }

            const relatedTarget = e.relatedTarget as Element

            // 如果鼠标移动到菜单相关元素，不隐藏
            if (relatedTarget && (
              relatedTarget.closest('.click-menu') ||
              relatedTarget.closest('.click-menu-container')
            )) {
              return
            }

            setTimeout(() => {
              if (!isMouseOverMenu) {
                hideMenu()
              }
            }, 100)
          }

          const handleMouseLeave = () => {
            hideMenu()
          }

          return {
            update: () => {
              if (!menuContainer) {
                createMenuContainer()
              }
            },
            mount: () => {
              console.log('ClickMenu: 插件挂载开始')
              // 延迟创建容器，确保DOM已准备好
              setTimeout(() => {
                createMenuContainer()
                editorView.dom.addEventListener('mouseover', handleMouseOver, { passive: true })
                editorView.dom.addEventListener('mouseout', handleMouseOut, { passive: true })
                console.log('ClickMenu: 事件监听器已绑定', {
                  editorDom: editorView.dom,
                  hasMouseOver: !!handleMouseOver,
                  hasMouseOut: !!handleMouseOut,
                  containerExists: !!menuContainer
                })
              }, 100)
            },
            destroy: () => {
              console.log('ClickMenu: 插件销毁开始')
              editorView.dom.removeEventListener('mouseover', handleMouseOver)
              editorView.dom.removeEventListener('mouseout', handleMouseOut)

              if (menuContainer) {
                menuContainer.remove()
                menuContainer = null
              }
              currentMenu = null
              console.log('ClickMenu: 插件已销毁')
            },
          }
        },
      }),
    ]
  },

  addCommands() {
    return {
      showClickMenu:
        () =>
        ({ editor }) => {
          return true
        },
    }
  },
})


