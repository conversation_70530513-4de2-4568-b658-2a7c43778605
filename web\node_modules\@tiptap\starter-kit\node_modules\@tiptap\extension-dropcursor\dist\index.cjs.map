{"version": 3, "sources": ["../src/index.ts"], "sourcesContent": ["import { Dropcursor } from '@tiptap/extensions'\n\nexport type { DropcursorOptions } from '@tiptap/extensions'\nexport { Dropcursor } from '@tiptap/extensions'\n\nexport default Dropcursor\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBAA2B;AAG3B,IAAAA,qBAA2B;AAE3B,IAAO,gBAAQ;", "names": ["import_extensions"]}