{"version": 3, "sources": ["../../src/gap-cursor/gap-cursor.ts"], "sourcesContent": ["import type { ParentConfig } from '@tiptap/core'\nimport { callOrReturn, Extension, getExtensionField } from '@tiptap/core'\nimport { gapCursor } from '@tiptap/pm/gapcursor'\n\ndeclare module '@tiptap/core' {\n  interface NodeConfig<Options, Storage> {\n    /**\n     * A function to determine whether the gap cursor is allowed at the current position. Must return `true` or `false`.\n     * @default null\n     */\n    allowGapCursor?:\n      | boolean\n      | null\n      | ((this: {\n          name: string\n          options: Options\n          storage: Storage\n          parent: ParentConfig<NodeConfig<Options>>['allowGapCursor']\n        }) => boolean | null)\n  }\n}\n\n/**\n * This extension allows you to add a gap cursor to your editor.\n * A gap cursor is a cursor that appears when you click on a place\n * where no content is present, for example inbetween nodes.\n * @see https://tiptap.dev/api/extensions/gapcursor\n */\nexport const Gapcursor = Extension.create({\n  name: 'gapCursor',\n\n  addProseMirrorPlugins() {\n    return [gapCursor()]\n  },\n\n  extendNodeSchema(extension) {\n    const context = {\n      name: extension.name,\n      options: extension.options,\n      storage: extension.storage,\n    }\n\n    return {\n      allowGapCursor: callOrReturn(getExtensionField(extension, 'allowGapCursor', context)) ?? null,\n    }\n  },\n})\n"], "mappings": ";AACA,SAAS,cAAc,WAAW,yBAAyB;AAC3D,SAAS,iBAAiB;AA0BnB,IAAM,YAAY,UAAU,OAAO;AAAA,EACxC,MAAM;AAAA,EAEN,wBAAwB;AACtB,WAAO,CAAC,UAAU,CAAC;AAAA,EACrB;AAAA,EAEA,iBAAiB,WAAW;AAnC9B;AAoCI,UAAM,UAAU;AAAA,MACd,MAAM,UAAU;AAAA,MAChB,SAAS,UAAU;AAAA,MACnB,SAAS,UAAU;AAAA,IACrB;AAEA,WAAO;AAAA,MACL,iBAAgB,kBAAa,kBAAkB,WAAW,kBAAkB,OAAO,CAAC,MAApE,YAAyE;AAAA,IAC3F;AAAA,EACF;AACF,CAAC;", "names": []}