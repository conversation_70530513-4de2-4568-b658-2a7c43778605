{"version": 3, "sources": ["../../@tiptap/vue-3/src/BubbleMenu.ts", "../../@tiptap/vue-3/src/Editor.ts", "../../@tiptap/vue-3/src/EditorContent.ts", "../../@tiptap/vue-3/src/FloatingMenu.ts", "../../@tiptap/vue-3/src/NodeViewContent.ts", "../../@tiptap/vue-3/src/NodeViewWrapper.ts", "../../@tiptap/vue-3/src/useEditor.ts", "../../@tiptap/vue-3/src/VueRenderer.ts", "../../@tiptap/vue-3/src/VueNodeViewRenderer.ts"], "sourcesContent": ["import { BubbleMenuPlugin, BubbleMenuPluginProps } from '@tiptap/extension-bubble-menu'\nimport {\n  defineComponent,\n  h,\n  onBeforeUnmount,\n  onMounted,\n  PropType,\n  ref,\n} from 'vue'\n\nexport const BubbleMenu = defineComponent({\n  name: 'BubbleMenu',\n\n  props: {\n    pluginKey: {\n      type: [String, Object] as PropType<BubbleMenuPluginProps['pluginKey']>,\n      default: 'bubbleMenu',\n    },\n\n    editor: {\n      type: Object as PropType<BubbleMenuPluginProps['editor']>,\n      required: true,\n    },\n\n    updateDelay: {\n      type: Number as PropType<BubbleMenuPluginProps['updateDelay']>,\n      default: undefined,\n    },\n\n    tippyOptions: {\n      type: Object as PropType<BubbleMenuPluginProps['tippyOptions']>,\n      default: () => ({}),\n    },\n\n    shouldShow: {\n      type: Function as PropType<Exclude<Required<BubbleMenuPluginProps>['shouldShow'], null>>,\n      default: null,\n    },\n  },\n\n  setup(props, { slots }) {\n    const root = ref<HTMLElement | null>(null)\n\n    onMounted(() => {\n      const {\n        updateDelay,\n        editor,\n        pluginKey,\n        shouldShow,\n        tippyOptions,\n      } = props\n\n      editor.registerPlugin(BubbleMenuPlugin({\n        updateDelay,\n        editor,\n        element: root.value as HTMLElement,\n        pluginKey,\n        shouldShow,\n        tippyOptions,\n      }))\n    })\n\n    onBeforeUnmount(() => {\n      const { pluginKey, editor } = props\n\n      editor.unregisterPlugin(pluginKey)\n    })\n\n    return () => h('div', { ref: root }, slots.default?.())\n  },\n})\n", "/* eslint-disable react-hooks/rules-of-hooks */\nimport { Editor as CoreEditor, EditorOptions } from '@tiptap/core'\nimport { Editor<PERSON><PERSON>, <PERSON>lugin, Plugin<PERSON>ey } from '@tiptap/pm/state'\nimport {\n  AppContext,\n  ComponentInternalInstance,\n  ComponentPublicInstance,\n  customRef,\n  markRaw,\n  Ref,\n} from 'vue'\n\nfunction useDebouncedRef<T>(value: T) {\n  return customRef<T>((track, trigger) => {\n    return {\n      get() {\n        track()\n        return value\n      },\n      set(newValue) {\n        // update state\n        value = newValue\n\n        // update view as soon as possible\n        requestAnimationFrame(() => {\n          requestAnimationFrame(() => {\n            trigger()\n          })\n        })\n      },\n    }\n  })\n}\n\nexport type ContentComponent = ComponentInternalInstance & {\n  ctx: ComponentPublicInstance\n}\n\nexport class Editor extends CoreEditor {\n  private reactiveState: Ref<EditorState>\n\n  private reactiveExtensionStorage: Ref<Record<string, any>>\n\n  public contentComponent: ContentComponent | null = null\n\n  public appContext: AppContext | null = null\n\n  constructor(options: Partial<EditorOptions> = {}) {\n    super(options)\n\n    this.reactiveState = useDebouncedRef(this.view.state)\n    this.reactiveExtensionStorage = useDebouncedRef(this.extensionStorage)\n\n    this.on('beforeTransaction', ({ nextState }) => {\n      this.reactiveState.value = nextState\n      this.reactiveExtensionStorage.value = this.extensionStorage\n    })\n\n    return markRaw(this) // eslint-disable-line\n  }\n\n  get state() {\n    return this.reactiveState ? this.reactiveState.value : this.view.state\n  }\n\n  get storage() {\n    return this.reactiveExtensionStorage ? this.reactiveExtensionStorage.value : super.storage\n  }\n\n  /**\n   * Register a ProseMirror plugin.\n   */\n  public registerPlugin(\n    plugin: Plugin,\n    handlePlugins?: (newPlugin: Plugin, plugins: Plugin[]) => Plugin[],\n  ): EditorState {\n    const nextState = super.registerPlugin(plugin, handlePlugins)\n\n    if (this.reactiveState) {\n      this.reactiveState.value = nextState\n    }\n\n    return nextState\n  }\n\n  /**\n   * Unregister a ProseMirror plugin.\n   */\n  public unregisterPlugin(nameOrPluginKey: string | PluginKey): EditorState | undefined {\n    const nextState = super.unregisterPlugin(nameOrPluginKey)\n\n    if (this.reactiveState && nextState) {\n      this.reactiveState.value = nextState\n    }\n\n    return nextState\n  }\n}\n", "import {\n  defineComponent,\n  getCurrentInstance,\n  h,\n  nextTick,\n  onBeforeUnmount,\n  PropType,\n  Ref,\n  ref,\n  unref,\n  watchEffect,\n} from 'vue'\n\nimport { Editor } from './Editor.js'\n\nexport const EditorContent = defineComponent({\n  name: 'EditorContent',\n\n  props: {\n    editor: {\n      default: null,\n      type: Object as PropType<Editor>,\n    },\n  },\n\n  setup(props) {\n    const rootEl: Ref<Element | undefined> = ref()\n    const instance = getCurrentInstance()\n\n    watchEffect(() => {\n      const editor = props.editor\n\n      if (editor && editor.options.element && rootEl.value) {\n        nextTick(() => {\n          if (!rootEl.value || !editor.options.element.firstChild) {\n            return\n          }\n\n          const element = unref(rootEl.value)\n\n          rootEl.value.append(...editor.options.element.childNodes)\n\n          // @ts-ignore\n          editor.contentComponent = instance.ctx._\n\n          if (instance) {\n            editor.appContext = {\n              ...instance.appContext,\n              // Vue internally uses prototype chain to forward/shadow injects across the entire component chain\n              // so don't use object spread operator or 'Object.assign' and just set `provides` as is on editor's appContext\n              // @ts-expect-error forward instance's 'provides' into appContext\n              provides: instance.provides,\n            }\n          }\n\n          editor.setOptions({\n            element,\n          })\n\n          editor.createNodeViews()\n        })\n      }\n    })\n\n    onBeforeUnmount(() => {\n      const editor = props.editor\n\n      if (!editor) {\n        return\n      }\n\n      editor.contentComponent = null\n      editor.appContext = null\n    })\n\n    return { rootEl }\n  },\n\n  render() {\n    return h(\n      'div',\n      {\n        ref: (el: any) => { this.rootEl = el },\n      },\n    )\n  },\n})\n", "import { FloatingMenuPlugin, FloatingMenuPluginProps } from '@tiptap/extension-floating-menu'\nimport {\n  defineComponent,\n  h,\n  onBeforeUnmount,\n  onMounted,\n  PropType,\n  ref,\n} from 'vue'\n\nexport const FloatingMenu = defineComponent({\n  name: 'FloatingMenu',\n\n  props: {\n    pluginKey: {\n      // TODO: TypeScript breaks :(\n      // type: [String, Object as PropType<Exclude<FloatingMenuPluginProps['pluginKey'], string>>],\n      type: null,\n      default: 'floatingMenu',\n    },\n\n    editor: {\n      type: Object as PropType<FloatingMenuPluginProps['editor']>,\n      required: true,\n    },\n\n    tippyOptions: {\n      type: Object as PropType<FloatingMenuPluginProps['tippyOptions']>,\n      default: () => ({}),\n    },\n\n    shouldShow: {\n      type: Function as PropType<Exclude<Required<FloatingMenuPluginProps>['shouldShow'], null>>,\n      default: null,\n    },\n  },\n\n  setup(props, { slots }) {\n    const root = ref<HTMLElement | null>(null)\n\n    onMounted(() => {\n      const {\n        pluginKey,\n        editor,\n        tippyOptions,\n        shouldShow,\n      } = props\n\n      editor.registerPlugin(FloatingMenuPlugin({\n        pluginKey,\n        editor,\n        element: root.value as HTMLElement,\n        tippyOptions,\n        shouldShow,\n      }))\n    })\n\n    onBeforeUnmount(() => {\n      const { pluginKey, editor } = props\n\n      editor.unregisterPlugin(pluginKey)\n    })\n\n    return () => h('div', { ref: root }, slots.default?.())\n  },\n})\n", "import { defineComponent, h } from 'vue'\n\nexport const NodeViewContent = defineComponent({\n  name: 'NodeViewContent',\n\n  props: {\n    as: {\n      type: String,\n      default: 'div',\n    },\n  },\n\n  render() {\n    return h(this.as, {\n      style: {\n        whiteSpace: 'pre-wrap',\n      },\n      'data-node-view-content': '',\n    })\n  },\n})\n", "import { defineComponent, h } from 'vue'\n\nexport const NodeViewWrapper = defineComponent({\n  name: 'NodeViewWrapper',\n\n  props: {\n    as: {\n      type: String,\n      default: 'div',\n    },\n  },\n\n  inject: ['onDragStart', 'decorationClasses'],\n\n  render() {\n    return h(\n      this.as,\n      {\n        // @ts-ignore\n        class: this.decorationClasses,\n        style: {\n          whiteSpace: 'normal',\n        },\n        'data-node-view-wrapper': '',\n        // @ts-ignore (https://github.com/vuejs/vue-next/issues/3031)\n        onDragstart: this.onDragStart,\n      },\n      this.$slots.default?.(),\n    )\n  },\n})\n", "import { EditorOptions } from '@tiptap/core'\nimport { onBeforeUnmount, onMounted, shallowRef } from 'vue'\n\nimport { Editor } from './Editor.js'\n\nexport const useEditor = (options: Partial<EditorOptions> = {}) => {\n  const editor = shallowRef<Editor>()\n\n  onMounted(() => {\n    editor.value = new Editor(options)\n  })\n\n  onBeforeUnmount(() => {\n    // Cloning root node (and its children) to avoid content being lost by destroy\n    const nodes = editor.value?.options.element\n    const newEl = nodes?.cloneNode(true) as HTMLElement\n\n    nodes?.parentNode?.replaceChild(newEl, nodes)\n\n    editor.value?.destroy()\n  })\n\n  return editor\n}\n", "import { Editor } from '@tiptap/core'\nimport {\n  Component, DefineComponent, h, markRaw, reactive, render,\n} from 'vue'\n\nimport { Editor as ExtendedEditor } from './Editor.js'\n\nexport interface VueRendererOptions {\n  editor: Editor;\n  props?: Record<string, any>;\n}\n\ntype ExtendedVNode = ReturnType<typeof h> | null;\n\ninterface RenderedComponent {\n  vNode: ExtendedVNode;\n  destroy: () => void;\n  el: Element | null;\n}\n\n/**\n * This class is used to render Vue components inside the editor.\n */\nexport class VueRenderer {\n  renderedComponent!: RenderedComponent\n\n  editor: ExtendedEditor\n\n  component: Component\n\n  el: Element | null\n\n  props: Record<string, any>\n\n  constructor(component: Component, { props = {}, editor }: VueRendererOptions) {\n    this.editor = editor as ExtendedEditor\n    this.component = markRaw(component)\n    this.el = document.createElement('div')\n    this.props = reactive(props)\n    this.renderedComponent = this.renderComponent()\n  }\n\n  get element(): Element | null {\n    return this.renderedComponent.el\n  }\n\n  get ref(): any {\n    // Composition API\n    if (this.renderedComponent.vNode?.component?.exposed) {\n      return this.renderedComponent.vNode.component.exposed\n    }\n    // Option API\n    return this.renderedComponent.vNode?.component?.proxy\n  }\n\n  renderComponent() {\n    let vNode: ExtendedVNode = h(this.component as DefineComponent, this.props)\n\n    if (this.editor.appContext) {\n      vNode.appContext = this.editor.appContext\n    }\n    if (typeof document !== 'undefined' && this.el) {\n      render(vNode, this.el)\n    }\n\n    const destroy = () => {\n      if (this.el) {\n        render(null, this.el)\n      }\n      this.el = null\n      vNode = null\n    }\n\n    return { vNode, destroy, el: this.el ? this.el.firstElementChild : null }\n  }\n\n  updateProps(props: Record<string, any> = {}): void {\n    Object.entries(props).forEach(([key, value]) => {\n      this.props[key] = value\n    })\n    this.renderComponent()\n  }\n\n  destroy(): void {\n    this.renderedComponent.destroy()\n  }\n}\n", "/* eslint-disable no-underscore-dangle */\nimport {\n  DecorationWithType,\n  NodeView,\n  NodeViewProps,\n  NodeViewRenderer,\n  NodeViewRendererOptions,\n} from '@tiptap/core'\nimport { Node as ProseMirrorNode } from '@tiptap/pm/model'\nimport { Decoration, DecorationSource, NodeView as ProseMirrorNodeView } from '@tiptap/pm/view'\nimport {\n  Component, defineComponent, PropType, provide, Ref, ref,\n} from 'vue'\n\nimport { Editor } from './Editor.js'\nimport { VueRenderer } from './VueRenderer.js'\n\nexport const nodeViewProps = {\n  editor: {\n    type: Object as PropType<NodeViewProps['editor']>,\n    required: true as const,\n  },\n  node: {\n    type: Object as PropType<NodeViewProps['node']>,\n    required: true as const,\n  },\n  decorations: {\n    type: Object as PropType<NodeViewProps['decorations']>,\n    required: true as const,\n  },\n  selected: {\n    type: Boolean as PropType<NodeViewProps['selected']>,\n    required: true as const,\n  },\n  extension: {\n    type: Object as PropType<NodeViewProps['extension']>,\n    required: true as const,\n  },\n  getPos: {\n    type: Function as PropType<NodeViewProps['getPos']>,\n    required: true as const,\n  },\n  updateAttributes: {\n    type: Function as PropType<NodeViewProps['updateAttributes']>,\n    required: true as const,\n  },\n  deleteNode: {\n    type: Function as PropType<NodeViewProps['deleteNode']>,\n    required: true as const,\n  },\n  view: {\n    type: Object as PropType<NodeViewProps['view']>,\n    required: true as const,\n  },\n  innerDecorations: {\n    type: Object as PropType<NodeViewProps['innerDecorations']>,\n    required: true as const,\n  },\n  HTMLAttributes: {\n    type: Object as PropType<NodeViewProps['HTMLAttributes']>,\n    required: true as const,\n  },\n}\n\nexport interface VueNodeViewRendererOptions extends NodeViewRendererOptions {\n  update:\n    | ((props: {\n        oldNode: ProseMirrorNode;\n        oldDecorations: readonly Decoration[];\n        oldInnerDecorations: DecorationSource;\n        newNode: ProseMirrorNode;\n        newDecorations: readonly Decoration[];\n        innerDecorations: DecorationSource;\n        updateProps: () => void;\n      }) => boolean)\n    | null;\n}\n\nclass VueNodeView extends NodeView<Component, Editor, VueNodeViewRendererOptions> {\n  renderer!: VueRenderer\n\n  decorationClasses!: Ref<string>\n\n  mount() {\n    const props = {\n      editor: this.editor,\n      node: this.node,\n      decorations: this.decorations as DecorationWithType[],\n      innerDecorations: this.innerDecorations,\n      view: this.view,\n      selected: false,\n      extension: this.extension,\n      HTMLAttributes: this.HTMLAttributes,\n      getPos: () => this.getPos(),\n      updateAttributes: (attributes = {}) => this.updateAttributes(attributes),\n      deleteNode: () => this.deleteNode(),\n    } satisfies NodeViewProps\n\n    const onDragStart = this.onDragStart.bind(this)\n\n    this.decorationClasses = ref(this.getDecorationClasses())\n\n    const extendedComponent = defineComponent({\n      extends: { ...this.component },\n      props: Object.keys(props),\n      template: (this.component as any).template,\n      setup: reactiveProps => {\n        provide('onDragStart', onDragStart)\n        provide('decorationClasses', this.decorationClasses)\n\n        return (this.component as any).setup?.(reactiveProps, {\n          expose: () => undefined,\n        })\n      },\n      // add support for scoped styles\n      // @ts-ignore\n      // eslint-disable-next-line\n      __scopeId: this.component.__scopeId,\n      // add support for CSS Modules\n      // @ts-ignore\n      // eslint-disable-next-line\n      __cssModules: this.component.__cssModules,\n      // add support for vue devtools\n      // @ts-ignore\n      // eslint-disable-next-line\n      __name: this.component.__name,\n      // @ts-ignore\n      // eslint-disable-next-line\n      __file: this.component.__file,\n    })\n\n    this.handleSelectionUpdate = this.handleSelectionUpdate.bind(this)\n    this.editor.on('selectionUpdate', this.handleSelectionUpdate)\n\n    this.renderer = new VueRenderer(extendedComponent, {\n      editor: this.editor,\n      props,\n    })\n  }\n\n  /**\n   * Return the DOM element.\n   * This is the element that will be used to display the node view.\n   */\n  get dom() {\n    if (!this.renderer.element || !this.renderer.element.hasAttribute('data-node-view-wrapper')) {\n      throw Error('Please use the NodeViewWrapper component for your node view.')\n    }\n\n    return this.renderer.element as HTMLElement\n  }\n\n  /**\n   * Return the content DOM element.\n   * This is the element that will be used to display the rich-text content of the node.\n   */\n  get contentDOM() {\n    if (this.node.isLeaf) {\n      return null\n    }\n\n    return this.dom.querySelector('[data-node-view-content]') as HTMLElement | null\n  }\n\n  /**\n   * On editor selection update, check if the node is selected.\n   * If it is, call `selectNode`, otherwise call `deselectNode`.\n   */\n  handleSelectionUpdate() {\n    const { from, to } = this.editor.state.selection\n    const pos = this.getPos()\n\n    if (typeof pos !== 'number') {\n      return\n    }\n\n    if (from <= pos && to >= pos + this.node.nodeSize) {\n      if (this.renderer.props.selected) {\n        return\n      }\n\n      this.selectNode()\n    } else {\n      if (!this.renderer.props.selected) {\n        return\n      }\n\n      this.deselectNode()\n    }\n  }\n\n  /**\n   * On update, update the React component.\n   * To prevent unnecessary updates, the `update` option can be used.\n   */\n  update(\n    node: ProseMirrorNode,\n    decorations: readonly Decoration[],\n    innerDecorations: DecorationSource,\n  ): boolean {\n    const rerenderComponent = (props?: Record<string, any>) => {\n      this.decorationClasses.value = this.getDecorationClasses()\n      this.renderer.updateProps(props)\n    }\n\n    if (typeof this.options.update === 'function') {\n      const oldNode = this.node\n      const oldDecorations = this.decorations\n      const oldInnerDecorations = this.innerDecorations\n\n      this.node = node\n      this.decorations = decorations\n      this.innerDecorations = innerDecorations\n\n      return this.options.update({\n        oldNode,\n        oldDecorations,\n        newNode: node,\n        newDecorations: decorations,\n        oldInnerDecorations,\n        innerDecorations,\n        updateProps: () => rerenderComponent({ node, decorations, innerDecorations }),\n      })\n    }\n\n    if (node.type !== this.node.type) {\n      return false\n    }\n\n    if (node === this.node && this.decorations === decorations && this.innerDecorations === innerDecorations) {\n      return true\n    }\n\n    this.node = node\n    this.decorations = decorations\n    this.innerDecorations = innerDecorations\n\n    rerenderComponent({ node, decorations, innerDecorations })\n\n    return true\n  }\n\n  /**\n   * Select the node.\n   * Add the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  selectNode() {\n    this.renderer.updateProps({\n      selected: true,\n    })\n    if (this.renderer.element) {\n      this.renderer.element.classList.add('ProseMirror-selectednode')\n    }\n  }\n\n  /**\n   * Deselect the node.\n   * Remove the `selected` prop and the `ProseMirror-selectednode` class.\n   */\n  deselectNode() {\n    this.renderer.updateProps({\n      selected: false,\n    })\n    if (this.renderer.element) {\n      this.renderer.element.classList.remove('ProseMirror-selectednode')\n    }\n  }\n\n  getDecorationClasses() {\n    return (\n      this.decorations\n        // @ts-ignore\n        .map(item => item.type.attrs.class)\n        .flat()\n        .join(' ')\n    )\n  }\n\n  destroy() {\n    this.renderer.destroy()\n    this.editor.off('selectionUpdate', this.handleSelectionUpdate)\n  }\n}\n\nexport function VueNodeViewRenderer(\n  component: Component<NodeViewProps>,\n  options?: Partial<VueNodeViewRendererOptions>,\n): NodeViewRenderer {\n  return props => {\n    // try to get the parent component\n    // this is important for vue devtools to show the component hierarchy correctly\n    // maybe it’s `undefined` because <editor-content> isn’t rendered yet\n    if (!(props.editor as Editor).contentComponent) {\n      return {} as unknown as ProseMirrorNodeView\n    }\n    // check for class-component and normalize if neccessary\n    const normalizedComponent = typeof component === 'function' && '__vccOpts' in component\n      ? (component.__vccOpts as Component)\n      : component\n\n    return new VueNodeView(normalizedComponent, props, options)\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUO,IAAM,aAAa,gBAAgB;EACxC,MAAM;EAEN,OAAO;IACL,WAAW;MACT,MAAM,CAAC,QAAQ,MAAM;MACrB,SAAS;IACV;IAED,QAAQ;MACN,MAAM;MACN,UAAU;IACX;IAED,aAAa;MACX,MAAM;MACN,SAAS;IACV;IAED,cAAc;MACZ,MAAM;MACN,SAAS,OAAO,CAAA;IACjB;IAED,YAAY;MACV,MAAM;MACN,SAAS;IACV;EACF;EAED,MAAM,OAAO,EAAE,MAAK,GAAE;AACpB,UAAM,OAAO,IAAwB,IAAI;AAEzC,cAAU,MAAK;AACb,YAAM,EACJ,aACA,QACA,WACA,YACA,aAAY,IACV;AAEJ,aAAO,eAAe,iBAAiB;QACrC;QACA;QACA,SAAS,KAAK;QACd;QACA;QACA;MACD,CAAA,CAAC;IACJ,CAAC;AAED,oBAAgB,MAAK;AACnB,YAAM,EAAE,WAAW,OAAM,IAAK;AAE9B,aAAO,iBAAiB,SAAS;IACnC,CAAC;AAED,WAAO,MAAM;AAAA,UAAA;AAAA,aAAA,EAAE,OAAO,EAAE,KAAK,KAAI,IAAI,KAAA,MAAM,aAAW,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,KAAA,CAAA;IAAC;;AAE1D,CAAA;AC1DD,SAAS,gBAAmB,OAAQ;AAClC,SAAO,UAAa,CAAC,OAAO,YAAW;AACrC,WAAO;MACL,MAAG;AACD,cAAK;AACL,eAAO;;MAET,IAAI,UAAQ;AAEV,gBAAQ;AAGR,8BAAsB,MAAK;AACzB,gCAAsB,MAAK;AACzB,oBAAO;UACT,CAAC;QACH,CAAC;;;EAGP,CAAC;AACH;AAMM,IAAOA,UAAP,cAAsBC,OAAU;EASpC,YAAY,UAAkC,CAAA,GAAE;AAC9C,UAAM,OAAO;AALR,SAAgB,mBAA4B;AAE5C,SAAU,aAAsB;AAKrC,SAAK,gBAAgB,gBAAgB,KAAK,KAAK,KAAK;AACpD,SAAK,2BAA2B,gBAAgB,KAAK,gBAAgB;AAErE,SAAK,GAAG,qBAAqB,CAAC,EAAE,UAAS,MAAM;AAC7C,WAAK,cAAc,QAAQ;AAC3B,WAAK,yBAAyB,QAAQ,KAAK;IAC7C,CAAC;AAED,WAAO,QAAQ,IAAI;;EAGrB,IAAI,QAAK;AACP,WAAO,KAAK,gBAAgB,KAAK,cAAc,QAAQ,KAAK,KAAK;;EAGnE,IAAI,UAAO;AACT,WAAO,KAAK,2BAA2B,KAAK,yBAAyB,QAAQ,MAAM;;;;;EAM9E,eACL,QACA,eAAkE;AAElE,UAAM,YAAY,MAAM,eAAe,QAAQ,aAAa;AAE5D,QAAI,KAAK,eAAe;AACtB,WAAK,cAAc,QAAQ;;AAG7B,WAAO;;;;;EAMF,iBAAiB,iBAAmC;AACzD,UAAM,YAAY,MAAM,iBAAiB,eAAe;AAExD,QAAI,KAAK,iBAAiB,WAAW;AACnC,WAAK,cAAc,QAAQ;;AAG7B,WAAO;;AAEV;AClFM,IAAM,gBAAgB,gBAAgB;EAC3C,MAAM;EAEN,OAAO;IACL,QAAQ;MACN,SAAS;MACT,MAAM;IACP;EACF;EAED,MAAM,OAAK;AACT,UAAM,SAAmC,IAAG;AAC5C,UAAM,WAAW,mBAAkB;AAEnC,gBAAY,MAAK;AACf,YAAM,SAAS,MAAM;AAErB,UAAI,UAAU,OAAO,QAAQ,WAAW,OAAO,OAAO;AACpD,iBAAS,MAAK;AACZ,cAAI,CAAC,OAAO,SAAS,CAAC,OAAO,QAAQ,QAAQ,YAAY;AACvD;;AAGF,gBAAM,UAAU,MAAM,OAAO,KAAK;AAElC,iBAAO,MAAM,OAAO,GAAG,OAAO,QAAQ,QAAQ,UAAU;AAGxD,iBAAO,mBAAmB,SAAS,IAAI;AAEvC,cAAI,UAAU;AACZ,mBAAO,aAAa;cAClB,GAAG,SAAS;;;;cAIZ,UAAU,SAAS;;;AAIvB,iBAAO,WAAW;YAChB;UACD,CAAA;AAED,iBAAO,gBAAe;QACxB,CAAC;;IAEL,CAAC;AAED,oBAAgB,MAAK;AACnB,YAAM,SAAS,MAAM;AAErB,UAAI,CAAC,QAAQ;AACX;;AAGF,aAAO,mBAAmB;AAC1B,aAAO,aAAa;IACtB,CAAC;AAED,WAAO,EAAE,OAAM;;EAGjB,SAAM;AACJ,WAAO,EACL,OACA;MACE,KAAK,CAAC,OAAW;AAAG,aAAK,SAAS;MAAE;IACrC,CAAA;;AAGN,CAAA;AC5EM,IAAM,eAAe,gBAAgB;EAC1C,MAAM;EAEN,OAAO;IACL,WAAW;;;MAGT,MAAM;MACN,SAAS;IACV;IAED,QAAQ;MACN,MAAM;MACN,UAAU;IACX;IAED,cAAc;MACZ,MAAM;MACN,SAAS,OAAO,CAAA;IACjB;IAED,YAAY;MACV,MAAM;MACN,SAAS;IACV;EACF;EAED,MAAM,OAAO,EAAE,MAAK,GAAE;AACpB,UAAM,OAAO,IAAwB,IAAI;AAEzC,cAAU,MAAK;AACb,YAAM,EACJ,WACA,QACA,cACA,WAAU,IACR;AAEJ,aAAO,eAAe,mBAAmB;QACvC;QACA;QACA,SAAS,KAAK;QACd;QACA;MACD,CAAA,CAAC;IACJ,CAAC;AAED,oBAAgB,MAAK;AACnB,YAAM,EAAE,WAAW,OAAM,IAAK;AAE9B,aAAO,iBAAiB,SAAS;IACnC,CAAC;AAED,WAAO,MAAM;AAAA,UAAA;AAAA,aAAA,EAAE,OAAO,EAAE,KAAK,KAAI,IAAI,KAAA,MAAM,aAAW,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,KAAA,CAAA;IAAC;;AAE1D,CAAA;AC/DM,IAAM,kBAAkB,gBAAgB;EAC7C,MAAM;EAEN,OAAO;IACL,IAAI;MACF,MAAM;MACN,SAAS;IACV;EACF;EAED,SAAM;AACJ,WAAO,EAAE,KAAK,IAAI;MAChB,OAAO;QACL,YAAY;MACb;MACD,0BAA0B;IAC3B,CAAA;;AAEJ,CAAA;AClBM,IAAM,kBAAkB,gBAAgB;EAC7C,MAAM;EAEN,OAAO;IACL,IAAI;MACF,MAAM;MACN,SAAS;IACV;EACF;EAED,QAAQ,CAAC,eAAe,mBAAmB;EAE3C,SAAM;;AACJ,WAAO,EACL,KAAK,IACL;;MAEE,OAAO,KAAK;MACZ,OAAO;QACL,YAAY;MACb;MACD,0BAA0B;;MAE1B,aAAa,KAAK;QAEpB,MAAA,KAAA,KAAK,QAAO,aAAW,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,EAAA,CAAA;;AAG5B,CAAA;ICzBY,YAAY,CAAC,UAAkC,CAAA,MAAM;AAChE,QAAM,SAAS,WAAU;AAEzB,YAAU,MAAK;AACb,WAAO,QAAQ,IAAID,QAAO,OAAO;EACnC,CAAC;AAED,kBAAgB,MAAK;;AAEnB,UAAM,SAAQ,KAAA,OAAO,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,QAAQ;AACpC,UAAM,QAAQ,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,UAAU,IAAI;AAEnC,KAAA,KAAA,UAAK,QAAL,UAAK,SAAA,SAAL,MAAO,gBAAY,QAAA,OAAA,SAAA,SAAA,GAAA,aAAa,OAAO,KAAK;AAE5C,KAAA,KAAA,OAAO,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,QAAO;EACvB,CAAC;AAED,SAAO;AACT;ICAa,oBAAW;EAWtB,YAAY,WAAsB,EAAE,QAAQ,CAAA,GAAI,OAAM,GAAsB;AAC1E,SAAK,SAAS;AACd,SAAK,YAAY,QAAQ,SAAS;AAClC,SAAK,KAAK,SAAS,cAAc,KAAK;AACtC,SAAK,QAAQ,SAAS,KAAK;AAC3B,SAAK,oBAAoB,KAAK,gBAAe;;EAG/C,IAAI,UAAO;AACT,WAAO,KAAK,kBAAkB;;EAGhC,IAAI,MAAG;;AAEL,SAAI,MAAA,KAAA,KAAK,kBAAkB,WAAK,QAAA,OAAA,SAAA,SAAA,GAAE,eAAS,QAAA,OAAA,SAAA,SAAA,GAAE,SAAS;AACpD,aAAO,KAAK,kBAAkB,MAAM,UAAU;;AAGhD,YAAO,MAAA,KAAA,KAAK,kBAAkB,WAAO,QAAA,OAAA,SAAA,SAAA,GAAA,eAAW,QAAA,OAAA,SAAA,SAAA,GAAA;;EAGlD,kBAAe;AACb,QAAI,QAAuB,EAAE,KAAK,WAA8B,KAAK,KAAK;AAE1E,QAAI,KAAK,OAAO,YAAY;AAC1B,YAAM,aAAa,KAAK,OAAO;;AAEjC,QAAI,OAAO,aAAa,eAAe,KAAK,IAAI;AAC9C,aAAO,OAAO,KAAK,EAAE;;AAGvB,UAAM,UAAU,MAAK;AACnB,UAAI,KAAK,IAAI;AACX,eAAO,MAAM,KAAK,EAAE;;AAEtB,WAAK,KAAK;AACV,cAAQ;IACV;AAEA,WAAO,EAAE,OAAO,SAAS,IAAI,KAAK,KAAK,KAAK,GAAG,oBAAoB,KAAI;;EAGzE,YAAY,QAA6B,CAAA,GAAE;AACzC,WAAO,QAAQ,KAAK,EAAE,QAAQ,CAAC,CAAC,KAAK,KAAK,MAAK;AAC7C,WAAK,MAAM,GAAG,IAAI;IACpB,CAAC;AACD,SAAK,gBAAe;;EAGtB,UAAO;AACL,SAAK,kBAAkB,QAAO;;AAEjC;ACrEY,IAAA,gBAAgB;EAC3B,QAAQ;IACN,MAAM;IACN,UAAU;EACX;EACD,MAAM;IACJ,MAAM;IACN,UAAU;EACX;EACD,aAAa;IACX,MAAM;IACN,UAAU;EACX;EACD,UAAU;IACR,MAAM;IACN,UAAU;EACX;EACD,WAAW;IACT,MAAM;IACN,UAAU;EACX;EACD,QAAQ;IACN,MAAM;IACN,UAAU;EACX;EACD,kBAAkB;IAChB,MAAM;IACN,UAAU;EACX;EACD,YAAY;IACV,MAAM;IACN,UAAU;EACX;EACD,MAAM;IACJ,MAAM;IACN,UAAU;EACX;EACD,kBAAkB;IAChB,MAAM;IACN,UAAU;EACX;EACD,gBAAgB;IACd,MAAM;IACN,UAAU;EACX;;AAiBH,IAAM,cAAN,cAA0B,SAAuD;EAK/E,QAAK;AACH,UAAM,QAAQ;MACZ,QAAQ,KAAK;MACb,MAAM,KAAK;MACX,aAAa,KAAK;MAClB,kBAAkB,KAAK;MACvB,MAAM,KAAK;MACX,UAAU;MACV,WAAW,KAAK;MAChB,gBAAgB,KAAK;MACrB,QAAQ,MAAM,KAAK,OAAM;MACzB,kBAAkB,CAAC,aAAa,CAAA,MAAO,KAAK,iBAAiB,UAAU;MACvE,YAAY,MAAM,KAAK,WAAU;;AAGnC,UAAM,cAAc,KAAK,YAAY,KAAK,IAAI;AAE9C,SAAK,oBAAoB,IAAI,KAAK,qBAAoB,CAAE;AAExD,UAAM,oBAAoB,gBAAgB;MACxC,SAAS,EAAE,GAAG,KAAK,UAAS;MAC5B,OAAO,OAAO,KAAK,KAAK;MACxB,UAAW,KAAK,UAAkB;MAClC,OAAO,mBAAgB;;AACrB,gBAAQ,eAAe,WAAW;AAClC,gBAAQ,qBAAqB,KAAK,iBAAiB;AAEnD,gBAAO,MAAA,KAAC,KAAK,WAAkB,WAAQ,QAAA,OAAA,SAAA,SAAA,GAAA,KAAA,IAAA,eAAe;UACpD,QAAQ,MAAM;QACf,CAAA;;;;;MAKH,WAAW,KAAK,UAAU;;;;MAI1B,cAAc,KAAK,UAAU;;;;MAI7B,QAAQ,KAAK,UAAU;;;MAGvB,QAAQ,KAAK,UAAU;IACxB,CAAA;AAED,SAAK,wBAAwB,KAAK,sBAAsB,KAAK,IAAI;AACjE,SAAK,OAAO,GAAG,mBAAmB,KAAK,qBAAqB;AAE5D,SAAK,WAAW,IAAI,YAAY,mBAAmB;MACjD,QAAQ,KAAK;MACb;IACD,CAAA;;;;;;EAOH,IAAI,MAAG;AACL,QAAI,CAAC,KAAK,SAAS,WAAW,CAAC,KAAK,SAAS,QAAQ,aAAa,wBAAwB,GAAG;AAC3F,YAAM,MAAM,8DAA8D;;AAG5E,WAAO,KAAK,SAAS;;;;;;EAOvB,IAAI,aAAU;AACZ,QAAI,KAAK,KAAK,QAAQ;AACpB,aAAO;;AAGT,WAAO,KAAK,IAAI,cAAc,0BAA0B;;;;;;EAO1D,wBAAqB;AACnB,UAAM,EAAE,MAAM,GAAE,IAAK,KAAK,OAAO,MAAM;AACvC,UAAM,MAAM,KAAK,OAAM;AAEvB,QAAI,OAAO,QAAQ,UAAU;AAC3B;;AAGF,QAAI,QAAQ,OAAO,MAAM,MAAM,KAAK,KAAK,UAAU;AACjD,UAAI,KAAK,SAAS,MAAM,UAAU;AAChC;;AAGF,WAAK,WAAU;WACV;AACL,UAAI,CAAC,KAAK,SAAS,MAAM,UAAU;AACjC;;AAGF,WAAK,aAAY;;;;;;;EAQrB,OACE,MACA,aACA,kBAAkC;AAElC,UAAM,oBAAoB,CAAC,UAA+B;AACxD,WAAK,kBAAkB,QAAQ,KAAK,qBAAoB;AACxD,WAAK,SAAS,YAAY,KAAK;IACjC;AAEA,QAAI,OAAO,KAAK,QAAQ,WAAW,YAAY;AAC7C,YAAM,UAAU,KAAK;AACrB,YAAM,iBAAiB,KAAK;AAC5B,YAAM,sBAAsB,KAAK;AAEjC,WAAK,OAAO;AACZ,WAAK,cAAc;AACnB,WAAK,mBAAmB;AAExB,aAAO,KAAK,QAAQ,OAAO;QACzB;QACA;QACA,SAAS;QACT,gBAAgB;QAChB;QACA;QACA,aAAa,MAAM,kBAAkB,EAAE,MAAM,aAAa,iBAAgB,CAAE;MAC7E,CAAA;;AAGH,QAAI,KAAK,SAAS,KAAK,KAAK,MAAM;AAChC,aAAO;;AAGT,QAAI,SAAS,KAAK,QAAQ,KAAK,gBAAgB,eAAe,KAAK,qBAAqB,kBAAkB;AACxG,aAAO;;AAGT,SAAK,OAAO;AACZ,SAAK,cAAc;AACnB,SAAK,mBAAmB;AAExB,sBAAkB,EAAE,MAAM,aAAa,iBAAgB,CAAE;AAEzD,WAAO;;;;;;EAOT,aAAU;AACR,SAAK,SAAS,YAAY;MACxB,UAAU;IACX,CAAA;AACD,QAAI,KAAK,SAAS,SAAS;AACzB,WAAK,SAAS,QAAQ,UAAU,IAAI,0BAA0B;;;;;;;EAQlE,eAAY;AACV,SAAK,SAAS,YAAY;MACxB,UAAU;IACX,CAAA;AACD,QAAI,KAAK,SAAS,SAAS;AACzB,WAAK,SAAS,QAAQ,UAAU,OAAO,0BAA0B;;;EAIrE,uBAAoB;AAClB,WACE,KAAK,YAEF,IAAI,UAAQ,KAAK,KAAK,MAAM,KAAK,EACjC,KAAI,EACJ,KAAK,GAAG;;EAIf,UAAO;AACL,SAAK,SAAS,QAAO;AACrB,SAAK,OAAO,IAAI,mBAAmB,KAAK,qBAAqB;;AAEhE;AAEe,SAAA,oBACd,WACA,SAA6C;AAE7C,SAAO,WAAQ;AAIb,QAAI,CAAE,MAAM,OAAkB,kBAAkB;AAC9C,aAAO,CAAA;;AAGT,UAAM,sBAAsB,OAAO,cAAc,cAAc,eAAe,YACzE,UAAU,YACX;AAEJ,WAAO,IAAI,YAAY,qBAAqB,OAAO,OAAO;EAC5D;AACF;", "names": ["Editor", "CoreEditor"]}