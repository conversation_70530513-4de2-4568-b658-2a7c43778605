import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  CommandManager,
  Editor,
  Extension,
  InputRule,
  Mark,
  Node,
  NodePos,
  NodeView,
  PasteRule,
  Tracker,
  callOrReturn,
  combineTransactionSteps,
  createChainableState,
  createDocument,
  createNodeFromContent,
  createStyleTag,
  defaultBlockAt,
  deleteProps,
  elementFromString,
  escapeForRegEx,
  findChildren,
  findChildrenInRange,
  findDuplicates,
  findParentNode,
  findParentNodeClosestToPos,
  fromString,
  generateHTML,
  generateJSON,
  generateText,
  getAttributes,
  getAttributesFromExtensions,
  getChangedRanges,
  getDebugJSON,
  getExtensionField,
  getHTMLFromFragment,
  getMarkAttributes,
  getMarkRange,
  getMarkType,
  getMarksBetween,
  getNodeAtPosition,
  getNodeAttributes,
  getNodeType,
  getRenderedAttributes,
  getSchema,
  getSchemaByResolvedExtensions,
  getSchemaTypeByName,
  getSchemaTypeNameByName,
  getSplittedAttributes,
  getText,
  getTextBetween,
  getTextContentFromNodes,
  getTextSerializersFromSchema,
  index,
  injectExtensionAttributesToParseRule,
  inputRulesPlugin,
  isActive,
  isAtEndOfNode,
  isAtStartOfNode,
  isEmptyObject,
  isExtensionRulesEnabled,
  isFunction,
  isList,
  isMacOS,
  isMarkActive,
  isNodeActive,
  isNodeEmpty,
  isNodeSelection,
  isNumber,
  isPlainObject,
  isRegExp,
  isString,
  isTextSelection,
  isiOS,
  markInputRule,
  markPasteRule,
  mergeAttributes,
  mergeDeep,
  minMax,
  nodeInputRule,
  nodePasteRule,
  objectIncludes,
  pasteRulesPlugin,
  posToDOMRect,
  removeDuplicates,
  resolveFocusPosition,
  rewriteUnknownContent,
  selectionToInsertionEnd,
  splitExtensions,
  textInputRule,
  textPasteRule,
  textblockTypeInputRule,
  wrappingInputRule
} from "./chunk-ZDW4XOTA.js";
import "./chunk-AL46U7Q7.js";
import "./chunk-REYSTJ5T.js";
import "./chunk-YZBTLTZP.js";
import "./chunk-XLOADKRT.js";
import "./chunk-CE2CDXOX.js";
import "./chunk-QHVAV34Y.js";
import "./chunk-O7RF7KNN.js";
import "./chunk-ZMSOBIYE.js";
export {
  CommandManager,
  Editor,
  Extension,
  InputRule,
  Mark,
  Node,
  NodePos,
  NodeView,
  PasteRule,
  Tracker,
  callOrReturn,
  combineTransactionSteps,
  createChainableState,
  createDocument,
  createNodeFromContent,
  createStyleTag,
  defaultBlockAt,
  deleteProps,
  elementFromString,
  escapeForRegEx,
  index as extensions,
  findChildren,
  findChildrenInRange,
  findDuplicates,
  findParentNode,
  findParentNodeClosestToPos,
  fromString,
  generateHTML,
  generateJSON,
  generateText,
  getAttributes,
  getAttributesFromExtensions,
  getChangedRanges,
  getDebugJSON,
  getExtensionField,
  getHTMLFromFragment,
  getMarkAttributes,
  getMarkRange,
  getMarkType,
  getMarksBetween,
  getNodeAtPosition,
  getNodeAttributes,
  getNodeType,
  getRenderedAttributes,
  getSchema,
  getSchemaByResolvedExtensions,
  getSchemaTypeByName,
  getSchemaTypeNameByName,
  getSplittedAttributes,
  getText,
  getTextBetween,
  getTextContentFromNodes,
  getTextSerializersFromSchema,
  injectExtensionAttributesToParseRule,
  inputRulesPlugin,
  isActive,
  isAtEndOfNode,
  isAtStartOfNode,
  isEmptyObject,
  isExtensionRulesEnabled,
  isFunction,
  isList,
  isMacOS,
  isMarkActive,
  isNodeActive,
  isNodeEmpty,
  isNodeSelection,
  isNumber,
  isPlainObject,
  isRegExp,
  isString,
  isTextSelection,
  isiOS,
  markInputRule,
  markPasteRule,
  mergeAttributes,
  mergeDeep,
  minMax,
  nodeInputRule,
  nodePasteRule,
  objectIncludes,
  pasteRulesPlugin,
  posToDOMRect,
  removeDuplicates,
  resolveFocusPosition,
  rewriteUnknownContent,
  selectionToInsertionEnd,
  splitExtensions,
  textInputRule,
  textPasteRule,
  textblockTypeInputRule,
  wrappingInputRule
};
