<template>
  <div class="menu-test-fixed">
    <h1>Tiptap 菜单功能测试 (修复版)</h1>
    
    <div class="test-instructions">
      <div class="instruction-item">
        <h3>🔸 斜杠菜单测试</h3>
        <ul>
          <li>在编辑器中输入 "/" 触发斜杠菜单</li>
          <li>继续输入文字进行搜索过滤</li>
          <li>使用 ↑↓ 箭头键导航，Enter 确认，Esc 取消</li>
          <li>尝试插入：标题、列表、引用、代码块等</li>
        </ul>
      </div>
      
      <div class="instruction-item">
        <h3>🔸 点击菜单测试</h3>
        <ul>
          <li>鼠标悬停在段落左侧查看点击菜单</li>
          <li>点击 "+" 按钮在当前位置插入新段落</li>
          <li>拖拽六点图标重新排列段落（开发中）</li>
          <li>菜单只在编辑模式下显示</li>
        </ul>
      </div>
    </div>

    <div class="editor-container">
      <div ref="editorRef" class="editor"></div>
    </div>

    <div class="test-controls">
      <button @click="toggleEditable" class="control-btn">
        {{ isEditable ? '切换到只读模式' : '切换到编辑模式' }}
      </button>
      <button @click="clearContent" class="control-btn">清空内容</button>
      <button @click="loadSampleContent" class="control-btn">加载示例内容</button>
    </div>

    <div class="content-display">
      <h3>编辑器内容预览：</h3>
      <div class="content-preview">{{ contentPreview }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Editor } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import { onMounted, onUnmounted, ref } from 'vue'

import { SlashMenuExtension, createDefaultSlashMenuItems } from '@/components/tiptap/extensions/slash-menu'
import { ClickMenuExtension } from '@/components/tiptap/extensions/click-menu'

const editorRef = ref<HTMLElement>()
const isEditable = ref(true)
const contentPreview = ref('')
let editor: Editor | null = null

const sampleContent = `
  <h1>欢迎使用 Tiptap 编辑器</h1>
  <p>这是一个功能完整的富文本编辑器，支持斜杠菜单和点击菜单。</p>
  <h2>斜杠菜单功能</h2>
  <p>输入 "/" 来体验斜杠菜单功能，可以快速插入各种块元素。</p>
  <h2>点击菜单功能</h2>
  <p>鼠标悬停在段落左侧可以看到点击菜单，提供快速操作。</p>
  <blockquote>
    <p>这是一个引用块，展示了编辑器的样式效果。</p>
  </blockquote>
  <ul>
    <li>无序列表项 1</li>
    <li>无序列表项 2</li>
    <li>无序列表项 3</li>
  </ul>
  <p>尝试在这里输入 "/" 来插入新的内容块。</p>
`

onMounted(() => {
  if (!editorRef.value) return

  editor = new Editor({
    element: editorRef.value,
    extensions: [
      StarterKit,
      SlashMenuExtension.configure({
        items: createDefaultSlashMenuItems(),
      }),
      ClickMenuExtension.configure({
        showDragHandle: true,
        showAddButton: true,
        onAddClick: (editor, pos) => {
          // 在指定位置插入新段落
          editor.chain().focus().insertContentAt(pos + 1, '<p></p>').run()
        },
        onDragStart: (editor, pos) => {
          console.log('拖拽开始:', pos)
        },
        onDragEnd: (editor, pos) => {
          console.log('拖拽结束:', pos)
        },
      }),
    ],
    content: sampleContent,
    editable: isEditable.value,
    onUpdate: ({ editor }) => {
      contentPreview.value = editor.getText().substring(0, 200) + '...'
    },
  })

  // 初始化内容预览
  contentPreview.value = editor.getText().substring(0, 200) + '...'
})

onUnmounted(() => {
  if (editor) {
    editor.destroy()
  }
})

const toggleEditable = () => {
  isEditable.value = !isEditable.value
  if (editor) {
    editor.setEditable(isEditable.value)
  }
}

const clearContent = () => {
  if (editor) {
    editor.commands.clearContent()
    contentPreview.value = ''
  }
}

const loadSampleContent = () => {
  if (editor) {
    editor.commands.setContent(sampleContent)
    contentPreview.value = editor.getText().substring(0, 200) + '...'
  }
}
</script>

<style scoped lang="scss">
.menu-test-fixed {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;

  h1 {
    color: #2c3e50;
    margin-bottom: 1.5rem;
    text-align: center;
  }

  .test-instructions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;

    .instruction-item {
      background-color: #f8f9fa;
      border: 1px solid #e9ecef;
      border-radius: 0.5rem;
      padding: 1.5rem;

      h3 {
        margin-top: 0;
        margin-bottom: 1rem;
        color: #495057;
      }

      ul {
        margin-bottom: 0;
        padding-left: 1.5rem;
      }

      li {
        margin-bottom: 0.5rem;
        line-height: 1.5;
      }
    }
  }

  .editor-container {
    border: 2px solid #dee2e6;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 2rem;
    background-color: white;
    min-height: 400px;
    position: relative;

    .editor {
      min-height: 350px;

      :deep(.ProseMirror) {
        outline: none;
        padding: 1rem;
        line-height: 1.6;
        font-size: 1rem;

        h1, h2, h3 {
          margin: 1.5rem 0 1rem 0;
          color: #2c3e50;
          font-weight: 600;
        }

        h1 { font-size: 2rem; }
        h2 { font-size: 1.5rem; }
        h3 { font-size: 1.25rem; }

        p {
          margin: 0.75rem 0;
        }

        ul, ol {
          padding-left: 2rem;
          margin: 1rem 0;
        }

        li {
          margin: 0.25rem 0;
        }

        blockquote {
          border-left: 4px solid #007bff;
          padding-left: 1rem;
          margin: 1.5rem 0;
          color: #6c757d;
          font-style: italic;
        }

        pre {
          background-color: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 0.375rem;
          padding: 1rem;
          overflow-x: auto;
          font-family: 'Courier New', monospace;
        }

        code {
          background-color: #f8f9fa;
          padding: 0.2rem 0.4rem;
          border-radius: 0.25rem;
          font-family: 'Courier New', monospace;
          font-size: 0.9em;
        }
      }
    }
  }

  .test-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    justify-content: center;

    .control-btn {
      padding: 0.75rem 1.5rem;
      border: 1px solid #007bff;
      border-radius: 0.375rem;
      background-color: #007bff;
      color: white;
      cursor: pointer;
      transition: all 0.15s ease;
      font-size: 0.9rem;

      &:hover {
        background-color: #0056b3;
        border-color: #0056b3;
      }

      &:active {
        transform: translateY(1px);
      }
    }
  }

  .content-display {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1.5rem;

    h3 {
      margin-top: 0;
      margin-bottom: 1rem;
      color: #495057;
    }

    .content-preview {
      background-color: #ffffff;
      border: 1px solid #dee2e6;
      border-radius: 0.25rem;
      padding: 1rem;
      font-family: monospace;
      font-size: 0.875rem;
      line-height: 1.5;
      color: #495057;
      max-height: 150px;
      overflow-y: auto;
    }
  }
}

// 暗色主题支持
.dark-theme .menu-test-fixed {
  .test-instructions .instruction-item,
  .content-display {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;

    h3 {
      color: #e2e8f0;
    }
  }

  .editor-container {
    background-color: #2d3748;
    border-color: #4a5568;
  }

  .content-display .content-preview {
    background-color: #1a202c;
    border-color: #4a5568;
    color: #e2e8f0;
  }

  .test-controls .control-btn {
    background-color: #3182ce;
    border-color: #3182ce;

    &:hover {
      background-color: #2c5aa0;
      border-color: #2c5aa0;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .menu-test-fixed {
    padding: 1rem;

    .test-instructions {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    .test-controls {
      flex-direction: column;
      align-items: center;
    }
  }
}
</style>
