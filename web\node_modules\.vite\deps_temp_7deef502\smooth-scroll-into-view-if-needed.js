import __buffer_polyfill from 'vite-plugin-node-polyfills/shims/buffer'
globalThis.Buffer = globalThis.Buffer || __buffer_polyfill
import __global_polyfill from 'vite-plugin-node-polyfills/shims/global'
globalThis.global = globalThis.global || __global_polyfill
import __process_polyfill from 'vite-plugin-node-polyfills/shims/process'
globalThis.process = globalThis.process || __process_polyfill

import {
  __toESM,
  require_dist,
  require_dist2,
  require_dist3
} from "./chunk-ZMSOBIYE.js";

// node_modules/smooth-scroll-into-view-if-needed/dist/index.js
var import_dist7 = __toESM(require_dist());
var import_dist8 = __toESM(require_dist2());
var import_dist9 = __toESM(require_dist3());

// node_modules/scroll-into-view-if-needed/dist/index.js
var import_dist4 = __toESM(require_dist());
var import_dist5 = __toESM(require_dist2());
var import_dist6 = __toESM(require_dist3());

// node_modules/compute-scroll-into-view/dist/index.js
var import_dist = __toESM(require_dist());
var import_dist2 = __toESM(require_dist2());
var import_dist3 = __toESM(require_dist3());
var t = (t3) => "object" == typeof t3 && null != t3 && 1 === t3.nodeType;
var e = (t3, e3) => (!e3 || "hidden" !== t3) && ("visible" !== t3 && "clip" !== t3);
var n = (t3, n3) => {
  if (t3.clientHeight < t3.scrollHeight || t3.clientWidth < t3.scrollWidth) {
    const o4 = getComputedStyle(t3, null);
    return e(o4.overflowY, n3) || e(o4.overflowX, n3) || ((t4) => {
      const e3 = ((t5) => {
        if (!t5.ownerDocument || !t5.ownerDocument.defaultView) return null;
        try {
          return t5.ownerDocument.defaultView.frameElement;
        } catch (t6) {
          return null;
        }
      })(t4);
      return !!e3 && (e3.clientHeight < t4.scrollHeight || e3.clientWidth < t4.scrollWidth);
    })(t3);
  }
  return false;
};
var o = (t3, e3, n3, o4, l3, r3, i, s2) => r3 < t3 && i > e3 || r3 > t3 && i < e3 ? 0 : r3 <= t3 && s2 <= n3 || i >= e3 && s2 >= n3 ? r3 - t3 - o4 : i > e3 && s2 < n3 || r3 < t3 && s2 > n3 ? i - e3 + l3 : 0;
var l = (t3) => {
  const e3 = t3.parentElement;
  return null == e3 ? t3.getRootNode().host || null : e3;
};
var r = (e3, r3) => {
  var i, s2, d, h;
  if ("undefined" == typeof document) return [];
  const { scrollMode: c, block: f, inline: u, boundary: a, skipOverflowHiddenElements: g } = r3, p = "function" == typeof a ? a : (t3) => t3 !== a;
  if (!t(e3)) throw new TypeError("Invalid target");
  const m = document.scrollingElement || document.documentElement, w = [];
  let W = e3;
  for (; t(W) && p(W); ) {
    if (W = l(W), W === m) {
      w.push(W);
      break;
    }
    null != W && W === document.body && n(W) && !n(document.documentElement) || null != W && n(W, g) && w.push(W);
  }
  const b = null != (s2 = null == (i = window.visualViewport) ? void 0 : i.width) ? s2 : innerWidth, H = null != (h = null == (d = window.visualViewport) ? void 0 : d.height) ? h : innerHeight, { scrollX: y, scrollY: M } = window, { height: v, width: E, top: x, right: C, bottom: I, left: R } = e3.getBoundingClientRect(), { top: T, right: B, bottom: F, left: V } = ((t3) => {
    const e4 = window.getComputedStyle(t3);
    return { top: parseFloat(e4.scrollMarginTop) || 0, right: parseFloat(e4.scrollMarginRight) || 0, bottom: parseFloat(e4.scrollMarginBottom) || 0, left: parseFloat(e4.scrollMarginLeft) || 0 };
  })(e3);
  let k = "start" === f || "nearest" === f ? x - T : "end" === f ? I + F : x + v / 2 - T + F, D = "center" === u ? R + E / 2 - V + B : "end" === u ? C + B : R - V;
  const L = [];
  for (let t3 = 0; t3 < w.length; t3++) {
    const e4 = w[t3], { height: l3, width: r4, top: i2, right: s3, bottom: d2, left: h2 } = e4.getBoundingClientRect();
    if ("if-needed" === c && x >= 0 && R >= 0 && I <= H && C <= b && (e4 === m && !n(e4) || x >= i2 && I <= d2 && R >= h2 && C <= s3)) return L;
    const a2 = getComputedStyle(e4), g2 = parseInt(a2.borderLeftWidth, 10), p2 = parseInt(a2.borderTopWidth, 10), W2 = parseInt(a2.borderRightWidth, 10), T2 = parseInt(a2.borderBottomWidth, 10);
    let B2 = 0, F2 = 0;
    const V2 = "offsetWidth" in e4 ? e4.offsetWidth - e4.clientWidth - g2 - W2 : 0, S = "offsetHeight" in e4 ? e4.offsetHeight - e4.clientHeight - p2 - T2 : 0, X = "offsetWidth" in e4 ? 0 === e4.offsetWidth ? 0 : r4 / e4.offsetWidth : 0, Y = "offsetHeight" in e4 ? 0 === e4.offsetHeight ? 0 : l3 / e4.offsetHeight : 0;
    if (m === e4) B2 = "start" === f ? k : "end" === f ? k - H : "nearest" === f ? o(M, M + H, H, p2, T2, M + k, M + k + v, v) : k - H / 2, F2 = "start" === u ? D : "center" === u ? D - b / 2 : "end" === u ? D - b : o(y, y + b, b, g2, W2, y + D, y + D + E, E), B2 = Math.max(0, B2 + M), F2 = Math.max(0, F2 + y);
    else {
      B2 = "start" === f ? k - i2 - p2 : "end" === f ? k - d2 + T2 + S : "nearest" === f ? o(i2, d2, l3, p2, T2 + S, k, k + v, v) : k - (i2 + l3 / 2) + S / 2, F2 = "start" === u ? D - h2 - g2 : "center" === u ? D - (h2 + r4 / 2) + V2 / 2 : "end" === u ? D - s3 + W2 + V2 : o(h2, s3, r4, g2, W2 + V2, D, D + E, E);
      const { scrollLeft: t4, scrollTop: n3 } = e4;
      B2 = 0 === Y ? 0 : Math.max(0, Math.min(n3 + B2 / Y, e4.scrollHeight - l3 / Y + S)), F2 = 0 === X ? 0 : Math.max(0, Math.min(t4 + F2 / X, e4.scrollWidth - r4 / X + V2)), k += n3 - B2, D += t4 - F2;
    }
    L.push({ el: e4, top: B2, left: F2 });
  }
  return L;
};

// node_modules/scroll-into-view-if-needed/dist/index.js
var o2 = (t3) => false === t3 ? { block: "end", inline: "nearest" } : ((t4) => t4 === Object(t4) && 0 !== Object.keys(t4).length)(t3) ? t3 : { block: "start", inline: "nearest" };
function e2(e3, r3) {
  if (!e3.isConnected || !((t3) => {
    let o4 = t3;
    for (; o4 && o4.parentNode; ) {
      if (o4.parentNode === document) return true;
      o4 = o4.parentNode instanceof ShadowRoot ? o4.parentNode.host : o4.parentNode;
    }
    return false;
  })(e3)) return;
  const n3 = ((t3) => {
    const o4 = window.getComputedStyle(t3);
    return { top: parseFloat(o4.scrollMarginTop) || 0, right: parseFloat(o4.scrollMarginRight) || 0, bottom: parseFloat(o4.scrollMarginBottom) || 0, left: parseFloat(o4.scrollMarginLeft) || 0 };
  })(e3);
  if (((t3) => "object" == typeof t3 && "function" == typeof t3.behavior)(r3)) return r3.behavior(r(e3, r3));
  const l3 = "boolean" == typeof r3 || null == r3 ? void 0 : r3.behavior;
  for (const { el: a, top: i, left: s2 } of r(e3, o2(r3))) {
    const t3 = i - n3.top + n3.bottom, o4 = s2 - n3.left + n3.right;
    a.scroll({ top: t3, left: o4, behavior: l3 });
  }
}

// node_modules/smooth-scroll-into-view-if-needed/dist/index.js
var o3;
var t2 = () => (o3 || (o3 = "performance" in window ? performance.now.bind(performance) : Date.now), o3());
function l2(e3) {
  const o4 = t2(), n3 = Math.min((o4 - e3.startTime) / e3.duration, 1), r3 = e3.ease(n3), s2 = e3.startX + (e3.x - e3.startX) * r3, i = e3.startY + (e3.y - e3.startY) * r3;
  e3.method(s2, i, n3, r3), s2 !== e3.x || i !== e3.y ? requestAnimationFrame(() => l2(e3)) : e3.cb();
}
function n2(e3, o4, n3) {
  let r3 = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 600, s2 = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : (e4) => 1 + --e4 * e4 * e4 * e4 * e4, i = arguments.length > 5 ? arguments[5] : void 0, a = arguments.length > 6 ? arguments[6] : void 0;
  const c = e3, d = e3.scrollLeft, m = e3.scrollTop;
  l2({ scrollable: c, method: (o5, t3, l3, n4) => {
    const r4 = Math.ceil(o5), s3 = Math.ceil(t3);
    e3.scrollLeft = r4, e3.scrollTop = s3, null == a || a({ target: e3, elapsed: l3, value: n4, left: r4, top: s3 });
  }, startTime: t2(), startX: d, startY: m, x: o4, y: n3, duration: r3, ease: s2, cb: i });
}
var r2 = (e3) => e3 && !e3.behavior || "smooth" === e3.behavior;
var s = function(o4, t3) {
  const l3 = t3 || {};
  return r2(l3) ? e2(o4, { block: l3.block, inline: l3.inline, scrollMode: l3.scrollMode, boundary: l3.boundary, skipOverflowHiddenElements: l3.skipOverflowHiddenElements, behavior: (e3) => Promise.all(e3.reduce((e4, o5) => {
    let { el: t4, left: r3, top: s2 } = o5;
    const i = t4.scrollLeft, a = t4.scrollTop;
    return i === r3 && a === s2 ? e4 : [...e4, new Promise((e5) => n2(t4, r3, s2, l3.duration, l3.ease, () => e5({ el: t4, left: [i, r3], top: [a, s2] }), l3.onScrollChange))];
  }, [])) }) : Promise.resolve(e2(o4, t3));
};
export {
  s as default
};
//# sourceMappingURL=smooth-scroll-into-view-if-needed.js.map
