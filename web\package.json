{"name": "wen-web", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode dev", "build": "run-p type-check \"build-only {@}\" --", "build:dev": "vite build --mode dev", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build --mode prod", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "lint:check": "eslint . --max-warnings=0", "format": "prettier --write src/", "format:check": "prettier --check src/", "stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --fix", "stylelint:check": "stylelint \"src/**/*.{css,scss,vue}\"", "code-check": "run-p lint:check format:check stylelint:check type-check", "prepare": "husky"}, "dependencies": {"@stomp/stompjs": "^7.1.1", "@tiptap/core": "^2.11.0", "@tiptap/extension-blockquote": "^2.11.2", "@tiptap/extension-bold": "^2.11.2", "@tiptap/extension-bullet-list": "^2.11.2", "@tiptap/extension-character-count": "^2.11.5", "@tiptap/extension-code": "^2.11.2", "@tiptap/extension-code-block": "^2.11.2", "@tiptap/extension-code-block-lowlight": "^2.11.2", "@tiptap/extension-color": "^2.11.2", "@tiptap/extension-document": "^2.11.2", "@tiptap/extension-dropcursor": "^2.11.2", "@tiptap/extension-floating-menu": "^2.11.2", "@tiptap/extension-focus": "^2.11.2", "@tiptap/extension-font-family": "^2.11.2", "@tiptap/extension-gapcursor": "^2.11.2", "@tiptap/extension-heading": "^2.11.2", "@tiptap/extension-highlight": "^2.11.2", "@tiptap/extension-history": "^2.11.2", "@tiptap/extension-horizontal-rule": "^2.11.2", "@tiptap/extension-image": "^2.11.0", "@tiptap/extension-italic": "^2.11.2", "@tiptap/extension-link": "^2.11.2", "@tiptap/extension-list-item": "^2.11.2", "@tiptap/extension-mention": "^2.11.0", "@tiptap/extension-ordered-list": "^2.11.2", "@tiptap/extension-paragraph": "^2.11.2", "@tiptap/extension-placeholder": "^2.11.0", "@tiptap/extension-strike": "^2.11.2", "@tiptap/extension-subscript": "^2.11.2", "@tiptap/extension-superscript": "^2.11.2", "@tiptap/extension-table": "^2.11.2", "@tiptap/extension-table-row": "^2.11.2", "@tiptap/extension-task-item": "^2.11.2", "@tiptap/extension-task-list": "^2.11.2", "@tiptap/extension-text": "^2.11.2", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-text-style": "^2.11.2", "@tiptap/extension-typography": "^2.11.2", "@tiptap/extension-underline": "^2.11.2", "@tiptap/starter-kit": "^3.0.7", "@tiptap/suggestion": "^2.11.2", "@tiptap/vue-3": "^2.11.0", "@vicons/ionicons5": "^0.13.0", "@vicons/material": "^0.13.0", "axios": "^1.7.8", "lowlight": "^3.3.0", "pinia": "^2.3.0", "smooth-scroll-into-view-if-needed": "^2.0.2", "sockjs-client": "^1.6.1", "tippy.js": "^6.3.7", "tiptap-markdown": "^0.8.10", "vue": "^3.5.13", "vue-draggable-plus": "^0.6.0", "vue-router": "^4.4.5"}, "devDependencies": {"@commitlint/cli": "^19.8.1", "@commitlint/config-conventional": "^19.8.1", "@tsconfig/node22": "^22.0.0", "@types/jsdom": "^21.1.7", "@types/node": "^22.9.3", "@types/prismjs": "^1.26.5", "@types/sockjs-client": "^1.5.4", "@types/stompjs": "^2.3.9", "@vicons/antd": "^0.13.0", "@vicons/carbon": "^0.13.0", "@vicons/fluent": "^0.13.0", "@vicons/ionicons4": "^0.13.0", "@vicons/tabler": "^0.13.0", "@vitejs/plugin-vue": "^5.2.1", "@vitejs/plugin-vue-jsx": "^4.1.1", "@vitest/eslint-plugin": "1.1.10", "@vue/compiler-sfc": "^3.5.13", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.14.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-vue": "^9.30.0", "husky": "^9.1.7", "jsdom": "^25.0.1", "naive-ui": "^2.41.0", "npm-run-all2": "^7.0.1", "prettier": "^3.4.2", "rollup-plugin-visualizer": "^5.14.0", "sass-embedded": "^1.87.0", "stylelint": "^16.18.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard-scss": "^14.0.0", "terser": "^5.39.0", "ts-node": "^10.9.2", "typescript": "^5.7.2", "typescript-eslint": "^8.32.0", "vfonts": "^0.0.3", "vite": "^6.0.4", "vite-plugin-checker": "^0.8.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-vue-devtools": "^7.6.5", "vitest": "^2.1.5", "vue-tsc": "^2.1.10"}}