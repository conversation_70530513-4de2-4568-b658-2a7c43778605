{"version": 3, "sources": ["../../vite-plugin-node-polyfills/node_modules/.pnpm/process@0.11.10/node_modules/process/browser.js"], "sourcesContent": ["// shim for using process in browser\nvar process = module.exports = {};\n\n// cached from whatever global is present so that test runners that stub it\n// don't break things.  But we need to wrap it in a try catch in case it is\n// wrapped in strict mode code which doesn't define any globals.  It's inside a\n// function because try/catches deoptimize in certain engines.\n\nvar cachedSetTimeout;\nvar cachedClearTimeout;\n\nfunction defaultSetTimout() {\n    throw new Error('setTimeout has not been defined');\n}\nfunction defaultClearTimeout () {\n    throw new Error('clearTimeout has not been defined');\n}\n(function () {\n    try {\n        if (typeof setTimeout === 'function') {\n            cachedSetTimeout = setTimeout;\n        } else {\n            cachedSetTimeout = defaultSetTimout;\n        }\n    } catch (e) {\n        cachedSetTimeout = defaultSetTimout;\n    }\n    try {\n        if (typeof clearTimeout === 'function') {\n            cachedClearTimeout = clearTimeout;\n        } else {\n            cachedClearTimeout = defaultClearTimeout;\n        }\n    } catch (e) {\n        cachedClearTimeout = defaultClearTimeout;\n    }\n} ())\nfunction runTimeout(fun) {\n    if (cachedSetTimeout === setTimeout) {\n        //normal enviroments in sane situations\n        return setTimeout(fun, 0);\n    }\n    // if setTimeout wasn't available but was latter defined\n    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {\n        cachedSetTimeout = setTimeout;\n        return setTimeout(fun, 0);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedSetTimeout(fun, 0);\n    } catch(e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally\n            return cachedSetTimeout.call(null, fun, 0);\n        } catch(e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error\n            return cachedSetTimeout.call(this, fun, 0);\n        }\n    }\n\n\n}\nfunction runClearTimeout(marker) {\n    if (cachedClearTimeout === clearTimeout) {\n        //normal enviroments in sane situations\n        return clearTimeout(marker);\n    }\n    // if clearTimeout wasn't available but was latter defined\n    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {\n        cachedClearTimeout = clearTimeout;\n        return clearTimeout(marker);\n    }\n    try {\n        // when when somebody has screwed with setTimeout but no I.E. maddness\n        return cachedClearTimeout(marker);\n    } catch (e){\n        try {\n            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally\n            return cachedClearTimeout.call(null, marker);\n        } catch (e){\n            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.\n            // Some versions of I.E. have different rules for clearTimeout vs setTimeout\n            return cachedClearTimeout.call(this, marker);\n        }\n    }\n\n\n\n}\nvar queue = [];\nvar draining = false;\nvar currentQueue;\nvar queueIndex = -1;\n\nfunction cleanUpNextTick() {\n    if (!draining || !currentQueue) {\n        return;\n    }\n    draining = false;\n    if (currentQueue.length) {\n        queue = currentQueue.concat(queue);\n    } else {\n        queueIndex = -1;\n    }\n    if (queue.length) {\n        drainQueue();\n    }\n}\n\nfunction drainQueue() {\n    if (draining) {\n        return;\n    }\n    var timeout = runTimeout(cleanUpNextTick);\n    draining = true;\n\n    var len = queue.length;\n    while(len) {\n        currentQueue = queue;\n        queue = [];\n        while (++queueIndex < len) {\n            if (currentQueue) {\n                currentQueue[queueIndex].run();\n            }\n        }\n        queueIndex = -1;\n        len = queue.length;\n    }\n    currentQueue = null;\n    draining = false;\n    runClearTimeout(timeout);\n}\n\nprocess.nextTick = function (fun) {\n    var args = new Array(arguments.length - 1);\n    if (arguments.length > 1) {\n        for (var i = 1; i < arguments.length; i++) {\n            args[i - 1] = arguments[i];\n        }\n    }\n    queue.push(new Item(fun, args));\n    if (queue.length === 1 && !draining) {\n        runTimeout(drainQueue);\n    }\n};\n\n// v8 likes predictible objects\nfunction Item(fun, array) {\n    this.fun = fun;\n    this.array = array;\n}\nItem.prototype.run = function () {\n    this.fun.apply(null, this.array);\n};\nprocess.title = 'browser';\nprocess.browser = true;\nprocess.env = {};\nprocess.argv = [];\nprocess.version = ''; // empty string to avoid regexp issues\nprocess.versions = {};\n\nfunction noop() {}\n\nprocess.on = noop;\nprocess.addListener = noop;\nprocess.once = noop;\nprocess.off = noop;\nprocess.removeListener = noop;\nprocess.removeAllListeners = noop;\nprocess.emit = noop;\nprocess.prependListener = noop;\nprocess.prependOnceListener = noop;\n\nprocess.listeners = function (name) { return [] }\n\nprocess.binding = function (name) {\n    throw new Error('process.binding is not supported');\n};\n\nprocess.cwd = function () { return '/' };\nprocess.chdir = function (dir) {\n    throw new Error('process.chdir is not supported');\n};\nprocess.umask = function() { return 0; };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AACA,IAAI,UAAUA,QAAc,UAAG,CAAA;AAO/B,IAAI;AACJ,IAAI;AAEJ,SAAS,mBAAmB;AACxB,QAAM,IAAI,MAAM,iCAAiC;AACrD;AACA,SAAS,sBAAuB;AAC5B,QAAM,IAAI,MAAM,mCAAmC;AACvD;CACC,WAAY;AACT,MAAI;AACA,QAAI,OAAO,eAAe,YAAY;AAClC,yBAAmB;IAC/B,OAAe;AACH,yBAAmB;IAC/B;EACA,SAAa,GAAG;AACR,uBAAmB;EAC3B;AACI,MAAI;AACA,QAAI,OAAO,iBAAiB,YAAY;AACpC,2BAAqB;IACjC,OAAe;AACH,2BAAqB;IACjC;EACA,SAAa,GAAG;AACR,yBAAqB;EAC7B;AACA,GAAC;AACD,SAAS,WAAW,KAAK;AACrB,MAAI,qBAAqB,YAAY;AAEjC,WAAO,WAAW,KAAK,CAAC;EAChC;AAEI,OAAK,qBAAqB,oBAAoB,CAAC,qBAAqB,YAAY;AAC5E,uBAAmB;AACnB,WAAO,WAAW,KAAK,CAAC;EAChC;AACI,MAAI;AAEA,WAAO,iBAAiB,KAAK,CAAC;EACtC,SAAY,GAAE;AACN,QAAI;AAEA,aAAO,iBAAiB,KAAK,MAAM,KAAK,CAAC;IACrD,SAAgBC,IAAE;AAEN,aAAO,iBAAiB,KAAK,MAAM,KAAK,CAAC;IACrD;EACA;AAGA;AACA,SAAS,gBAAgB,QAAQ;AAC7B,MAAI,uBAAuB,cAAc;AAErC,WAAO,aAAa,MAAM;EAClC;AAEI,OAAK,uBAAuB,uBAAuB,CAAC,uBAAuB,cAAc;AACrF,yBAAqB;AACrB,WAAO,aAAa,MAAM;EAClC;AACI,MAAI;AAEA,WAAO,mBAAmB,MAAM;EACxC,SAAa,GAAE;AACP,QAAI;AAEA,aAAO,mBAAmB,KAAK,MAAM,MAAM;IACvD,SAAiBA,IAAE;AAGP,aAAO,mBAAmB,KAAK,MAAM,MAAM;IACvD;EACA;AAIA;AACA,IAAI,QAAQ,CAAA;AACZ,IAAI,WAAW;AACf,IAAI;AACJ,IAAI,aAAa;AAEjB,SAAS,kBAAkB;AACvB,MAAI,CAAC,YAAY,CAAC,cAAc;AAC5B;EACR;AACI,aAAW;AACX,MAAI,aAAa,QAAQ;AACrB,YAAQ,aAAa,OAAO,KAAK;EACzC,OAAW;AACH,iBAAa;EACrB;AACI,MAAI,MAAM,QAAQ;AACd,eAAU;EAClB;AACA;AAEA,SAAS,aAAa;AAClB,MAAI,UAAU;AACV;EACR;AACI,MAAI,UAAU,WAAW,eAAe;AACxC,aAAW;AAEX,MAAI,MAAM,MAAM;AAChB,SAAM,KAAK;AACP,mBAAe;AACf,YAAQ,CAAA;AACR,WAAO,EAAE,aAAa,KAAK;AACvB,UAAI,cAAc;AACd,qBAAa,UAAU,EAAE,IAAG;MAC5C;IACA;AACQ,iBAAa;AACb,UAAM,MAAM;EACpB;AACI,iBAAe;AACf,aAAW;AACX,kBAAgB,OAAO;AAC3B;AAEA,QAAQ,WAAW,SAAU,KAAK;AAC9B,MAAI,OAAO,IAAI,MAAM,UAAU,SAAS,CAAC;AACzC,MAAI,UAAU,SAAS,GAAG;AACtB,aAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACvC,WAAK,IAAI,CAAC,IAAI,UAAU,CAAC;IACrC;EACA;AACI,QAAM,KAAK,IAAI,KAAK,KAAK,IAAI,CAAC;AAC9B,MAAI,MAAM,WAAW,KAAK,CAAC,UAAU;AACjC,eAAW,UAAU;EAC7B;AACA;AAGA,SAAS,KAAK,KAAK,OAAO;AACtB,OAAK,MAAM;AACX,OAAK,QAAQ;AACjB;AACA,KAAK,UAAU,MAAM,WAAY;AAC7B,OAAK,IAAI,MAAM,MAAM,KAAK,KAAK;AACnC;AACA,QAAQ,QAAQ;AAChB,QAAQ,UAAU;AAClB,QAAQ,MAAM,CAAA;AACd,QAAQ,OAAO,CAAA;AACf,QAAQ,UAAU;AAClB,QAAQ,WAAW,CAAA;AAEnB,SAAS,OAAO;AAAA;AAEhB,QAAQ,KAAK;AACb,QAAQ,cAAc;AACtB,QAAQ,OAAO;AACf,QAAQ,MAAM;AACd,QAAQ,iBAAiB;AACzB,QAAQ,qBAAqB;AAC7B,QAAQ,OAAO;AACf,QAAQ,kBAAkB;AAC1B,QAAQ,sBAAsB;AAE9B,QAAQ,YAAY,SAAU,MAAM;AAAE,SAAO,CAAA;AAAE;AAE/C,QAAQ,UAAU,SAAU,MAAM;AAC9B,QAAM,IAAI,MAAM,kCAAkC;AACtD;AAEA,QAAQ,MAAM,WAAY;AAAE,SAAO;AAAG;AACtC,QAAQ,QAAQ,SAAU,KAAK;AAC3B,QAAM,IAAI,MAAM,gCAAgC;AACpD;AACA,QAAQ,QAAQ,WAAW;AAAE,SAAO;AAAE;;;", "names": ["browserModule", "e"]}