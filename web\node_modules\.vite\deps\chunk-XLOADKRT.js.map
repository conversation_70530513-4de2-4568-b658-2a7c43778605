{"version": 3, "sources": ["../../prosemirror-keymap/dist/index.js", "../../w3c-keyname/index.js", "../../prosemirror-commands/dist/index.js", "../../prosemirror-schema-list/dist/index.js"], "sourcesContent": ["import { keyName, base } from 'w3c-keyname';\nimport { Plugin } from 'prosemirror-state';\n\nconst mac = typeof navigator != \"undefined\" && /Mac|iP(hone|[oa]d)/.test(navigator.platform);\nconst windows = typeof navigator != \"undefined\" && /Win/.test(navigator.platform);\nfunction normalizeKeyName(name) {\n    let parts = name.split(/-(?!$)/), result = parts[parts.length - 1];\n    if (result == \"Space\")\n        result = \" \";\n    let alt, ctrl, shift, meta;\n    for (let i = 0; i < parts.length - 1; i++) {\n        let mod = parts[i];\n        if (/^(cmd|meta|m)$/i.test(mod))\n            meta = true;\n        else if (/^a(lt)?$/i.test(mod))\n            alt = true;\n        else if (/^(c|ctrl|control)$/i.test(mod))\n            ctrl = true;\n        else if (/^s(hift)?$/i.test(mod))\n            shift = true;\n        else if (/^mod$/i.test(mod)) {\n            if (mac)\n                meta = true;\n            else\n                ctrl = true;\n        }\n        else\n            throw new Error(\"Unrecognized modifier name: \" + mod);\n    }\n    if (alt)\n        result = \"Alt-\" + result;\n    if (ctrl)\n        result = \"Ctrl-\" + result;\n    if (meta)\n        result = \"Meta-\" + result;\n    if (shift)\n        result = \"Shift-\" + result;\n    return result;\n}\nfunction normalize(map) {\n    let copy = Object.create(null);\n    for (let prop in map)\n        copy[normalizeKeyName(prop)] = map[prop];\n    return copy;\n}\nfunction modifiers(name, event, shift = true) {\n    if (event.altKey)\n        name = \"Alt-\" + name;\n    if (event.ctrlKey)\n        name = \"Ctrl-\" + name;\n    if (event.metaKey)\n        name = \"Meta-\" + name;\n    if (shift && event.shiftKey)\n        name = \"Shift-\" + name;\n    return name;\n}\n/**\nCreate a keymap plugin for the given set of bindings.\n\nBindings should map key names to [command](https://prosemirror.net/docs/ref/#commands)-style\nfunctions, which will be called with `(EditorState, dispatch,\nEditorView)` arguments, and should return true when they've handled\nthe key. Note that the view argument isn't part of the command\nprotocol, but can be used as an escape hatch if a binding needs to\ndirectly interact with the UI.\n\nKey names may be strings like `\"Shift-Ctrl-Enter\"`—a key\nidentifier prefixed with zero or more modifiers. Key identifiers\nare based on the strings that can appear in\n[`KeyEvent.key`](https:developer.mozilla.org/en-US/docs/Web/API/KeyboardEvent/key).\nUse lowercase letters to refer to letter keys (or uppercase letters\nif you want shift to be held). You may use `\"Space\"` as an alias\nfor the `\" \"` name.\n\nModifiers can be given in any order. `Shift-` (or `s-`), `Alt-` (or\n`a-`), `Ctrl-` (or `c-` or `Control-`) and `Cmd-` (or `m-` or\n`Meta-`) are recognized. For characters that are created by holding\nshift, the `Shift-` prefix is implied, and should not be added\nexplicitly.\n\nYou can use `Mod-` as a shorthand for `Cmd-` on Mac and `Ctrl-` on\nother platforms.\n\nYou can add multiple keymap plugins to an editor. The order in\nwhich they appear determines their precedence (the ones early in\nthe array get to dispatch first).\n*/\nfunction keymap(bindings) {\n    return new Plugin({ props: { handleKeyDown: keydownHandler(bindings) } });\n}\n/**\nGiven a set of bindings (using the same format as\n[`keymap`](https://prosemirror.net/docs/ref/#keymap.keymap)), return a [keydown\nhandler](https://prosemirror.net/docs/ref/#view.EditorProps.handleKeyDown) that handles them.\n*/\nfunction keydownHandler(bindings) {\n    let map = normalize(bindings);\n    return function (view, event) {\n        let name = keyName(event), baseName, direct = map[modifiers(name, event)];\n        if (direct && direct(view.state, view.dispatch, view))\n            return true;\n        // A character key\n        if (name.length == 1 && name != \" \") {\n            if (event.shiftKey) {\n                // In case the name was already modified by shift, try looking\n                // it up without its shift modifier\n                let noShift = map[modifiers(name, event, false)];\n                if (noShift && noShift(view.state, view.dispatch, view))\n                    return true;\n            }\n            if ((event.altKey || event.metaKey || event.ctrlKey) &&\n                // Ctrl-Alt may be used for AltGr on Windows\n                !(windows && event.ctrlKey && event.altKey) &&\n                (baseName = base[event.keyCode]) && baseName != name) {\n                // Try falling back to the keyCode when there's a modifier\n                // active or the character produced isn't ASCII, and our table\n                // produces a different name from the the keyCode. See #668,\n                // #1060, #1529.\n                let fromCode = map[modifiers(baseName, event)];\n                if (fromCode && fromCode(view.state, view.dispatch, view))\n                    return true;\n            }\n        }\n        return false;\n    };\n}\n\nexport { keydownHandler, keymap };\n", "export var base = {\n  8: \"Backspace\",\n  9: \"Tab\",\n  10: \"Enter\",\n  12: \"NumLock\",\n  13: \"Enter\",\n  16: \"Shift\",\n  17: \"Control\",\n  18: \"Alt\",\n  20: \"CapsLock\",\n  27: \"Escape\",\n  32: \" \",\n  33: \"PageUp\",\n  34: \"PageDown\",\n  35: \"End\",\n  36: \"Home\",\n  37: \"ArrowLeft\",\n  38: \"ArrowUp\",\n  39: \"ArrowRight\",\n  40: \"ArrowDown\",\n  44: \"PrintScreen\",\n  45: \"Insert\",\n  46: \"Delete\",\n  59: \";\",\n  61: \"=\",\n  91: \"Meta\",\n  92: \"Meta\",\n  106: \"*\",\n  107: \"+\",\n  108: \",\",\n  109: \"-\",\n  110: \".\",\n  111: \"/\",\n  144: \"NumLock\",\n  145: \"ScrollLock\",\n  160: \"Shift\",\n  161: \"Shift\",\n  162: \"Control\",\n  163: \"Control\",\n  164: \"Alt\",\n  165: \"Alt\",\n  173: \"-\",\n  186: \";\",\n  187: \"=\",\n  188: \",\",\n  189: \"-\",\n  190: \".\",\n  191: \"/\",\n  192: \"`\",\n  219: \"[\",\n  220: \"\\\\\",\n  221: \"]\",\n  222: \"'\"\n}\n\nexport var shift = {\n  48: \")\",\n  49: \"!\",\n  50: \"@\",\n  51: \"#\",\n  52: \"$\",\n  53: \"%\",\n  54: \"^\",\n  55: \"&\",\n  56: \"*\",\n  57: \"(\",\n  59: \":\",\n  61: \"+\",\n  173: \"_\",\n  186: \":\",\n  187: \"+\",\n  188: \"<\",\n  189: \"_\",\n  190: \">\",\n  191: \"?\",\n  192: \"~\",\n  219: \"{\",\n  220: \"|\",\n  221: \"}\",\n  222: \"\\\"\"\n}\n\nvar mac = typeof navigator != \"undefined\" && /Mac/.test(navigator.platform)\nvar ie = typeof navigator != \"undefined\" && /MSIE \\d|Trident\\/(?:[7-9]|\\d{2,})\\..*rv:(\\d+)/.exec(navigator.userAgent)\n\n// Fill in the digit keys\nfor (var i = 0; i < 10; i++) base[48 + i] = base[96 + i] = String(i)\n\n// The function keys\nfor (var i = 1; i <= 24; i++) base[i + 111] = \"F\" + i\n\n// And the alphabetic keys\nfor (var i = 65; i <= 90; i++) {\n  base[i] = String.fromCharCode(i + 32)\n  shift[i] = String.fromCharCode(i)\n}\n\n// For each code that doesn't have a shift-equivalent, copy the base name\nfor (var code in base) if (!shift.hasOwnProperty(code)) shift[code] = base[code]\n\nexport function keyName(event) {\n  // On macOS, keys held with Shift and Cmd don't reflect the effect of Shift in `.key`.\n  // On IE, shift effect is never included in `.key`.\n  var ignoreKey = mac && event.metaKey && event.shiftKey && !event.ctrlKey && !event.altKey ||\n      ie && event.shiftKey && event.key && event.key.length == 1 ||\n      event.key == \"Unidentified\"\n  var name = (!ignoreKey && event.key) ||\n    (event.shiftKey ? shift : base)[event.keyCode] ||\n    event.key || \"Unidentified\"\n  // Edge sometimes produces wrong names (Issue #3)\n  if (name == \"Esc\") name = \"Escape\"\n  if (name == \"Del\") name = \"Delete\"\n  // https://developer.microsoft.com/en-us/microsoft-edge/platform/issues/8860571/\n  if (name == \"Left\") name = \"ArrowLeft\"\n  if (name == \"Up\") name = \"ArrowUp\"\n  if (name == \"Right\") name = \"ArrowRight\"\n  if (name == \"Down\") name = \"ArrowDown\"\n  return name\n}\n", "import { liftTarget, replaceStep, ReplaceStep, canJoin, joinPoint, canSplit, ReplaceAroundStep, findWrapping } from 'prosemirror-transform';\nimport { Slice, Fragment } from 'prosemirror-model';\nimport { NodeSelection, Selection, TextSelection, AllSelection, SelectionRange } from 'prosemirror-state';\n\n/**\nDelete the selection, if there is one.\n*/\nconst deleteSelection = (state, dispatch) => {\n    if (state.selection.empty)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.deleteSelection().scrollIntoView());\n    return true;\n};\nfunction atBlockStart(state, view) {\n    let { $cursor } = state.selection;\n    if (!$cursor || (view ? !view.endOfTextblock(\"backward\", state)\n        : $cursor.parentOffset > 0))\n        return null;\n    return $cursor;\n}\n/**\nIf the selection is empty and at the start of a textblock, try to\nreduce the distance between that block and the one before it—if\nthere's a block directly before it that can be joined, join them.\nIf not, try to move the selected block closer to the next one in\nthe document structure by lifting it out of its parent or moving it\ninto a parent of the previous block. Will use the view for accurate\n(bidi-aware) start-of-textblock detection if given.\n*/\nconst joinBackward = (state, dispatch, view) => {\n    let $cursor = atBlockStart(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutBefore($cursor);\n    // If there is no node before this, try to lift\n    if (!$cut) {\n        let range = $cursor.blockRange(), target = range && liftTarget(range);\n        if (target == null)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.lift(range, target).scrollIntoView());\n        return true;\n    }\n    let before = $cut.nodeBefore;\n    // Apply the joining algorithm\n    if (deleteBarrier(state, $cut, dispatch, -1))\n        return true;\n    // If the node below has no content and the node above is\n    // selectable, delete the node below and select the one above.\n    if ($cursor.parent.content.size == 0 &&\n        (textblockAt(before, \"end\") || NodeSelection.isSelectable(before))) {\n        for (let depth = $cursor.depth;; depth--) {\n            let delStep = replaceStep(state.doc, $cursor.before(depth), $cursor.after(depth), Slice.empty);\n            if (delStep && delStep.slice.size < delStep.to - delStep.from) {\n                if (dispatch) {\n                    let tr = state.tr.step(delStep);\n                    tr.setSelection(textblockAt(before, \"end\")\n                        ? Selection.findFrom(tr.doc.resolve(tr.mapping.map($cut.pos, -1)), -1)\n                        : NodeSelection.create(tr.doc, $cut.pos - before.nodeSize));\n                    dispatch(tr.scrollIntoView());\n                }\n                return true;\n            }\n            if (depth == 1 || $cursor.node(depth - 1).childCount > 1)\n                break;\n        }\n    }\n    // If the node before is an atom, delete it\n    if (before.isAtom && $cut.depth == $cursor.depth - 1) {\n        if (dispatch)\n            dispatch(state.tr.delete($cut.pos - before.nodeSize, $cut.pos).scrollIntoView());\n        return true;\n    }\n    return false;\n};\n/**\nA more limited form of [`joinBackward`](https://prosemirror.net/docs/ref/#commands.joinBackward)\nthat only tries to join the current textblock to the one before\nit, if the cursor is at the start of a textblock.\n*/\nconst joinTextblockBackward = (state, dispatch, view) => {\n    let $cursor = atBlockStart(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutBefore($cursor);\n    return $cut ? joinTextblocksAround(state, $cut, dispatch) : false;\n};\n/**\nA more limited form of [`joinForward`](https://prosemirror.net/docs/ref/#commands.joinForward)\nthat only tries to join the current textblock to the one after\nit, if the cursor is at the end of a textblock.\n*/\nconst joinTextblockForward = (state, dispatch, view) => {\n    let $cursor = atBlockEnd(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutAfter($cursor);\n    return $cut ? joinTextblocksAround(state, $cut, dispatch) : false;\n};\nfunction joinTextblocksAround(state, $cut, dispatch) {\n    let before = $cut.nodeBefore, beforeText = before, beforePos = $cut.pos - 1;\n    for (; !beforeText.isTextblock; beforePos--) {\n        if (beforeText.type.spec.isolating)\n            return false;\n        let child = beforeText.lastChild;\n        if (!child)\n            return false;\n        beforeText = child;\n    }\n    let after = $cut.nodeAfter, afterText = after, afterPos = $cut.pos + 1;\n    for (; !afterText.isTextblock; afterPos++) {\n        if (afterText.type.spec.isolating)\n            return false;\n        let child = afterText.firstChild;\n        if (!child)\n            return false;\n        afterText = child;\n    }\n    let step = replaceStep(state.doc, beforePos, afterPos, Slice.empty);\n    if (!step || step.from != beforePos ||\n        step instanceof ReplaceStep && step.slice.size >= afterPos - beforePos)\n        return false;\n    if (dispatch) {\n        let tr = state.tr.step(step);\n        tr.setSelection(TextSelection.create(tr.doc, beforePos));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n}\nfunction textblockAt(node, side, only = false) {\n    for (let scan = node; scan; scan = (side == \"start\" ? scan.firstChild : scan.lastChild)) {\n        if (scan.isTextblock)\n            return true;\n        if (only && scan.childCount != 1)\n            return false;\n    }\n    return false;\n}\n/**\nWhen the selection is empty and at the start of a textblock, select\nthe node before that textblock, if possible. This is intended to be\nbound to keys like backspace, after\n[`joinBackward`](https://prosemirror.net/docs/ref/#commands.joinBackward) or other deleting\ncommands, as a fall-back behavior when the schema doesn't allow\ndeletion at the selected point.\n*/\nconst selectNodeBackward = (state, dispatch, view) => {\n    let { $head, empty } = state.selection, $cut = $head;\n    if (!empty)\n        return false;\n    if ($head.parent.isTextblock) {\n        if (view ? !view.endOfTextblock(\"backward\", state) : $head.parentOffset > 0)\n            return false;\n        $cut = findCutBefore($head);\n    }\n    let node = $cut && $cut.nodeBefore;\n    if (!node || !NodeSelection.isSelectable(node))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.setSelection(NodeSelection.create(state.doc, $cut.pos - node.nodeSize)).scrollIntoView());\n    return true;\n};\nfunction findCutBefore($pos) {\n    if (!$pos.parent.type.spec.isolating)\n        for (let i = $pos.depth - 1; i >= 0; i--) {\n            if ($pos.index(i) > 0)\n                return $pos.doc.resolve($pos.before(i + 1));\n            if ($pos.node(i).type.spec.isolating)\n                break;\n        }\n    return null;\n}\nfunction atBlockEnd(state, view) {\n    let { $cursor } = state.selection;\n    if (!$cursor || (view ? !view.endOfTextblock(\"forward\", state)\n        : $cursor.parentOffset < $cursor.parent.content.size))\n        return null;\n    return $cursor;\n}\n/**\nIf the selection is empty and the cursor is at the end of a\ntextblock, try to reduce or remove the boundary between that block\nand the one after it, either by joining them or by moving the other\nblock closer to this one in the tree structure. Will use the view\nfor accurate start-of-textblock detection if given.\n*/\nconst joinForward = (state, dispatch, view) => {\n    let $cursor = atBlockEnd(state, view);\n    if (!$cursor)\n        return false;\n    let $cut = findCutAfter($cursor);\n    // If there is no node after this, there's nothing to do\n    if (!$cut)\n        return false;\n    let after = $cut.nodeAfter;\n    // Try the joining algorithm\n    if (deleteBarrier(state, $cut, dispatch, 1))\n        return true;\n    // If the node above has no content and the node below is\n    // selectable, delete the node above and select the one below.\n    if ($cursor.parent.content.size == 0 &&\n        (textblockAt(after, \"start\") || NodeSelection.isSelectable(after))) {\n        let delStep = replaceStep(state.doc, $cursor.before(), $cursor.after(), Slice.empty);\n        if (delStep && delStep.slice.size < delStep.to - delStep.from) {\n            if (dispatch) {\n                let tr = state.tr.step(delStep);\n                tr.setSelection(textblockAt(after, \"start\") ? Selection.findFrom(tr.doc.resolve(tr.mapping.map($cut.pos)), 1)\n                    : NodeSelection.create(tr.doc, tr.mapping.map($cut.pos)));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n    }\n    // If the next node is an atom, delete it\n    if (after.isAtom && $cut.depth == $cursor.depth - 1) {\n        if (dispatch)\n            dispatch(state.tr.delete($cut.pos, $cut.pos + after.nodeSize).scrollIntoView());\n        return true;\n    }\n    return false;\n};\n/**\nWhen the selection is empty and at the end of a textblock, select\nthe node coming after that textblock, if possible. This is intended\nto be bound to keys like delete, after\n[`joinForward`](https://prosemirror.net/docs/ref/#commands.joinForward) and similar deleting\ncommands, to provide a fall-back behavior when the schema doesn't\nallow deletion at the selected point.\n*/\nconst selectNodeForward = (state, dispatch, view) => {\n    let { $head, empty } = state.selection, $cut = $head;\n    if (!empty)\n        return false;\n    if ($head.parent.isTextblock) {\n        if (view ? !view.endOfTextblock(\"forward\", state) : $head.parentOffset < $head.parent.content.size)\n            return false;\n        $cut = findCutAfter($head);\n    }\n    let node = $cut && $cut.nodeAfter;\n    if (!node || !NodeSelection.isSelectable(node))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.setSelection(NodeSelection.create(state.doc, $cut.pos)).scrollIntoView());\n    return true;\n};\nfunction findCutAfter($pos) {\n    if (!$pos.parent.type.spec.isolating)\n        for (let i = $pos.depth - 1; i >= 0; i--) {\n            let parent = $pos.node(i);\n            if ($pos.index(i) + 1 < parent.childCount)\n                return $pos.doc.resolve($pos.after(i + 1));\n            if (parent.type.spec.isolating)\n                break;\n        }\n    return null;\n}\n/**\nJoin the selected block or, if there is a text selection, the\nclosest ancestor block of the selection that can be joined, with\nthe sibling above it.\n*/\nconst joinUp = (state, dispatch) => {\n    let sel = state.selection, nodeSel = sel instanceof NodeSelection, point;\n    if (nodeSel) {\n        if (sel.node.isTextblock || !canJoin(state.doc, sel.from))\n            return false;\n        point = sel.from;\n    }\n    else {\n        point = joinPoint(state.doc, sel.from, -1);\n        if (point == null)\n            return false;\n    }\n    if (dispatch) {\n        let tr = state.tr.join(point);\n        if (nodeSel)\n            tr.setSelection(NodeSelection.create(tr.doc, point - state.doc.resolve(point).nodeBefore.nodeSize));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nJoin the selected block, or the closest ancestor of the selection\nthat can be joined, with the sibling after it.\n*/\nconst joinDown = (state, dispatch) => {\n    let sel = state.selection, point;\n    if (sel instanceof NodeSelection) {\n        if (sel.node.isTextblock || !canJoin(state.doc, sel.to))\n            return false;\n        point = sel.to;\n    }\n    else {\n        point = joinPoint(state.doc, sel.to, 1);\n        if (point == null)\n            return false;\n    }\n    if (dispatch)\n        dispatch(state.tr.join(point).scrollIntoView());\n    return true;\n};\n/**\nLift the selected block, or the closest ancestor block of the\nselection that can be lifted, out of its parent node.\n*/\nconst lift = (state, dispatch) => {\n    let { $from, $to } = state.selection;\n    let range = $from.blockRange($to), target = range && liftTarget(range);\n    if (target == null)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.lift(range, target).scrollIntoView());\n    return true;\n};\n/**\nIf the selection is in a node whose type has a truthy\n[`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) property in its spec, replace the\nselection with a newline character.\n*/\nconst newlineInCode = (state, dispatch) => {\n    let { $head, $anchor } = state.selection;\n    if (!$head.parent.type.spec.code || !$head.sameParent($anchor))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.insertText(\"\\n\").scrollIntoView());\n    return true;\n};\nfunction defaultBlockAt(match) {\n    for (let i = 0; i < match.edgeCount; i++) {\n        let { type } = match.edge(i);\n        if (type.isTextblock && !type.hasRequiredAttrs())\n            return type;\n    }\n    return null;\n}\n/**\nWhen the selection is in a node with a truthy\n[`code`](https://prosemirror.net/docs/ref/#model.NodeSpec.code) property in its spec, create a\ndefault block after the code block, and move the cursor there.\n*/\nconst exitCode = (state, dispatch) => {\n    let { $head, $anchor } = state.selection;\n    if (!$head.parent.type.spec.code || !$head.sameParent($anchor))\n        return false;\n    let above = $head.node(-1), after = $head.indexAfter(-1), type = defaultBlockAt(above.contentMatchAt(after));\n    if (!type || !above.canReplaceWith(after, after, type))\n        return false;\n    if (dispatch) {\n        let pos = $head.after(), tr = state.tr.replaceWith(pos, pos, type.createAndFill());\n        tr.setSelection(Selection.near(tr.doc.resolve(pos), 1));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nIf a block node is selected, create an empty paragraph before (if\nit is its parent's first child) or after it.\n*/\nconst createParagraphNear = (state, dispatch) => {\n    let sel = state.selection, { $from, $to } = sel;\n    if (sel instanceof AllSelection || $from.parent.inlineContent || $to.parent.inlineContent)\n        return false;\n    let type = defaultBlockAt($to.parent.contentMatchAt($to.indexAfter()));\n    if (!type || !type.isTextblock)\n        return false;\n    if (dispatch) {\n        let side = (!$from.parentOffset && $to.index() < $to.parent.childCount ? $from : $to).pos;\n        let tr = state.tr.insert(side, type.createAndFill());\n        tr.setSelection(TextSelection.create(tr.doc, side + 1));\n        dispatch(tr.scrollIntoView());\n    }\n    return true;\n};\n/**\nIf the cursor is in an empty textblock that can be lifted, lift the\nblock.\n*/\nconst liftEmptyBlock = (state, dispatch) => {\n    let { $cursor } = state.selection;\n    if (!$cursor || $cursor.parent.content.size)\n        return false;\n    if ($cursor.depth > 1 && $cursor.after() != $cursor.end(-1)) {\n        let before = $cursor.before();\n        if (canSplit(state.doc, before)) {\n            if (dispatch)\n                dispatch(state.tr.split(before).scrollIntoView());\n            return true;\n        }\n    }\n    let range = $cursor.blockRange(), target = range && liftTarget(range);\n    if (target == null)\n        return false;\n    if (dispatch)\n        dispatch(state.tr.lift(range, target).scrollIntoView());\n    return true;\n};\n/**\nCreate a variant of [`splitBlock`](https://prosemirror.net/docs/ref/#commands.splitBlock) that uses\na custom function to determine the type of the newly split off block.\n*/\nfunction splitBlockAs(splitNode) {\n    return (state, dispatch) => {\n        let { $from, $to } = state.selection;\n        if (state.selection instanceof NodeSelection && state.selection.node.isBlock) {\n            if (!$from.parentOffset || !canSplit(state.doc, $from.pos))\n                return false;\n            if (dispatch)\n                dispatch(state.tr.split($from.pos).scrollIntoView());\n            return true;\n        }\n        if (!$from.depth)\n            return false;\n        let types = [];\n        let splitDepth, deflt, atEnd = false, atStart = false;\n        for (let d = $from.depth;; d--) {\n            let node = $from.node(d);\n            if (node.isBlock) {\n                atEnd = $from.end(d) == $from.pos + ($from.depth - d);\n                atStart = $from.start(d) == $from.pos - ($from.depth - d);\n                deflt = defaultBlockAt($from.node(d - 1).contentMatchAt($from.indexAfter(d - 1)));\n                let splitType = splitNode && splitNode($to.parent, atEnd, $from);\n                types.unshift(splitType || (atEnd && deflt ? { type: deflt } : null));\n                splitDepth = d;\n                break;\n            }\n            else {\n                if (d == 1)\n                    return false;\n                types.unshift(null);\n            }\n        }\n        let tr = state.tr;\n        if (state.selection instanceof TextSelection || state.selection instanceof AllSelection)\n            tr.deleteSelection();\n        let splitPos = tr.mapping.map($from.pos);\n        let can = canSplit(tr.doc, splitPos, types.length, types);\n        if (!can) {\n            types[0] = deflt ? { type: deflt } : null;\n            can = canSplit(tr.doc, splitPos, types.length, types);\n        }\n        if (!can)\n            return false;\n        tr.split(splitPos, types.length, types);\n        if (!atEnd && atStart && $from.node(splitDepth).type != deflt) {\n            let first = tr.mapping.map($from.before(splitDepth)), $first = tr.doc.resolve(first);\n            if (deflt && $from.node(splitDepth - 1).canReplaceWith($first.index(), $first.index() + 1, deflt))\n                tr.setNodeMarkup(tr.mapping.map($from.before(splitDepth)), deflt);\n        }\n        if (dispatch)\n            dispatch(tr.scrollIntoView());\n        return true;\n    };\n}\n/**\nSplit the parent block of the selection. If the selection is a text\nselection, also delete its content.\n*/\nconst splitBlock = splitBlockAs();\n/**\nActs like [`splitBlock`](https://prosemirror.net/docs/ref/#commands.splitBlock), but without\nresetting the set of active marks at the cursor.\n*/\nconst splitBlockKeepMarks = (state, dispatch) => {\n    return splitBlock(state, dispatch && (tr => {\n        let marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks());\n        if (marks)\n            tr.ensureMarks(marks);\n        dispatch(tr);\n    }));\n};\n/**\nMove the selection to the node wrapping the current selection, if\nany. (Will not select the document node.)\n*/\nconst selectParentNode = (state, dispatch) => {\n    let { $from, to } = state.selection, pos;\n    let same = $from.sharedDepth(to);\n    if (same == 0)\n        return false;\n    pos = $from.before(same);\n    if (dispatch)\n        dispatch(state.tr.setSelection(NodeSelection.create(state.doc, pos)));\n    return true;\n};\n/**\nSelect the whole document.\n*/\nconst selectAll = (state, dispatch) => {\n    if (dispatch)\n        dispatch(state.tr.setSelection(new AllSelection(state.doc)));\n    return true;\n};\nfunction joinMaybeClear(state, $pos, dispatch) {\n    let before = $pos.nodeBefore, after = $pos.nodeAfter, index = $pos.index();\n    if (!before || !after || !before.type.compatibleContent(after.type))\n        return false;\n    if (!before.content.size && $pos.parent.canReplace(index - 1, index)) {\n        if (dispatch)\n            dispatch(state.tr.delete($pos.pos - before.nodeSize, $pos.pos).scrollIntoView());\n        return true;\n    }\n    if (!$pos.parent.canReplace(index, index + 1) || !(after.isTextblock || canJoin(state.doc, $pos.pos)))\n        return false;\n    if (dispatch)\n        dispatch(state.tr.join($pos.pos).scrollIntoView());\n    return true;\n}\nfunction deleteBarrier(state, $cut, dispatch, dir) {\n    let before = $cut.nodeBefore, after = $cut.nodeAfter, conn, match;\n    let isolated = before.type.spec.isolating || after.type.spec.isolating;\n    if (!isolated && joinMaybeClear(state, $cut, dispatch))\n        return true;\n    let canDelAfter = !isolated && $cut.parent.canReplace($cut.index(), $cut.index() + 1);\n    if (canDelAfter &&\n        (conn = (match = before.contentMatchAt(before.childCount)).findWrapping(after.type)) &&\n        match.matchType(conn[0] || after.type).validEnd) {\n        if (dispatch) {\n            let end = $cut.pos + after.nodeSize, wrap = Fragment.empty;\n            for (let i = conn.length - 1; i >= 0; i--)\n                wrap = Fragment.from(conn[i].create(null, wrap));\n            wrap = Fragment.from(before.copy(wrap));\n            let tr = state.tr.step(new ReplaceAroundStep($cut.pos - 1, end, $cut.pos, end, new Slice(wrap, 1, 0), conn.length, true));\n            let $joinAt = tr.doc.resolve(end + 2 * conn.length);\n            if ($joinAt.nodeAfter && $joinAt.nodeAfter.type == before.type &&\n                canJoin(tr.doc, $joinAt.pos))\n                tr.join($joinAt.pos);\n            dispatch(tr.scrollIntoView());\n        }\n        return true;\n    }\n    let selAfter = after.type.spec.isolating || (dir > 0 && isolated) ? null : Selection.findFrom($cut, 1);\n    let range = selAfter && selAfter.$from.blockRange(selAfter.$to), target = range && liftTarget(range);\n    if (target != null && target >= $cut.depth) {\n        if (dispatch)\n            dispatch(state.tr.lift(range, target).scrollIntoView());\n        return true;\n    }\n    if (canDelAfter && textblockAt(after, \"start\", true) && textblockAt(before, \"end\")) {\n        let at = before, wrap = [];\n        for (;;) {\n            wrap.push(at);\n            if (at.isTextblock)\n                break;\n            at = at.lastChild;\n        }\n        let afterText = after, afterDepth = 1;\n        for (; !afterText.isTextblock; afterText = afterText.firstChild)\n            afterDepth++;\n        if (at.canReplace(at.childCount, at.childCount, afterText.content)) {\n            if (dispatch) {\n                let end = Fragment.empty;\n                for (let i = wrap.length - 1; i >= 0; i--)\n                    end = Fragment.from(wrap[i].copy(end));\n                let tr = state.tr.step(new ReplaceAroundStep($cut.pos - wrap.length, $cut.pos + after.nodeSize, $cut.pos + afterDepth, $cut.pos + after.nodeSize - afterDepth, new Slice(end, wrap.length, 0), 0, true));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n    }\n    return false;\n}\nfunction selectTextblockSide(side) {\n    return function (state, dispatch) {\n        let sel = state.selection, $pos = side < 0 ? sel.$from : sel.$to;\n        let depth = $pos.depth;\n        while ($pos.node(depth).isInline) {\n            if (!depth)\n                return false;\n            depth--;\n        }\n        if (!$pos.node(depth).isTextblock)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.setSelection(TextSelection.create(state.doc, side < 0 ? $pos.start(depth) : $pos.end(depth))));\n        return true;\n    };\n}\n/**\nMoves the cursor to the start of current text block.\n*/\nconst selectTextblockStart = selectTextblockSide(-1);\n/**\nMoves the cursor to the end of current text block.\n*/\nconst selectTextblockEnd = selectTextblockSide(1);\n// Parameterized commands\n/**\nWrap the selection in a node of the given type with the given\nattributes.\n*/\nfunction wrapIn(nodeType, attrs = null) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to), wrapping = range && findWrapping(range, nodeType, attrs);\n        if (!wrapping)\n            return false;\n        if (dispatch)\n            dispatch(state.tr.wrap(range, wrapping).scrollIntoView());\n        return true;\n    };\n}\n/**\nReturns a command that tries to set the selected textblocks to the\ngiven node type with the given attributes.\n*/\nfunction setBlockType(nodeType, attrs = null) {\n    return function (state, dispatch) {\n        let applicable = false;\n        for (let i = 0; i < state.selection.ranges.length && !applicable; i++) {\n            let { $from: { pos: from }, $to: { pos: to } } = state.selection.ranges[i];\n            state.doc.nodesBetween(from, to, (node, pos) => {\n                if (applicable)\n                    return false;\n                if (!node.isTextblock || node.hasMarkup(nodeType, attrs))\n                    return;\n                if (node.type == nodeType) {\n                    applicable = true;\n                }\n                else {\n                    let $pos = state.doc.resolve(pos), index = $pos.index();\n                    applicable = $pos.parent.canReplaceWith(index, index + 1, nodeType);\n                }\n            });\n        }\n        if (!applicable)\n            return false;\n        if (dispatch) {\n            let tr = state.tr;\n            for (let i = 0; i < state.selection.ranges.length; i++) {\n                let { $from: { pos: from }, $to: { pos: to } } = state.selection.ranges[i];\n                tr.setBlockType(from, to, nodeType, attrs);\n            }\n            dispatch(tr.scrollIntoView());\n        }\n        return true;\n    };\n}\nfunction markApplies(doc, ranges, type, enterAtoms) {\n    for (let i = 0; i < ranges.length; i++) {\n        let { $from, $to } = ranges[i];\n        let can = $from.depth == 0 ? doc.inlineContent && doc.type.allowsMarkType(type) : false;\n        doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n            if (can || !enterAtoms && node.isAtom && node.isInline && pos >= $from.pos && pos + node.nodeSize <= $to.pos)\n                return false;\n            can = node.inlineContent && node.type.allowsMarkType(type);\n        });\n        if (can)\n            return true;\n    }\n    return false;\n}\nfunction removeInlineAtoms(ranges) {\n    let result = [];\n    for (let i = 0; i < ranges.length; i++) {\n        let { $from, $to } = ranges[i];\n        $from.doc.nodesBetween($from.pos, $to.pos, (node, pos) => {\n            if (node.isAtom && node.content.size && node.isInline && pos >= $from.pos && pos + node.nodeSize <= $to.pos) {\n                if (pos + 1 > $from.pos)\n                    result.push(new SelectionRange($from, $from.doc.resolve(pos + 1)));\n                $from = $from.doc.resolve(pos + 1 + node.content.size);\n                return false;\n            }\n        });\n        if ($from.pos < $to.pos)\n            result.push(new SelectionRange($from, $to));\n    }\n    return result;\n}\n/**\nCreate a command function that toggles the given mark with the\ngiven attributes. Will return `false` when the current selection\ndoesn't support that mark. This will remove the mark if any marks\nof that type exist in the selection, or add it otherwise. If the\nselection is empty, this applies to the [stored\nmarks](https://prosemirror.net/docs/ref/#state.EditorState.storedMarks) instead of a range of the\ndocument.\n*/\nfunction toggleMark(markType, attrs = null, options) {\n    let removeWhenPresent = (options && options.removeWhenPresent) !== false;\n    let enterAtoms = (options && options.enterInlineAtoms) !== false;\n    let dropSpace = !(options && options.includeWhitespace);\n    return function (state, dispatch) {\n        let { empty, $cursor, ranges } = state.selection;\n        if ((empty && !$cursor) || !markApplies(state.doc, ranges, markType, enterAtoms))\n            return false;\n        if (dispatch) {\n            if ($cursor) {\n                if (markType.isInSet(state.storedMarks || $cursor.marks()))\n                    dispatch(state.tr.removeStoredMark(markType));\n                else\n                    dispatch(state.tr.addStoredMark(markType.create(attrs)));\n            }\n            else {\n                let add, tr = state.tr;\n                if (!enterAtoms)\n                    ranges = removeInlineAtoms(ranges);\n                if (removeWhenPresent) {\n                    add = !ranges.some(r => state.doc.rangeHasMark(r.$from.pos, r.$to.pos, markType));\n                }\n                else {\n                    add = !ranges.every(r => {\n                        let missing = false;\n                        tr.doc.nodesBetween(r.$from.pos, r.$to.pos, (node, pos, parent) => {\n                            if (missing)\n                                return false;\n                            missing = !markType.isInSet(node.marks) && !!parent && parent.type.allowsMarkType(markType) &&\n                                !(node.isText && /^\\s*$/.test(node.textBetween(Math.max(0, r.$from.pos - pos), Math.min(node.nodeSize, r.$to.pos - pos))));\n                        });\n                        return !missing;\n                    });\n                }\n                for (let i = 0; i < ranges.length; i++) {\n                    let { $from, $to } = ranges[i];\n                    if (!add) {\n                        tr.removeMark($from.pos, $to.pos, markType);\n                    }\n                    else {\n                        let from = $from.pos, to = $to.pos, start = $from.nodeAfter, end = $to.nodeBefore;\n                        let spaceStart = dropSpace && start && start.isText ? /^\\s*/.exec(start.text)[0].length : 0;\n                        let spaceEnd = dropSpace && end && end.isText ? /\\s*$/.exec(end.text)[0].length : 0;\n                        if (from + spaceStart < to) {\n                            from += spaceStart;\n                            to -= spaceEnd;\n                        }\n                        tr.addMark(from, to, markType.create(attrs));\n                    }\n                }\n                dispatch(tr.scrollIntoView());\n            }\n        }\n        return true;\n    };\n}\nfunction wrapDispatchForJoin(dispatch, isJoinable) {\n    return (tr) => {\n        if (!tr.isGeneric)\n            return dispatch(tr);\n        let ranges = [];\n        for (let i = 0; i < tr.mapping.maps.length; i++) {\n            let map = tr.mapping.maps[i];\n            for (let j = 0; j < ranges.length; j++)\n                ranges[j] = map.map(ranges[j]);\n            map.forEach((_s, _e, from, to) => ranges.push(from, to));\n        }\n        // Figure out which joinable points exist inside those ranges,\n        // by checking all node boundaries in their parent nodes.\n        let joinable = [];\n        for (let i = 0; i < ranges.length; i += 2) {\n            let from = ranges[i], to = ranges[i + 1];\n            let $from = tr.doc.resolve(from), depth = $from.sharedDepth(to), parent = $from.node(depth);\n            for (let index = $from.indexAfter(depth), pos = $from.after(depth + 1); pos <= to; ++index) {\n                let after = parent.maybeChild(index);\n                if (!after)\n                    break;\n                if (index && joinable.indexOf(pos) == -1) {\n                    let before = parent.child(index - 1);\n                    if (before.type == after.type && isJoinable(before, after))\n                        joinable.push(pos);\n                }\n                pos += after.nodeSize;\n            }\n        }\n        // Join the joinable points\n        joinable.sort((a, b) => a - b);\n        for (let i = joinable.length - 1; i >= 0; i--) {\n            if (canJoin(tr.doc, joinable[i]))\n                tr.join(joinable[i]);\n        }\n        dispatch(tr);\n    };\n}\n/**\nWrap a command so that, when it produces a transform that causes\ntwo joinable nodes to end up next to each other, those are joined.\nNodes are considered joinable when they are of the same type and\nwhen the `isJoinable` predicate returns true for them or, if an\narray of strings was passed, if their node type name is in that\narray.\n*/\nfunction autoJoin(command, isJoinable) {\n    let canJoin = Array.isArray(isJoinable) ? (node) => isJoinable.indexOf(node.type.name) > -1\n        : isJoinable;\n    return (state, dispatch, view) => command(state, dispatch && wrapDispatchForJoin(dispatch, canJoin), view);\n}\n/**\nCombine a number of command functions into a single function (which\ncalls them one by one until one returns true).\n*/\nfunction chainCommands(...commands) {\n    return function (state, dispatch, view) {\n        for (let i = 0; i < commands.length; i++)\n            if (commands[i](state, dispatch, view))\n                return true;\n        return false;\n    };\n}\nlet backspace = chainCommands(deleteSelection, joinBackward, selectNodeBackward);\nlet del = chainCommands(deleteSelection, joinForward, selectNodeForward);\n/**\nA basic keymap containing bindings not specific to any schema.\nBinds the following keys (when multiple commands are listed, they\nare chained with [`chainCommands`](https://prosemirror.net/docs/ref/#commands.chainCommands)):\n\n* **Enter** to `newlineInCode`, `createParagraphNear`, `liftEmptyBlock`, `splitBlock`\n* **Mod-Enter** to `exitCode`\n* **Backspace** and **Mod-Backspace** to `deleteSelection`, `joinBackward`, `selectNodeBackward`\n* **Delete** and **Mod-Delete** to `deleteSelection`, `joinForward`, `selectNodeForward`\n* **Mod-Delete** to `deleteSelection`, `joinForward`, `selectNodeForward`\n* **Mod-a** to `selectAll`\n*/\nconst pcBaseKeymap = {\n    \"Enter\": chainCommands(newlineInCode, createParagraphNear, liftEmptyBlock, splitBlock),\n    \"Mod-Enter\": exitCode,\n    \"Backspace\": backspace,\n    \"Mod-Backspace\": backspace,\n    \"Shift-Backspace\": backspace,\n    \"Delete\": del,\n    \"Mod-Delete\": del,\n    \"Mod-a\": selectAll\n};\n/**\nA copy of `pcBaseKeymap` that also binds **Ctrl-h** like Backspace,\n**Ctrl-d** like Delete, **Alt-Backspace** like Ctrl-Backspace, and\n**Ctrl-Alt-Backspace**, **Alt-Delete**, and **Alt-d** like\nCtrl-Delete.\n*/\nconst macBaseKeymap = {\n    \"Ctrl-h\": pcBaseKeymap[\"Backspace\"],\n    \"Alt-Backspace\": pcBaseKeymap[\"Mod-Backspace\"],\n    \"Ctrl-d\": pcBaseKeymap[\"Delete\"],\n    \"Ctrl-Alt-Backspace\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Alt-Delete\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Alt-d\": pcBaseKeymap[\"Mod-Delete\"],\n    \"Ctrl-a\": selectTextblockStart,\n    \"Ctrl-e\": selectTextblockEnd\n};\nfor (let key in pcBaseKeymap)\n    macBaseKeymap[key] = pcBaseKeymap[key];\nconst mac = typeof navigator != \"undefined\" ? /Mac|iP(hone|[oa]d)/.test(navigator.platform)\n    // @ts-ignore\n    : typeof os != \"undefined\" && os.platform ? os.platform() == \"darwin\" : false;\n/**\nDepending on the detected platform, this will hold\n[`pcBasekeymap`](https://prosemirror.net/docs/ref/#commands.pcBaseKeymap) or\n[`macBaseKeymap`](https://prosemirror.net/docs/ref/#commands.macBaseKeymap).\n*/\nconst baseKeymap = mac ? macBaseKeymap : pcBaseKeymap;\n\nexport { autoJoin, baseKeymap, chainCommands, createParagraphNear, deleteSelection, exitCode, joinBackward, joinDown, joinForward, joinTextblockBackward, joinTextblockForward, joinUp, lift, liftEmptyBlock, macBaseKeymap, newlineInCode, pcBaseKeymap, selectAll, selectNodeBackward, selectNodeForward, selectParentNode, selectTextblockEnd, selectTextblockStart, setBlockType, splitBlock, splitBlockAs, splitBlockKeepMarks, toggleMark, wrapIn };\n", "import { findWrapping, ReplaceAroundStep, canSplit, liftTarget, canJoin } from 'prosemirror-transform';\nimport { NodeRange, Fragment, Slice } from 'prosemirror-model';\nimport { Selection } from 'prosemirror-state';\n\nconst olDOM = [\"ol\", 0], ulDOM = [\"ul\", 0], liDOM = [\"li\", 0];\n/**\nAn ordered list [node spec](https://prosemirror.net/docs/ref/#model.NodeSpec). Has a single\nattribute, `order`, which determines the number at which the list\nstarts counting, and defaults to 1. Represented as an `<ol>`\nelement.\n*/\nconst orderedList = {\n    attrs: { order: { default: 1, validate: \"number\" } },\n    parseDOM: [{ tag: \"ol\", getAttrs(dom) {\n                return { order: dom.hasAttribute(\"start\") ? +dom.getAttribute(\"start\") : 1 };\n            } }],\n    toDOM(node) {\n        return node.attrs.order == 1 ? olDOM : [\"ol\", { start: node.attrs.order }, 0];\n    }\n};\n/**\nA bullet list node spec, represented in the DOM as `<ul>`.\n*/\nconst bulletList = {\n    parseDOM: [{ tag: \"ul\" }],\n    toDOM() { return ulDOM; }\n};\n/**\nA list item (`<li>`) spec.\n*/\nconst listItem = {\n    parseDOM: [{ tag: \"li\" }],\n    toDOM() { return liDOM; },\n    defining: true\n};\nfunction add(obj, props) {\n    let copy = {};\n    for (let prop in obj)\n        copy[prop] = obj[prop];\n    for (let prop in props)\n        copy[prop] = props[prop];\n    return copy;\n}\n/**\nConvenience function for adding list-related node types to a map\nspecifying the nodes for a schema. Adds\n[`orderedList`](https://prosemirror.net/docs/ref/#schema-list.orderedList) as `\"ordered_list\"`,\n[`bulletList`](https://prosemirror.net/docs/ref/#schema-list.bulletList) as `\"bullet_list\"`, and\n[`listItem`](https://prosemirror.net/docs/ref/#schema-list.listItem) as `\"list_item\"`.\n\n`itemContent` determines the content expression for the list items.\nIf you want the commands defined in this module to apply to your\nlist structure, it should have a shape like `\"paragraph block*\"` or\n`\"paragraph (ordered_list | bullet_list)*\"`. `listGroup` can be\ngiven to assign a group name to the list node types, for example\n`\"block\"`.\n*/\nfunction addListNodes(nodes, itemContent, listGroup) {\n    return nodes.append({\n        ordered_list: add(orderedList, { content: \"list_item+\", group: listGroup }),\n        bullet_list: add(bulletList, { content: \"list_item+\", group: listGroup }),\n        list_item: add(listItem, { content: itemContent })\n    });\n}\n/**\nReturns a command function that wraps the selection in a list with\nthe given type an attributes. If `dispatch` is null, only return a\nvalue to indicate whether this is possible, but don't actually\nperform the change.\n*/\nfunction wrapInList(listType, attrs = null) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to);\n        if (!range)\n            return false;\n        let tr = dispatch ? state.tr : null;\n        if (!wrapRangeInList(tr, range, listType, attrs))\n            return false;\n        if (dispatch)\n            dispatch(tr.scrollIntoView());\n        return true;\n    };\n}\n/**\nTry to wrap the given node range in a list of the given type.\nReturn `true` when this is possible, `false` otherwise. When `tr`\nis non-null, the wrapping is added to that transaction. When it is\n`null`, the function only queries whether the wrapping is\npossible.\n*/\nfunction wrapRangeInList(tr, range, listType, attrs = null) {\n    let doJoin = false, outerRange = range, doc = range.$from.doc;\n    // This is at the top of an existing list item\n    if (range.depth >= 2 && range.$from.node(range.depth - 1).type.compatibleContent(listType) && range.startIndex == 0) {\n        // Don't do anything if this is the top of the list\n        if (range.$from.index(range.depth - 1) == 0)\n            return false;\n        let $insert = doc.resolve(range.start - 2);\n        outerRange = new NodeRange($insert, $insert, range.depth);\n        if (range.endIndex < range.parent.childCount)\n            range = new NodeRange(range.$from, doc.resolve(range.$to.end(range.depth)), range.depth);\n        doJoin = true;\n    }\n    let wrap = findWrapping(outerRange, listType, attrs, range);\n    if (!wrap)\n        return false;\n    if (tr)\n        doWrapInList(tr, range, wrap, doJoin, listType);\n    return true;\n}\nfunction doWrapInList(tr, range, wrappers, joinBefore, listType) {\n    let content = Fragment.empty;\n    for (let i = wrappers.length - 1; i >= 0; i--)\n        content = Fragment.from(wrappers[i].type.create(wrappers[i].attrs, content));\n    tr.step(new ReplaceAroundStep(range.start - (joinBefore ? 2 : 0), range.end, range.start, range.end, new Slice(content, 0, 0), wrappers.length, true));\n    let found = 0;\n    for (let i = 0; i < wrappers.length; i++)\n        if (wrappers[i].type == listType)\n            found = i + 1;\n    let splitDepth = wrappers.length - found;\n    let splitPos = range.start + wrappers.length - (joinBefore ? 2 : 0), parent = range.parent;\n    for (let i = range.startIndex, e = range.endIndex, first = true; i < e; i++, first = false) {\n        if (!first && canSplit(tr.doc, splitPos, splitDepth)) {\n            tr.split(splitPos, splitDepth);\n            splitPos += 2 * splitDepth;\n        }\n        splitPos += parent.child(i).nodeSize;\n    }\n    return tr;\n}\n/**\nBuild a command that splits a non-empty textblock at the top level\nof a list item by also splitting that list item.\n*/\nfunction splitListItem(itemType, itemAttrs) {\n    return function (state, dispatch) {\n        let { $from, $to, node } = state.selection;\n        if ((node && node.isBlock) || $from.depth < 2 || !$from.sameParent($to))\n            return false;\n        let grandParent = $from.node(-1);\n        if (grandParent.type != itemType)\n            return false;\n        if ($from.parent.content.size == 0 && $from.node(-1).childCount == $from.indexAfter(-1)) {\n            // In an empty block. If this is a nested list, the wrapping\n            // list item should be split. Otherwise, bail out and let next\n            // command handle lifting.\n            if ($from.depth == 3 || $from.node(-3).type != itemType ||\n                $from.index(-2) != $from.node(-2).childCount - 1)\n                return false;\n            if (dispatch) {\n                let wrap = Fragment.empty;\n                let depthBefore = $from.index(-1) ? 1 : $from.index(-2) ? 2 : 3;\n                // Build a fragment containing empty versions of the structure\n                // from the outer list item to the parent node of the cursor\n                for (let d = $from.depth - depthBefore; d >= $from.depth - 3; d--)\n                    wrap = Fragment.from($from.node(d).copy(wrap));\n                let depthAfter = $from.indexAfter(-1) < $from.node(-2).childCount ? 1\n                    : $from.indexAfter(-2) < $from.node(-3).childCount ? 2 : 3;\n                // Add a second list item with an empty default start node\n                wrap = wrap.append(Fragment.from(itemType.createAndFill()));\n                let start = $from.before($from.depth - (depthBefore - 1));\n                let tr = state.tr.replace(start, $from.after(-depthAfter), new Slice(wrap, 4 - depthBefore, 0));\n                let sel = -1;\n                tr.doc.nodesBetween(start, tr.doc.content.size, (node, pos) => {\n                    if (sel > -1)\n                        return false;\n                    if (node.isTextblock && node.content.size == 0)\n                        sel = pos + 1;\n                });\n                if (sel > -1)\n                    tr.setSelection(Selection.near(tr.doc.resolve(sel)));\n                dispatch(tr.scrollIntoView());\n            }\n            return true;\n        }\n        let nextType = $to.pos == $from.end() ? grandParent.contentMatchAt(0).defaultType : null;\n        let tr = state.tr.delete($from.pos, $to.pos);\n        let types = nextType ? [itemAttrs ? { type: itemType, attrs: itemAttrs } : null, { type: nextType }] : undefined;\n        if (!canSplit(tr.doc, $from.pos, 2, types))\n            return false;\n        if (dispatch)\n            dispatch(tr.split($from.pos, 2, types).scrollIntoView());\n        return true;\n    };\n}\n/**\nActs like [`splitListItem`](https://prosemirror.net/docs/ref/#schema-list.splitListItem), but\nwithout resetting the set of active marks at the cursor.\n*/\nfunction splitListItemKeepMarks(itemType, itemAttrs) {\n    let split = splitListItem(itemType, itemAttrs);\n    return (state, dispatch) => {\n        return split(state, dispatch && (tr => {\n            let marks = state.storedMarks || (state.selection.$to.parentOffset && state.selection.$from.marks());\n            if (marks)\n                tr.ensureMarks(marks);\n            dispatch(tr);\n        }));\n    };\n}\n/**\nCreate a command to lift the list item around the selection up into\na wrapping list.\n*/\nfunction liftListItem(itemType) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to, node => node.childCount > 0 && node.firstChild.type == itemType);\n        if (!range)\n            return false;\n        if (!dispatch)\n            return true;\n        if ($from.node(range.depth - 1).type == itemType) // Inside a parent list\n            return liftToOuterList(state, dispatch, itemType, range);\n        else // Outer list node\n            return liftOutOfList(state, dispatch, range);\n    };\n}\nfunction liftToOuterList(state, dispatch, itemType, range) {\n    let tr = state.tr, end = range.end, endOfList = range.$to.end(range.depth);\n    if (end < endOfList) {\n        // There are siblings after the lifted items, which must become\n        // children of the last item\n        tr.step(new ReplaceAroundStep(end - 1, endOfList, end, endOfList, new Slice(Fragment.from(itemType.create(null, range.parent.copy())), 1, 0), 1, true));\n        range = new NodeRange(tr.doc.resolve(range.$from.pos), tr.doc.resolve(endOfList), range.depth);\n    }\n    const target = liftTarget(range);\n    if (target == null)\n        return false;\n    tr.lift(range, target);\n    let $after = tr.doc.resolve(tr.mapping.map(end, -1) - 1);\n    if (canJoin(tr.doc, $after.pos) && $after.nodeBefore.type == $after.nodeAfter.type)\n        tr.join($after.pos);\n    dispatch(tr.scrollIntoView());\n    return true;\n}\nfunction liftOutOfList(state, dispatch, range) {\n    let tr = state.tr, list = range.parent;\n    // Merge the list items into a single big item\n    for (let pos = range.end, i = range.endIndex - 1, e = range.startIndex; i > e; i--) {\n        pos -= list.child(i).nodeSize;\n        tr.delete(pos - 1, pos + 1);\n    }\n    let $start = tr.doc.resolve(range.start), item = $start.nodeAfter;\n    if (tr.mapping.map(range.end) != range.start + $start.nodeAfter.nodeSize)\n        return false;\n    let atStart = range.startIndex == 0, atEnd = range.endIndex == list.childCount;\n    let parent = $start.node(-1), indexBefore = $start.index(-1);\n    if (!parent.canReplace(indexBefore + (atStart ? 0 : 1), indexBefore + 1, item.content.append(atEnd ? Fragment.empty : Fragment.from(list))))\n        return false;\n    let start = $start.pos, end = start + item.nodeSize;\n    // Strip off the surrounding list. At the sides where we're not at\n    // the end of the list, the existing list is closed. At sides where\n    // this is the end, it is overwritten to its end.\n    tr.step(new ReplaceAroundStep(start - (atStart ? 1 : 0), end + (atEnd ? 1 : 0), start + 1, end - 1, new Slice((atStart ? Fragment.empty : Fragment.from(list.copy(Fragment.empty)))\n        .append(atEnd ? Fragment.empty : Fragment.from(list.copy(Fragment.empty))), atStart ? 0 : 1, atEnd ? 0 : 1), atStart ? 0 : 1));\n    dispatch(tr.scrollIntoView());\n    return true;\n}\n/**\nCreate a command to sink the list item around the selection down\ninto an inner list.\n*/\nfunction sinkListItem(itemType) {\n    return function (state, dispatch) {\n        let { $from, $to } = state.selection;\n        let range = $from.blockRange($to, node => node.childCount > 0 && node.firstChild.type == itemType);\n        if (!range)\n            return false;\n        let startIndex = range.startIndex;\n        if (startIndex == 0)\n            return false;\n        let parent = range.parent, nodeBefore = parent.child(startIndex - 1);\n        if (nodeBefore.type != itemType)\n            return false;\n        if (dispatch) {\n            let nestedBefore = nodeBefore.lastChild && nodeBefore.lastChild.type == parent.type;\n            let inner = Fragment.from(nestedBefore ? itemType.create() : null);\n            let slice = new Slice(Fragment.from(itemType.create(null, Fragment.from(parent.type.create(null, inner)))), nestedBefore ? 3 : 1, 0);\n            let before = range.start, after = range.end;\n            dispatch(state.tr.step(new ReplaceAroundStep(before - (nestedBefore ? 3 : 1), after, before, after, slice, 1, true))\n                .scrollIntoView());\n        }\n        return true;\n    };\n}\n\nexport { addListNodes, bulletList, liftListItem, listItem, orderedList, sinkListItem, splitListItem, splitListItemKeepMarks, wrapInList, wrapRangeInList };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;;;ACAA;AAAA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAO,IAAI,OAAO;AAAA,EAChB,GAAG;AAAA,EACH,GAAG;AAAA,EACH,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AAEO,IAAI,QAAQ;AAAA,EACjB,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AAEA,IAAI,MAAM,OAAO,aAAa,eAAe,MAAM,KAAK,UAAU,QAAQ;AAC1E,IAAI,KAAK,OAAO,aAAa,eAAe,gDAAgD,KAAK,UAAU,SAAS;AAGpH,KAAS,IAAI,GAAG,IAAI,IAAI,IAAK,MAAK,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,OAAO,CAAC;AAA1D;AAGT,KAAS,IAAI,GAAG,KAAK,IAAI,IAAK,MAAK,IAAI,GAAG,IAAI,MAAM;AAA3C;AAGT,KAAS,IAAI,IAAI,KAAK,IAAI,KAAK;AAC7B,OAAK,CAAC,IAAI,OAAO,aAAa,IAAI,EAAE;AACpC,QAAM,CAAC,IAAI,OAAO,aAAa,CAAC;AAClC;AAHS;AAMT,KAAS,QAAQ,KAAM,KAAI,CAAC,MAAM,eAAe,IAAI,EAAG,OAAM,IAAI,IAAI,KAAK,IAAI;AAAtE;AAEF,SAAS,QAAQ,OAAO;AAG7B,MAAI,YAAY,OAAO,MAAM,WAAW,MAAM,YAAY,CAAC,MAAM,WAAW,CAAC,MAAM,UAC/E,MAAM,MAAM,YAAY,MAAM,OAAO,MAAM,IAAI,UAAU,KACzD,MAAM,OAAO;AACjB,MAAI,OAAQ,CAAC,aAAa,MAAM,QAC7B,MAAM,WAAW,QAAQ,MAAM,MAAM,OAAO,KAC7C,MAAM,OAAO;AAEf,MAAI,QAAQ,MAAO,QAAO;AAC1B,MAAI,QAAQ,MAAO,QAAO;AAE1B,MAAI,QAAQ,OAAQ,QAAO;AAC3B,MAAI,QAAQ,KAAM,QAAO;AACzB,MAAI,QAAQ,QAAS,QAAO;AAC5B,MAAI,QAAQ,OAAQ,QAAO;AAC3B,SAAO;AACT;;;ADnHA,IAAMC,OAAM,OAAO,aAAa,eAAe,qBAAqB,KAAK,UAAU,QAAQ;AAC3F,IAAM,UAAU,OAAO,aAAa,eAAe,MAAM,KAAK,UAAU,QAAQ;AAChF,SAAS,iBAAiB,MAAM;AAC5B,MAAI,QAAQ,KAAK,MAAM,QAAQ,GAAG,SAAS,MAAM,MAAM,SAAS,CAAC;AACjE,MAAI,UAAU;AACV,aAAS;AACb,MAAI,KAAK,MAAMC,QAAO;AACtB,WAAS,IAAI,GAAG,IAAI,MAAM,SAAS,GAAG,KAAK;AACvC,QAAI,MAAM,MAAM,CAAC;AACjB,QAAI,kBAAkB,KAAK,GAAG;AAC1B,aAAO;AAAA,aACF,YAAY,KAAK,GAAG;AACzB,YAAM;AAAA,aACD,sBAAsB,KAAK,GAAG;AACnC,aAAO;AAAA,aACF,cAAc,KAAK,GAAG;AAC3B,MAAAA,SAAQ;AAAA,aACH,SAAS,KAAK,GAAG,GAAG;AACzB,UAAID;AACA,eAAO;AAAA;AAEP,eAAO;AAAA,IACf;AAEI,YAAM,IAAI,MAAM,iCAAiC,GAAG;AAAA,EAC5D;AACA,MAAI;AACA,aAAS,SAAS;AACtB,MAAI;AACA,aAAS,UAAU;AACvB,MAAI;AACA,aAAS,UAAU;AACvB,MAAIC;AACA,aAAS,WAAW;AACxB,SAAO;AACX;AACA,SAAS,UAAU,KAAK;AACpB,MAAI,OAAO,uBAAO,OAAO,IAAI;AAC7B,WAAS,QAAQ;AACb,SAAK,iBAAiB,IAAI,CAAC,IAAI,IAAI,IAAI;AAC3C,SAAO;AACX;AACA,SAAS,UAAU,MAAM,OAAOA,SAAQ,MAAM;AAC1C,MAAI,MAAM;AACN,WAAO,SAAS;AACpB,MAAI,MAAM;AACN,WAAO,UAAU;AACrB,MAAI,MAAM;AACN,WAAO,UAAU;AACrB,MAAIA,UAAS,MAAM;AACf,WAAO,WAAW;AACtB,SAAO;AACX;AAgCA,SAAS,OAAO,UAAU;AACtB,SAAO,IAAI,OAAO,EAAE,OAAO,EAAE,eAAe,eAAe,QAAQ,EAAE,EAAE,CAAC;AAC5E;AAMA,SAAS,eAAe,UAAU;AAC9B,MAAI,MAAM,UAAU,QAAQ;AAC5B,SAAO,SAAU,MAAM,OAAO;AAC1B,QAAI,OAAO,QAAQ,KAAK,GAAG,UAAU,SAAS,IAAI,UAAU,MAAM,KAAK,CAAC;AACxE,QAAI,UAAU,OAAO,KAAK,OAAO,KAAK,UAAU,IAAI;AAChD,aAAO;AAEX,QAAI,KAAK,UAAU,KAAK,QAAQ,KAAK;AACjC,UAAI,MAAM,UAAU;AAGhB,YAAI,UAAU,IAAI,UAAU,MAAM,OAAO,KAAK,CAAC;AAC/C,YAAI,WAAW,QAAQ,KAAK,OAAO,KAAK,UAAU,IAAI;AAClD,iBAAO;AAAA,MACf;AACA,WAAK,MAAM,UAAU,MAAM,WAAW,MAAM;AAAA,MAExC,EAAE,WAAW,MAAM,WAAW,MAAM,YACnC,WAAW,KAAK,MAAM,OAAO,MAAM,YAAY,MAAM;AAKtD,YAAI,WAAW,IAAI,UAAU,UAAU,KAAK,CAAC;AAC7C,YAAI,YAAY,SAAS,KAAK,OAAO,KAAK,UAAU,IAAI;AACpD,iBAAO;AAAA,MACf;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AACJ;;;AE7HA,IAAAC,eAAA;AAAA,IAAAA,eAAA;AAAA,IAAAA,eAAA;AAOA,IAAM,kBAAkB,CAAC,OAAO,aAAa;AACzC,MAAI,MAAM,UAAU;AAChB,WAAO;AACX,MAAI;AACA,aAAS,MAAM,GAAG,gBAAgB,EAAE,eAAe,CAAC;AACxD,SAAO;AACX;AACA,SAAS,aAAa,OAAO,MAAM;AAC/B,MAAI,EAAE,QAAQ,IAAI,MAAM;AACxB,MAAI,CAAC,YAAY,OAAO,CAAC,KAAK,eAAe,YAAY,KAAK,IACxD,QAAQ,eAAe;AACzB,WAAO;AACX,SAAO;AACX;AAUA,IAAM,eAAe,CAAC,OAAO,UAAU,SAAS;AAC5C,MAAI,UAAU,aAAa,OAAO,IAAI;AACtC,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,cAAc,OAAO;AAEhC,MAAI,CAAC,MAAM;AACP,QAAI,QAAQ,QAAQ,WAAW,GAAG,SAAS,SAAS,WAAW,KAAK;AACpE,QAAI,UAAU;AACV,aAAO;AACX,QAAI;AACA,eAAS,MAAM,GAAG,KAAK,OAAO,MAAM,EAAE,eAAe,CAAC;AAC1D,WAAO;AAAA,EACX;AACA,MAAI,SAAS,KAAK;AAElB,MAAI,cAAc,OAAO,MAAM,UAAU,EAAE;AACvC,WAAO;AAGX,MAAI,QAAQ,OAAO,QAAQ,QAAQ,MAC9B,YAAY,QAAQ,KAAK,KAAK,cAAc,aAAa,MAAM,IAAI;AACpE,aAAS,QAAQ,QAAQ,SAAQ,SAAS;AACtC,UAAI,UAAU,YAAY,MAAM,KAAK,QAAQ,OAAO,KAAK,GAAG,QAAQ,MAAM,KAAK,GAAG,MAAM,KAAK;AAC7F,UAAI,WAAW,QAAQ,MAAM,OAAO,QAAQ,KAAK,QAAQ,MAAM;AAC3D,YAAI,UAAU;AACV,cAAI,KAAK,MAAM,GAAG,KAAK,OAAO;AAC9B,aAAG,aAAa,YAAY,QAAQ,KAAK,IACnC,UAAU,SAAS,GAAG,IAAI,QAAQ,GAAG,QAAQ,IAAI,KAAK,KAAK,EAAE,CAAC,GAAG,EAAE,IACnE,cAAc,OAAO,GAAG,KAAK,KAAK,MAAM,OAAO,QAAQ,CAAC;AAC9D,mBAAS,GAAG,eAAe,CAAC;AAAA,QAChC;AACA,eAAO;AAAA,MACX;AACA,UAAI,SAAS,KAAK,QAAQ,KAAK,QAAQ,CAAC,EAAE,aAAa;AACnD;AAAA,IACR;AAAA,EACJ;AAEA,MAAI,OAAO,UAAU,KAAK,SAAS,QAAQ,QAAQ,GAAG;AAClD,QAAI;AACA,eAAS,MAAM,GAAG,OAAO,KAAK,MAAM,OAAO,UAAU,KAAK,GAAG,EAAE,eAAe,CAAC;AACnF,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAMA,IAAM,wBAAwB,CAAC,OAAO,UAAU,SAAS;AACrD,MAAI,UAAU,aAAa,OAAO,IAAI;AACtC,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,cAAc,OAAO;AAChC,SAAO,OAAO,qBAAqB,OAAO,MAAM,QAAQ,IAAI;AAChE;AAMA,IAAM,uBAAuB,CAAC,OAAO,UAAU,SAAS;AACpD,MAAI,UAAU,WAAW,OAAO,IAAI;AACpC,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,aAAa,OAAO;AAC/B,SAAO,OAAO,qBAAqB,OAAO,MAAM,QAAQ,IAAI;AAChE;AACA,SAAS,qBAAqB,OAAO,MAAM,UAAU;AACjD,MAAI,SAAS,KAAK,YAAY,aAAa,QAAQ,YAAY,KAAK,MAAM;AAC1E,SAAO,CAAC,WAAW,aAAa,aAAa;AACzC,QAAI,WAAW,KAAK,KAAK;AACrB,aAAO;AACX,QAAI,QAAQ,WAAW;AACvB,QAAI,CAAC;AACD,aAAO;AACX,iBAAa;AAAA,EACjB;AACA,MAAI,QAAQ,KAAK,WAAW,YAAY,OAAO,WAAW,KAAK,MAAM;AACrE,SAAO,CAAC,UAAU,aAAa,YAAY;AACvC,QAAI,UAAU,KAAK,KAAK;AACpB,aAAO;AACX,QAAI,QAAQ,UAAU;AACtB,QAAI,CAAC;AACD,aAAO;AACX,gBAAY;AAAA,EAChB;AACA,MAAI,OAAO,YAAY,MAAM,KAAK,WAAW,UAAU,MAAM,KAAK;AAClE,MAAI,CAAC,QAAQ,KAAK,QAAQ,aACtB,gBAAgB,eAAe,KAAK,MAAM,QAAQ,WAAW;AAC7D,WAAO;AACX,MAAI,UAAU;AACV,QAAI,KAAK,MAAM,GAAG,KAAK,IAAI;AAC3B,OAAG,aAAa,cAAc,OAAO,GAAG,KAAK,SAAS,CAAC;AACvD,aAAS,GAAG,eAAe,CAAC;AAAA,EAChC;AACA,SAAO;AACX;AACA,SAAS,YAAY,MAAM,MAAM,OAAO,OAAO;AAC3C,WAAS,OAAO,MAAM,MAAM,OAAQ,QAAQ,UAAU,KAAK,aAAa,KAAK,WAAY;AACrF,QAAI,KAAK;AACL,aAAO;AACX,QAAI,QAAQ,KAAK,cAAc;AAC3B,aAAO;AAAA,EACf;AACA,SAAO;AACX;AASA,IAAM,qBAAqB,CAAC,OAAO,UAAU,SAAS;AAClD,MAAI,EAAE,OAAO,MAAM,IAAI,MAAM,WAAW,OAAO;AAC/C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,MAAM,OAAO,aAAa;AAC1B,QAAI,OAAO,CAAC,KAAK,eAAe,YAAY,KAAK,IAAI,MAAM,eAAe;AACtE,aAAO;AACX,WAAO,cAAc,KAAK;AAAA,EAC9B;AACA,MAAI,OAAO,QAAQ,KAAK;AACxB,MAAI,CAAC,QAAQ,CAAC,cAAc,aAAa,IAAI;AACzC,WAAO;AACX,MAAI;AACA,aAAS,MAAM,GAAG,aAAa,cAAc,OAAO,MAAM,KAAK,KAAK,MAAM,KAAK,QAAQ,CAAC,EAAE,eAAe,CAAC;AAC9G,SAAO;AACX;AACA,SAAS,cAAc,MAAM;AACzB,MAAI,CAAC,KAAK,OAAO,KAAK,KAAK;AACvB,aAAS,IAAI,KAAK,QAAQ,GAAG,KAAK,GAAG,KAAK;AACtC,UAAI,KAAK,MAAM,CAAC,IAAI;AAChB,eAAO,KAAK,IAAI,QAAQ,KAAK,OAAO,IAAI,CAAC,CAAC;AAC9C,UAAI,KAAK,KAAK,CAAC,EAAE,KAAK,KAAK;AACvB;AAAA,IACR;AACJ,SAAO;AACX;AACA,SAAS,WAAW,OAAO,MAAM;AAC7B,MAAI,EAAE,QAAQ,IAAI,MAAM;AACxB,MAAI,CAAC,YAAY,OAAO,CAAC,KAAK,eAAe,WAAW,KAAK,IACvD,QAAQ,eAAe,QAAQ,OAAO,QAAQ;AAChD,WAAO;AACX,SAAO;AACX;AAQA,IAAM,cAAc,CAAC,OAAO,UAAU,SAAS;AAC3C,MAAI,UAAU,WAAW,OAAO,IAAI;AACpC,MAAI,CAAC;AACD,WAAO;AACX,MAAI,OAAO,aAAa,OAAO;AAE/B,MAAI,CAAC;AACD,WAAO;AACX,MAAI,QAAQ,KAAK;AAEjB,MAAI,cAAc,OAAO,MAAM,UAAU,CAAC;AACtC,WAAO;AAGX,MAAI,QAAQ,OAAO,QAAQ,QAAQ,MAC9B,YAAY,OAAO,OAAO,KAAK,cAAc,aAAa,KAAK,IAAI;AACpE,QAAI,UAAU,YAAY,MAAM,KAAK,QAAQ,OAAO,GAAG,QAAQ,MAAM,GAAG,MAAM,KAAK;AACnF,QAAI,WAAW,QAAQ,MAAM,OAAO,QAAQ,KAAK,QAAQ,MAAM;AAC3D,UAAI,UAAU;AACV,YAAI,KAAK,MAAM,GAAG,KAAK,OAAO;AAC9B,WAAG,aAAa,YAAY,OAAO,OAAO,IAAI,UAAU,SAAS,GAAG,IAAI,QAAQ,GAAG,QAAQ,IAAI,KAAK,GAAG,CAAC,GAAG,CAAC,IACtG,cAAc,OAAO,GAAG,KAAK,GAAG,QAAQ,IAAI,KAAK,GAAG,CAAC,CAAC;AAC5D,iBAAS,GAAG,eAAe,CAAC;AAAA,MAChC;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AAEA,MAAI,MAAM,UAAU,KAAK,SAAS,QAAQ,QAAQ,GAAG;AACjD,QAAI;AACA,eAAS,MAAM,GAAG,OAAO,KAAK,KAAK,KAAK,MAAM,MAAM,QAAQ,EAAE,eAAe,CAAC;AAClF,WAAO;AAAA,EACX;AACA,SAAO;AACX;AASA,IAAM,oBAAoB,CAAC,OAAO,UAAU,SAAS;AACjD,MAAI,EAAE,OAAO,MAAM,IAAI,MAAM,WAAW,OAAO;AAC/C,MAAI,CAAC;AACD,WAAO;AACX,MAAI,MAAM,OAAO,aAAa;AAC1B,QAAI,OAAO,CAAC,KAAK,eAAe,WAAW,KAAK,IAAI,MAAM,eAAe,MAAM,OAAO,QAAQ;AAC1F,aAAO;AACX,WAAO,aAAa,KAAK;AAAA,EAC7B;AACA,MAAI,OAAO,QAAQ,KAAK;AACxB,MAAI,CAAC,QAAQ,CAAC,cAAc,aAAa,IAAI;AACzC,WAAO;AACX,MAAI;AACA,aAAS,MAAM,GAAG,aAAa,cAAc,OAAO,MAAM,KAAK,KAAK,GAAG,CAAC,EAAE,eAAe,CAAC;AAC9F,SAAO;AACX;AACA,SAAS,aAAa,MAAM;AACxB,MAAI,CAAC,KAAK,OAAO,KAAK,KAAK;AACvB,aAAS,IAAI,KAAK,QAAQ,GAAG,KAAK,GAAG,KAAK;AACtC,UAAI,SAAS,KAAK,KAAK,CAAC;AACxB,UAAI,KAAK,MAAM,CAAC,IAAI,IAAI,OAAO;AAC3B,eAAO,KAAK,IAAI,QAAQ,KAAK,MAAM,IAAI,CAAC,CAAC;AAC7C,UAAI,OAAO,KAAK,KAAK;AACjB;AAAA,IACR;AACJ,SAAO;AACX;AAMA,IAAM,SAAS,CAAC,OAAO,aAAa;AAChC,MAAI,MAAM,MAAM,WAAW,UAAU,eAAe,eAAe;AACnE,MAAI,SAAS;AACT,QAAI,IAAI,KAAK,eAAe,CAAC,QAAQ,MAAM,KAAK,IAAI,IAAI;AACpD,aAAO;AACX,YAAQ,IAAI;AAAA,EAChB,OACK;AACD,YAAQ,UAAU,MAAM,KAAK,IAAI,MAAM,EAAE;AACzC,QAAI,SAAS;AACT,aAAO;AAAA,EACf;AACA,MAAI,UAAU;AACV,QAAI,KAAK,MAAM,GAAG,KAAK,KAAK;AAC5B,QAAI;AACA,SAAG,aAAa,cAAc,OAAO,GAAG,KAAK,QAAQ,MAAM,IAAI,QAAQ,KAAK,EAAE,WAAW,QAAQ,CAAC;AACtG,aAAS,GAAG,eAAe,CAAC;AAAA,EAChC;AACA,SAAO;AACX;AAKA,IAAM,WAAW,CAAC,OAAO,aAAa;AAClC,MAAI,MAAM,MAAM,WAAW;AAC3B,MAAI,eAAe,eAAe;AAC9B,QAAI,IAAI,KAAK,eAAe,CAAC,QAAQ,MAAM,KAAK,IAAI,EAAE;AAClD,aAAO;AACX,YAAQ,IAAI;AAAA,EAChB,OACK;AACD,YAAQ,UAAU,MAAM,KAAK,IAAI,IAAI,CAAC;AACtC,QAAI,SAAS;AACT,aAAO;AAAA,EACf;AACA,MAAI;AACA,aAAS,MAAM,GAAG,KAAK,KAAK,EAAE,eAAe,CAAC;AAClD,SAAO;AACX;AAKA,IAAM,OAAO,CAAC,OAAO,aAAa;AAC9B,MAAI,EAAE,OAAO,IAAI,IAAI,MAAM;AAC3B,MAAI,QAAQ,MAAM,WAAW,GAAG,GAAG,SAAS,SAAS,WAAW,KAAK;AACrE,MAAI,UAAU;AACV,WAAO;AACX,MAAI;AACA,aAAS,MAAM,GAAG,KAAK,OAAO,MAAM,EAAE,eAAe,CAAC;AAC1D,SAAO;AACX;AAMA,IAAM,gBAAgB,CAAC,OAAO,aAAa;AACvC,MAAI,EAAE,OAAO,QAAQ,IAAI,MAAM;AAC/B,MAAI,CAAC,MAAM,OAAO,KAAK,KAAK,QAAQ,CAAC,MAAM,WAAW,OAAO;AACzD,WAAO;AACX,MAAI;AACA,aAAS,MAAM,GAAG,WAAW,IAAI,EAAE,eAAe,CAAC;AACvD,SAAO;AACX;AACA,SAAS,eAAe,OAAO;AAC3B,WAAS,IAAI,GAAG,IAAI,MAAM,WAAW,KAAK;AACtC,QAAI,EAAE,KAAK,IAAI,MAAM,KAAK,CAAC;AAC3B,QAAI,KAAK,eAAe,CAAC,KAAK,iBAAiB;AAC3C,aAAO;AAAA,EACf;AACA,SAAO;AACX;AAMA,IAAM,WAAW,CAAC,OAAO,aAAa;AAClC,MAAI,EAAE,OAAO,QAAQ,IAAI,MAAM;AAC/B,MAAI,CAAC,MAAM,OAAO,KAAK,KAAK,QAAQ,CAAC,MAAM,WAAW,OAAO;AACzD,WAAO;AACX,MAAI,QAAQ,MAAM,KAAK,EAAE,GAAG,QAAQ,MAAM,WAAW,EAAE,GAAG,OAAO,eAAe,MAAM,eAAe,KAAK,CAAC;AAC3G,MAAI,CAAC,QAAQ,CAAC,MAAM,eAAe,OAAO,OAAO,IAAI;AACjD,WAAO;AACX,MAAI,UAAU;AACV,QAAI,MAAM,MAAM,MAAM,GAAG,KAAK,MAAM,GAAG,YAAY,KAAK,KAAK,KAAK,cAAc,CAAC;AACjF,OAAG,aAAa,UAAU,KAAK,GAAG,IAAI,QAAQ,GAAG,GAAG,CAAC,CAAC;AACtD,aAAS,GAAG,eAAe,CAAC;AAAA,EAChC;AACA,SAAO;AACX;AAKA,IAAM,sBAAsB,CAAC,OAAO,aAAa;AAC7C,MAAI,MAAM,MAAM,WAAW,EAAE,OAAO,IAAI,IAAI;AAC5C,MAAI,eAAe,gBAAgB,MAAM,OAAO,iBAAiB,IAAI,OAAO;AACxE,WAAO;AACX,MAAI,OAAO,eAAe,IAAI,OAAO,eAAe,IAAI,WAAW,CAAC,CAAC;AACrE,MAAI,CAAC,QAAQ,CAAC,KAAK;AACf,WAAO;AACX,MAAI,UAAU;AACV,QAAI,QAAQ,CAAC,MAAM,gBAAgB,IAAI,MAAM,IAAI,IAAI,OAAO,aAAa,QAAQ,KAAK;AACtF,QAAI,KAAK,MAAM,GAAG,OAAO,MAAM,KAAK,cAAc,CAAC;AACnD,OAAG,aAAa,cAAc,OAAO,GAAG,KAAK,OAAO,CAAC,CAAC;AACtD,aAAS,GAAG,eAAe,CAAC;AAAA,EAChC;AACA,SAAO;AACX;AAKA,IAAM,iBAAiB,CAAC,OAAO,aAAa;AACxC,MAAI,EAAE,QAAQ,IAAI,MAAM;AACxB,MAAI,CAAC,WAAW,QAAQ,OAAO,QAAQ;AACnC,WAAO;AACX,MAAI,QAAQ,QAAQ,KAAK,QAAQ,MAAM,KAAK,QAAQ,IAAI,EAAE,GAAG;AACzD,QAAI,SAAS,QAAQ,OAAO;AAC5B,QAAI,SAAS,MAAM,KAAK,MAAM,GAAG;AAC7B,UAAI;AACA,iBAAS,MAAM,GAAG,MAAM,MAAM,EAAE,eAAe,CAAC;AACpD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,MAAI,QAAQ,QAAQ,WAAW,GAAG,SAAS,SAAS,WAAW,KAAK;AACpE,MAAI,UAAU;AACV,WAAO;AACX,MAAI;AACA,aAAS,MAAM,GAAG,KAAK,OAAO,MAAM,EAAE,eAAe,CAAC;AAC1D,SAAO;AACX;AAKA,SAAS,aAAa,WAAW;AAC7B,SAAO,CAAC,OAAO,aAAa;AACxB,QAAI,EAAE,OAAO,IAAI,IAAI,MAAM;AAC3B,QAAI,MAAM,qBAAqB,iBAAiB,MAAM,UAAU,KAAK,SAAS;AAC1E,UAAI,CAAC,MAAM,gBAAgB,CAAC,SAAS,MAAM,KAAK,MAAM,GAAG;AACrD,eAAO;AACX,UAAI;AACA,iBAAS,MAAM,GAAG,MAAM,MAAM,GAAG,EAAE,eAAe,CAAC;AACvD,aAAO;AAAA,IACX;AACA,QAAI,CAAC,MAAM;AACP,aAAO;AACX,QAAI,QAAQ,CAAC;AACb,QAAI,YAAY,OAAO,QAAQ,OAAO,UAAU;AAChD,aAAS,IAAI,MAAM,SAAQ,KAAK;AAC5B,UAAI,OAAO,MAAM,KAAK,CAAC;AACvB,UAAI,KAAK,SAAS;AACd,gBAAQ,MAAM,IAAI,CAAC,KAAK,MAAM,OAAO,MAAM,QAAQ;AACnD,kBAAU,MAAM,MAAM,CAAC,KAAK,MAAM,OAAO,MAAM,QAAQ;AACvD,gBAAQ,eAAe,MAAM,KAAK,IAAI,CAAC,EAAE,eAAe,MAAM,WAAW,IAAI,CAAC,CAAC,CAAC;AAChF,YAAI,YAAY,aAAa,UAAU,IAAI,QAAQ,OAAO,KAAK;AAC/D,cAAM,QAAQ,cAAc,SAAS,QAAQ,EAAE,MAAM,MAAM,IAAI,KAAK;AACpE,qBAAa;AACb;AAAA,MACJ,OACK;AACD,YAAI,KAAK;AACL,iBAAO;AACX,cAAM,QAAQ,IAAI;AAAA,MACtB;AAAA,IACJ;AACA,QAAI,KAAK,MAAM;AACf,QAAI,MAAM,qBAAqB,iBAAiB,MAAM,qBAAqB;AACvE,SAAG,gBAAgB;AACvB,QAAI,WAAW,GAAG,QAAQ,IAAI,MAAM,GAAG;AACvC,QAAI,MAAM,SAAS,GAAG,KAAK,UAAU,MAAM,QAAQ,KAAK;AACxD,QAAI,CAAC,KAAK;AACN,YAAM,CAAC,IAAI,QAAQ,EAAE,MAAM,MAAM,IAAI;AACrC,YAAM,SAAS,GAAG,KAAK,UAAU,MAAM,QAAQ,KAAK;AAAA,IACxD;AACA,QAAI,CAAC;AACD,aAAO;AACX,OAAG,MAAM,UAAU,MAAM,QAAQ,KAAK;AACtC,QAAI,CAAC,SAAS,WAAW,MAAM,KAAK,UAAU,EAAE,QAAQ,OAAO;AAC3D,UAAI,QAAQ,GAAG,QAAQ,IAAI,MAAM,OAAO,UAAU,CAAC,GAAG,SAAS,GAAG,IAAI,QAAQ,KAAK;AACnF,UAAI,SAAS,MAAM,KAAK,aAAa,CAAC,EAAE,eAAe,OAAO,MAAM,GAAG,OAAO,MAAM,IAAI,GAAG,KAAK;AAC5F,WAAG,cAAc,GAAG,QAAQ,IAAI,MAAM,OAAO,UAAU,CAAC,GAAG,KAAK;AAAA,IACxE;AACA,QAAI;AACA,eAAS,GAAG,eAAe,CAAC;AAChC,WAAO;AAAA,EACX;AACJ;AAKA,IAAM,aAAa,aAAa;AAiBhC,IAAM,mBAAmB,CAAC,OAAO,aAAa;AAC1C,MAAI,EAAE,OAAO,GAAG,IAAI,MAAM,WAAW;AACrC,MAAI,OAAO,MAAM,YAAY,EAAE;AAC/B,MAAI,QAAQ;AACR,WAAO;AACX,QAAM,MAAM,OAAO,IAAI;AACvB,MAAI;AACA,aAAS,MAAM,GAAG,aAAa,cAAc,OAAO,MAAM,KAAK,GAAG,CAAC,CAAC;AACxE,SAAO;AACX;AAIA,IAAM,YAAY,CAAC,OAAO,aAAa;AACnC,MAAI;AACA,aAAS,MAAM,GAAG,aAAa,IAAI,aAAa,MAAM,GAAG,CAAC,CAAC;AAC/D,SAAO;AACX;AACA,SAAS,eAAe,OAAO,MAAM,UAAU;AAC3C,MAAI,SAAS,KAAK,YAAY,QAAQ,KAAK,WAAW,QAAQ,KAAK,MAAM;AACzE,MAAI,CAAC,UAAU,CAAC,SAAS,CAAC,OAAO,KAAK,kBAAkB,MAAM,IAAI;AAC9D,WAAO;AACX,MAAI,CAAC,OAAO,QAAQ,QAAQ,KAAK,OAAO,WAAW,QAAQ,GAAG,KAAK,GAAG;AAClE,QAAI;AACA,eAAS,MAAM,GAAG,OAAO,KAAK,MAAM,OAAO,UAAU,KAAK,GAAG,EAAE,eAAe,CAAC;AACnF,WAAO;AAAA,EACX;AACA,MAAI,CAAC,KAAK,OAAO,WAAW,OAAO,QAAQ,CAAC,KAAK,EAAE,MAAM,eAAe,QAAQ,MAAM,KAAK,KAAK,GAAG;AAC/F,WAAO;AACX,MAAI;AACA,aAAS,MAAM,GAAG,KAAK,KAAK,GAAG,EAAE,eAAe,CAAC;AACrD,SAAO;AACX;AACA,SAAS,cAAc,OAAO,MAAM,UAAU,KAAK;AAC/C,MAAI,SAAS,KAAK,YAAY,QAAQ,KAAK,WAAW,MAAM;AAC5D,MAAI,WAAW,OAAO,KAAK,KAAK,aAAa,MAAM,KAAK,KAAK;AAC7D,MAAI,CAAC,YAAY,eAAe,OAAO,MAAM,QAAQ;AACjD,WAAO;AACX,MAAI,cAAc,CAAC,YAAY,KAAK,OAAO,WAAW,KAAK,MAAM,GAAG,KAAK,MAAM,IAAI,CAAC;AACpF,MAAI,gBACC,QAAQ,QAAQ,OAAO,eAAe,OAAO,UAAU,GAAG,aAAa,MAAM,IAAI,MAClF,MAAM,UAAU,KAAK,CAAC,KAAK,MAAM,IAAI,EAAE,UAAU;AACjD,QAAI,UAAU;AACV,UAAI,MAAM,KAAK,MAAM,MAAM,UAAU,OAAO,SAAS;AACrD,eAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG;AAClC,eAAO,SAAS,KAAK,KAAK,CAAC,EAAE,OAAO,MAAM,IAAI,CAAC;AACnD,aAAO,SAAS,KAAK,OAAO,KAAK,IAAI,CAAC;AACtC,UAAI,KAAK,MAAM,GAAG,KAAK,IAAI,kBAAkB,KAAK,MAAM,GAAG,KAAK,KAAK,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG,CAAC,GAAG,KAAK,QAAQ,IAAI,CAAC;AACxH,UAAI,UAAU,GAAG,IAAI,QAAQ,MAAM,IAAI,KAAK,MAAM;AAClD,UAAI,QAAQ,aAAa,QAAQ,UAAU,QAAQ,OAAO,QACtD,QAAQ,GAAG,KAAK,QAAQ,GAAG;AAC3B,WAAG,KAAK,QAAQ,GAAG;AACvB,eAAS,GAAG,eAAe,CAAC;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AACA,MAAI,WAAW,MAAM,KAAK,KAAK,aAAc,MAAM,KAAK,WAAY,OAAO,UAAU,SAAS,MAAM,CAAC;AACrG,MAAI,QAAQ,YAAY,SAAS,MAAM,WAAW,SAAS,GAAG,GAAG,SAAS,SAAS,WAAW,KAAK;AACnG,MAAI,UAAU,QAAQ,UAAU,KAAK,OAAO;AACxC,QAAI;AACA,eAAS,MAAM,GAAG,KAAK,OAAO,MAAM,EAAE,eAAe,CAAC;AAC1D,WAAO;AAAA,EACX;AACA,MAAI,eAAe,YAAY,OAAO,SAAS,IAAI,KAAK,YAAY,QAAQ,KAAK,GAAG;AAChF,QAAI,KAAK,QAAQ,OAAO,CAAC;AACzB,eAAS;AACL,WAAK,KAAK,EAAE;AACZ,UAAI,GAAG;AACH;AACJ,WAAK,GAAG;AAAA,IACZ;AACA,QAAI,YAAY,OAAO,aAAa;AACpC,WAAO,CAAC,UAAU,aAAa,YAAY,UAAU;AACjD;AACJ,QAAI,GAAG,WAAW,GAAG,YAAY,GAAG,YAAY,UAAU,OAAO,GAAG;AAChE,UAAI,UAAU;AACV,YAAI,MAAM,SAAS;AACnB,iBAAS,IAAI,KAAK,SAAS,GAAG,KAAK,GAAG;AAClC,gBAAM,SAAS,KAAK,KAAK,CAAC,EAAE,KAAK,GAAG,CAAC;AACzC,YAAI,KAAK,MAAM,GAAG,KAAK,IAAI,kBAAkB,KAAK,MAAM,KAAK,QAAQ,KAAK,MAAM,MAAM,UAAU,KAAK,MAAM,YAAY,KAAK,MAAM,MAAM,WAAW,YAAY,IAAI,MAAM,KAAK,KAAK,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC;AACvM,iBAAS,GAAG,eAAe,CAAC;AAAA,MAChC;AACA,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;AACA,SAAS,oBAAoB,MAAM;AAC/B,SAAO,SAAU,OAAO,UAAU;AAC9B,QAAI,MAAM,MAAM,WAAW,OAAO,OAAO,IAAI,IAAI,QAAQ,IAAI;AAC7D,QAAI,QAAQ,KAAK;AACjB,WAAO,KAAK,KAAK,KAAK,EAAE,UAAU;AAC9B,UAAI,CAAC;AACD,eAAO;AACX;AAAA,IACJ;AACA,QAAI,CAAC,KAAK,KAAK,KAAK,EAAE;AAClB,aAAO;AACX,QAAI;AACA,eAAS,MAAM,GAAG,aAAa,cAAc,OAAO,MAAM,KAAK,OAAO,IAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,CAAC,CAAC;AACnH,WAAO;AAAA,EACX;AACJ;AAIA,IAAM,uBAAuB,oBAAoB,EAAE;AAInD,IAAM,qBAAqB,oBAAoB,CAAC;AAMhD,SAAS,OAAO,UAAU,QAAQ,MAAM;AACpC,SAAO,SAAU,OAAO,UAAU;AAC9B,QAAI,EAAE,OAAO,IAAI,IAAI,MAAM;AAC3B,QAAI,QAAQ,MAAM,WAAW,GAAG,GAAG,WAAW,SAAS,aAAa,OAAO,UAAU,KAAK;AAC1F,QAAI,CAAC;AACD,aAAO;AACX,QAAI;AACA,eAAS,MAAM,GAAG,KAAK,OAAO,QAAQ,EAAE,eAAe,CAAC;AAC5D,WAAO;AAAA,EACX;AACJ;AAKA,SAAS,aAAa,UAAU,QAAQ,MAAM;AAC1C,SAAO,SAAU,OAAO,UAAU;AAC9B,QAAI,aAAa;AACjB,aAAS,IAAI,GAAG,IAAI,MAAM,UAAU,OAAO,UAAU,CAAC,YAAY,KAAK;AACnE,UAAI,EAAE,OAAO,EAAE,KAAK,KAAK,GAAG,KAAK,EAAE,KAAK,GAAG,EAAE,IAAI,MAAM,UAAU,OAAO,CAAC;AACzE,YAAM,IAAI,aAAa,MAAM,IAAI,CAAC,MAAM,QAAQ;AAC5C,YAAI;AACA,iBAAO;AACX,YAAI,CAAC,KAAK,eAAe,KAAK,UAAU,UAAU,KAAK;AACnD;AACJ,YAAI,KAAK,QAAQ,UAAU;AACvB,uBAAa;AAAA,QACjB,OACK;AACD,cAAI,OAAO,MAAM,IAAI,QAAQ,GAAG,GAAG,QAAQ,KAAK,MAAM;AACtD,uBAAa,KAAK,OAAO,eAAe,OAAO,QAAQ,GAAG,QAAQ;AAAA,QACtE;AAAA,MACJ,CAAC;AAAA,IACL;AACA,QAAI,CAAC;AACD,aAAO;AACX,QAAI,UAAU;AACV,UAAI,KAAK,MAAM;AACf,eAAS,IAAI,GAAG,IAAI,MAAM,UAAU,OAAO,QAAQ,KAAK;AACpD,YAAI,EAAE,OAAO,EAAE,KAAK,KAAK,GAAG,KAAK,EAAE,KAAK,GAAG,EAAE,IAAI,MAAM,UAAU,OAAO,CAAC;AACzE,WAAG,aAAa,MAAM,IAAI,UAAU,KAAK;AAAA,MAC7C;AACA,eAAS,GAAG,eAAe,CAAC;AAAA,IAChC;AACA,WAAO;AAAA,EACX;AACJ;AAwJA,SAAS,iBAAiB,UAAU;AAChC,SAAO,SAAU,OAAO,UAAU,MAAM;AACpC,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ;AACjC,UAAI,SAAS,CAAC,EAAE,OAAO,UAAU,IAAI;AACjC,eAAO;AACf,WAAO;AAAA,EACX;AACJ;AACA,IAAI,YAAY,cAAc,iBAAiB,cAAc,kBAAkB;AAC/E,IAAI,MAAM,cAAc,iBAAiB,aAAa,iBAAiB;AAavE,IAAM,eAAe;AAAA,EACjB,SAAS,cAAc,eAAe,qBAAqB,gBAAgB,UAAU;AAAA,EACrF,aAAa;AAAA,EACb,aAAa;AAAA,EACb,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,UAAU;AAAA,EACV,cAAc;AAAA,EACd,SAAS;AACb;AAOA,IAAM,gBAAgB;AAAA,EAClB,UAAU,aAAa,WAAW;AAAA,EAClC,iBAAiB,aAAa,eAAe;AAAA,EAC7C,UAAU,aAAa,QAAQ;AAAA,EAC/B,sBAAsB,aAAa,YAAY;AAAA,EAC/C,cAAc,aAAa,YAAY;AAAA,EACvC,SAAS,aAAa,YAAY;AAAA,EAClC,UAAU;AAAA,EACV,UAAU;AACd;AACA,SAAS,OAAO;AACZ,gBAAc,GAAG,IAAI,aAAa,GAAG;AACzC,IAAMC,OAAM,OAAO,aAAa,cAAc,qBAAqB,KAAK,UAAU,QAAQ,IAEpF,OAAO,MAAM,eAAe,GAAG,WAAW,GAAG,SAAS,KAAK,WAAW;;;ACz0B5E,IAAAC,gBAAA;AAAA,IAAAA,gBAAA;AAAA,IAAAA,gBAAA;AAsEA,SAAS,WAAW,UAAU,QAAQ,MAAM;AACxC,SAAO,SAAU,OAAO,UAAU;AAC9B,QAAI,EAAE,OAAO,IAAI,IAAI,MAAM;AAC3B,QAAI,QAAQ,MAAM,WAAW,GAAG;AAChC,QAAI,CAAC;AACD,aAAO;AACX,QAAI,KAAK,WAAW,MAAM,KAAK;AAC/B,QAAI,CAAC,gBAAgB,IAAI,OAAO,UAAU,KAAK;AAC3C,aAAO;AACX,QAAI;AACA,eAAS,GAAG,eAAe,CAAC;AAChC,WAAO;AAAA,EACX;AACJ;AAQA,SAAS,gBAAgB,IAAI,OAAO,UAAU,QAAQ,MAAM;AACxD,MAAI,SAAS,OAAO,aAAa,OAAO,MAAM,MAAM,MAAM;AAE1D,MAAI,MAAM,SAAS,KAAK,MAAM,MAAM,KAAK,MAAM,QAAQ,CAAC,EAAE,KAAK,kBAAkB,QAAQ,KAAK,MAAM,cAAc,GAAG;AAEjH,QAAI,MAAM,MAAM,MAAM,MAAM,QAAQ,CAAC,KAAK;AACtC,aAAO;AACX,QAAI,UAAU,IAAI,QAAQ,MAAM,QAAQ,CAAC;AACzC,iBAAa,IAAI,UAAU,SAAS,SAAS,MAAM,KAAK;AACxD,QAAI,MAAM,WAAW,MAAM,OAAO;AAC9B,cAAQ,IAAI,UAAU,MAAM,OAAO,IAAI,QAAQ,MAAM,IAAI,IAAI,MAAM,KAAK,CAAC,GAAG,MAAM,KAAK;AAC3F,aAAS;AAAA,EACb;AACA,MAAI,OAAO,aAAa,YAAY,UAAU,OAAO,KAAK;AAC1D,MAAI,CAAC;AACD,WAAO;AACX,MAAI;AACA,iBAAa,IAAI,OAAO,MAAM,QAAQ,QAAQ;AAClD,SAAO;AACX;AACA,SAAS,aAAa,IAAI,OAAO,UAAU,YAAY,UAAU;AAC7D,MAAI,UAAU,SAAS;AACvB,WAAS,IAAI,SAAS,SAAS,GAAG,KAAK,GAAG;AACtC,cAAU,SAAS,KAAK,SAAS,CAAC,EAAE,KAAK,OAAO,SAAS,CAAC,EAAE,OAAO,OAAO,CAAC;AAC/E,KAAG,KAAK,IAAI,kBAAkB,MAAM,SAAS,aAAa,IAAI,IAAI,MAAM,KAAK,MAAM,OAAO,MAAM,KAAK,IAAI,MAAM,SAAS,GAAG,CAAC,GAAG,SAAS,QAAQ,IAAI,CAAC;AACrJ,MAAI,QAAQ;AACZ,WAAS,IAAI,GAAG,IAAI,SAAS,QAAQ;AACjC,QAAI,SAAS,CAAC,EAAE,QAAQ;AACpB,cAAQ,IAAI;AACpB,MAAI,aAAa,SAAS,SAAS;AACnC,MAAI,WAAW,MAAM,QAAQ,SAAS,UAAU,aAAa,IAAI,IAAI,SAAS,MAAM;AACpF,WAAS,IAAI,MAAM,YAAY,IAAI,MAAM,UAAU,QAAQ,MAAM,IAAI,GAAG,KAAK,QAAQ,OAAO;AACxF,QAAI,CAAC,SAAS,SAAS,GAAG,KAAK,UAAU,UAAU,GAAG;AAClD,SAAG,MAAM,UAAU,UAAU;AAC7B,kBAAY,IAAI;AAAA,IACpB;AACA,gBAAY,OAAO,MAAM,CAAC,EAAE;AAAA,EAChC;AACA,SAAO;AACX;AA2EA,SAAS,aAAa,UAAU;AAC5B,SAAO,SAAU,OAAO,UAAU;AAC9B,QAAI,EAAE,OAAO,IAAI,IAAI,MAAM;AAC3B,QAAI,QAAQ,MAAM,WAAW,KAAK,UAAQ,KAAK,aAAa,KAAK,KAAK,WAAW,QAAQ,QAAQ;AACjG,QAAI,CAAC;AACD,aAAO;AACX,QAAI,CAAC;AACD,aAAO;AACX,QAAI,MAAM,KAAK,MAAM,QAAQ,CAAC,EAAE,QAAQ;AACpC,aAAO,gBAAgB,OAAO,UAAU,UAAU,KAAK;AAAA;AAEvD,aAAO,cAAc,OAAO,UAAU,KAAK;AAAA,EACnD;AACJ;AACA,SAAS,gBAAgB,OAAO,UAAU,UAAU,OAAO;AACvD,MAAI,KAAK,MAAM,IAAI,MAAM,MAAM,KAAK,YAAY,MAAM,IAAI,IAAI,MAAM,KAAK;AACzE,MAAI,MAAM,WAAW;AAGjB,OAAG,KAAK,IAAI,kBAAkB,MAAM,GAAG,WAAW,KAAK,WAAW,IAAI,MAAM,SAAS,KAAK,SAAS,OAAO,MAAM,MAAM,OAAO,KAAK,CAAC,CAAC,GAAG,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC;AACtJ,YAAQ,IAAI,UAAU,GAAG,IAAI,QAAQ,MAAM,MAAM,GAAG,GAAG,GAAG,IAAI,QAAQ,SAAS,GAAG,MAAM,KAAK;AAAA,EACjG;AACA,QAAM,SAAS,WAAW,KAAK;AAC/B,MAAI,UAAU;AACV,WAAO;AACX,KAAG,KAAK,OAAO,MAAM;AACrB,MAAI,SAAS,GAAG,IAAI,QAAQ,GAAG,QAAQ,IAAI,KAAK,EAAE,IAAI,CAAC;AACvD,MAAI,QAAQ,GAAG,KAAK,OAAO,GAAG,KAAK,OAAO,WAAW,QAAQ,OAAO,UAAU;AAC1E,OAAG,KAAK,OAAO,GAAG;AACtB,WAAS,GAAG,eAAe,CAAC;AAC5B,SAAO;AACX;AACA,SAAS,cAAc,OAAO,UAAU,OAAO;AAC3C,MAAI,KAAK,MAAM,IAAI,OAAO,MAAM;AAEhC,WAAS,MAAM,MAAM,KAAK,IAAI,MAAM,WAAW,GAAG,IAAI,MAAM,YAAY,IAAI,GAAG,KAAK;AAChF,WAAO,KAAK,MAAM,CAAC,EAAE;AACrB,OAAG,OAAO,MAAM,GAAG,MAAM,CAAC;AAAA,EAC9B;AACA,MAAI,SAAS,GAAG,IAAI,QAAQ,MAAM,KAAK,GAAG,OAAO,OAAO;AACxD,MAAI,GAAG,QAAQ,IAAI,MAAM,GAAG,KAAK,MAAM,QAAQ,OAAO,UAAU;AAC5D,WAAO;AACX,MAAI,UAAU,MAAM,cAAc,GAAG,QAAQ,MAAM,YAAY,KAAK;AACpE,MAAI,SAAS,OAAO,KAAK,EAAE,GAAG,cAAc,OAAO,MAAM,EAAE;AAC3D,MAAI,CAAC,OAAO,WAAW,eAAe,UAAU,IAAI,IAAI,cAAc,GAAG,KAAK,QAAQ,OAAO,QAAQ,SAAS,QAAQ,SAAS,KAAK,IAAI,CAAC,CAAC;AACtI,WAAO;AACX,MAAI,QAAQ,OAAO,KAAK,MAAM,QAAQ,KAAK;AAI3C,KAAG,KAAK,IAAI,kBAAkB,SAAS,UAAU,IAAI,IAAI,OAAO,QAAQ,IAAI,IAAI,QAAQ,GAAG,MAAM,GAAG,IAAI,OAAO,UAAU,SAAS,QAAQ,SAAS,KAAK,KAAK,KAAK,SAAS,KAAK,CAAC,GAC5K,OAAO,QAAQ,SAAS,QAAQ,SAAS,KAAK,KAAK,KAAK,SAAS,KAAK,CAAC,CAAC,GAAG,UAAU,IAAI,GAAG,QAAQ,IAAI,CAAC,GAAG,UAAU,IAAI,CAAC,CAAC;AACjI,WAAS,GAAG,eAAe,CAAC;AAC5B,SAAO;AACX;AAKA,SAAS,aAAa,UAAU;AAC5B,SAAO,SAAU,OAAO,UAAU;AAC9B,QAAI,EAAE,OAAO,IAAI,IAAI,MAAM;AAC3B,QAAI,QAAQ,MAAM,WAAW,KAAK,UAAQ,KAAK,aAAa,KAAK,KAAK,WAAW,QAAQ,QAAQ;AACjG,QAAI,CAAC;AACD,aAAO;AACX,QAAI,aAAa,MAAM;AACvB,QAAI,cAAc;AACd,aAAO;AACX,QAAI,SAAS,MAAM,QAAQ,aAAa,OAAO,MAAM,aAAa,CAAC;AACnE,QAAI,WAAW,QAAQ;AACnB,aAAO;AACX,QAAI,UAAU;AACV,UAAI,eAAe,WAAW,aAAa,WAAW,UAAU,QAAQ,OAAO;AAC/E,UAAI,QAAQ,SAAS,KAAK,eAAe,SAAS,OAAO,IAAI,IAAI;AACjE,UAAI,QAAQ,IAAI,MAAM,SAAS,KAAK,SAAS,OAAO,MAAM,SAAS,KAAK,OAAO,KAAK,OAAO,MAAM,KAAK,CAAC,CAAC,CAAC,GAAG,eAAe,IAAI,GAAG,CAAC;AACnI,UAAI,SAAS,MAAM,OAAO,QAAQ,MAAM;AACxC,eAAS,MAAM,GAAG,KAAK,IAAI,kBAAkB,UAAU,eAAe,IAAI,IAAI,OAAO,QAAQ,OAAO,OAAO,GAAG,IAAI,CAAC,EAC9G,eAAe,CAAC;AAAA,IACzB;AACA,WAAO;AAAA,EACX;AACJ;", "names": ["import_dist", "import_dist", "mac", "shift", "import_dist", "mac", "import_dist"]}