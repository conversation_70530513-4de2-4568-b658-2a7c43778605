import { createRouter, createWebHistory } from 'vue-router'

import articleApi from '@/api/article'
import { useArticleStore, useCommentStore } from '@/stores/index'
import type { ResponseData } from '@/types/response_data.types'
import ArticlePage from '@/views/Article.vue'
import HomePage from '@/views/Home.vue'
import LoginPage from '@/views/Login.vue'
import TiptapMenuTest from '@/views/test/TiptapMenuTest.vue'
import SimpleMenuTest from '@/views/test/SimpleMenuTest.vue'
import MenuTestFixed from '@/views/test/MenuTestFixed.vue'
import ClickMenuTest from '@/views/test/ClickMenuTest.vue'
import SimpleClickMenuTest from '@/views/test/SimpleClickMenuTest.vue'
const router = createRouter({
  history: createWebHistory('/'),
  routes: [
    {
      path: '/login',
      name: 'Login',
      component: LoginPage,
      meta: {
        requiresAuth: false, // 标记登录路由不需要登录验证
      },
    },
    {
      path: '/',
      name: 'Home',
      component: HomePage,
      meta: {
        requiresAuth: true, // 标记该路由需要登录验证，可根据实际情况调整
      },
    },
    {
      path: '/article/:articleId/:commentId?',
      name: 'Article',
      component: ArticlePage,
      meta: {
        requiresAuth: true, // 标记该路由需要登录验证，可根据实际情况调整
      },
    },
    {
      path: '/test/tiptap-menu',
      name: 'TiptapMenuTest',
      component: TiptapMenuTest,
      meta: {
        requiresAuth: false, // 测试页面不需要登录
      },
    },
    {
      path: '/test/simple-menu',
      name: 'SimpleMenuTest',
      component: SimpleMenuTest,
      meta: {
        requiresAuth: false, // 测试页面不需要登录
      },
    },
    {
      path: '/test/menu-fixed',
      name: 'MenuTestFixed',
      component: MenuTestFixed,
      meta: {
        requiresAuth: false, // 测试页面不需要登录
      },
    },
    {
      path: '/test/click-menu',
      name: 'ClickMenuTest',
      component: ClickMenuTest,
      meta: {
        requiresAuth: false, // 测试页面不需要登录
      },
    },
    {
      path: '/test/simple-click-menu',
      name: 'SimpleClickMenuTest',
      component: SimpleClickMenuTest,
      meta: {
        requiresAuth: false, // 测试页面不需要登录
      },
    },
  ],
})

const defaultTitle = 'Shenmo'
router.beforeEach((to, from, next) => {
  document.title = `${defaultTitle} - ${to.name as string}`
  function getCookieValue(name: string) {
    const cookies = document.cookie.split('; ')
    for (const cookie of cookies) {
      const parts = cookie.split('=')
      if (parts[0] === name) {
        return parts[1]
      }
    }
    return null
  }
  const wentk = getCookieValue('wentk')
  const articleStore = useArticleStore()
  const commentStore = useCommentStore()
  articleStore.setId('')
  commentStore.setId('')
  if (to.meta?.requiresAuth && !wentk) {
    // 如果要访问的路由需要登录验证且没有token，则跳转到登录页
    next({ name: 'Login' })
  } else if (to.name === 'Login' && wentk) {
    // 如果已登录（有token）且要访问登录路由，则跳转到首页
    next({ name: 'Home' })
  } else {
    // 当访问到 Article 页面时，将路由参数存储到 Pinia
    if (to.name === 'Article') {
      const { articleId, commentId } = to.params
      articleStore.setId(String(articleId))
      if (commentId !== undefined && commentId !== null) {
        commentStore.setId(String(commentId))
      }
      articleApi.title(articleStore.getId).then((res: ResponseData) => {
        document.title = `${defaultTitle} - ${res.data}`
      })
    }
    next()
  }
})
export default router
