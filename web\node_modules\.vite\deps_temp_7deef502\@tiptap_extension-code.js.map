{"version": 3, "sources": ["../../@tiptap/extension-code/src/code.ts"], "sourcesContent": ["import {\n  Mark,\n  markInputRule,\n  markPasteRule,\n  mergeAttributes,\n} from '@tiptap/core'\n\nexport interface CodeOptions {\n  /**\n   * The HTML attributes applied to the code element.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>,\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    code: {\n      /**\n       * Set a code mark\n       */\n      setCode: () => ReturnType,\n      /**\n       * Toggle inline code\n       */\n      toggleCode: () => ReturnType,\n      /**\n       * Unset a code mark\n       */\n      unsetCode: () => ReturnType,\n    }\n  }\n}\n\n/**\n * Regular expressions to match inline code blocks enclosed in backticks.\n *  It matches:\n *     - An opening backtick, followed by\n *     - Any text that doesn't include a backtick (captured for marking), followed by\n *     - A closing backtick.\n *  This ensures that any text between backticks is formatted as code,\n *  regardless of the surrounding characters (exception being another backtick).\n */\nexport const inputRegex = /(^|[^`])`([^`]+)`(?!`)/\n\n/**\n * Matches inline code while pasting.\n */\nexport const pasteRegex = /(^|[^`])`([^`]+)`(?!`)/g\n\n/**\n * This extension allows you to mark text as inline code.\n * @see https://tiptap.dev/api/marks/code\n */\nexport const Code = Mark.create<CodeOptions>({\n  name: 'code',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  excludes: '_',\n\n  code: true,\n\n  exitable: true,\n\n  parseHTML() {\n    return [\n      { tag: 'code' },\n    ]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['code', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      setCode: () => ({ commands }) => {\n        return commands.setMark(this.name)\n      },\n      toggleCode: () => ({ commands }) => {\n        return commands.toggleMark(this.name)\n      },\n      unsetCode: () => ({ commands }) => {\n        return commands.unsetMark(this.name)\n      },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-e': () => this.editor.commands.toggleCode(),\n    }\n  },\n\n  addInputRules() {\n    return [\n      markInputRule({\n        find: inputRegex,\n        type: this.type,\n      }),\n    ]\n  },\n\n  addPasteRules() {\n    return [\n      markPasteRule({\n        find: pasteRegex,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4CO,IAAM,aAAa;AAKnB,IAAM,aAAa;AAMb,IAAA,OAAO,KAAK,OAAoB;EAC3C,MAAM;EAEN,aAAU;AACR,WAAO;MACL,gBAAgB,CAAA;;;EAIpB,UAAU;EAEV,MAAM;EAEN,UAAU;EAEV,YAAS;AACP,WAAO;MACL,EAAE,KAAK,OAAM;;;EAIjB,WAAW,EAAE,eAAc,GAAE;AAC3B,WAAO,CAAC,QAAQ,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;;EAGjF,cAAW;AACT,WAAO;MACL,SAAS,MAAM,CAAC,EAAE,SAAQ,MAAM;AAC9B,eAAO,SAAS,QAAQ,KAAK,IAAI;;MAEnC,YAAY,MAAM,CAAC,EAAE,SAAQ,MAAM;AACjC,eAAO,SAAS,WAAW,KAAK,IAAI;;MAEtC,WAAW,MAAM,CAAC,EAAE,SAAQ,MAAM;AAChC,eAAO,SAAS,UAAU,KAAK,IAAI;;;;EAKzC,uBAAoB;AAClB,WAAO;MACL,SAAS,MAAM,KAAK,OAAO,SAAS,WAAU;;;EAIlD,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;EAIL,gBAAa;AACX,WAAO;MACL,cAAc;QACZ,MAAM;QACN,MAAM,KAAK;OACZ;;;AAGN,CAAA;", "names": []}