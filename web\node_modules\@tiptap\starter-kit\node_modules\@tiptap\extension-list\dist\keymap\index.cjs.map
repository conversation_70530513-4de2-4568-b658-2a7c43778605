{"version": 3, "sources": ["../../src/keymap/index.ts", "../../src/keymap/list-keymap.ts", "../../src/keymap/listHelpers/index.ts", "../../src/keymap/listHelpers/findListItemPos.ts", "../../src/keymap/listHelpers/getNextListDepth.ts", "../../src/keymap/listHelpers/handleBackspace.ts", "../../src/keymap/listHelpers/hasListBefore.ts", "../../src/keymap/listHelpers/hasListItemBefore.ts", "../../src/keymap/listHelpers/listItemHasSubList.ts", "../../src/keymap/listHelpers/handleDelete.ts", "../../src/keymap/listHelpers/nextListIsDeeper.ts", "../../src/keymap/listHelpers/nextListIsHigher.ts", "../../src/keymap/listHelpers/hasListItemAfter.ts"], "sourcesContent": ["export * from './list-keymap.js'\nexport * as listHelpers from './listHelpers/index.js'\n", "import { Extension } from '@tiptap/core'\n\nimport { handleBackspace, handleDelete } from './listHelpers/index.js'\n\nexport type ListKeymapOptions = {\n  /**\n   * An array of list types. This is used for item and wrapper list matching.\n   * @default []\n   * @example [{ itemName: 'listItem', wrapperNames: ['bulletList', 'orderedList'] }]\n   */\n  listTypes: Array<{\n    itemName: string\n    wrapperNames: string[]\n  }>\n}\n\n/**\n * This extension registers custom keymaps to change the behaviour of the backspace and delete keys.\n * By default Prosemirror keyhandling will always lift or sink items so paragraphs are joined into\n * the adjacent or previous list item. This extension will prevent this behaviour and instead will\n * try to join paragraphs from two list items into a single list item.\n * @see https://www.tiptap.dev/api/extensions/list-keymap\n */\nexport const ListKeymap = Extension.create<ListKeymapOptions>({\n  name: 'listKeymap',\n\n  addOptions() {\n    return {\n      listTypes: [\n        {\n          itemName: 'listItem',\n          wrapperNames: ['bulletList', 'orderedList'],\n        },\n        {\n          itemName: 'taskItem',\n          wrapperNames: ['taskList'],\n        },\n      ],\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      Delete: ({ editor }) => {\n        let handled = false\n\n        this.options.listTypes.forEach(({ itemName }) => {\n          if (editor.state.schema.nodes[itemName] === undefined) {\n            return\n          }\n\n          if (handleDelete(editor, itemName)) {\n            handled = true\n          }\n        })\n\n        return handled\n      },\n      'Mod-Delete': ({ editor }) => {\n        let handled = false\n\n        this.options.listTypes.forEach(({ itemName }) => {\n          if (editor.state.schema.nodes[itemName] === undefined) {\n            return\n          }\n\n          if (handleDelete(editor, itemName)) {\n            handled = true\n          }\n        })\n\n        return handled\n      },\n      Backspace: ({ editor }) => {\n        let handled = false\n\n        this.options.listTypes.forEach(({ itemName, wrapperNames }) => {\n          if (editor.state.schema.nodes[itemName] === undefined) {\n            return\n          }\n\n          if (handleBackspace(editor, itemName, wrapperNames)) {\n            handled = true\n          }\n        })\n\n        return handled\n      },\n      'Mod-Backspace': ({ editor }) => {\n        let handled = false\n\n        this.options.listTypes.forEach(({ itemName, wrapperNames }) => {\n          if (editor.state.schema.nodes[itemName] === undefined) {\n            return\n          }\n\n          if (handleBackspace(editor, itemName, wrapperNames)) {\n            handled = true\n          }\n        })\n\n        return handled\n      },\n    }\n  },\n})\n", "export * from './findListItemPos.js'\nexport * from './getNextListDepth.js'\nexport * from './handleBackspace.js'\nexport * from './handleDelete.js'\nexport * from './hasListBefore.js'\nexport * from './hasListItemAfter.js'\nexport * from './hasListItemBefore.js'\nexport * from './listItemHasSubList.js'\nexport * from './nextListIsDeeper.js'\nexport * from './nextListIsHigher.js'\n", "import { getNodeType } from '@tiptap/core'\nimport type { NodeType } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\n\nexport const findListItemPos = (typeOrName: string | NodeType, state: EditorState) => {\n  const { $from } = state.selection\n  const nodeType = getNodeType(typeOrName, state.schema)\n\n  let currentNode = null\n  let currentDepth = $from.depth\n  let currentPos = $from.pos\n  let targetDepth: number | null = null\n\n  while (currentDepth > 0 && targetDepth === null) {\n    currentNode = $from.node(currentDepth)\n\n    if (currentNode.type === nodeType) {\n      targetDepth = currentDepth\n    } else {\n      currentDepth -= 1\n      currentPos -= 1\n    }\n  }\n\n  if (targetDepth === null) {\n    return null\n  }\n\n  return { $pos: state.doc.resolve(currentPos), depth: targetDepth }\n}\n", "import { getNodeAtPosition } from '@tiptap/core'\nimport type { EditorState } from '@tiptap/pm/state'\n\nimport { findListItemPos } from './findListItemPos.js'\n\nexport const getNextListDepth = (typeOrName: string, state: EditorState) => {\n  const listItemPos = findListItemPos(typeOrName, state)\n\n  if (!listItemPos) {\n    return false\n  }\n\n  const [, depth] = getNodeAtPosition(state, typeOrName, listItemPos.$pos.pos + 4)\n\n  return depth\n}\n", "import type { Editor } from '@tiptap/core'\nimport { isAtStartOfNode, isNodeActive } from '@tiptap/core'\nimport type { Node } from '@tiptap/pm/model'\n\nimport { findListItemPos } from './findListItemPos.js'\nimport { hasListBefore } from './hasListBefore.js'\nimport { hasListItemBefore } from './hasListItemBefore.js'\nimport { listItemHasSubList } from './listItemHasSubList.js'\n\nexport const handleBackspace = (editor: Editor, name: string, parentListTypes: string[]) => {\n  // this is required to still handle the undo handling\n  if (editor.commands.undoInputRule()) {\n    return true\n  }\n\n  // if the selection is not collapsed\n  // we can rely on the default backspace behavior\n  if (editor.state.selection.from !== editor.state.selection.to) {\n    return false\n  }\n\n  // if the current item is NOT inside a list item &\n  // the previous item is a list (orderedList or bulletList)\n  // move the cursor into the list and delete the current item\n  if (!isNodeActive(editor.state, name) && hasListBefore(editor.state, name, parentListTypes)) {\n    const { $anchor } = editor.state.selection\n\n    const $listPos = editor.state.doc.resolve($anchor.before() - 1)\n\n    const listDescendants: Array<{ node: Node; pos: number }> = []\n\n    $listPos.node().descendants((node, pos) => {\n      if (node.type.name === name) {\n        listDescendants.push({ node, pos })\n      }\n    })\n\n    const lastItem = listDescendants.at(-1)\n\n    if (!lastItem) {\n      return false\n    }\n\n    const $lastItemPos = editor.state.doc.resolve($listPos.start() + lastItem.pos + 1)\n\n    return editor\n      .chain()\n      .cut({ from: $anchor.start() - 1, to: $anchor.end() + 1 }, $lastItemPos.end())\n      .joinForward()\n      .run()\n  }\n\n  // if the cursor is not inside the current node type\n  // do nothing and proceed\n  if (!isNodeActive(editor.state, name)) {\n    return false\n  }\n\n  // if the cursor is not at the start of a node\n  // do nothing and proceed\n  if (!isAtStartOfNode(editor.state)) {\n    return false\n  }\n\n  const listItemPos = findListItemPos(name, editor.state)\n\n  if (!listItemPos) {\n    return false\n  }\n\n  const $prev = editor.state.doc.resolve(listItemPos.$pos.pos - 2)\n  const prevNode = $prev.node(listItemPos.depth)\n\n  const previousListItemHasSubList = listItemHasSubList(name, editor.state, prevNode)\n\n  // if the previous item is a list item and doesn't have a sublist, join the list items\n  if (hasListItemBefore(name, editor.state) && !previousListItemHasSubList) {\n    return editor.commands.joinItemBackward()\n  }\n\n  // otherwise in the end, a backspace should\n  // always just lift the list item if\n  // joining / merging is not possible\n  return editor.chain().liftListItem(name).run()\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nexport const hasListBefore = (editorState: EditorState, name: string, parentListTypes: string[]) => {\n  const { $anchor } = editorState.selection\n\n  const previousNodePos = Math.max(0, $anchor.pos - 2)\n\n  const previousNode = editorState.doc.resolve(previousNodePos).node()\n\n  if (!previousNode || !parentListTypes.includes(previousNode.type.name)) {\n    return false\n  }\n\n  return true\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nexport const hasListItemBefore = (typeOrName: string, state: EditorState): boolean => {\n  const { $anchor } = state.selection\n\n  const $targetPos = state.doc.resolve($anchor.pos - 2)\n\n  if ($targetPos.index() === 0) {\n    return false\n  }\n\n  if ($targetPos.nodeBefore?.type.name !== typeOrName) {\n    return false\n  }\n\n  return true\n}\n", "import { getNodeType } from '@tiptap/core'\nimport type { Node } from '@tiptap/pm/model'\nimport type { EditorState } from '@tiptap/pm/state'\n\nexport const listItemHasSubList = (typeOrName: string, state: EditorState, node?: Node) => {\n  if (!node) {\n    return false\n  }\n\n  const nodeType = getNodeType(typeOrName, state.schema)\n\n  let hasSubList = false\n\n  node.descendants(child => {\n    if (child.type === nodeType) {\n      hasSubList = true\n    }\n  })\n\n  return hasSubList\n}\n", "import type { Editor } from '@tiptap/core'\nimport { isAtEndOfNode, isNodeActive } from '@tiptap/core'\n\nimport { nextListIsDeeper } from './nextListIsDeeper.js'\nimport { nextListIsHigher } from './nextListIsHigher.js'\n\nexport const handleDelete = (editor: Editor, name: string) => {\n  // if the cursor is not inside the current node type\n  // do nothing and proceed\n  if (!isNodeActive(editor.state, name)) {\n    return false\n  }\n\n  // if the cursor is not at the end of a node\n  // do nothing and proceed\n  if (!isAtEndOfNode(editor.state, name)) {\n    return false\n  }\n\n  // if the selection is not collapsed, or not within a single node\n  // do nothing and proceed\n  const { selection } = editor.state\n  const { $from, $to } = selection\n\n  if (!selection.empty && $from.sameParent($to)) {\n    return false\n  }\n\n  // check if the next node is a list with a deeper depth\n  if (nextListIsDeeper(name, editor.state)) {\n    return editor\n      .chain()\n      .focus(editor.state.selection.from + 4)\n      .lift(name)\n      .joinBackward()\n      .run()\n  }\n\n  if (nextList<PERSON>s<PERSON>igh<PERSON>(name, editor.state)) {\n    return editor.chain().joinForward().joinBackward().run()\n  }\n\n  return editor.commands.joinItemForward()\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nimport { findListItemPos } from './findListItemPos.js'\nimport { getNextListDepth } from './getNextListDepth.js'\n\nexport const nextListIsDeeper = (typeOrName: string, state: EditorState) => {\n  const listDepth = getNextListDepth(typeOrName, state)\n  const listItemPos = findListItemPos(typeOrName, state)\n\n  if (!listItemPos || !listDepth) {\n    return false\n  }\n\n  if (listDepth > listItemPos.depth) {\n    return true\n  }\n\n  return false\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nimport { findListItemPos } from './findListItemPos.js'\nimport { getNextListDepth } from './getNextListDepth.js'\n\nexport const nextListIsHigher = (typeOrName: string, state: EditorState) => {\n  const listDepth = getNextListDepth(typeOrName, state)\n  const listItemPos = findListItemPos(typeOrName, state)\n\n  if (!listItemPos || !listDepth) {\n    return false\n  }\n\n  if (listDepth < listItemPos.depth) {\n    return true\n  }\n\n  return false\n}\n", "import type { EditorState } from '@tiptap/pm/state'\n\nexport const hasListItemAfter = (typeOrName: string, state: EditorState): boolean => {\n  const { $anchor } = state.selection\n\n  const $targetPos = state.doc.resolve($anchor.pos - $anchor.parentOffset - 2)\n\n  if ($targetPos.index() === $targetPos.parent.childCount - 1) {\n    return false\n  }\n\n  if ($targetPos.nodeAfter?.type.name !== typeOrName) {\n    return false\n  }\n\n  return true\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAA,eAA0B;;;ACA1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,kBAA4B;AAIrB,IAAM,kBAAkB,CAAC,YAA+B,UAAuB;AACpF,QAAM,EAAE,MAAM,IAAI,MAAM;AACxB,QAAM,eAAW,yBAAY,YAAY,MAAM,MAAM;AAErD,MAAI,cAAc;AAClB,MAAI,eAAe,MAAM;AACzB,MAAI,aAAa,MAAM;AACvB,MAAI,cAA6B;AAEjC,SAAO,eAAe,KAAK,gBAAgB,MAAM;AAC/C,kBAAc,MAAM,KAAK,YAAY;AAErC,QAAI,YAAY,SAAS,UAAU;AACjC,oBAAc;AAAA,IAChB,OAAO;AACL,sBAAgB;AAChB,oBAAc;AAAA,IAChB;AAAA,EACF;AAEA,MAAI,gBAAgB,MAAM;AACxB,WAAO;AAAA,EACT;AAEA,SAAO,EAAE,MAAM,MAAM,IAAI,QAAQ,UAAU,GAAG,OAAO,YAAY;AACnE;;;AC7BA,IAAAC,eAAkC;AAK3B,IAAM,mBAAmB,CAAC,YAAoB,UAAuB;AAC1E,QAAM,cAAc,gBAAgB,YAAY,KAAK;AAErD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,QAAM,CAAC,EAAE,KAAK,QAAI,gCAAkB,OAAO,YAAY,YAAY,KAAK,MAAM,CAAC;AAE/E,SAAO;AACT;;;ACdA,IAAAC,eAA8C;;;ACCvC,IAAM,gBAAgB,CAAC,aAA0B,MAAc,oBAA8B;AAClG,QAAM,EAAE,QAAQ,IAAI,YAAY;AAEhC,QAAM,kBAAkB,KAAK,IAAI,GAAG,QAAQ,MAAM,CAAC;AAEnD,QAAM,eAAe,YAAY,IAAI,QAAQ,eAAe,EAAE,KAAK;AAEnE,MAAI,CAAC,gBAAgB,CAAC,gBAAgB,SAAS,aAAa,KAAK,IAAI,GAAG;AACtE,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACZO,IAAM,oBAAoB,CAAC,YAAoB,UAAgC;AAFtF;AAGE,QAAM,EAAE,QAAQ,IAAI,MAAM;AAE1B,QAAM,aAAa,MAAM,IAAI,QAAQ,QAAQ,MAAM,CAAC;AAEpD,MAAI,WAAW,MAAM,MAAM,GAAG;AAC5B,WAAO;AAAA,EACT;AAEA,QAAI,gBAAW,eAAX,mBAAuB,KAAK,UAAS,YAAY;AACnD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AChBA,IAAAC,eAA4B;AAIrB,IAAM,qBAAqB,CAAC,YAAoB,OAAoB,SAAgB;AACzF,MAAI,CAAC,MAAM;AACT,WAAO;AAAA,EACT;AAEA,QAAM,eAAW,0BAAY,YAAY,MAAM,MAAM;AAErD,MAAI,aAAa;AAEjB,OAAK,YAAY,WAAS;AACxB,QAAI,MAAM,SAAS,UAAU;AAC3B,mBAAa;AAAA,IACf;AAAA,EACF,CAAC;AAED,SAAO;AACT;;;AHXO,IAAM,kBAAkB,CAAC,QAAgB,MAAc,oBAA8B;AAE1F,MAAI,OAAO,SAAS,cAAc,GAAG;AACnC,WAAO;AAAA,EACT;AAIA,MAAI,OAAO,MAAM,UAAU,SAAS,OAAO,MAAM,UAAU,IAAI;AAC7D,WAAO;AAAA,EACT;AAKA,MAAI,KAAC,2BAAa,OAAO,OAAO,IAAI,KAAK,cAAc,OAAO,OAAO,MAAM,eAAe,GAAG;AAC3F,UAAM,EAAE,QAAQ,IAAI,OAAO,MAAM;AAEjC,UAAM,WAAW,OAAO,MAAM,IAAI,QAAQ,QAAQ,OAAO,IAAI,CAAC;AAE9D,UAAM,kBAAsD,CAAC;AAE7D,aAAS,KAAK,EAAE,YAAY,CAAC,MAAM,QAAQ;AACzC,UAAI,KAAK,KAAK,SAAS,MAAM;AAC3B,wBAAgB,KAAK,EAAE,MAAM,IAAI,CAAC;AAAA,MACpC;AAAA,IACF,CAAC;AAED,UAAM,WAAW,gBAAgB,GAAG,EAAE;AAEtC,QAAI,CAAC,UAAU;AACb,aAAO;AAAA,IACT;AAEA,UAAM,eAAe,OAAO,MAAM,IAAI,QAAQ,SAAS,MAAM,IAAI,SAAS,MAAM,CAAC;AAEjF,WAAO,OACJ,MAAM,EACN,IAAI,EAAE,MAAM,QAAQ,MAAM,IAAI,GAAG,IAAI,QAAQ,IAAI,IAAI,EAAE,GAAG,aAAa,IAAI,CAAC,EAC5E,YAAY,EACZ,IAAI;AAAA,EACT;AAIA,MAAI,KAAC,2BAAa,OAAO,OAAO,IAAI,GAAG;AACrC,WAAO;AAAA,EACT;AAIA,MAAI,KAAC,8BAAgB,OAAO,KAAK,GAAG;AAClC,WAAO;AAAA,EACT;AAEA,QAAM,cAAc,gBAAgB,MAAM,OAAO,KAAK;AAEtD,MAAI,CAAC,aAAa;AAChB,WAAO;AAAA,EACT;AAEA,QAAM,QAAQ,OAAO,MAAM,IAAI,QAAQ,YAAY,KAAK,MAAM,CAAC;AAC/D,QAAM,WAAW,MAAM,KAAK,YAAY,KAAK;AAE7C,QAAM,6BAA6B,mBAAmB,MAAM,OAAO,OAAO,QAAQ;AAGlF,MAAI,kBAAkB,MAAM,OAAO,KAAK,KAAK,CAAC,4BAA4B;AACxE,WAAO,OAAO,SAAS,iBAAiB;AAAA,EAC1C;AAKA,SAAO,OAAO,MAAM,EAAE,aAAa,IAAI,EAAE,IAAI;AAC/C;;;AInFA,IAAAC,eAA4C;;;ACIrC,IAAM,mBAAmB,CAAC,YAAoB,UAAuB;AAC1E,QAAM,YAAY,iBAAiB,YAAY,KAAK;AACpD,QAAM,cAAc,gBAAgB,YAAY,KAAK;AAErD,MAAI,CAAC,eAAe,CAAC,WAAW;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,YAAY,OAAO;AACjC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;ACbO,IAAM,mBAAmB,CAAC,YAAoB,UAAuB;AAC1E,QAAM,YAAY,iBAAiB,YAAY,KAAK;AACpD,QAAM,cAAc,gBAAgB,YAAY,KAAK;AAErD,MAAI,CAAC,eAAe,CAAC,WAAW;AAC9B,WAAO;AAAA,EACT;AAEA,MAAI,YAAY,YAAY,OAAO;AACjC,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AFZO,IAAM,eAAe,CAAC,QAAgB,SAAiB;AAG5D,MAAI,KAAC,2BAAa,OAAO,OAAO,IAAI,GAAG;AACrC,WAAO;AAAA,EACT;AAIA,MAAI,KAAC,4BAAc,OAAO,OAAO,IAAI,GAAG;AACtC,WAAO;AAAA,EACT;AAIA,QAAM,EAAE,UAAU,IAAI,OAAO;AAC7B,QAAM,EAAE,OAAO,IAAI,IAAI;AAEvB,MAAI,CAAC,UAAU,SAAS,MAAM,WAAW,GAAG,GAAG;AAC7C,WAAO;AAAA,EACT;AAGA,MAAI,iBAAiB,MAAM,OAAO,KAAK,GAAG;AACxC,WAAO,OACJ,MAAM,EACN,MAAM,OAAO,MAAM,UAAU,OAAO,CAAC,EACrC,KAAK,IAAI,EACT,aAAa,EACb,IAAI;AAAA,EACT;AAEA,MAAI,iBAAiB,MAAM,OAAO,KAAK,GAAG;AACxC,WAAO,OAAO,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,IAAI;AAAA,EACzD;AAEA,SAAO,OAAO,SAAS,gBAAgB;AACzC;;;AGzCO,IAAM,mBAAmB,CAAC,YAAoB,UAAgC;AAFrF;AAGE,QAAM,EAAE,QAAQ,IAAI,MAAM;AAE1B,QAAM,aAAa,MAAM,IAAI,QAAQ,QAAQ,MAAM,QAAQ,eAAe,CAAC;AAE3E,MAAI,WAAW,MAAM,MAAM,WAAW,OAAO,aAAa,GAAG;AAC3D,WAAO;AAAA,EACT;AAEA,QAAI,gBAAW,cAAX,mBAAsB,KAAK,UAAS,YAAY;AAClD,WAAO;AAAA,EACT;AAEA,SAAO;AACT;;;AXOO,IAAM,aAAa,uBAAU,OAA0B;AAAA,EAC5D,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,WAAW;AAAA,QACT;AAAA,UACE,UAAU;AAAA,UACV,cAAc,CAAC,cAAc,aAAa;AAAA,QAC5C;AAAA,QACA;AAAA,UACE,UAAU;AAAA,UACV,cAAc,CAAC,UAAU;AAAA,QAC3B;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,QAAQ,CAAC,EAAE,OAAO,MAAM;AACtB,YAAI,UAAU;AAEd,aAAK,QAAQ,UAAU,QAAQ,CAAC,EAAE,SAAS,MAAM;AAC/C,cAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAW;AACrD;AAAA,UACF;AAEA,cAAI,aAAa,QAAQ,QAAQ,GAAG;AAClC,sBAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,MACA,cAAc,CAAC,EAAE,OAAO,MAAM;AAC5B,YAAI,UAAU;AAEd,aAAK,QAAQ,UAAU,QAAQ,CAAC,EAAE,SAAS,MAAM;AAC/C,cAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAW;AACrD;AAAA,UACF;AAEA,cAAI,aAAa,QAAQ,QAAQ,GAAG;AAClC,sBAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,MACA,WAAW,CAAC,EAAE,OAAO,MAAM;AACzB,YAAI,UAAU;AAEd,aAAK,QAAQ,UAAU,QAAQ,CAAC,EAAE,UAAU,aAAa,MAAM;AAC7D,cAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAW;AACrD;AAAA,UACF;AAEA,cAAI,gBAAgB,QAAQ,UAAU,YAAY,GAAG;AACnD,sBAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,MACA,iBAAiB,CAAC,EAAE,OAAO,MAAM;AAC/B,YAAI,UAAU;AAEd,aAAK,QAAQ,UAAU,QAAQ,CAAC,EAAE,UAAU,aAAa,MAAM;AAC7D,cAAI,OAAO,MAAM,OAAO,MAAM,QAAQ,MAAM,QAAW;AACrD;AAAA,UACF;AAEA,cAAI,gBAAgB,QAAQ,UAAU,YAAY,GAAG;AACnD,sBAAU;AAAA,UACZ;AAAA,QACF,CAAC;AAED,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF,CAAC;", "names": ["import_core", "import_core", "import_core", "import_core", "import_core"]}