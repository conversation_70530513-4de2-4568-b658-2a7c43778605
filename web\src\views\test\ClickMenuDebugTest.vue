<template>
  <div class="click-menu-debug-test">
    <h1>点击菜单调试测试</h1>
    
    <div class="test-controls">
      <button @click="toggleEditable" :class="{ active: isEditable }">
        {{ isEditable ? '编辑模式' : '只读模式' }}
      </button>
      <button @click="clearLogs">清除日志</button>
    </div>
    
    <div class="test-instructions">
      <h3>测试说明：</h3>
      <ul>
        <li>鼠标悬停在段落左侧应该显示绿色背景的菜单容器区域</li>
        <li>在菜单区域内应该显示添加按钮(+)和拖拽手柄(⋮⋮)</li>
        <li>只有在编辑模式下菜单才会显示</li>
        <li>查看控制台日志了解详细的调试信息</li>
      </ul>
    </div>

    <div class="editor-container">
      <TipTapEditor
        v-model="content"
        :extensions="extensions"
        :all-extensions="false"
        placeholder="鼠标悬停在段落左侧查看点击菜单..."
        :character-limit="10000"
        :editable="isEditable"
        file-bucket="test"
        class="debug-editor"
      />
    </div>

    <div class="debug-info">
      <h3>调试信息：</h3>
      <div class="debug-logs">
        <div v-for="(log, index) in logs" :key="index" class="log-entry">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

import TipTapEditor from '@/components/tiptap/core/TipTapEditor.vue'

// 编辑器状态
const isEditable = ref(true)
const logs = ref<Array<{ time: string, message: string }>>([])

// 编辑器内容
const content = ref({
  type: 'doc',
  content: [
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: '这是第一个段落，鼠标悬停在左侧查看点击菜单。',
        },
      ],
    },
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: '这是第二个段落，也应该有点击菜单。',
        },
      ],
    },
    {
      type: 'heading',
      attrs: { level: 2 },
      content: [
        {
          type: 'text',
          text: '这是一个标题',
        },
      ],
    },
    {
      type: 'paragraph',
      content: [
        {
          type: 'text',
          text: '这是标题后的段落。',
        },
      ],
    },
    {
      type: 'paragraph',
    },
  ],
})

// 启用的扩展
const extensions = [
  'heading',
  'bold',
  'italic',
  'clickMenu', // 重点测试的扩展
]

// 控制方法
const toggleEditable = () => {
  isEditable.value = !isEditable.value
  addLog(`切换到${isEditable.value ? '编辑' : '只读'}模式`)
}

const clearLogs = () => {
  logs.value = []
}

const addLog = (message: string) => {
  const time = new Date().toLocaleTimeString()
  logs.value.push({ time, message })
  console.log(`[ClickMenuDebug] ${time}: ${message}`)
}

// 监听控制台日志
const originalConsoleLog = console.log
const originalConsoleError = console.error

onMounted(() => {
  addLog('调试页面已加载')
  
  // 拦截控制台日志
  console.log = (...args) => {
    const message = args.join(' ')
    if (message.includes('ClickMenu:')) {
      addLog(message)
    }
    originalConsoleLog.apply(console, args)
  }
  
  console.error = (...args) => {
    const message = args.join(' ')
    if (message.includes('ClickMenu:')) {
      addLog(`ERROR: ${message}`)
    }
    originalConsoleError.apply(console, args)
  }
})

onUnmounted(() => {
  // 恢复原始控制台方法
  console.log = originalConsoleLog
  console.error = originalConsoleError
})
</script>

<style scoped>
.click-menu-debug-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-controls {
  margin: 20px 0;
  display: flex;
  gap: 10px;
}

.test-controls button {
  padding: 8px 16px;
  border: 1px solid #ccc;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.test-controls button.active {
  background: #007bff;
  color: white;
}

.test-instructions {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 4px;
  margin: 20px 0;
}

.editor-container {
  border: 2px solid #007bff;
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  background: white;
}

.debug-editor {
  min-height: 300px;
}

.debug-info {
  margin-top: 20px;
}

.debug-logs {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
  font-family: monospace;
  font-size: 12px;
}

.log-entry {
  margin: 2px 0;
  padding: 2px 0;
  border-bottom: 1px solid #eee;
}

.log-time {
  color: #666;
  margin-right: 10px;
}

.log-message {
  color: #333;
}
</style>
