{"version": 3, "sources": ["../../src/bullet-list/bullet-list.ts"], "sourcesContent": ["import { mergeAttributes, Node, wrappingInputRule } from '@tiptap/core'\n\nconst ListItemName = 'listItem'\nconst TextStyleName = 'textStyle'\n\nexport interface BulletListOptions {\n  /**\n   * The node name for the list items\n   * @default 'listItem'\n   * @example 'paragraph'\n   */\n  itemTypeName: string\n\n  /**\n   * HTML attributes to add to the bullet list element\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n\n  /**\n   * Keep the marks when splitting the list\n   * @default false\n   * @example true\n   */\n  keepMarks: boolean\n\n  /**\n   * Keep the attributes when splitting the list\n   * @default false\n   * @example true\n   */\n  keepAttributes: boolean\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    bulletList: {\n      /**\n       * Toggle a bullet list\n       */\n      toggleBulletList: () => ReturnType\n    }\n  }\n}\n\n/**\n * Matches a bullet list to a dash or asterisk.\n */\nexport const bulletListInputRegex = /^\\s*([-+*])\\s$/\n\n/**\n * This extension allows you to create bullet lists.\n * This requires the ListItem extension\n * @see https://tiptap.dev/api/nodes/bullet-list\n * @see https://tiptap.dev/api/nodes/list-item.\n */\nexport const BulletList = Node.create<BulletListOptions>({\n  name: 'bulletList',\n\n  addOptions() {\n    return {\n      itemTypeName: 'listItem',\n      HTMLAttributes: {},\n      keepMarks: false,\n      keepAttributes: false,\n    }\n  },\n\n  group: 'block list',\n\n  content() {\n    return `${this.options.itemTypeName}+`\n  },\n\n  parseHTML() {\n    return [{ tag: 'ul' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['ul', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes), 0]\n  },\n\n  addCommands() {\n    return {\n      toggleBulletList:\n        () =>\n        ({ commands, chain }) => {\n          if (this.options.keepAttributes) {\n            return chain()\n              .toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n              .updateAttributes(ListItemName, this.editor.getAttributes(TextStyleName))\n              .run()\n          }\n          return commands.toggleList(this.name, this.options.itemTypeName, this.options.keepMarks)\n        },\n    }\n  },\n\n  addKeyboardShortcuts() {\n    return {\n      'Mod-Shift-8': () => this.editor.commands.toggleBulletList(),\n    }\n  },\n\n  addInputRules() {\n    let inputRule = wrappingInputRule({\n      find: bulletListInputRegex,\n      type: this.type,\n    })\n\n    if (this.options.keepMarks || this.options.keepAttributes) {\n      inputRule = wrappingInputRule({\n        find: bulletListInputRegex,\n        type: this.type,\n        keepMarks: this.options.keepMarks,\n        keepAttributes: this.options.keepAttributes,\n        getAttributes: () => {\n          return this.editor.getAttributes(TextStyleName)\n        },\n        editor: this.editor,\n      })\n    }\n    return [inputRule]\n  },\n})\n"], "mappings": ";AAAA,SAAS,iBAAiB,MAAM,yBAAyB;AAEzD,IAAM,eAAe;AACrB,IAAM,gBAAgB;AA8Cf,IAAM,uBAAuB;AAQ7B,IAAM,aAAa,KAAK,OAA0B;AAAA,EACvD,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,cAAc;AAAA,MACd,gBAAgB,CAAC;AAAA,MACjB,WAAW;AAAA,MACX,gBAAgB;AAAA,IAClB;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,EAEP,UAAU;AACR,WAAO,GAAG,KAAK,QAAQ,YAAY;AAAA,EACrC;AAAA,EAEA,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,KAAK,CAAC;AAAA,EACvB;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,MAAM,gBAAgB,KAAK,QAAQ,gBAAgB,cAAc,GAAG,CAAC;AAAA,EAC/E;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,kBACE,MACA,CAAC,EAAE,UAAU,MAAM,MAAM;AACvB,YAAI,KAAK,QAAQ,gBAAgB;AAC/B,iBAAO,MAAM,EACV,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS,EACvE,iBAAiB,cAAc,KAAK,OAAO,cAAc,aAAa,CAAC,EACvE,IAAI;AAAA,QACT;AACA,eAAO,SAAS,WAAW,KAAK,MAAM,KAAK,QAAQ,cAAc,KAAK,QAAQ,SAAS;AAAA,MACzF;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,uBAAuB;AACrB,WAAO;AAAA,MACL,eAAe,MAAM,KAAK,OAAO,SAAS,iBAAiB;AAAA,IAC7D;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,QAAI,YAAY,kBAAkB;AAAA,MAChC,MAAM;AAAA,MACN,MAAM,KAAK;AAAA,IACb,CAAC;AAED,QAAI,KAAK,QAAQ,aAAa,KAAK,QAAQ,gBAAgB;AACzD,kBAAY,kBAAkB;AAAA,QAC5B,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,QACX,WAAW,KAAK,QAAQ;AAAA,QACxB,gBAAgB,KAAK,QAAQ;AAAA,QAC7B,eAAe,MAAM;AACnB,iBAAO,KAAK,OAAO,cAAc,aAAa;AAAA,QAChD;AAAA,QACA,QAAQ,KAAK;AAAA,MACf,CAAC;AAAA,IACH;AACA,WAAO,CAAC,SAAS;AAAA,EACnB;AACF,CAAC;", "names": []}