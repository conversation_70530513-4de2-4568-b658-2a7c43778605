{"version": 3, "sources": ["../src/index.ts", "../src/horizontal-rule.ts"], "sourcesContent": ["import { HorizontalRule } from './horizontal-rule.js'\n\nexport * from './horizontal-rule.js'\n\nexport default HorizontalRule\n", "import { canInsertNode, isNodeSelection, mergeAttributes, Node, nodeInputRule } from '@tiptap/core'\nimport { NodeSelection, TextSelection } from '@tiptap/pm/state'\n\nexport interface HorizontalRuleOptions {\n  /**\n   * The HTML attributes for a horizontal rule node.\n   * @default {}\n   * @example { class: 'foo' }\n   */\n  HTMLAttributes: Record<string, any>\n}\n\ndeclare module '@tiptap/core' {\n  interface Commands<ReturnType> {\n    horizontalRule: {\n      /**\n       * Add a horizontal rule\n       * @example editor.commands.setHorizontalRule()\n       */\n      setHorizontalRule: () => ReturnType\n    }\n  }\n}\n\n/**\n * This extension allows you to insert horizontal rules.\n * @see https://www.tiptap.dev/api/nodes/horizontal-rule\n */\nexport const HorizontalRule = Node.create<HorizontalRuleOptions>({\n  name: 'horizontalRule',\n\n  addOptions() {\n    return {\n      HTMLAttributes: {},\n    }\n  },\n\n  group: 'block',\n\n  parseHTML() {\n    return [{ tag: 'hr' }]\n  },\n\n  renderHTML({ HTMLAttributes }) {\n    return ['hr', mergeAttributes(this.options.HTMLAttributes, HTMLAttributes)]\n  },\n\n  addCommands() {\n    return {\n      setHorizontalRule:\n        () =>\n        ({ chain, state }) => {\n          // Check if we can insert the node at the current selection\n          if (!canInsertNode(state, state.schema.nodes[this.name])) {\n            return false\n          }\n\n          const { selection } = state\n          const { $to: $originTo } = selection\n\n          const currentChain = chain()\n\n          if (isNodeSelection(selection)) {\n            currentChain.insertContentAt($originTo.pos, {\n              type: this.name,\n            })\n          } else {\n            currentChain.insertContent({ type: this.name })\n          }\n\n          return (\n            currentChain\n              // set cursor after horizontal rule\n              .command(({ tr, dispatch }) => {\n                if (dispatch) {\n                  const { $to } = tr.selection\n                  const posAfter = $to.end()\n\n                  if ($to.nodeAfter) {\n                    if ($to.nodeAfter.isTextblock) {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos + 1))\n                    } else if ($to.nodeAfter.isBlock) {\n                      tr.setSelection(NodeSelection.create(tr.doc, $to.pos))\n                    } else {\n                      tr.setSelection(TextSelection.create(tr.doc, $to.pos))\n                    }\n                  } else {\n                    // add node after horizontal rule if it’s the end of the document\n                    const node = $to.parent.type.contentMatch.defaultType?.create()\n\n                    if (node) {\n                      tr.insert(posAfter, node)\n                      tr.setSelection(TextSelection.create(tr.doc, posAfter + 1))\n                    }\n                  }\n\n                  tr.scrollIntoView()\n                }\n\n                return true\n              })\n              .run()\n          )\n        },\n    }\n  },\n\n  addInputRules() {\n    return [\n      nodeInputRule({\n        find: /^(?:---|—-|___\\s|\\*\\*\\*\\s)$/,\n        type: this.type,\n      }),\n    ]\n  },\n})\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,kBAAqF;AACrF,mBAA6C;AA2BtC,IAAM,iBAAiB,iBAAK,OAA8B;AAAA,EAC/D,MAAM;AAAA,EAEN,aAAa;AACX,WAAO;AAAA,MACL,gBAAgB,CAAC;AAAA,IACnB;AAAA,EACF;AAAA,EAEA,OAAO;AAAA,EAEP,YAAY;AACV,WAAO,CAAC,EAAE,KAAK,KAAK,CAAC;AAAA,EACvB;AAAA,EAEA,WAAW,EAAE,eAAe,GAAG;AAC7B,WAAO,CAAC,UAAM,6BAAgB,KAAK,QAAQ,gBAAgB,cAAc,CAAC;AAAA,EAC5E;AAAA,EAEA,cAAc;AACZ,WAAO;AAAA,MACL,mBACE,MACA,CAAC,EAAE,OAAO,MAAM,MAAM;AAEpB,YAAI,KAAC,2BAAc,OAAO,MAAM,OAAO,MAAM,KAAK,IAAI,CAAC,GAAG;AACxD,iBAAO;AAAA,QACT;AAEA,cAAM,EAAE,UAAU,IAAI;AACtB,cAAM,EAAE,KAAK,UAAU,IAAI;AAE3B,cAAM,eAAe,MAAM;AAE3B,gBAAI,6BAAgB,SAAS,GAAG;AAC9B,uBAAa,gBAAgB,UAAU,KAAK;AAAA,YAC1C,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH,OAAO;AACL,uBAAa,cAAc,EAAE,MAAM,KAAK,KAAK,CAAC;AAAA,QAChD;AAEA,eACE,aAEG,QAAQ,CAAC,EAAE,IAAI,SAAS,MAAM;AAzE7C;AA0EgB,cAAI,UAAU;AACZ,kBAAM,EAAE,IAAI,IAAI,GAAG;AACnB,kBAAM,WAAW,IAAI,IAAI;AAEzB,gBAAI,IAAI,WAAW;AACjB,kBAAI,IAAI,UAAU,aAAa;AAC7B,mBAAG,aAAa,2BAAc,OAAO,GAAG,KAAK,IAAI,MAAM,CAAC,CAAC;AAAA,cAC3D,WAAW,IAAI,UAAU,SAAS;AAChC,mBAAG,aAAa,2BAAc,OAAO,GAAG,KAAK,IAAI,GAAG,CAAC;AAAA,cACvD,OAAO;AACL,mBAAG,aAAa,2BAAc,OAAO,GAAG,KAAK,IAAI,GAAG,CAAC;AAAA,cACvD;AAAA,YACF,OAAO;AAEL,oBAAM,QAAO,SAAI,OAAO,KAAK,aAAa,gBAA7B,mBAA0C;AAEvD,kBAAI,MAAM;AACR,mBAAG,OAAO,UAAU,IAAI;AACxB,mBAAG,aAAa,2BAAc,OAAO,GAAG,KAAK,WAAW,CAAC,CAAC;AAAA,cAC5D;AAAA,YACF;AAEA,eAAG,eAAe;AAAA,UACpB;AAEA,iBAAO;AAAA,QACT,CAAC,EACA,IAAI;AAAA,MAEX;AAAA,IACJ;AAAA,EACF;AAAA,EAEA,gBAAgB;AACd,WAAO;AAAA,UACL,2BAAc;AAAA,QACZ,MAAM;AAAA,QACN,MAAM,KAAK;AAAA,MACb,CAAC;AAAA,IACH;AAAA,EACF;AACF,CAAC;;;AD/GD,IAAO,gBAAQ;", "names": []}